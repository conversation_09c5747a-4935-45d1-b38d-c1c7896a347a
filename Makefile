# Makefile for GeNieGO SSO

.PHONY: help install dev setup test test-watch format lint type-check clean run docker-build docker-up docker-down

help:
	@echo "GeNieGO SSO - Available Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install    Install dependencies"
	@echo "  dev        Install dev dependencies"
	@echo "  setup      Complete project setup"
	@echo ""
	@echo "Development:"
	@echo "  run        Start development server"
	@echo "  test       Run tests in Docker container"
	@echo "  test-local Run tests locally (requires MySQL)"
	@echo "  test-watch Run tests in watch mode (Docker)"
	@echo "  d          Deploy with Docker"
	@echo "  bd         Build frontend and deploy with Docker"
	@echo ""
	@echo "Code Quality:"
	@echo "  format     Format code (black + isort)"
	@echo "  lint       Run linter (flake8)"
	@echo "  type-check Run type checking (mypy + pyright)"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build Build image"
	@echo "  docker-up    Start containers"
	@echo "  docker-down  Stop containers"
	@echo ""
	@echo "Utilities:"
	@echo "  clean      Clean temporary files"

# Installation targets
install:
	pip install -r requirements.txt

dev: install
	pip install -r requirements-dev.txt

setup: dev
	black app/ tests/
	isort app/ tests/
	@echo "✅ Setup complete! Run 'make run' to start the server."

# Testing targets
test:
	@echo "Running tests in Docker container..."
	@docker exec geniengo-sso-server pytest tests/ -v

test-local:
	@echo "Running tests locally (requires local MySQL setup)..."
	pytest tests/ -v

test-watch:
	@echo "Running tests in watch mode in Docker container..."
	@docker exec geniengo-sso-server ptw tests/ --runner "pytest tests/ -v"

# Code quality targets
format:
	black app/ tests/
	isort app/ tests/

lint:
	flake8 app/ tests/

type-check:
	mypy app/
	pyright

d:
	docker compose -f docker-compose.yml up -d --build
bd:
	cd frontend && npm run build && cd .. && docker compose -f docker-compose.yml up -d --build

# Runtime targets
run:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 5550

# Docker targets
docker-build:
	docker build -t geniego-sso-server .

docker-up:
	docker compose up --build

docker-down:
	docker compose down

# Utility targets
clean:
	find . -name "__pycache__" -type d -exec rm -rf {} +
	find . -name "*.pyc" -delete
