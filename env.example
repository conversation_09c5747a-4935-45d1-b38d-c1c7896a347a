# GeNieGO SSO Server Environment Configuration

# Application Settings
PROJECT_NAME=GeNieGO SSO Server
VERSION=1.0.0
# API Configuration - Clean and scalable design
API_PREFIX=/api
API_VERSION=v1
HOST=0.0.0.0
PORT=5550
DEBUG=true
ENVIRONMENT=development

# Database Configuration - GeNieGO SSO Server
DATABASE_URL=mysql+aiomysql://geniengo_user:geniengo_password@localhost:3306/geniengo_sso_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Configuration - GeNieGO SSO Server
JWT_SECRET_KEY=geniengo-jwt-secret-key-change-in-production
ENCRYPTION_KEY=geniengo-32-byte-encryption-key-change-this
JWT_ALGORITHM=HS256
SESSION_EXPIRE_HOURS=24
AUTHORIZATION_CODE_EXPIRE_MINUTES=10

# CORS Configuration - Application Origins
CORS_ORIGINS=http://localhost:5550,http://localhost:5001,https://geniemove.yourdomain.com,https://genieflow.yourdomain.com

# Email Configuration (for password reset)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=GeNieGO SSO

# Multi-tenant Configuration
DEFAULT_ROLE_ID=2
ADMIN_ROLE_ID=1
SUPER_ADMIN_ROLE_ID=4

# Application OAuth2 Client Secrets (Change in Production!)
GENIE_MOVE_AUTH_SECRET=genie-move-auth-secret-change-in-production
GENIE_FLOW_SECRET=genie-flow-secret-change-in-production
GENIE_ADMIN_SECRET=genie-admin-secret-change-in-production

# Domain Configuration
SSO_DOMAIN=geniengo.yourdomain.com

# Security Settings (Production)
ENABLE_DOCS=true
ENABLE_ADMIN_ENDPOINTS=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
LOG_LEVEL=INFO 