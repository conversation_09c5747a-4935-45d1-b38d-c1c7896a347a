# =============================================================================
# ENVIRONMENT FILES (NEVER COMMIT PRODUCTION SECRETS)
# =============================================================================
.env
.env.local
.env.prod
.env.production
*.env.prod
*.env.production

# =============================================================================
# PYTHON
# =============================================================================
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# =============================================================================
# OPERATING SYSTEM
# =============================================================================
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# =============================================================================
# IDE AND EDITORS
# =============================================================================
.vscode/
.idea/
.augment/
*.swp
*.swo

# =============================================================================
# PYTHON DEVELOPMENT TOOLS
# =============================================================================
.ruff_cache/
.black/
.isort.cfg
pyrightconfig.json

# =============================================================================
# TESTING AND COVERAGE
# =============================================================================
htmlcov/
junit.xml
.nox/

# =============================================================================
# FASTAPI/DATABASE
# =============================================================================
*.db
*.sqlite3

# =============================================================================
# ALEMBIC (KEEP MIGRATIONS, IGNORE TEMP FILES)
# =============================================================================
alembic/versions/__pycache__/

# =============================================================================
# DOCKER AND DEPLOYMENT
# =============================================================================
docker-compose.override.yml

# =============================================================================
# DOCUMENTATION AND BUILD
# =============================================================================
site/
dist/
build/
app/static/frontend/
*.egg-info/

# =============================================================================
# LOGS AND TEMPORARY FILES
# =============================================================================
logs/
*.tmp
*.temp
*.log
.pre-commit-cache/

# Project specific
memory/
memory_bank/
reports/
base-portal-dashboard/
revamp/
