# Development Dependencies
# Install with: pip install -r requirements-dev.txt

# Include production requirements
-r requirements.txt

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
httpx>=0.24.1

# Code Quality
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
flake8-docstrings>=1.7.0
mypy>=1.5.1
pyright>=1.1.300

# Development Tools
pre-commit>=3.3.3
watchdog>=3.0.0
pytest-watch>=4.2.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.1.0

# Debugging
ipdb>=0.13.13
rich>=13.4.0

# Type Stubs
types-redis>=4.6.0.20240331
types-requests>=2.32.0.20250315
types-passlib>=1.7.7.20240312
types-python-jose>=3.3.0.20240220