import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/static/frontend/',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'lucide-react',
      '@radix-ui/react-slot',
      '@radix-ui/react-toast',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
      'framer-motion',
      'class-variance-authority',
      'tailwind-merge',
      'clsx',
      'axios',
      'react-intl',
      'date-fns'
    ],
    force: true
  },
  server: {
    fs: {
      strict: false
    }
  },
  build: {
    outDir: '../app/static/frontend',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Ensure React is in its own chunk and loaded first
          'react-core': ['react', 'react-dom'],
          'react-router': ['react-router-dom'],
          'vendor-misc': [
            'axios',
            'react-intl',
            'clsx',
            'class-variance-authority',
            'tailwind-merge',
            'framer-motion'
          ]
        },
      },
    },
    // Optimize chunk size warning limit
    chunkSizeWarningLimit: 600,
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
});
