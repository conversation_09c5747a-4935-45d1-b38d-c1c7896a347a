import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import importPlugin from 'eslint-plugin-import'
import tsParser from '@typescript-eslint/parser'

export default [
  { ignores: ['dist/**'] },
  js.configs.recommended,
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: {
        ...globals.browser
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      'import/resolver': {
        typescript: {
          project: './tsconfig.app.json',
        },
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx']
        }
      },
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'import': importPlugin,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'off', // Disable this rule as it's too restrictive for component libraries
      ],

      // Handle unused variables in function signatures (especially for interfaces and type definitions)
      'no-unused-vars': [
        'error',
        {
          'argsIgnorePattern': '^_',
          'varsIgnorePattern': '^_',
          'ignoreRestSiblings': true
        }
      ],

      // Enforce unidirectional codebase architecture
      'import/no-restricted-paths': [
        'error',
        {
          zones: [
            // Pages/App layer can import from features, but not the other way around
            {
              target: './src/features',
              from: './src/pages',
              message: 'Features should not import from pages. Keep dependencies unidirectional: pages -> features.'
            },

            // Routes can import from features, but not the other way around
            {
              target: './src/features',
              from: './src/routes',
              message: 'Features should not import from routes. Keep dependencies unidirectional: routes -> features.'
            },

            // Layout components can import from features, but not the other way around
            {
              target: './src/features',
              from: './src/layout',
              message: 'Features should not import from layout. Keep dependencies unidirectional: layout -> features.'
            },

            // Shared modules (except hooks) should not import from higher-level modules
            {
              target: [
                './src/components',
                './src/lib',
                './src/types',
                './src/utils',
                './src/constants',
              ],
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/contexts',
                './src/stores',
                './src/services',
              ],
              message: 'Shared modules should not import from higher-level modules. Keep dependencies unidirectional: higher-level -> shared.'
            },

            // Hooks have specific rules - they can import from contexts but not from UI layers
            {
              target: './src/hooks',
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/components',
              ],
              message: 'Hooks should not import from UI layers. Hooks can import from contexts, stores, services, and utilities.'
            },

            // Services should not import from UI layers
            {
              target: './src/services',
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/components',
              ],
              message: 'Services should not import from UI layers. Keep services independent of UI.'
            },

            // Stores should not import from UI layers (except types)
            {
              target: './src/stores',
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/components',
              ],
              message: 'Stores should not import from UI layers. Keep state management independent of UI.'
            },
          ],
        },
      ],
    },
  },
  // Configuration for JS files
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.node,
      }
    },
    plugins: {
      'import': importPlugin,
    },
    rules: {
      // Same import restrictions for JS files but with exceptions for hooks
      'import/no-restricted-paths': [
        'error',
        {
          zones: [
            {
              target: [
                './src/components',
                './src/lib',
                './src/types',
                './src/utils',
                './src/constants',
              ],
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/contexts',
                './src/stores',
                './src/services',
              ],
              message: 'Shared modules should not import from higher-level modules. Keep dependencies unidirectional: higher-level -> shared.'
            },
            {
              target: './src/hooks',
              from: [
                './src/features',
                './src/pages',
                './src/routes',
                './src/layout',
                './src/components',
              ],
              message: 'Hooks should not import from UI layers. Hooks can import from contexts, stores, services, and utilities.'
            },
          ],
        },
      ],
    },
  }
]
