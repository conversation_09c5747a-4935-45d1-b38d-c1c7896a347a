{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "formik": "^2.4.6", "framer-motion": "^12.23.0", "gsap": "^3.13.0", "input-otp": "^1.4.2", "lucide-react": "^0.456.0", "moment": "^2.30.1", "motion": "^12.23.6", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-hook-form": "^7.59.0", "react-intl": "^6.8.9", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "^6.28.0", "react-use-measure": "^2.1.7", "recharts": "^3.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/node": "^24.0.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.13", "typescript": "~5.6.2", "typescript-eslint": "^8.7.0", "vite": "^7.0.0"}}