import React, { createContext, useContext, useState, ReactNode } from 'react';

interface BreadcrumbContextType {
  dynamicTitles: Record<string, string>;
  setDynamicTitle: (segment: string, title: string) => void;
  removeDynamicTitle: (segment: string) => void;
  clearDynamicTitles: () => void;
}

const BreadcrumbContext = createContext<BreadcrumbContextType | undefined>(undefined);

interface BreadcrumbProviderProps {
  children: ReactNode;
}

export const BreadcrumbProvider: React.FC<BreadcrumbProviderProps> = ({ children }) => {
  const [dynamicTitles, setDynamicTitles] = useState<Record<string, string>>({});

  const setDynamicTitle = (segment: string, title: string) => {
    setDynamicTitles(prev => ({
      ...prev,
      [segment]: title
    }));
  };

  const removeDynamicTitle = (segment: string) => {
    setDynamicTitles(prev => {
      const newTitles = { ...prev };
      delete newTitles[segment];
      return newTitles;
    });
  };

  const clearDynamicTitles = () => {
    setDynamicTitles({});
  };

  return (
    <BreadcrumbContext.Provider value={{
      dynamicTitles,
      setDynamicTitle,
      removeDynamicTitle,
      clearDynamicTitles
    }}>
      {children}
    </BreadcrumbContext.Provider>
  );
};

export const useBreadcrumb = () => {
  const context = useContext(BreadcrumbContext);
  if (context === undefined) {
    throw new Error('useBreadcrumb must be used within a BreadcrumbProvider');
  }
  return context;
};
