import React, { createContext, useContext, useEffect, useState } from 'react';

// Types
interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_verified: boolean;
  role?: 'admin' | 'developer' | 'user';
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoggedIn: boolean;
  isLoading: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  logoutWithConfirmation: () => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
}

interface RegisterData {
  email: string;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5550';

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    // Check for existing session on mount
    const token = localStorage.getItem('session_token');
    if (token) {
      // TODO: Validate token and get user info from session
      validateSession();
    } else {
      setIsLoading(false);
    }
  }, []);

  const validateSession = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/auth/profile`, {
        method: 'GET',
        credentials: 'include', // Use session cookies instead of Bearer token
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        localStorage.removeItem('session_token');
        setUser(null);
      }
    } catch (error) {
      console.error('Session validation error:', error);
      localStorage.removeItem('session_token');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in request
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || errorData.error || 'Login failed');
      }

      const data = await response.json();
      // Store session token in localStorage as backup
      localStorage.setItem('session_token', data.session_token);
      setUser(data.user);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Call logout endpoint with session cookie (now uses centralized SLO)
      const response = await fetch(`${API_BASE_URL}/api/v1/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in request
      });

      if (response.ok) {
        // Successful logout - redirect to success page
        localStorage.removeItem('session_token');
        setUser(null);
        window.location.href = '/auth/logout-success?status=success';
      } else {
        // Logout failed - redirect to error page
        localStorage.removeItem('session_token');
        setUser(null);
        window.location.href = '/auth/logout-error';
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Network error - redirect to error page
      localStorage.removeItem('session_token');
      setUser(null);
      window.location.href = '/auth/logout-error';
    }
  };

  const logoutWithConfirmation = async () => {
    // This method can be used by components that want to show confirmation
    // For now, it just calls the regular logout
    // In the future, this could trigger a confirmation modal
    await logout();
  };

  const checkAuthStatus = async () => {
    try {
      // First check if we have any indication of a session before making API calls
      const hasSessionToken = localStorage.getItem('session_token');
      const hasSessionCookie = document.cookie.includes('geniengo_session');

      // Only make API call if we have some indication of authentication
      if (!hasSessionToken && !hasSessionCookie) {
        setUser(null);
        setIsLoading(false);
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/v1/auth/profile`, {
        method: 'GET',
        credentials: 'include', // Include cookies in request
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        return true;
      } else {
        setUser(null);
        localStorage.removeItem('session_token');
        return false;
      }
    } catch (error) {
      // Only log errors if they're not expected authentication failures
      if (error instanceof Error && !error.message.includes('401')) {
        console.error('Auth check error:', error);
      }
      setUser(null);
      localStorage.removeItem('session_token');
      return false;
    }
  };

  const register = async (userData: RegisterData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Registration failed');
      }

      const data = await response.json();
      // Registration doesn't automatically log in, redirect to login
      return data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Check authentication status on mount
  React.useEffect(() => {
    checkAuthStatus();
  }, []);

  const value = {
    user,
    isAuthenticated,
    isLoggedIn: isAuthenticated,
    isLoading,
    loading: isLoading,
    login,
    logout,
    logoutWithConfirmation,
    register,
    checkAuthStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
