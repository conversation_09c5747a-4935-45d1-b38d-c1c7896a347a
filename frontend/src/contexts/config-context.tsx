import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';

interface ConfigState {
  locale: string;
  setLocale: (locale: string) => void;
  onChangeLocale: (locale: string) => void;
  theme: string;
  onChangeTheme: (theme: string) => void;
  fontSize: string;
  onChangeFontSize: (fontSize: string) => void;
}

const ConfigContext = createContext<ConfigState | undefined>(undefined);

interface ConfigProviderProps {
  children: React.ReactNode;
}

export function ConfigProvider({ children }: ConfigProviderProps) {
  const [locale, setLocaleState] = useState<string>(() => {
    const stored = localStorage.getItem('locale');
    return stored || 'en';
  });

  const [theme, setThemeState] = useState<string>(() => {
    const stored = localStorage.getItem('theme');
    return stored || 'light';
  });

  const [fontSize, setFontSizeState] = useState<string>(() => {
    const stored = localStorage.getItem('fontSize');
    return stored || 'default';
  });

  // Save to localStorage whenever values change
  useEffect(() => {
    localStorage.setItem('locale', locale);
  }, [locale]);

  useEffect(() => {
    // Write to both keys to keep systems in sync
    localStorage.setItem('theme', theme);
  }, [theme]);

  useEffect(() => {
    localStorage.setItem('fontSize', fontSize);
  }, [fontSize]);

  const setLocale = useCallback((newLocale: string) => {
    setLocaleState(newLocale);
    localStorage.setItem('locale', newLocale);
  }, []);

  const setTheme = useCallback((newTheme: string) => {
    setThemeState(newTheme);
  }, []);

  const setFontSize = useCallback((newFontSize: string) => {
    setFontSizeState(newFontSize);
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    locale,
    setLocale,
    onChangeLocale: setLocale,
    theme,
    onChangeTheme: setTheme,
    fontSize,
    onChangeFontSize: setFontSize,
  }), [locale, setLocale, theme, setTheme, fontSize, setFontSize]);

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfig(): ConfigState {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
} 