/**
 * Invitation Context
 * 
 * Provides real-time invitation updates across the application
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface InvitationContextType {
  invitationCount: number;
  updateInvitationCount: (count: number) => void;
  refreshInvitations: () => void;
  refreshTrigger: number;
}

const InvitationContext = createContext<InvitationContextType | undefined>(undefined);

interface InvitationProviderProps {
  children: ReactNode;
}

export const InvitationProvider: React.FC<InvitationProviderProps> = ({ children }) => {
  const [invitationCount, setInvitationCount] = useState(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const updateInvitationCount = useCallback((count: number) => {
    setInvitationCount(count);
  }, []);

  const refreshInvitations = useCallback(() => {
    setRefreshTrigger(prev => prev + 1);
  }, []);

  const value = {
    invitationCount,
    updateInvitationCount,
    refreshInvitations,
    refreshTrigger,
  };

  return (
    <InvitationContext.Provider value={value}>
      {children}
    </InvitationContext.Provider>
  );
};

export const useInvitationContext = () => {
  const context = useContext(InvitationContext);
  if (context === undefined) {
    throw new Error('useInvitationContext must be used within an InvitationProvider');
  }
  return context;
};

export default InvitationProvider;
