import { cn } from '@/lib/utils';

interface BackgroundBoxesProps {
  className?: string;
  children?: React.ReactNode;
}

export function BackgroundBoxes({ 
  className, 
  children
}: BackgroundBoxesProps) {
  return (
    <div className={cn("relative min-h-screen overflow-hidden", className)}>
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-emerald-50/30 dark:via-emerald-950/30 to-muted" />
      
      {/* Minimalist floating boxes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large floating boxes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-emerald-500/5 dark:bg-emerald-400/10 border border-emerald-200/20 dark:border-emerald-800/20 rotate-12 animate-pulse" style={{animationDelay: '0s', animationDuration: '4s'}} />
        <div className="absolute top-40 right-20 w-24 h-24 bg-emerald-400/5 dark:bg-emerald-500/10 border border-emerald-200/15 dark:border-emerald-800/15 -rotate-6 animate-pulse" style={{animationDelay: '1s', animationDuration: '5s'}} />
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-emerald-600/5 dark:bg-emerald-300/10 border border-emerald-200/25 dark:border-emerald-800/25 rotate-45 animate-pulse" style={{animationDelay: '2s', animationDuration: '6s'}} />
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-emerald-300/5 border border-emerald-200/20 -rotate-12 animate-pulse" style={{animationDelay: '0.5s', animationDuration: '4.5s'}} />
        
        {/* Medium boxes */}
        <div className="absolute top-1/3 left-1/2 w-20 h-20 bg-emerald-500/3 border border-emerald-200/10 rotate-30 animate-pulse" style={{animationDelay: '3s', animationDuration: '7s'}} />
        <div className="absolute top-60 right-1/4 w-16 h-16 bg-emerald-400/3 border border-emerald-200/10 -rotate-30 animate-pulse" style={{animationDelay: '1.5s', animationDuration: '5.5s'}} />
        
        {/* Small accent boxes */}
        <div className="absolute top-80 left-20 w-12 h-12 bg-emerald-600/3 dark:bg-emerald-400/8 border border-emerald-200/10 dark:border-emerald-800/10 rotate-60 animate-pulse" style={{animationDelay: '2.5s', animationDuration: '8s'}} />
        <div className="absolute bottom-40 right-10 w-14 h-14 bg-emerald-300/3 dark:bg-emerald-500/8 border border-emerald-200/10 dark:border-emerald-800/10 -rotate-45 animate-pulse" style={{animationDelay: '4s', animationDuration: '6.5s'}} />
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
} 