import React from 'react';
import { <PERSON>, <PERSON>, Check } from 'lucide-react';
import useConfig from '../../hooks/use-config';
import { useIntl } from 'react-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/shadcn/dropdown-menu';
import { cn } from '../../lib/utils';

/**
 * Theme Switcher Component
 *
 * Provides a dropdown to switch between light and dark themes
 * with visual feedback showing the current selection
 */
export function ThemeSwitcher() {
  const { theme, onChangeTheme } = useConfig();
  const intl = useIntl();

  const getThemeIcon = () => {
    switch (theme) {
      case 'dark':
        return <Moon className="h-5 w-5" />;
      default:
        return <Sun className="h-5 w-5" />;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center justify-center text-muted-foreground transition-colors hover:text-primary">
        {getThemeIcon()}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[160px]">
        <DropdownMenuItem
          onClick={() => onChangeTheme('light')}
          className="flex items-center space-x-2"
        >
          <Sun className="h-4 w-4" />
          <span className="flex-1">
            {intl.formatMessage(
              { id: 'nav.user.theme.light' },
              { defaultMessage: 'Light' }
            )}
          </span>
          <div className="relative flex h-4 w-4 items-center justify-center">
            <Check
              className={cn(
                'h-4 w-4',
                theme === 'light' ? 'opacity-100' : 'opacity-0'
              )}
            />
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onChangeTheme('dark')}
          className="flex items-center space-x-2"
        >
          <Moon className="h-4 w-4" />
          <span className="flex-1">
            {intl.formatMessage(
              { id: 'nav.user.theme.dark' },
              { defaultMessage: 'Dark' }
            )}
          </span>
          <div className="relative flex h-4 w-4 items-center justify-center">
            <Check
              className={cn(
                'h-4 w-4',
                theme === 'dark' ? 'opacity-100' : 'opacity-0'
              )}
            />
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
