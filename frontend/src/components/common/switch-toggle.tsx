"use client"

import * as React from "react"
import { motion } from "motion/react"

interface SwitchToggleOption {
  value: string
  icon: React.ReactNode
  label: string
}

interface SwitchToggleProps {
  options: SwitchToggleOption[]
  value: string
  onChange: (value: string) => void
  className?: string
}

export function SwitchToggle({ options, value, onChange, className = "" }: SwitchToggleProps) {
  const activeIndex = options.findIndex(option => option.value === value)
  
  return (
    <div className={`relative flex h-10 items-center bg-neutral-200/60 dark:bg-neutral-600/80 rounded-lg p-1 ${className}`}>
      <motion.div
        className="absolute h-8 bg-primary rounded-md"
        animate={{
          x: activeIndex * 40,
          width: 40
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      />
      {options.map((option, index) => (
        <motion.button
          key={option.value}
          whileTap={{ scale: 0.95 }}
          className={`relative z-10 flex h-8 w-10 items-center justify-center rounded-md transition-colors ${
            value === option.value
              ? 'text-primary-foreground'
              : 'text-neutral-600 dark:text-neutral-200'
          }`}
          onClick={() => onChange(option.value)}
          title={option.label}
        >
          {option.icon}
        </motion.button>
      ))}
    </div>
  )
}
