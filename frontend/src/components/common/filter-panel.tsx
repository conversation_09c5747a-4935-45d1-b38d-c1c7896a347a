import { useState, useRef, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Filter, X, ChevronRight, Search, Command } from 'lucide-react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

/**
 * Interface for filter option items
 */
export interface FilterOption {
  id: string;
  name: string;
  icon?: LucideIcon;
}

/**
 * Props for the FilterPanel component
 */
export interface FilterPanelProps {
  /**
   * Title for the filter panel on mobile view
   */
  title?: string;
  /**
   * Filter categories with their options
   */
  filterCategories: {
    id: string;
    name: string;
    options: FilterOption[];
    color?: 'primary' | 'secondary' | 'destructive' | 'accent';
    selected: string | string[];
    onSelect: (id: string | string[]) => void;
    icon?: LucideIcon;
    multiSelect?: boolean;
  }[];
  /**
   * Current search query
   */
  searchQuery?: string;
  /**
   * Function to handle search query changes
   */
  onSearchChange?: (query: string) => void;
  /**
   * Function to clear search query
   */
  onClearSearch?: () => void;
  /**
   * Function to reset all filters
   */
  onResetAll: () => void;
  /**
   * Visual style variant
   */
  variant?: 'default' | 'compact';
  /**
   * Whether to show icons in filter options
   */
  showIcons?: boolean;
  /**
   * Whether to show the integrated search bar
   */
  showSearchBar?: boolean;
  /**
   * Search bar placeholder text
   */
  searchPlaceholder?: string;
  /**
   * Hotkey combination to focus the search bar (e.g., 'cmd+f', 'ctrl+f')
   */
  searchHotkey?: string;
  /**
   * Size variant for tags and options
   */
  size?: 'sm' | 'md' | 'lg';
}

/**
 * A reusable filter panel component for filtering content
 * 
 * Features:
 * - Mobile responsive with collapsible interface
 * - Multiple filter categories with color coding
 * - Active filters display
 * - Individual and global reset options
 * - Support for category and option icons
 * - Integrated search bar with hotkey support
 * - OS-specific hotkey detection (Cmd on macOS, Ctrl on Windows/Linux)
 */
const FilterPanel = ({
  title = 'Filter',
  filterCategories,
  searchQuery = '',
  onSearchChange,
  onClearSearch,
  onResetAll,
  variant = 'default',
  showIcons = false,
  showSearchBar = false,
  searchPlaceholder = 'Search...',
  searchHotkey,
  size = 'md'
}: FilterPanelProps) => {
  const intl = useIntl();
  const [filterMenuOpen, setFilterMenuOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Detect OS and set default hotkey
  const isMac = typeof window !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const defaultHotkey = isMac ? 'cmd+f' : 'ctrl+f';
  const activeHotkey = searchHotkey || defaultHotkey;

  // Parse hotkey string
  const parseHotkey = (hotkey: string) => {
    const parts = hotkey.toLowerCase().split('+');
    return {
      key: parts[parts.length - 1],
      cmd: parts.includes('cmd'),
      ctrl: parts.includes('ctrl'),
      alt: parts.includes('alt'),
      shift: parts.includes('shift')
    };
  };

  // Handle hotkey press
  useEffect(() => {
    if (!showSearchBar) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const hotkey = parseHotkey(activeHotkey);
      
      const isMatch = 
        event.key.toLowerCase() === hotkey.key &&
        event.metaKey === hotkey.cmd &&
        event.ctrlKey === hotkey.ctrl &&
        event.altKey === hotkey.alt &&
        event.shiftKey === hotkey.shift;

      if (isMatch) {
        event.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [activeHotkey, showSearchBar]);

  // Format hotkey for display
  const formatHotkeyDisplay = (hotkey: string) => {
    const parts = hotkey.toLowerCase().split('+');
    return parts.map(part => {
      switch (part) {
        case 'cmd': return isMac ? '⌘' : 'Cmd';
        case 'ctrl': return isMac ? '⌃' : 'Ctrl';
        case 'alt': return isMac ? '⌥' : 'Alt';
        case 'shift': return isMac ? '⇧' : 'Shift';
        default: return part.toUpperCase();
      }
    }).join(isMac ? '' : '+');
  };

  // Check if any filters are active
  const hasActiveFilters = filterCategories.some(cat => {
    if (cat.multiSelect) {
      return Array.isArray(cat.selected) ? cat.selected.length > 0 : false;
    }
    return cat.selected !== 'all';
  }) || !!searchQuery;

  // Helper function to check if an option is selected
  const isOptionSelected = (category: any, optionId: string) => {
    if (category.multiSelect) {
      return Array.isArray(category.selected) ? category.selected.includes(optionId) : false;
    }
    return category.selected === optionId;
  };

  // Helper function to handle option selection
  const handleOptionSelect = (category: any, optionId: string) => {
    if (category.multiSelect) {
      const currentSelected = Array.isArray(category.selected) ? category.selected : [];
      if (optionId === 'all') {
        category.onSelect([]);
      } else if (currentSelected.includes(optionId)) {
        category.onSelect(currentSelected.filter((id: string) => id !== optionId));
      } else {
        category.onSelect([...currentSelected, optionId]);
      }
    } else {
      category.onSelect(optionId);
    }
  };

  // Get size classes
  const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
    const sizeMap = {
      sm: {
        button: "px-2 py-0.5 text-xs",
        icon: "h-2.5 w-2.5",
        badge: "text-xs py-0.5 px-1.5",
        badgeIcon: "h-2.5 w-2.5"
      },
      md: {
        button: "px-3 py-1 text-xs",
        icon: "h-3 w-3",
        badge: "text-xs py-0.5 px-2",
        badgeIcon: "h-3 w-3"
      },
      lg: {
        button: "px-4 py-1.5 text-sm",
        icon: "h-4 w-4",
        badge: "text-sm py-1 px-2.5",
        badgeIcon: "h-3.5 w-3.5"
      }
    };
    return sizeMap[size];
  };

  const sizeClasses = getSizeClasses(size);

  // Toggle filter menu on mobile
  const toggleFilterMenu = () => {
    setFilterMenuOpen(prev => !prev);
  };

  // Standardized color styling for both filter buttons and active badges
  const getStandardizedColorStyle = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary') => {
    const colorMap = {
      primary: "bg-primary/10 text-primary border-primary/20 dark:bg-primary/20 dark:text-primary dark:border-primary/50 dark:shadow-sm dark:ring-1 dark:ring-primary/20",
      secondary: "bg-blue-100 text-blue-800 border border-blue-200 dark:bg-secondary/20 dark:text-secondary-foreground dark:border-secondary/50 dark:shadow-sm dark:ring-1 dark:ring-secondary/20",
      destructive: "bg-red-100 text-red-800 border border-red-200 dark:bg-destructive/20 dark:text-destructive dark:border-destructive/50 dark:shadow-sm dark:ring-1 dark:ring-destructive/20",
      accent: "bg-purple-100 text-purple-800 border border-purple-200 dark:bg-accent/20 dark:text-accent-foreground dark:border-accent/50 dark:shadow-sm dark:ring-1 dark:ring-accent/20"
    };
    return colorMap[color];
  };

  // Get button style based on category color and selection state
  const getButtonStyle = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary', isSelected: boolean) => {
    const baseClasses = `${sizeClasses.button} rounded-md font-medium transition-all duration-200 border`;

    if (!isSelected) {
      return cn(baseClasses, "bg-muted text-muted-foreground hover:bg-accent border-border hover:border-accent-foreground");
    }

    return cn(baseClasses, getStandardizedColorStyle(color));
  };

  // Get dot color class
  const getDotColorClass = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary') => {
    const colorMap = {
      primary: "bg-primary",
      secondary: "bg-secondary",
      destructive: "bg-destructive",
      accent: "bg-accent"
    };

    return colorMap[color];
  };

  // Get hover text color for buttons
  const getHoverTextColor = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary') => {
    const colorMap = {
      primary: "hover:text-primary",
      secondary: "hover:text-secondary",
      destructive: "hover:text-destructive",
      accent: "hover:text-accent"
    };

    return colorMap[color];
  };

  // Get badge style for active filters - uses same standardized styling as buttons
  const getBadgeStyle = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary') => {
    return cn(sizeClasses.badge, "rounded-full flex items-center", getStandardizedColorStyle(color));
  };

  // Get text color for icons
  const getIconColor = (color: 'primary' | 'secondary' | 'destructive' | 'accent' = 'primary', isSelected: boolean = false) => {
    if (!isSelected) return "text-muted-foreground";

    const colorMap = {
      primary: "text-primary",
      secondary: "text-secondary-foreground",
      destructive: "text-destructive",
      accent: "text-accent-foreground"
    };

    return colorMap[color];
  };

  return (
    <div className="bg-card rounded-lg shadow-sm border overflow-hidden">
      {/* Mobile filter toggle */}
      <div className="md:hidden border-b">
        <button
          onClick={toggleFilterMenu}
          className="w-full flex items-center justify-between p-4 text-sm font-medium text-card-foreground hover:bg-accent transition-colors"
        >
          <span className="flex items-center">
            <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
            {title}
          </span>
          <ChevronRight className={`h-4 w-4 text-muted-foreground transition-transform duration-200 ${filterMenuOpen ? 'rotate-90' : ''}`} />
        </button>
      </div>

      {/* Filter content */}
      <div className={`${filterMenuOpen ? 'block' : 'hidden md:block'}`}>
        {/* Search Bar */}
        {showSearchBar && (
          <div className={`${variant === 'compact' ? 'p-3' : 'p-4'}`}>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-muted-foreground" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => onSearchChange?.(e.target.value)}
                placeholder={searchPlaceholder}
                className="block w-full pl-10 pr-16 py-2 border rounded-md leading-5 bg-background placeholder-muted-foreground focus:outline-none focus:placeholder-muted-foreground focus:ring-1 focus:ring-primary focus:border-primary text-sm text-foreground"
              />
              <div className="absolute inset-y-0 right-0 flex items-center">
                {searchQuery && (
                  <button
                    onClick={() => {
                      onClearSearch?.();
                      onSearchChange?.('');
                    }}
                    className="p-1 mr-1 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
                <div className="flex items-center pr-3 text-xs text-muted-foreground border-l ml-1 pl-2">
                  <span className="hidden sm:inline-flex items-center space-x-1">
                    {isMac ? (
                      <>
                        <Command className="h-3 w-3" />
                        <span>F</span>
                      </>
                    ) : (
                      <span>{formatHotkeyDisplay(activeHotkey)}</span>
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className={`${variant === 'compact' ? 'p-3' : 'p-4'} ${showSearchBar ? 'pt-2' : ''} grid md:grid-cols-${Math.min(filterCategories.length, 3)} gap-4`}>
          {/* Render each filter category */}
          {filterCategories.map((category) => {
            const color = category.color || 'primary';
            const CategoryIcon = category.icon;

            return (
              <div key={category.id}>
                <div className="flex items-center justify-between mb-2.5">
                  <label className="text-sm font-medium text-card-foreground flex items-center">
                    {showIcons && CategoryIcon ? (
                      <CategoryIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                    ) : (
                      <span className={cn("inline-block w-2 h-2 rounded-full mr-2", getDotColorClass(color))}></span>
                    )}
                    {category.name}
                  </label>
                  {(category.multiSelect && Array.isArray(category.selected) && category.selected.length > 0) && (
                    <button
                      onClick={() => category.onSelect([])}
                      className={cn("text-xs text-muted-foreground flex items-center", getHoverTextColor(color))}
                    >
                      <X className="h-3 w-3 mr-1" />
                      Clear
                    </button>
                  )}
                  {(!category.multiSelect && category.selected !== 'all') && (
                    <button
                      onClick={() => category.onSelect('all')}
                      className={cn("text-xs text-muted-foreground flex items-center", getHoverTextColor(color))}
                    >
                      <X className="h-3 w-3 mr-1" />
                      {intl.formatMessage({ id: 'components.filters.clear' })}
                    </button>
                  )}
                </div>
                <div className="flex flex-wrap gap-1.5">
                  {category.options.map(option => {
                    const isSelected = isOptionSelected(category, option.id);
                    const OptionIcon = option.icon;

                    return (
                      <button
                        key={option.id}
                        onClick={() => handleOptionSelect(category, option.id)}
                        className={getButtonStyle(color, isSelected)}
                      >
                        {showIcons && OptionIcon && (
                          <span className="mr-1.5 inline-flex items-center">
                            <OptionIcon
                              className={cn(
                                sizeClasses.icon,
                                getIconColor(color, isSelected)
                              )}
                            />
                          </span>
                        )}
                        {option.name}
                      </button>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* Active filters */}
        {hasActiveFilters && (
          <div className="bg-muted px-4 py-2 border-t flex items-center justify-between">
            <div className="flex items-center flex-wrap gap-2">
              <span className="text-xs text-muted-foreground">{intl.formatMessage({ id: 'components.filters.activeFilters' })}:</span>

              {/* Render active category filters */}
              {filterCategories.map(category => {
                const color = category.color || 'primary';
                
                if (category.multiSelect) {
                  const selectedOptions = Array.isArray(category.selected) ? category.selected : [];
                  if (selectedOptions.length === 0) return null;
                  
                  return selectedOptions.map(selectedId => {
                    const selectedOption = category.options.find(opt => opt.id === selectedId);
                    if (!selectedOption) return null;
                    
                    const OptionIcon = selectedOption.icon;
                    
                    return (
                      <span
                        key={`${category.id}-${selectedId}`}
                        className={getBadgeStyle(color)}
                      >
                        {showIcons && OptionIcon && (
                          <OptionIcon className={cn(sizeClasses.badgeIcon, "mr-1", getIconColor(color, true))} />
                        )}
                        {selectedOption.name}
                        <button
                          onClick={() => {
                            const currentSelected = Array.isArray(category.selected) ? category.selected : [];
                            category.onSelect(currentSelected.filter((id: string) => id !== selectedId));
                          }}
                          className={cn("ml-1", getHoverTextColor(color))}
                        >
                          <X className={cn(sizeClasses.badgeIcon)} />
                        </button>
                      </span>
                    );
                  });
                } else {
                  if (category.selected === 'all') return null;

                  const selectedOption = category.options.find(opt => opt.id === category.selected);
                  if (!selectedOption) return null;

                  const OptionIcon = selectedOption.icon;

                  return (
                    <span
                      key={`${category.id}-${category.selected}`}
                      className={getBadgeStyle(color)}
                    >
                      {showIcons && OptionIcon && (
                        <OptionIcon className={cn(sizeClasses.badgeIcon, "mr-1", getIconColor(color, true))} />
                      )}
                      {selectedOption.name}
                      <button
                        onClick={() => category.onSelect('all')}
                        className={cn("ml-1", getHoverTextColor(color))}
                      >
                        <X className={cn(sizeClasses.badgeIcon)} />
                      </button>
                    </span>
                  );
                }
              })}

              {/* Show search query if present */}
              {searchQuery && (
                <span className={cn(
                  sizeClasses.badge,
                  "bg-secondary text-secondary-foreground border border-border rounded-full flex items-center"
                )}>
                  "{searchQuery}"
                  <button
                    onClick={() => {
                      onClearSearch?.();
                      onSearchChange?.('');
                    }}
                    className="ml-1 text-secondary-foreground hover:text-foreground"
                  >
                    <X className={cn(sizeClasses.badgeIcon)} />
                  </button>
                </span>
              )}
            </div>
            <button
              onClick={onResetAll}
              className="text-xs text-primary hover:text-primary-600 font-medium"
            >
              {intl.formatMessage({ id: 'components.filters.resetAll' })}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterPanel; 