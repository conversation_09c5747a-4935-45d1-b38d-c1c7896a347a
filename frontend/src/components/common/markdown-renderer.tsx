import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useIntl } from 'react-intl';

/**
 * MarkdownRender Component
 *
 * A reusable component that renders markdown content with variable substitution.
 * Supports replacing placeholders with actual values and proper styling.
 * Used primarily for policy pages and other content-heavy pages.
 *
 * @param content - The markdown content to render
 * @param variables - An object containing key-value pairs for variable substitution
 * @param title - Optional title to display above the content
 * @param lastUpdated - Optional last updated date to display
 */
interface MarkdownRenderProps {
  content: string;
  variables?: Record<string, string>;
  title?: string;
  lastUpdated?: string;
  className?: string;
}

const MarkdownRender: React.FC<MarkdownRenderProps> = ({
  content,
  variables = {},
  title,
  lastUpdated,
  className = '',
}) => {
  const intl = useIntl();

  // Process content by replacing all variables
  const processedContent = useMemo(() => {
    let result = content;

    // Replace all variables in the content
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{${key}}`, 'g');
      result = result.replace(regex, value);
    });

    // Process any double asterisk (**) that aren't being rendered correctly
    // This ensures that **bold text** is properly converted to <strong>bold text</strong>
    // First, ensure that we don't touch code blocks or inline code
    const codeBlockRegex = /```[\s\S]*?```/g;
    const inlineCodeRegex = /`[^`]+`/g;

    const codeBlocks: string[] = [];
    const inlineCodes: string[] = [];

    // Extract code blocks
    result = result.replace(codeBlockRegex, match => {
      codeBlocks.push(match);
      return `%%CODEBLOCK${codeBlocks.length - 1}%%`;
    });

    // Extract inline code
    result = result.replace(inlineCodeRegex, match => {
      inlineCodes.push(match);
      return `%%INLINECODE${inlineCodes.length - 1}%%`;
    });

    // Now process the ** for bold text
    result = result.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Restore code blocks
    codeBlocks.forEach((block, i) => {
      result = result.replace(`%%CODEBLOCK${i}%%`, block);
    });

    // Restore inline code
    inlineCodes.forEach((code, i) => {
      result = result.replace(`%%INLINECODE${i}%%`, code);
    });

    return result;
  }, [content, variables]);

  // Custom components for rendering markdown with proper styling
  const components = {
    // Headings with proper spacing and sizing
    h1: ({ ...props }: any) => (
      <h1 className="mb-4 mt-8 text-3xl font-bold" {...props} />
    ),
    h2: ({ ...props }: any) => (
      <h2
        className="mb-4 mt-8 text-2xl font-semibold text-foreground"
        {...props}
      />
    ),
    h3: ({ ...props }: any) => (
      <h3
        className="mb-3 mt-6 text-xl font-semibold text-foreground"
        {...props}
      />
    ),
    h4: ({ ...props }: any) => (
      <h4
        className="mb-2 mt-4 text-lg font-semibold text-foreground"
        {...props}
      />
    ),

    // Paragraphs with proper spacing
    p: ({ ...props }: any) => (
      <p className="my-4 leading-relaxed text-foreground" {...props} />
    ),

    // Lists with proper indentation and bullets
    ul: ({ ...props }: any) => (
      <ul className="my-4 list-disc space-y-2 pl-8" {...props} />
    ),
    ol: ({ ...props }: any) => (
      <ol className="my-4 list-decimal space-y-2 pl-8" {...props} />
    ),
    li: ({ ...props }: any) => <li className="mb-1" {...props} />,

    // Links with proper styling
    a: ({ href, ...props }: any) => {
      const isMailTo = href?.startsWith('mailto:');
      if (isMailTo) {
        return (
          <a href={href} className="text-primary hover:underline" {...props} />
        );
      }

      const isExternal = href?.startsWith('http');
      if (isExternal) {
        return (
          <a
            href={href}
            className="text-primary hover:underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        );
      }

      return (
        <a
          href={href || '#'}
          className="text-primary hover:underline"
          {...props}
        />
      );
    },

    // Block quotes with proper styling
    blockquote: ({ ...props }: any) => (
      <blockquote
        className="my-4 border-l-4 border-border pl-4 italic text-muted-foreground"
        {...props}
      />
    ),

    // Code blocks with proper styling
    code: ({ inline, className, children, ...props }: any) => {
      if (inline) {
        return (
          <code
            className="rounded bg-muted px-1 py-0.5 font-mono text-sm text-muted-foreground"
            {...props}
          >
            {children}
          </code>
        );
      }

      return (
        <pre className="my-4 overflow-x-auto rounded-md bg-muted p-4">
          <code className={`${className || ''} font-mono text-sm`} {...props}>
            {children}
          </code>
        </pre>
      );
    },

    // Tables with proper styling
    table: ({ ...props }: any) => (
      <div className="my-6 overflow-x-auto">
        <table
          className="min-w-full border border-border bg-card"
          {...props}
        />
      </div>
    ),
    thead: ({ ...props }: any) => <thead className="bg-muted" {...props} />,
    tbody: ({ ...props }: any) => (
      <tbody className="divide-y divide-border" {...props} />
    ),
    tr: ({ ...props }: any) => <tr className="hover:bg-accent" {...props} />,
    th: ({ ...props }: any) => (
      <th
        className="border-b px-4 py-3 text-left text-sm font-semibold text-card-foreground"
        {...props}
      />
    ),
    td: ({ ...props }: any) => (
      <td className="border-b px-4 py-3 text-sm text-card-foreground" {...props} />
    ),

    // Strong and emphasis with proper styling
    strong: ({ ...props }: any) => (
      <strong className="font-semibold" {...props} />
    ),
    em: ({ ...props }: any) => <em className="italic" {...props} />,

    // Horizontal rule with proper styling
    hr: () => <hr className="my-8" />,
  };

  return (
    <div className={`container mx-auto px-4 py-8 ${className}`}>
      <div className="mx-auto max-w-4xl">
        {title && <h1 className="mb-6 text-3xl font-bold">{title}</h1>}

        {lastUpdated && (
          <p className="mb-6 text-sm text-muted-foreground">
            {intl.formatMessage(
              { id: 'page.policy.last.updated' },
              { date: lastUpdated }
            )}
          </p>
        )}

        <div className="prose prose-lg dark:prose-invert policy-content max-w-none">
          <ReactMarkdown
            components={components}
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {processedContent}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export default MarkdownRender;
