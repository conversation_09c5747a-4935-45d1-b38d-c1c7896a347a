import React from 'react';
import { Globe, Check } from 'lucide-react';
import { useConfig } from '../../contexts/config-context';
import { useIntl } from 'react-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/shadcn/dropdown-menu';
import { cn } from '../../lib/utils';

/**
 * Language Switcher Component
 * 
 * Provides a dropdown to switch between English, Chinese (Simplified), and Chinese (Traditional)
 * with visual feedback showing the current selection
 */
export function LanguageSwitcher() {
  const { locale, onChangeLocale } = useConfig();
  const intl = useIntl();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center justify-center text-gray-600 transition-colors hover:text-primary">
        <Globe className="h-5 w-5" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[180px]">
        <DropdownMenuItem
          onClick={() => onChangeLocale('en')}
          className="flex items-center space-x-2"
        >
          <div className="relative flex h-4 w-4 items-center justify-center">
            <Check
              className={cn(
                'h-4 w-4',
                locale === 'en' ? 'opacity-100' : 'opacity-0'
              )}
            />
          </div>
          <span>
            {intl.formatMessage(
              { id: 'nav.user.language.english' },
              { defaultMessage: 'English' }
            )}
          </span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onChangeLocale('zh')}
          className="flex items-center space-x-2"
        >
          <div className="relative flex h-4 w-4 items-center justify-center">
            <Check
              className={cn(
                'h-4 w-4',
                locale === 'zh' ? 'opacity-100' : 'opacity-0'
              )}
            />
          </div>
          <span>
            {intl.formatMessage(
              { id: 'nav.user.language.chinese' },
              { defaultMessage: '中文 (简体)' }
            )}
          </span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onChangeLocale('zh-TW')}
          className="flex items-center space-x-2"
        >
          <div className="relative flex h-4 w-4 items-center justify-center">
            <Check
              className={cn(
                'h-4 w-4',
                locale === 'zh-TW' ? 'opacity-100' : 'opacity-0'
              )}
            />
          </div>
          <span>
            {intl.formatMessage(
              { id: 'nav.user.language.traditional' },
              { defaultMessage: '中文 (繁體)' }
            )}
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 