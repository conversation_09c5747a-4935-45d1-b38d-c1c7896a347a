"use client"

import * as React from "react"
import { motion, type Variants, type Transition } from "motion/react"

// Button motion configuration for expandable buttons
const BUTTON_MOTION_CONFIG = {
  initial: "rest",
  whileHover: "hover",
  whileTap: "tap",
  variants: {
    rest: { maxWidth: "40px" },
    hover: {
      maxWidth: "140px",
      transition: { type: "spring", stiffness: 200, damping: 35, delay: 0.15 },
    },
    tap: { scale: 0.95 },
  },
  transition: { type: "spring", stiffness: 250, damping: 25 },
} as const

const LABEL_VARIANTS: Variants = {
  rest: { opacity: 0, x: 4 },
  hover: { opacity: 1, x: 0, visibility: "visible" },
  tap: { opacity: 1, x: 0, visibility: "visible" },
}

const LABEL_TRANSITION: Transition = {
  type: "spring",
  stiffness: 200,
  damping: 25,
}

interface ExpandableButtonProps {
  icon: React.ReactNode
  label: string
  onClick?: () => void
  className?: string
  variant?: 'default' | 'primary'
  maxWidth?: string
  'aria-label'?: string
}

export function ExpandableButton({ 
  icon, 
  label, 
  onClick, 
  className = "", 
  variant = 'default',
  maxWidth = "140px",
  'aria-label': ariaLabel
}: ExpandableButtonProps) {
  const baseClasses = "flex h-10 items-center space-x-2 overflow-hidden whitespace-nowrap rounded-lg px-2.5 py-2"
  const variantClasses = variant === 'primary' 
    ? "bg-primary text-primary-foreground" 
    : "bg-neutral-200/60 dark:bg-neutral-600/80 text-neutral-600 dark:text-neutral-200"

  const customMotionConfig = {
    ...BUTTON_MOTION_CONFIG,
    variants: {
      ...BUTTON_MOTION_CONFIG.variants,
      hover: {
        ...BUTTON_MOTION_CONFIG.variants.hover,
        maxWidth: maxWidth,
      }
    }
  }

  return (
    <motion.button
      {...customMotionConfig}
      className={`${baseClasses} ${variantClasses} ${className}`}
      onClick={onClick}
      aria-label={ariaLabel || label}
    >
      <div className="shrink-0">
        {icon}
      </div>
      <motion.span
        variants={LABEL_VARIANTS}
        transition={LABEL_TRANSITION}
        className="invisible text-sm"
      >
        {label}
      </motion.span>
    </motion.button>
  )
}
