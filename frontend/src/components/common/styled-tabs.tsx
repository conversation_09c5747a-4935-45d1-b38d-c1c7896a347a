import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

/**
 * Interface for tab item configuration
 */
export interface StyledTabItem {
  id: string;
  label: string;
  isActive?: boolean;
}

/**
 * Props for the StyledTabs component
 */
export interface StyledTabsProps {
  /**
   * Array of tab items to display
   */
  tabs: StyledTabItem[];
  /**
   * Currently active tab ID
   */
  activeTab: string;
  /**
   * Function called when a tab is clicked
   */
  onTabChange: (tabId: string) => void;
  /**
   * Additional CSS classes for the container
   */
  className?: string;
  /**
   * Size variant for the tabs
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Style variant for the tabs
   */
  variant?: 'default' | 'underline' | 'pills';
}

/**
 * A styled tab navigation component that matches the design from the image
 * 
 * Features:
 * - Clean, modern design with subtle borders
 * - Hover and active states
 * - Multiple size variants
 * - Responsive design
 * - Accessible keyboard navigation
 */
const StyledTabs: React.FC<StyledTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
  size = 'md',
  variant = 'default'
}) => {
  // Size configuration
  const sizeClasses = {
    sm: {
      container: 'h-8',
      tab: 'px-3 py-1 text-xs',
      indicator: 'h-0.5'
    },
    md: {
      container: 'h-10',
      tab: 'px-4 py-2 text-sm',
      indicator: 'h-0.5'
    },
    lg: {
      container: 'h-12',
      tab: 'px-6 py-3 text-base',
      indicator: 'h-1'
    }
  };

  const currentSize = sizeClasses[size];

  // Variant styles with dark mode support
  const getVariantStyles = () => {
    switch (variant) {
      case 'underline':
        return {
          container: 'border-b border-border',
          tab: 'border-b-2 border-transparent relative',
          activeTab: 'border-foreground text-foreground',
          inactiveTab: 'text-muted-foreground hover:text-foreground hover:border-muted-foreground'
        };
      case 'pills':
        return {
          container: 'bg-muted rounded-lg p-1',
          tab: 'rounded-md transition-all duration-200',
          activeTab: 'bg-background text-foreground shadow-sm',
          inactiveTab: 'text-muted-foreground hover:text-foreground hover:bg-accent'
        };
      default:
        return {
          container: 'border-b border-border bg-background relative',
          tab: 'relative transition-all duration-200',
          activeTab: 'text-primary font-semibold',
          inactiveTab: 'text-muted-foreground hover:text-foreground'
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent, tabId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onTabChange(tabId);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <nav 
        className={cn(
          'flex relative',
          currentSize.container,
          variantStyles.container
        )}
        role="tablist"
        aria-label="Tab navigation"
      >
        {tabs.map((tab, index) => {
          const isActive = tab.id === activeTab;
          
          return (
            <button
              key={tab.id}
              role="tab"
              tabIndex={isActive ? 0 : -1}
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              onClick={() => onTabChange(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
              className={cn(
                'font-medium transition-all duration-200 focus:outline-none whitespace-nowrap relative',
                currentSize.tab,
                variantStyles.tab,
                isActive ? variantStyles.activeTab : variantStyles.inactiveTab
              )}
            >
              {tab.label}
              {/* Individual tab indicator */}
              {variant === 'default' && isActive && (
                <motion.div
                  className={cn('absolute bottom-0 left-0 right-0 bg-primary', currentSize.indicator)}
                  layoutId="tabIndicator"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 30,
                  }}
                />
              )}
            </button>
          );
        })}
      </nav>
    </div>
  );
};

export default StyledTabs;
