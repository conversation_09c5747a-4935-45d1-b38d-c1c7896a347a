import { Suspense, ElementType } from 'react';

// project imports
import { PageLoader } from './page-loader';

// ==============================|| LOADABLE - LAZY LOADING ||============================== //

/**
 * Simple and clean lazy loading wrapper for template projects
 *
 * Usage:
 * const LazyComponent = Loadable(lazy(() => import('./MyComponent')));
 *
 * This provides:
 * - Consistent loading states across the app
 * - Error boundaries (handled by React)
 * - Simple, maintainable pattern
 */
export const Loadable = (Component: ElementType) => (props: any) => (
  <Suspense fallback={<PageLoader />}>
    <Component {...props} />
  </Suspense>
);

export default Loadable;
