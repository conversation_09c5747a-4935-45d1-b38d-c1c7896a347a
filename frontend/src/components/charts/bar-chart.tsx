/**
 * Bar Chart Component
 *
 * Reusable bar chart component using Chart.js for displaying
 * categorical data, comparisons, and statistical information.
 */

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export interface BarChartData {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
}

export interface BarChartProps {
  title?: string;
  labels: string[];
  datasets: BarChartData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  horizontal?: boolean;
  stacked?: boolean;
  className?: string;
}

export const BarChart: React.FC<BarChartProps> = ({
  title,
  labels,
  datasets,
  height = 300,
  showLegend = true,
  showGrid = true,
  horizontal = false,
  stacked = false,
  className = '',
}) => {
  // Add null checks for labels and datasets
  const safeLabels = labels || [];
  const safeDatasets = datasets || [];

  const data = {
    labels: safeLabels,
    datasets: safeDatasets.map((dataset, index) => ({
      label: dataset.label,
      data: dataset.data || [],
      backgroundColor: dataset.backgroundColor ||
        (Array.isArray(dataset.data)
          ? (dataset.data || []).map((_, i) => `hsla(${(index * 60 + i * 30) % 360}, 70%, 50%, 0.8)`)
          : `hsla(${index * 60}, 70%, 50%, 0.8)`),
      borderColor: dataset.borderColor ||
        (Array.isArray(dataset.data)
          ? (dataset.data || []).map((_, i) => `hsl(${(index * 60 + i * 30) % 360}, 70%, 40%)`)
          : `hsl(${index * 60}, 70%, 40%)`),
      borderWidth: dataset.borderWidth || 1,
      borderRadius: 4,
      borderSkipped: false,
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: horizontal ? ('y' as const) : ('x' as const),
    plugins: {
      legend: {
        display: showLegend,
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
        padding: {
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#50C878',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.y || context.parsed.x;
            return `${label}: ${value.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        stacked,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 11,
          },
        },
      },
      y: {
        display: true,
        stacked,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 11,
          },
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      intersect: true,
    },
  };

  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <Bar data={data} options={options} />
    </div>
  );
};

export default BarChart;
