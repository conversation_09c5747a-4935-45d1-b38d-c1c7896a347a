/**
 * Doughnut Chart Component
 *
 * Reusable doughnut chart component using Chart.js for displaying
 * proportional data, distributions, and percentage breakdowns.
 */

import React from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

export interface DoughnutChartData {
  label: string;
  value: number;
  color?: string;
}

export interface DoughnutChartProps {
  title?: string;
  data: DoughnutChartData[];
  height?: number;
  showLegend?: boolean;
  showPercentages?: boolean;
  centerText?: string;
  className?: string;
}

export const DoughnutChart: React.FC<DoughnutChartProps> = ({
  title,
  data,
  height = 300,
  showLegend = true,
  showPercentages = true,
  centerText,
  className = '',
}) => {
  // Add null check for data
  const safeData = data || [];
  const total = safeData.reduce((sum, item) => sum + item.value, 0);

  const chartData = {
    labels: safeData.map(item => item.label),
    datasets: [
      {
        data: safeData.map(item => item.value),
        backgroundColor: safeData.map((item, index) =>
          item.color || `hsl(${index * 60}, 70%, 50%)`
        ),
        borderColor: safeData.map((item, index) =>
          item.color ? item.color.replace('0.8', '1') : `hsl(${index * 60}, 70%, 40%)`
        ),
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverOffset: 8,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        display: showLegend,
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: showPercentages ? `${label} (${percentage}%)` : label,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: data.datasets[0].borderWidth,
                  hidden: false,
                  index: i,
                };
              });
            }
            return [];
          },
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
        padding: {
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#50C878',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
          },
        },
      },
    },
    interaction: {
      intersect: false,
    },
  };

  return (
    <div className={`w-full relative ${className}`} style={{ height }}>
      <Doughnut data={chartData} options={options} />
      {centerText && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {total.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {centerText}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DoughnutChart;
