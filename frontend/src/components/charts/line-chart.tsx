/**
 * Line Chart Component
 *
 * Reusable line chart component using Chart.js for displaying
 * time-series data, analytics trends, and activity metrics.
 */

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export interface LineChartData {
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
  fill?: boolean;
}

export interface LineChartProps {
  title?: string;
  labels: string[];
  datasets: LineChartData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  tension?: number;
  className?: string;
}

export const LineChart: React.FC<LineChartProps> = ({
  title,
  labels,
  datasets,
  height = 300,
  showLegend = true,
  showGrid = true,
  tension = 0.4,
  className = '',
}) => {
  // Add null checks for labels and datasets
  const safeLabels = labels || [];
  const safeDatasets = datasets || [];

  const data = {
    labels: safeLabels,
    datasets: safeDatasets.map((dataset, index) => ({
      label: dataset.label,
      data: dataset.data || [],
      borderColor: dataset.borderColor || `hsl(${index * 60}, 70%, 50%)`,
      backgroundColor: dataset.backgroundColor || `hsla(${index * 60}, 70%, 50%, 0.1)`,
      fill: dataset.fill ?? false,
      tension,
      pointRadius: 4,
      pointHoverRadius: 6,
      borderWidth: 2,
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
        padding: {
          bottom: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#50C878',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 11,
          },
        },
      },
      y: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 11,
          },
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
    elements: {
      point: {
        hoverBackgroundColor: '#50C878',
      },
    },
  };

  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <Line data={data} options={options} />
    </div>
  );
};

export default LineChart;
