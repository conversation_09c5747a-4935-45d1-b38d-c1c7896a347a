import * as React from 'react';
import { useIntl } from 'react-intl';
import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import useAuth from '@/hooks/use-auth';
import InvitationNotificationBadge from '@/components/features/user/invitation-notification-badge';
import {
  Activity,
  Bell,
  Lock,
  Settings,
  User,
  Shield,
  Key,
  Globe,
  Download,
  Home,
  UserCheck,
  Smartphone,
  FileText,
  ArrowUp,
  Mail,
  Building2
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/shadcn/sidebar';
import { useActiveRoute } from '@/hooks/use-active-route';
import { useUserData } from '@/hooks/use-user-data';
import { cn } from '@/lib/utils';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/shadcn/avatar';

/**
 * UserSidebar Component
 *
 * Provides navigation for user-related pages including:
 * - Profile
 * - Security
 * - Notifications
 * - Settings
 *
 * This sidebar is displayed only when users navigate to user-related pages.
 */
export const UserSidebar = memo(
  ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
    const intl = useIntl();
    const { isActive } = useActiveRoute();
    const { authUser, profileData } = useUserData();
    const { user: authUserRole } = useAuth();

    // Use data from API if available, fallback to auth user data
    const user = profileData
      ? {
          name:
            `${profileData.firstName} ${profileData.lastName}`.trim() ||
            authUser?.email ||
            'User',
          email: profileData.email || authUser?.email || '',
          avatar: profileData.avatarUrl || '/static/avatars/default.jpg',
        }
      : {
          name: authUser?.email || 'User',
          email: authUser?.email || '',
          avatar: '/static/avatars/default.jpg',
        };

    // Memoize the static data structure with active states
    const data = useMemo(
      () => ({
        user,
        navGroups: [
          {
            label: intl.formatMessage({ id: 'nav.user.accountProfile' }),
            items: [
              {
                title: intl.formatMessage({ id: 'nav.user.accountHome' }),
                url: '/user/home',
                icon: Home,
                isActive: isActive('/user/home'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.personalInfo' }),
                url: '/user/profile',
                icon: UserCheck,
                isActive: isActive('/user/profile'),
              },
              // Developer access link - visible to all users
              {
                title: intl.formatMessage({ id: 'nav.user.roleUpgrade' }),
                url: '/user/role-transitions',
                icon: ArrowUp,
                isActive: isActive('/user/role-transitions'),
              },
            ]
          },
          {
            label: intl.formatMessage({ id: 'nav.user.securityPrivacy' }),
            items: [
              {
                title: intl.formatMessage({ id: 'nav.user.securityOverview' }),
                url: '/user/security',
                icon: Shield,
                isActive: isActive('/user/security'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.passwordManagement' }),
                url: '/user/security/password',
                icon: Lock,
                isActive: isActive('/user/security/password'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.twoFactorAuth' }),
                url: '/user/security/2fa',
                icon: Key,
                isActive: isActive('/user/security/2fa'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.deviceManagement' }),
                url: '/user/security/devices',
                icon: Smartphone,
                isActive: isActive('/user/security/devices'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.sessionManagement' }),
                url: '/user/sessions',
                icon: Activity,
                isActive: isActive('/user/sessions'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.privacyControls' }),
                url: '/user/privacy',
                icon: Settings,
                isActive: isActive('/user/privacy'),
              },
            ]
          },
          {
            label: intl.formatMessage({ id: 'nav.user.applicationsData' }),
            items: [
              {
                title: intl.formatMessage({ id: 'nav.user.connectedApps' }),
                url: '/user/applications',
                icon: Globe,
                isActive: isActive('/user/applications'),
              },
              // Only show invitations for users and developers (not system admins)
              ...(authUserRole && authUserRole.role !== 'admin' ? [{
                title: intl.formatMessage({ id: 'nav.user.invitations' }),
                url: '/user/invitations',
                icon: Mail,
                isActive: isActive('/user/invitations'),
                badge: <InvitationNotificationBadge />,
              }] : []),

              {
                title: intl.formatMessage({ id: 'nav.user.permissions' }),
                url: '/user/permissions',
                icon: Shield,
                isActive: isActive('/user/permissions'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.consentHistory' }),
                url: '/user/consent/history',
                icon: FileText,
                isActive: isActive('/user/consent/history'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.activityHistory' }),
                url: '/user/activity',
                icon: Activity,
                isActive: isActive('/user/activity'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.dataExport' }),
                url: '/user/data-export',
                icon: Download,
                isActive: isActive('/user/data-export'),
              },
            ]
          },
          {
            label: intl.formatMessage({ id: 'nav.user.preferences' }),
            items: [
              {
                title: intl.formatMessage({ id: 'nav.user.notifications' }),
                url: '/user/notifications',
                icon: Bell,
                isActive: isActive('/user/notifications'),
              },
              {
                title: intl.formatMessage({ id: 'nav.user.settings' }),
                url: '/user/settings',
                icon: Settings,
                isActive: isActive('/user/settings'),
              },
            ]
          }
        ],
      }),
      [intl, isActive, user]
    );

    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader className="px-4 py-4">
          <div className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
            <Avatar className="h-8 w-8 rounded-full group-data-[collapsible=icon]:h-6 group-data-[collapsible=icon]:w-6">
              <AvatarImage src={data.user.avatar} alt={data.user.name} />
              <AvatarFallback className="rounded-full text-xs">CA</AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden">
              <span className="text-sm font-semibold">{data.user.name}</span>
              <span className="text-xs text-muted-foreground">
                {data.user.email}
              </span>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          {data.navGroups.map((group, groupIndex) => (
            <SidebarGroup key={groupIndex}>
              <SidebarGroupLabel>
                {group.label}
              </SidebarGroupLabel>
              <SidebarMenu>
                {group.items.map(item => (
                  <SidebarMenuItem key={item.url}>
                    <SidebarMenuButton
                      tooltip={item.title}
                      asChild
                      className={cn(
                        item.isActive &&
                          'bg-sidebar-accent text-sidebar-accent-foreground'
                      )}
                    >
                      <Link to={item.url}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                        {item.badge && item.badge}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          ))}
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip={intl.formatMessage({ id: 'nav.user.backToDashboard' })}
                asChild
              >
                <Link to="/dashboard">
                  <Home className="h-4 w-4" />
                  <span>{intl.formatMessage({ id: 'nav.user.backToDashboard' })}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }
);

UserSidebar.displayName = 'UserSidebar';
