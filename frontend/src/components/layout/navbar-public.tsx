import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '../../constants/site-config';
import { useConfig } from '../../contexts/config-context';
import { LanguageSwitcher } from '../common/language-switcher';
import { ThemeSwitcher } from '../common/theme-switcher';
import useAuth from '../../hooks/use-auth';
import { useUserData } from '../../hooks/use-user-data';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '../ui/shadcn/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/shadcn/avatar';
import { Button } from '../ui/shadcn/button';
import { LogOut, User, Settings } from 'lucide-react';

/**
 * NavbarPublic Component
 *
 * @description Provides the header navigation for public pages including
 * logo with link to homepage, desktop/mobile navigation menu, login button,
 * language switcher, and theme switcher
 *
 * @returns {JSX.Element} The NavbarPublic component
 */
const NavbarPublic = (): React.JSX.Element => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const intl = useIntl();
  const { locale } = useConfig();
  const { isAuthenticated, logout } = useAuth();
  const { authUser, profileData } = useUserData();

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: false,
  });

  // Refs for the sections
  const headerRef = useRef<HTMLElement>(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe header section
    if (headerRef.current) observer.observe(headerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const mainNavItems = [
    { 
      name: intl.formatMessage({ id: 'navigation.home' }, { defaultMessage: 'Home' }),
      path: '/' 
    },
    {
      name: intl.formatMessage({ id: 'navigation.about' }, { defaultMessage: 'About' }),
      path: '/about',
    },
    {
      name: intl.formatMessage({ id: 'navigation.blog' }, { defaultMessage: 'Blog' }),
      path: '/blog',
    },
    {
      name: intl.formatMessage({ id: 'navigation.contact' }, { defaultMessage: 'Contact' }),
      path: '/contact',
    },
    {
      name: intl.formatMessage({ id: 'navigation.faq' }, { defaultMessage: 'FAQ' }),
      path: '/faq',
    },
  ];

  /**
   * Renders the auth button or user menu
   * @returns {JSX.Element} Login button or user dropdown
   */
  const renderAuthButton = () => {
    if (!isAuthenticated) {
      return (
        <Link
          to="/login"
          className="shimmer-effect rounded-md bg-primary px-4 py-2 text-primary-foreground transition-colors hover:bg-primary/90"
        >
          {intl.formatMessage({ id: 'page.login.submit' }, { defaultMessage: 'Sign In' })}
        </Link>
      );
    }

    // Get user data and role
    const user = profileData
      ? {
          name:
            `${profileData.firstName} ${profileData.lastName}`.trim() ||
            authUser?.email ||
            'User',
          email: profileData.email || authUser?.email || '',
          avatar: profileData.avatarUrl || '/static/avatars/default.jpg',
          role: authUser?.role || 'user',
        }
      : {
          name: authUser?.email || 'User',
          email: authUser?.email || '',
          avatar: '/static/avatars/default.jpg',
          role: authUser?.role || 'user',
        };

    // Determine dashboard URL based on user role
    const getDashboardUrl = () => {
      switch (user.role) {
        case 'admin':
          return '/admin';
        case 'developer':
          return '/developer';
        default:
          return '/dashboard';
      }
    };

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-10 w-10 rounded-full">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback>
                {user.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{user.name}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to={getDashboardUrl()} className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              <span>{intl.formatMessage({ id: 'nav.user.dashboard' }, { defaultMessage: 'Dashboard' })}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/user/profile" className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>{intl.formatMessage({ id: 'nav.user.account' }, { defaultMessage: 'Account' })}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={logout} className="cursor-pointer">
            <LogOut className="mr-2 h-4 w-4" />
            <span>{intl.formatMessage({ id: 'nav.user.logout' }, { defaultMessage: 'Log out' })}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <header
      ref={headerRef}
      data-section="header"
      className={`section-reveal border-b border-border bg-background ${visibleSections.header ? 'visible' : ''}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link to="/" className="animate-fadeIn flex items-center space-x-3">
            <img 
              src={SITE_INFO.organization.logo.main} 
              alt={displayName}
              className="h-8 w-auto"
            />
            <span className="text-xl font-bold text-primary">
              {displayName}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="animate-fadeIn animation-delay-200 hidden space-x-8 md:flex">
            {mainNavItems.map((item, index) => (
              <Link
                key={item.path}
                to={item.path}
                className="shimmer-effect font-medium text-muted-foreground hover:text-primary"
                style={{ animationDelay: `${index * 100 + 200}ms` }}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="animate-fadeIn animation-delay-400 hidden items-center space-x-4 md:flex">
            {/* Theme Switcher */}
            <ThemeSwitcher />

            {/* Language Switcher */}
            <LanguageSwitcher />

            {/* Auth Button */}
            {renderAuthButton()}
          </div>

          {/* Mobile menu button */}
          <button
            className="animate-fadeIn text-gray-600 md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <nav className="animate-slideUp border-t border-border py-4 md:hidden">
            <div className="flex flex-col space-y-4">
              {mainNavItems.map((item, index) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className="stagger-card font-medium text-muted-foreground hover:text-primary"
                  style={{ animationDelay: `${index * 100}ms` }}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Theme and Language Controls */}
              <div className="animate-fadeIn animation-delay-300 flex flex-col space-y-4 border-t border-border pt-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">
                    {intl.formatMessage({ id: 'nav.user.theme' }, { defaultMessage: 'Theme' })}
                  </span>
                  <ThemeSwitcher />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">
                    {intl.formatMessage({ id: 'nav.user.language' }, { defaultMessage: 'Language' })}
                  </span>
                  <LanguageSwitcher />
                </div>
              </div>

              {/* Auth Button for Mobile */}
              <div
                onClick={() => setMobileMenuOpen(false)}
                className="inline-block text-center pt-2"
              >
                {renderAuthButton()}
              </div>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};

export default NavbarPublic;
