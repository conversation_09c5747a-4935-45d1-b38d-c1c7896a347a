'use client';

import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  Globe,
  Moon,
  LogOut,
  Sparkles,
  Sun,
  Check,
  TextCursor,
} from 'lucide-react';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/shadcn/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/shadcn/sidebar';
import { cn } from '@/lib/utils';

import useAuth from '@/hooks/use-auth';
import useConfig from '@/hooks/use-config';
import { useUserData } from '@/hooks/use-user-data';
import { useIntl } from 'react-intl';
import { Link, useNavigate } from 'react-router-dom';
import {
  ToggleGroup,
  ToggleGroupItem,
} from '@/components/ui/shadcn/toggle-group';

/**
 * NavUser Component
 *
 * Renders a user navigation dropdown in the sidebar with user profile information,
 * language selection, theme switching, and logout functionality.
 * Integrates with the /account/me API for real user data.
 */
export function NavUser() {
  const intl = useIntl();
  const navigate = useNavigate();
  const { isMobile } = useSidebar();
  const { logout } = useAuth();
  const { authUser, profileData } = useUserData();
  const {
    theme,
    onChangeTheme,
    locale,
    onChangeLocale,
    fontSize,
    onChangeFontSize,
  } = useConfig();

  // Use data from API if available, fallback to auth user data
  const user = profileData
    ? {
        name:
          `${profileData.firstName} ${profileData.lastName}`.trim() ||
          authUser?.email ||
          'User',
        email: profileData.email || authUser?.email || '',
        avatar: profileData.avatarUrl || '/static/avatars/default.jpg',
      }
    : {
        name: authUser?.email || 'User',
        email: authUser?.email || '',
        avatar: '/static/avatars/default.jpg',
      };

  const handleLogout = async () => {
    await logout();
    navigate('/auth/login', { replace: true });
  };

  const handleThemeChange = (newTheme: string) => {
    onChangeTheme(newTheme as 'light' | 'dark');
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate text-sm font-semibold">
                  {user.name}
                </span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left leading-tight">
                  <span className="truncate text-sm font-semibold">
                    {user.name}
                  </span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* disable upgrade for now */}
            {/* <DropdownMenuGroup>
              <DropdownMenuItem className="flex h-9 items-center text-sm">
                <Sparkles className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'nav.user.upgrade' })}
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator /> */}
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link
                  to="/user/home"
                  className="flex h-9 items-center text-sm"
                >
                  <BadgeCheck className="mr-2 h-4 w-4" />
                  {intl.formatMessage({ id: 'nav.user.account' })}
                </Link>
              </DropdownMenuItem>
              {/* disable billing for now */}
              {/* <DropdownMenuItem className="flex h-9 items-center text-sm">
                <CreditCard className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'nav.user.billing' })}
              </DropdownMenuItem> */}
              <DropdownMenuItem asChild>
                <Link
                  to="/user/notifications"
                  className="flex h-9 items-center text-sm"
                >
                  <Bell className="mr-2 h-4 w-4" />
                  {intl.formatMessage({ id: 'nav.user.notifications' })}
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger className="flex h-9 w-full cursor-default items-center justify-between px-2 py-1.5 text-sm transition-colors duration-150 ease-in-out hover:bg-accent focus:bg-transparent">
                  <div className="flex items-center gap-3">
                    <Globe className="h-4 w-4" />
                    <span>
                      {intl.formatMessage({ id: 'nav.user.language' })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <span className="text-sm transition-colors duration-150">
                      {locale === 'en' &&
                        intl.formatMessage({ id: 'nav.user.language.english' })}
                      {locale === 'zh' &&
                        intl.formatMessage({ id: 'nav.user.language.chinese' })}
                      {locale === 'zh-TW' &&
                        intl.formatMessage({
                          id: 'nav.user.language.traditional',
                        })}
                    </span>
                  </div>
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent
                  className="min-w-[12rem] rounded-md p-1"
                  alignOffset={-4}
                >
                  <DropdownMenuItem
                    onClick={() => onChangeLocale('en')}
                    className="flex h-9 items-center gap-3 px-3 text-sm transition-colors duration-150 ease-in-out"
                  >
                    <div className="relative flex h-4 w-4 items-center justify-center">
                      <Check
                        className={cn(
                          'absolute h-4 w-4 transition-all duration-200',
                          locale === 'en'
                            ? 'scale-100 opacity-100'
                            : 'scale-95 opacity-0'
                        )}
                      />
                    </div>
                    <span className="transition-colors duration-150">
                      {intl.formatMessage({ id: 'nav.user.language.english' })}
                    </span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onChangeLocale('zh')}
                    className="flex h-9 items-center gap-3 px-3 text-sm transition-colors duration-150 ease-in-out"
                  >
                    <div className="relative flex h-4 w-4 items-center justify-center">
                      <Check
                        className={cn(
                          'absolute h-4 w-4 transition-all duration-200',
                          locale === 'zh'
                            ? 'scale-100 opacity-100'
                            : 'scale-95 opacity-0'
                        )}
                      />
                    </div>
                    <span className="transition-colors duration-150">
                      {intl.formatMessage({ id: 'nav.user.language.chinese' })}
                    </span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onChangeLocale('zh-TW')}
                    className="flex h-9 items-center text-sm"
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        locale === 'zh-TW' ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    {intl.formatMessage({
                      id: 'nav.user.language.traditional',
                    })}
                  </DropdownMenuItem>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger className="flex h-9 w-full cursor-default items-center justify-between px-2 py-1.5 text-sm transition-colors duration-150 ease-in-out hover:bg-accent focus:bg-transparent">
                  <div className="flex items-center gap-3">
                    {theme === 'light' ? (
                      <Sun className="h-4 w-4 rotate-0 transition-transform duration-200" />
                    ) : (
                      <Moon className="h-4 w-4 rotate-0 transition-transform duration-200" />
                    )}
                    <span>{intl.formatMessage({ id: 'nav.user.theme' })}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <span className="text-sm transition-colors duration-150">
                      {intl.formatMessage({ id: `nav.user.theme.${theme}` })}
                    </span>
                  </div>
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent
                  className="min-w-[12rem] rounded-md p-1"
                  alignOffset={-4}
                >
                  <DropdownMenuItem
                    onClick={() => handleThemeChange('light')}
                    className="flex h-9 items-center justify-between px-3 text-sm transition-colors duration-150 ease-in-out"
                  >
                    <div className="flex items-center gap-3">
                      <Sun className="h-4 w-4 transition-transform duration-200 ease-in-out" />
                      <span className="transition-colors duration-150">
                        {intl.formatMessage({ id: 'nav.user.theme.light' })}
                      </span>
                    </div>
                    <div className="relative flex h-4 w-4 items-center justify-center">
                      <Check
                        className={cn(
                          'absolute h-4 w-4 transition-all duration-200',
                          theme === 'light'
                            ? 'scale-100 opacity-100'
                            : 'scale-95 opacity-0'
                        )}
                      />
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleThemeChange('dark')}
                    className="flex h-9 items-center justify-between px-3 text-sm transition-colors duration-150 ease-in-out"
                  >
                    <div className="flex items-center gap-3">
                      <Moon className="h-4 w-4 transition-transform duration-200 ease-in-out" />
                      <span className="transition-colors duration-150">
                        {intl.formatMessage({ id: 'nav.user.theme.dark' })}
                      </span>
                    </div>
                    <div className="relative flex h-4 w-4 items-center justify-center">
                      <Check
                        className={cn(
                          'absolute h-4 w-4 transition-all duration-200',
                          theme === 'dark'
                            ? 'scale-100 opacity-100'
                            : 'scale-95 opacity-0'
                        )}
                      />
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            </DropdownMenuGroup>
            <DropdownMenuItem
              className="flex h-9 min-w-0 items-center justify-between"
              onSelect={e => e.preventDefault()}
            >
              <div className="flex min-w-0 flex-shrink-0 items-center">
                <TextCursor className="mr-2 h-4 w-4 flex-shrink-0" />
                <span className="whitespace-nowrap text-sm">
                  {intl.formatMessage({ id: 'nav.user.fontSize' })}
                </span>
              </div>
              <ToggleGroup
                type="single"
                value={fontSize}
                onValueChange={value => {
                  if (value)
                    onChangeFontSize(value as 'small' | 'default' | 'large');
                }}
                className="ml-2 flex-shrink-0"
              >
                <ToggleGroupItem
                  value="small"
                  aria-label="Small text"
                  className={cn(
                    'ring-offset-background transition-colors duration-150 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                    fontSize === 'small'
                      ? 'bg-primary text-primary-foreground hover:bg-primary'
                      : 'bg-transparent hover:bg-muted'
                  )}
                >
                  <span className="text-xs">A</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                  value="default"
                  aria-label="Default text"
                  className={cn(
                    'ring-offset-background transition-colors duration-150 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                    fontSize === 'default'
                      ? 'bg-primary text-primary-foreground hover:bg-primary'
                      : 'bg-transparent hover:bg-muted'
                  )}
                >
                  <span className="text-sm">A</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                  value="large"
                  aria-label="Large text"
                  className={cn(
                    'ring-offset-background transition-colors duration-150 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                    fontSize === 'large'
                      ? 'bg-primary text-primary-foreground hover:bg-primary'
                      : 'bg-transparent hover:bg-muted'
                  )}
                >
                  <span className="text-base">A</span>
                </ToggleGroupItem>
              </ToggleGroup>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleLogout}
              className="flex h-9 items-center text-sm"
            >
              <LogOut className="mr-2 h-4 w-4" />
              {intl.formatMessage({ id: 'nav.user.logout' })}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
