import * as React from 'react';
import { useIntl } from 'react-intl';
import { memo, useMemo } from 'react';
import {
  Building2,
  GalleryVerticalEnd,
  Home,
  Settings,
  Users,
  Code,
  Monitor,
  UserCheck,
  Shield,
} from 'lucide-react';

import { NavMain } from '@/components/layout/nav-main';
import { NavUser } from '@/components/layout/nav-user';
import { TeamSwitcher } from '@/components/layout/team-switcher';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/shadcn/sidebar';
import { useActiveRoute } from '@/hooks/use-active-route';
import { useUserData } from '@/hooks/use-user-data';

// Memoize the entire AppSidebar component
export const AppSidebar = memo(
  ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
    const intl = useIntl();
    const { isActive } = useActiveRoute();
    const { authUser } = useUserData();

    // Get user role
    const userRole = authUser?.role || 'user';

    // Generate the data structure with active states based on role
    const data = (() => {
        const baseNavMain = [
          {
            title: intl.formatMessage({ id: 'nav.app.userPortal' }),
            url: '/dashboard',
            icon: Home,
            isActive: isActive('/dashboard') || isActive('/user/home') || isActive('/user/profile') || isActive('/user/security') || isActive('/user/notifications') || isActive('/user/settings') || isActive('/user/applications') || isActive('/user/privacy'),
          },
        ];

        // Add role-specific navigation items with proper grouping
        const administrationItems = [];
        const developmentItems = [];

        if (userRole === 'admin') {
          administrationItems.push(
            {
            title: intl.formatMessage({ id: 'nav.app.adminConsole' }),
            url: '/admin/dashboard',
            icon: UserCheck,
            isActive: isActive('/admin/dashboard'),
            },
            {
              title: intl.formatMessage({ id: 'nav.app.userManagement' }),
              url: '#',
              icon: Users,
              isActive: isActive('/admin/users'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.admin.users' }),
                  url: '/admin/users',
                  isActive: isActive('/admin/users'),
                },
              ],
            },
            {
              title: intl.formatMessage({ id: 'nav.app.applicationsManagement' }),
              url: '#',
              icon: Monitor,
              isActive: isActive('/admin/applications') || isActive('/admin/developer-applications'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.admin.applications' }),
                  url: '/admin/applications',
                  isActive: isActive('/admin/applications'),
                },
                {
                  title: intl.formatMessage({ id: 'nav.admin.developer-applications' }),
                  url: '/admin/developer-applications',
                  isActive: isActive('/admin/developer-applications'),
                },
              ],
            },
            {
              title: intl.formatMessage({ id: 'nav.app.organizationManagement' }),
              url: '#',
              icon: Building2,
              isActive: isActive('/admin/organizations'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.admin.organizations' }),
                  url: '/admin/organizations',
                  isActive: isActive('/admin/organizations'),
                },
              ],
            },
            {
              title: intl.formatMessage({ id: 'nav.app.systemAdmin' }),
              url: '#',
              icon: Settings,
              isActive: isActive('/admin/system/settings') || isActive('/admin/security'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.admin.settings' }),
                  url: '/admin/system/settings',
                  isActive: isActive('/admin/system/settings'),
                },
                {
                  title: intl.formatMessage({ id: 'nav.admin.security' }),
                  url: '/admin/security',
                  isActive: isActive('/admin/security'),
                },
              ],
            }
          );
        }

        if (userRole === 'developer' || userRole === 'admin') {
          developmentItems.push(
            {
            title: intl.formatMessage({ id: 'nav.app.developerConsole' }),
            url: '/developer/dashboard',
            icon: UserCheck,
            isActive: isActive('/developer/dashboard'),
            },
            {
              title: intl.formatMessage({ id: 'nav.developer.settings' }),
              url: '/developer/settings',
              icon: Settings,
              isActive: isActive('/developer/settings'),
            },
            {
              title: intl.formatMessage({ id: 'nav.app.applicationsManagement' }),
              url: '#',
              icon: Code,
              isActive: isActive('/developer/applications') || isActive('/developer/register'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.developer.register' }),
                  url: '/developer/register',
                  isActive: isActive('/developer/register'),
                },
                {
                  title: intl.formatMessage({ id: 'nav.developer.applications' }),
                  url: '/developer/applications',
                  isActive: isActive('/developer/applications'),
                },
              ],
            },
            {
              title: intl.formatMessage({ id: 'nav.app.organizationManagement' }),
              url: '#',
              icon: Users,
              isActive: isActive('/developer/organizations'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.developer.organizations' }),
                  url: '/developer/organizations',
                  isActive: isActive('/developer/organizations'),
                },
              ],
            },
            {
              title: intl.formatMessage({ id: 'nav.app.toolsResources' }),
              url: '#',
              icon: Monitor,
              isActive: isActive('/developer/docs'),
              items: [
                {
                  title: intl.formatMessage({ id: 'nav.developer.api.docs' }),
                  url: '/developer/docs',
                  isActive: isActive('/developer/docs'),
                },
              ],
            }
          );
        }

        return {
          teams: [
            {
              name: intl.formatMessage({ id: 'nav.app.company' }),
              logo: GalleryVerticalEnd,
              plan: intl.formatMessage({ id: 'nav.app.plan' }),
              iconBackground: 'bg-primary/10',
              iconForeground: 'text-primary',
            },
          ],
          navMain: [...baseNavMain],
          administrationItems,
          developmentItems,
        };
      })();

    // Check if current route is a user page
    const isUserPage =
      isActive('user/profile') ||
      isActive('user/security') ||
      isActive('user/notifications') ||
      isActive('user/settings');

    // Don't render the AppSidebar on user pages, as they will use UserSidebar instead
    if (isUserPage) {
      return null;
    }

    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <TeamSwitcher teams={data.teams} />
        </SidebarHeader>
        <SidebarContent>
          {/* Administration Section */}
          {data.administrationItems.length > 0 && (
            <NavMain items={data.administrationItems} groupLabel={intl.formatMessage({ id: 'nav.app.administration' })} />
          )}

          {/* Development Section */}
          {data.developmentItems.length > 0 && (
            <NavMain items={data.developmentItems} groupLabel={intl.formatMessage({ id: 'nav.app.development' })} />
          )}

          {/* User Overview Section */}
          <NavMain items={data.navMain} />

        </SidebarContent>
        <SidebarFooter>
          <NavUser />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }
);

AppSidebar.displayName = 'AppSidebar';
