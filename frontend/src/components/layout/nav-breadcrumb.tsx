import * as React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { memo, useMemo, useCallback } from 'react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbLink,
} from '@/components/ui/shadcn/breadcrumb';

/**
 * Interface for breadcrumb item properties
 * @property {string} title - The display title of the breadcrumb (fallback)
 * @property {string} translationKey - The i18n key for the breadcrumb title translation
 * @property {string} href - The URL the breadcrumb links to
 * @property {string} dynamicTitle - Dynamic title that overrides static title (e.g., organization name)
 */
interface BreadcrumbItemData {
  title: string;
  translationKey?: string;
  href?: string;
  dynamicTitle?: string;
}

/**
 * Props for the NavBreadcrumb component
 * @property {BreadcrumbItemData[]} items - Optional custom breadcrumb items
 * @property {Record<string, string>} dynamicTitles - Map of path segments to dynamic titles (e.g., organization ID -> organization name)
 */
interface BreadcrumbNavProps {
  items?: BreadcrumbItemData[];
  dynamicTitles?: Record<string, string>;
}

/**
 * Navigation breadcrumb component that supports internationalization
 * Displays a hierarchical navigation path based on the current URL or provided items
 */
export const NavBreadcrumb = memo(({ items, dynamicTitles }: BreadcrumbNavProps) => {
  const location = useLocation();
  const intl = useIntl();

  // Memoize breadcrumb items generation with i18n support
  const breadcrumbItems = useMemo(
    () => items || generateBreadcrumbItems(location.pathname, intl, dynamicTitles),
    [items, location.pathname, intl, dynamicTitles]
  );

  // Memoize the translation function
  const getTranslatedTitle = useCallback(
    (item: BreadcrumbItemData) => {
      // Priority 0: Use dynamic title if provided (e.g., organization name)
      if (item.dynamicTitle) {
        return item.dynamicTitle;
      }

      // Priority 1: Use provided specific translation key
      if (item.translationKey && intl.messages[item.translationKey]) {
        return intl.formatMessage({ id: item.translationKey });
      }

      // Priority 2: Try page namespace (for page titles)
      const pageKey = `page.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[pageKey]) {
        return intl.formatMessage({ id: pageKey });
      }

      // Priority 3: Try navigation namespace
      const navKey = `navigation.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[navKey]) {
        return intl.formatMessage({ id: navKey });
      }

      // Priority 4: Try nav.main namespace
      const navMainKey = `nav.main.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[navMainKey]) {
        return intl.formatMessage({ id: navMainKey });
      }

      // Priority 5: Check common translations
      if (intl.messages[item.title.toLowerCase().replace(/\s+/g, '.')]) {
        return intl.formatMessage({
          id: item.title.toLowerCase().replace(/\s+/g, '.'),
        });
      }

      // Fallback: Use the title as is
      return item.title;
    },
    [intl]
  );

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const title = getTranslatedTitle(item);

          return (
            <React.Fragment key={`breadcrumb-group-${index}`}>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage>{title}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={item.href || '#'}>
                      {title}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLast && <BreadcrumbSeparator className="hidden md:block" />}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
});

NavBreadcrumb.displayName = 'NavBreadcrumb';

/**
 * Generates breadcrumb items from URL path with i18n support
 * @param {string} path - The current URL path
 * @param {IntlShape} intl - The intl object for translations
 * @param {Record<string, string>} dynamicTitles - Map of path segments to dynamic titles
 * @returns {BreadcrumbItemData[]} - Array of breadcrumb items
 */
function generateBreadcrumbItems(
  path: string,
  intl: any,
  dynamicTitles?: Record<string, string>
): BreadcrumbItemData[] {
  const paths = path.split('/').filter(Boolean);

  if (paths.length === 0) {
    return [];
  }

  // Define route mappings for better breadcrumb generation
  const routeMappings: Record<string, { title: string; translationKey?: string }> = {
    'user': { title: 'My Account', translationKey: 'nav.user.portal' },
    'profile': { title: 'Account', translationKey: 'nav.user.account' },
    'admin': { title: 'Admin Portal', translationKey: 'nav.admin.portal' },
    'developer': { title: 'Developer Portal', translationKey: 'nav.developer.portal' },
    'dashboard': { title: 'Dashboard', translationKey: 'nav.main.dashboard' },
    'users': { title: 'User Management', translationKey: 'nav.admin.users' },
    'applications': { title: 'Applications', translationKey: 'nav.admin.applications' },
    'settings': { title: 'Settings', translationKey: 'nav.admin.settings' },
    'security': { title: 'Security', translationKey: 'nav.admin.security' },
    'register': { title: 'Register Application', translationKey: 'nav.developer.register' },
    'home': { title: 'Account Home' },
    'personal': { title: 'Personal Information' },
    'password': { title: 'Password Management' },
    '2fa': { title: 'Two-Factor Authentication' },
    'privacy': { title: 'Privacy Controls' },
    'connected-apps': { title: 'Connected Applications' },
    'data-export': { title: 'Data Export' },
    'notifications': { title: 'Notifications', translationKey: 'nav.user.notifications' },
  };

  // Handle user portal as independent - start breadcrumbs from user context
  if (paths[0] === 'user') {
    // For user portal, only show breadcrumbs within the user context
    const userPaths = paths.slice(1); // Remove 'user' from the path
    if (userPaths.length === 0) {
      return []; // No breadcrumbs for /user root
    }

    // Generate breadcrumbs starting from user context
    return userPaths.map((segment, index) => {
      const href = `/user/${userPaths.slice(0, index + 1).join('/')}`;
      const mapping = routeMappings[segment];
      const title = mapping?.title || formatBreadcrumbTitle(segment);
      const translationKey = mapping?.translationKey;

      return {
        title,
        translationKey,
        href,
      };
    });
  }

  return paths.map((segment, index) => {
    const href = `/${paths.slice(0, index + 1).join('/')}`;

    // Check for dynamic title first (e.g., organization name instead of ID)
    const dynamicTitle = dynamicTitles?.[segment];

    // Use route mapping if available, otherwise format the segment
    const mapping = routeMappings[segment];
    const title = mapping?.title || formatBreadcrumbTitle(segment);
    const translationKey = mapping?.translationKey;

    // If no specific mapping, try to find translation keys
    if (!translationKey) {
      const segmentLower = segment.toLowerCase().replace(/-/g, '.');
      const possibleTranslationKeys = [
        `page.${segmentLower}`,
        `navigation.${segmentLower}`,
        `nav.main.${segmentLower}`,
        `nav.user.${segmentLower}`,
        `nav.admin.${segmentLower}`,
        `nav.developer.${segmentLower}`,
        segmentLower,
      ];

      // Find the first translation key that exists
      const foundTranslationKey = possibleTranslationKeys.find(
        key => !!intl.messages[key]
      );

      return {
        title,
        translationKey: foundTranslationKey,
        href,
        dynamicTitle,
      };
    }

    return {
      title,
      translationKey,
      href,
      dynamicTitle,
    };
  });
}

/**
 * Formats a URL segment into a readable breadcrumb title
 * Used as fallback when no translation is available
 * @param {string} segment - URL path segment
 * @returns {string} - Formatted title
 */
function formatBreadcrumbTitle(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
