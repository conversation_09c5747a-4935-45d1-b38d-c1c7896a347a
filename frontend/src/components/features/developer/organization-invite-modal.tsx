import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useToast } from '@/hooks/use-toast';

// UI Components
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';
import { Badge } from '@/components/ui/shadcn/badge';

// Icons
import { Mail, Send, Shield, User, Calendar } from 'lucide-react';

// Services
import { organizationApi, OrganizationInvitationCreate } from '@/services/organization-api';

interface OrganizationInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string;
  organizationName: string;
  currentUserRole: string;
  onInvitationSent: () => void;
}

interface InviteFormData {
  email: string;
  role: 'admin' | 'member';
  message: string;
  expiresInDays: number;
}

interface InviteFormErrors {
  email?: string;
  role?: string;
  message?: string;
  expiresInDays?: string;
}

export const OrganizationInviteModal: React.FC<OrganizationInviteModalProps> = ({
  isOpen,
  onClose,
  organizationId,
  organizationName,
  currentUserRole,
  onInvitationSent,
}) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<InviteFormData>({
    email: '',
    role: 'member',
    message: '',
    expiresInDays: 7,
  });
  
  const [errors, setErrors] = useState<InviteFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        email: '',
        role: 'member',
        message: '',
        expiresInDays: 7,
      });
      setErrors({});
      onClose();
    }
  };

  const validateForm = (): boolean => {
    const newErrors: InviteFormErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = intl.formatMessage({ id: 'developer.organizations.invitations.create.errors.emailRequired' });
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = intl.formatMessage({ id: 'developer.organizations.invitations.create.errors.emailInvalid' });
    }

    if (!formData.role) {
      newErrors.role = intl.formatMessage({ id: 'developer.organizations.invitations.create.errors.roleRequired' });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const invitationData: OrganizationInvitationCreate = {
        email: formData.email.trim().toLowerCase(),
        role: formData.role,
        message: formData.message.trim() || undefined,
        expires_in_days: formData.expiresInDays,
      };

      await organizationApi.createOrganizationInvitation(organizationId, invitationData);

      toast({
        title: intl.formatMessage({ id: 'developer.organizations.invitations.create.success' }),
      });
      onInvitationSent();
      handleClose();
    } catch (error: any) {
      console.error('Failed to send invitation:', error);
      
      if (error.response?.data?.detail?.includes('already a member')) {
        setErrors({ email: 'User is already a member of this organization' });
      } else if (error.response?.data?.detail?.includes('pending invitation')) {
        setErrors({ email: 'User already has a pending invitation' });
      } else {
        toast({
          title: intl.formatMessage({ id: 'developer.organizations.invitations.create.errors.sendFailed' }),
          variant: 'destructive',
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleDescription = (role: string): string => {
    return intl.formatMessage({ id: `developer.organizations.members.roleDescriptions.${role}` });
  };

  const canInviteAsAdmin = currentUserRole === 'owner';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span>{intl.formatMessage({ id: 'developer.organizations.invitations.create.title' })}</span>
          </DialogTitle>
          <DialogDescription>
            Invite new members to join <strong>{organizationName}</strong>
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Address */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.email.label' })}
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, email: e.target.value }));
                if (errors.email) {
                  setErrors(prev => ({ ...prev, email: undefined }));
                }
              }}
              placeholder={intl.formatMessage({ id: 'developer.organizations.invitations.create.form.email.placeholder' })}
              className={`${errors.email ? 'border-red-500 dark:border-red-400' : ''}`}
              disabled={isSubmitting}
            />
            {errors.email && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.email}</p>
            )}
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.email.help' })}
            </p>
          </div>

          {/* Role Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.role.label' })}
            </Label>
            <Select
              value={formData.role}
              onValueChange={(value: 'admin' | 'member') => {
                setFormData(prev => ({ ...prev, role: value }));
                if (errors.role) {
                  setErrors(prev => ({ ...prev, role: undefined }));
                }
              }}
              disabled={isSubmitting}
            >
              <SelectTrigger className={`${errors.role ? 'border-red-500 dark:border-red-400' : ''}`}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {canInviteAsAdmin && (
                  <SelectItem value="admin">
                    <div className="flex items-center space-x-2">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span>{intl.formatMessage({ id: 'developer.organizations.members.roles.admin' })}</span>
                    </div>
                  </SelectItem>
                )}
                <SelectItem value="member">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-600" />
                    <span>{intl.formatMessage({ id: 'developer.organizations.members.roles.member' })}</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.role}</p>
            )}
            
            {/* Role Description */}
            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {intl.formatMessage({ id: `developer.organizations.members.roles.${formData.role}` })}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {getRoleDescription(formData.role)}
              </p>
            </div>
            
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.role.help' })}
            </p>
          </div>

          {/* Custom Message */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.message.label' })}
            </Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              placeholder={intl.formatMessage({ id: 'developer.organizations.invitations.create.form.message.placeholder' })}
              rows={3}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {intl.formatMessage({ id: 'developer.organizations.invitations.create.form.message.help' })}
            </p>
          </div>

          {/* Expiration */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Invitation Expiry
            </Label>
            <Select
              value={formData.expiresInDays.toString()}
              onValueChange={(value) => setFormData(prev => ({ ...prev, expiresInDays: parseInt(value) }))}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 day</SelectItem>
                <SelectItem value="3">3 days</SelectItem>
                <SelectItem value="7">7 days (recommended)</SelectItem>
                <SelectItem value="14">14 days</SelectItem>
                <SelectItem value="30">30 days</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>
                Invitation will expire on {new Date(Date.now() + formData.expiresInDays * 24 * 60 * 60 * 1000).toLocaleDateString()}
              </span>
            </div>
          </div>

          {/* Preview */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Invitation Preview
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-blue-700 dark:text-blue-300">Email:</span>
                <span className="text-blue-900 dark:text-blue-100 font-medium">
                  {formData.email || '<EMAIL>'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-700 dark:text-blue-300">Role:</span>
                <Badge variant="outline" className="capitalize">
                  {formData.role}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-700 dark:text-blue-300">Organization:</span>
                <span className="text-blue-900 dark:text-blue-100 font-medium">
                  {organizationName}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-700 dark:text-blue-300">Expires:</span>
                <span className="text-blue-900 dark:text-blue-100">
                  {formData.expiresInDays} day{formData.expiresInDays !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            {intl.formatMessage({ id: 'developer.organizations.invitations.create.actions.cancel' })}
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting || !formData.email || !formData.role}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? (
              <>
                <Send className="w-4 h-4 mr-2 animate-pulse" />
                {intl.formatMessage({ id: 'developer.organizations.invitations.create.actions.sending' })}
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                {intl.formatMessage({ id: 'developer.organizations.invitations.create.actions.send' })}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
