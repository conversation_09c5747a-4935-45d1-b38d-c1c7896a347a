import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useBreadcrumb } from '@/contexts/breadcrumb-context';
import useAuth from '@/hooks/use-auth';

// Debounce utility function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// UI Components
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import StyledTabs from '@/components/common/styled-tabs';
import { Badge } from '@/components/ui/shadcn/badge';
import { Skeleton } from '@/components/ui/shadcn/skeleton';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/shadcn/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/shadcn/dialog';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';

// Icons
import {
  ArrowLeft,
  Building2,
  Users,
  Mail,
  Settings,
  Globe,
  Calendar,
  Shield,
  Plus,
  BarChart3,
  Crown,
  MoreVertical,
  UserCog,
  UserMinus,
  X
} from 'lucide-react';

// Services
import { organizationApi, Organization, OrganizationMembership, OrganizationInvitation, ApplicationOrganization } from '@/services/organization-api';
import { getDeveloperApplications } from '@/services/developer-api';

interface OrganizationDashboardProps {}

export const OrganizationDashboard: React.FC<OrganizationDashboardProps> = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user: systemUser } = useAuth();
  const { organizationId } = useParams<{ organizationId: string }>();
  const { setDynamicTitle, removeDynamicTitle } = useBreadcrumb();

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [members, setMembers] = useState<OrganizationMembership[]>([]);
  const [invitations, setInvitations] = useState<OrganizationInvitation[]>([]);
  const [applications, setApplications] = useState<ApplicationOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [addApplicationDialog, setAddApplicationDialog] = useState(false);
  const [applicationId, setApplicationId] = useState('');
  const [applicationDescription, setApplicationDescription] = useState('');
  const [availableApplications, setAvailableApplications] = useState<any[]>([]);
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [sendInvitationDialog, setSendInvitationDialog] = useState(false);
  const [invitationEmail, setInvitationEmail] = useState('');
  const [invitationRole, setInvitationRole] = useState<'admin' | 'member'>('member');
  const [invitationMessage, setInvitationMessage] = useState('');
  const [searchingUsers, setSearchingUsers] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [searchError, setSearchError] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);

  // Filtering and sorting state
  const [memberFilter, setMemberFilter] = useState('');
  const [memberSort, setMemberSort] = useState<'name' | 'role' | 'joined'>('name');
  const [applicationFilter, setApplicationFilter] = useState('');
  const [applicationSort, setApplicationSort] = useState<'name' | 'status' | 'added'>('name');

  // Settings state
  const [editingSettings, setEditingSettings] = useState(false);
  const [settingsForm, setSettingsForm] = useState({
    name: '',
    description: '',
    website: '',
    is_public: false,
  });

  // Filtered and sorted data
  const filteredMembers = React.useMemo(() => {
    let filtered = members.filter(member => {
      const searchTerm = memberFilter.toLowerCase();
      const userName = (member.user_name || member.user_email || '').toLowerCase();
      const userRole = (member.role || '').toLowerCase();
      return userName.includes(searchTerm) || userRole.includes(searchTerm);
    });

    return filtered.sort((a, b) => {
      switch (memberSort) {
        case 'name':
          return (a.user_name || a.user_email || '').localeCompare(b.user_name || b.user_email || '');
        case 'role':
          return (a.role || '').localeCompare(b.role || '');
        case 'joined':
          return new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
        default:
          return 0;
      }
    });
  }, [members, memberFilter, memberSort]);

  const filteredApplications = React.useMemo(() => {
    let filtered = applications.filter(app => {
      const searchTerm = applicationFilter.toLowerCase();
      const appName = (app.application_name || '').toLowerCase();
      return appName.includes(searchTerm);
    });

    return filtered.sort((a, b) => {
      switch (applicationSort) {
        case 'name':
          return (a.application_name || '').localeCompare(b.application_name || '');
        case 'status':
          return (a.is_active ? 'active' : 'inactive').localeCompare(b.is_active ? 'active' : 'inactive');
        case 'added':
          return new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
        default:
          return 0;
      }
    });
  }, [applications, applicationFilter, applicationSort]);

  useEffect(() => {
    if (organizationId) {
      loadOrganizationData();
    }

    // Cleanup dynamic title when component unmounts or organizationId changes
    return () => {
      if (organizationId) {
        removeDynamicTitle(organizationId);
      }
    };
  }, [organizationId]);



  const loadOrganizationData = async () => {
    if (!organizationId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const [orgData, membersData, applicationsData] = await Promise.all([
        organizationApi.getOrganizationById(organizationId),
        organizationApi.getOrganizationMembers(organizationId),
        organizationApi.getOrganizationApplications(organizationId)
      ]);

      setOrganization(orgData);
      setMembers(membersData);
      setApplications(applicationsData);

      // Set dynamic breadcrumb title for organization
      if (organizationId && orgData.name) {
        setDynamicTitle(organizationId, orgData.name);
      }

      // Try to load invitations, but don't fail if user doesn't have permission
      try {
        const invitationsData = await organizationApi.getOrganizationInvitations(organizationId, 'all');
        setInvitations(invitationsData);
      } catch (invitationError: any) {
        console.warn('User does not have permission to view invitations:', invitationError);
        setInvitations([]); // Set empty array if no permission
      }
    } catch (err: any) {
      console.error('Failed to load organization data:', err);
      setError(intl.formatMessage({ id: 'developer.organizations.common.error' }));
      
      if (err.response?.status === 403) {
        toast({
          title: 'You do not have permission to view this organization',
          variant: 'destructive',
        });
        navigate('/developer/organizations');
      } else if (err.response?.status === 404) {
        toast({
          title: 'Organization not found',
          variant: 'destructive',
        });
        navigate('/developer/organizations');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/developer/organizations');
  };

  const handleInviteMembers = () => {
    if (!canManage) {
      toast({
        title: "Permission Denied",
        description: "You need admin or owner role to invite members",
        variant: "destructive",
      });
      return;
    }
    setActiveTab('invitations');
  };

  const handleSendInvitation = () => {
    if (!canManage) {
      toast({
        title: "Permission Denied",
        description: "You need admin or owner role to add members",
        variant: "destructive",
      });
      return;
    }
    // Reset search state when opening dialog
    setInvitationEmail('');
    setAvailableUsers([]);
    setSearchingUsers(false);
    setSearchError('');
    setSelectedUser(null);
    setSendInvitationDialog(true);
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!organizationId) return;

    try {
      await organizationApi.cancelInvitation(organizationId, invitationId);

      // Refresh invitations data
      const updatedInvitations = await organizationApi.getOrganizationInvitations(organizationId);
      setInvitations(updatedInvitations);

      toast({
        title: "Invitation Cancelled",
        description: "The invitation has been cancelled successfully",
      });
    } catch (error: any) {
      console.error('Failed to cancel invitation:', error);
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to cancel invitation",
        variant: "destructive",
      });
    }
  };

  // Search function that uses current userRole
  const searchUsers = React.useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setAvailableUsers([]);
      setSearchingUsers(false);
      setSearchError('');
      return;
    }

    setSearchingUsers(true);
    setSearchError('');

    try {
      // Use the correct API endpoint with authentication headers
      const token = localStorage.getItem('access_token');
      // Get current userRole from organization
      const currentUserRole = organization?.user_role || 'member';

      // First, refresh the members list to ensure we have the latest data
      const freshMembers = await organizationApi.getOrganizationMembers(organizationId!);

      // Use admin endpoint if user is system admin, otherwise developer endpoint
      const endpoint = systemUser?.role === 'admin'
        ? `/api/v1/admin/users?limit=100&offset=0`
        : `/api/v1/developer/users?limit=100&offset=0`;
      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          setSearchError(intl.formatMessage(
            { id: 'developer.organizations.dashboard.members.addUserDialog.noResults' },
            { query: searchTerm }
          ));
          setAvailableUsers([]);
          return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const users = data.users || [];

      // Filter by search term and exclude users already in the organization or invited
      const memberUserIds = freshMembers.map(m => m.user_id);
      const invitedUserIds = invitations.map(inv => inv.user_id).filter(Boolean);
      console.log('Fresh members user IDs:', memberUserIds);
      console.log('Invited user IDs:', invitedUserIds);
      console.log('All users from API:', users.map((u: any) => ({ id: u.id, email: u.email })));

      const searchLower = searchTerm.toLowerCase();
      const filteredUsers = users.filter((user: any) => {
        const matchesSearch =
          user.email?.toLowerCase().includes(searchLower) ||
          user.username?.toLowerCase().includes(searchLower) ||
          user.first_name?.toLowerCase().includes(searchLower) ||
          user.last_name?.toLowerCase().includes(searchLower);
        const isNotMember = !memberUserIds.includes(user.id);
        const isNotInvited = !invitedUserIds.includes(user.id);

        console.log(`User ${user.email}: matchesSearch=${matchesSearch}, isNotMember=${isNotMember}, isNotInvited=${isNotInvited}, user.id=${user.id}`);

        return matchesSearch && isNotMember && isNotInvited;
      });

      if (filteredUsers.length === 0) {
        if (users.length === 0) {
          setSearchError(intl.formatMessage(
            { id: 'developer.organizations.dashboard.members.addUserDialog.noResults' },
            { query: searchTerm }
          ));
        } else {
          // Check if users are already invited
          const invitedUserIds = invitations.map(inv => inv.user_id).filter(Boolean);
          const alreadyInvited = users.filter((user: any) => invitedUserIds.includes(user.id));

          if (alreadyInvited.length > 0) {
            setSearchError(intl.formatMessage(
              { id: 'developer.organizations.dashboard.members.addUserDialog.alreadyInvited' },
              { query: searchTerm }
            ));
          } else {
            setSearchError(intl.formatMessage(
              { id: 'developer.organizations.dashboard.members.addUserDialog.allMembersAlready' },
              { query: searchTerm }
            ));
          }
        }
      }

      setAvailableUsers(filteredUsers);
    } catch (error) {
      console.error('Failed to search users:', error);
      setSearchError(intl.formatMessage(
        { id: 'developer.organizations.dashboard.members.addUserDialog.searchError' },
        { query: searchTerm }
      ));
      setAvailableUsers([]);
    } finally {
      setSearchingUsers(false);
    }
  }, [members, intl, organization]);

  // Debounced search function
  const debouncedSearchUsers = React.useCallback(
    debounce(searchUsers, 300),
    [searchUsers]
  );

  const handleSearchUsers = (searchTerm: string) => {
    setInvitationEmail(searchTerm); // Update input immediately
    debouncedSearchUsers(searchTerm); // Debounce the API call
  };

  const handleSubmitInvitation = async () => {
    if (!organizationId || !selectedUser) return;

    try {
      // Send invitation to user instead of adding directly
      await organizationApi.createOrganizationInvitation(organizationId, {
        email: selectedUser.email,
        role: invitationRole,
        message: invitationMessage,
      });

      // Refresh invitations data
      const updatedInvitations = await organizationApi.getOrganizationInvitations(organizationId);
      setInvitations(updatedInvitations);

      // Reset form and close dialog
      setSelectedUser(null);
      setInvitationEmail('');
      setInvitationRole('member');
      setInvitationMessage('');
      setAvailableUsers([]);
      setSendInvitationDialog(false);

      toast({
        title: "Invitation Sent",
        description: `Invitation sent to ${selectedUser.email}`,
      });
    } catch (error: any) {
      console.error('Failed to send invitation:', error);

      let errorMessage = 'Failed to send invitation';
      if (error.response?.data?.detail) {
        if (error.response.data.detail.includes('already invited')) {
          errorMessage = 'User has already been invited to this organization';
        } else if (error.response.data.detail.includes('already a member')) {
          errorMessage = 'User is already a member of this organization';
        } else {
          errorMessage = error.response.data.detail;
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleManageApplications = () => {
    setActiveTab('applications');
  };

  const handleAddApplication = async () => {
    if (!canManage) {
      toast({
        title: "Permission Denied",
        description: "You need admin or owner role to add applications",
        variant: "destructive",
      });
      return;
    }

    setLoadingApplications(true);
    try {
      // Load developer's applications
      const apps = await getDeveloperApplications();

      // Filter out applications already assigned to this organization
      const assignedAppIds = applications.map(app => app.application_id);
      const availableApps = apps.filter((app: any) => !assignedAppIds.includes(app.id));

      setAvailableApplications(availableApps);
      setAddApplicationDialog(true);
    } catch (error) {
      console.error('Failed to load applications:', error);
      toast({
        title: "Error",
        description: "Failed to load available applications",
        variant: "destructive",
      });
    } finally {
      setLoadingApplications(false);
    }
  };

  const handleSubmitApplication = async () => {
    if (!organizationId || !applicationId) return;

    try {
      await organizationApi.assignApplicationToOrganization(organizationId, {
        application_id: applicationId,
        description: applicationDescription.trim() || undefined,
        role: 'application'
      });

      // Refresh applications data
      const updatedApplications = await organizationApi.getOrganizationApplications(organizationId);
      setApplications(updatedApplications);

      // Reset form and close dialog
      setApplicationId('');
      setApplicationDescription('');
      setAvailableApplications([]);
      setAddApplicationDialog(false);

      toast({
        title: intl.formatMessage({ id: 'developer.organizations.dashboard.applications.add' }),
        description: "Application added to organization successfully",
      });
    } catch (error) {
      console.error('Failed to add application:', error);
      toast({
        title: "Error",
        description: "Failed to add application to organization",
        variant: "destructive",
      });
    }
  };

  const handleEditApplication = (app: any) => {
    // Navigate to application edit page
    window.open(`/developer/applications/${app.application_id}`, '_blank');
  };

  const handleRemoveApplication = async (app: any) => {
    if (!organizationId) return;

    try {
      await organizationApi.removeApplicationFromOrganization(organizationId, app.application_id);

      // Refresh applications data
      const updatedApplications = await organizationApi.getOrganizationApplications(organizationId);
      setApplications(updatedApplications);

      toast({
        title: "Application Removed",
        description: `Removed ${app.application_name} from organization`,
      });
    } catch (error) {
      console.error('Failed to remove application:', error);
      toast({
        title: "Error",
        description: "Failed to remove application from organization",
        variant: "destructive",
      });
    }
  };

  const handleEditSettings = () => {
    if (organization) {
      setSettingsForm({
        name: organization.name || '',
        description: organization.description || '',
        website: organization.website || '',
        is_public: organization.is_public || false,
      });
      setEditingSettings(true);
    }
  };

  const handleSaveSettings = async () => {
    if (!organizationId) return;

    try {
      await organizationApi.updateOrganization(organizationId, settingsForm);

      // Refresh organization data
      const updatedOrganization = await organizationApi.getOrganizationById(organizationId);
      setOrganization(updatedOrganization);

      setEditingSettings(false);

      toast({
        title: "Settings Updated",
        description: "Organization settings have been updated successfully",
      });
    } catch (error) {
      console.error('Failed to update settings:', error);
      toast({
        title: "Error",
        description: "Failed to update organization settings",
        variant: "destructive",
      });
    }
  };

  const handleCancelSettings = () => {
    setEditingSettings(false);
    if (organization) {
      setSettingsForm({
        name: organization.name || '',
        description: organization.description || '',
        website: organization.website || '',
        is_public: organization.is_public || false,
      });
    }
  };

  const handleDeleteOrganization = async () => {
    if (!organizationId || !organization) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${organization.name}"? This will remove all relations but keep applications and users intact.`
    );

    if (!confirmed) return;

    try {
      await organizationApi.deleteOrganization(organizationId);

      toast({
        title: "Organization Deleted",
        description: "Organization has been deleted successfully",
      });

      // Navigate back to organizations list
      window.location.href = '/developer/organizations';
    } catch (error) {
      console.error('Failed to delete organization:', error);
      toast({
        title: "Error",
        description: "Failed to delete organization",
        variant: "destructive",
      });
    }
  };

  const handleChangeRole = async (member: any, newRole: 'admin' | 'owner' | 'member') => {
    if (!organizationId) return;

    try {
      // Use user_id for the API call, not the membership id
      const memberUserId = member.user_id || member.id;
      await organizationApi.updateOrganizationMember(organizationId, memberUserId, { role: newRole });

      // Refresh member data
      const updatedMembers = await organizationApi.getOrganizationMembers(organizationId);
      setMembers(updatedMembers);

      const roleName = newRole === 'admin' ?
        intl.formatMessage({ id: 'developer.organizations.role.admin' }) :
        intl.formatMessage({ id: 'developer.organizations.role.member' });

      toast({
        title: intl.formatMessage({ id: 'developer.organizations.dashboard.members.changeRole' }),
        description: intl.formatMessage(
          { id: 'developer.organizations.dashboard.members.roleChanged' },
          {
            user: member.user_email || member.user_name || 'member',
            role: roleName
          }
        ),
      });
    } catch (error) {
      console.error('Failed to change member role:', error);
      toast({
        title: "Error",
        description: "Failed to change member role",
        variant: "destructive",
      });
    }
  };

  const handleRemoveMember = async (member: any) => {
    if (!organizationId) return;

    try {
      // Use user_id instead of id for the member removal
      const memberUserId = member.user_id || member.id;
      await organizationApi.removeOrganizationMember(organizationId, memberUserId);

      // Refresh member data
      const updatedMembers = await organizationApi.getOrganizationMembers(organizationId);
      setMembers(updatedMembers);

      toast({
        title: intl.formatMessage({ id: 'developer.organizations.dashboard.members.remove' }),
        description: `Removed ${member.user_email || 'member'} from organization`,
      });
    } catch (error) {
      console.error('Failed to remove member:', error);
      toast({
        title: "Error",
        description: "Failed to remove member",
        variant: "destructive",
      });
    }
  };



  // Mock data removed - using real API data

  if (loading) {
    return <OrganizationDashboardSkeleton />;
  }

  if (error || !organization) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {intl.formatMessage({ id: 'developer.organizations.common.error' })}
          </h2>
            <Button onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              {intl.formatMessage({ id: 'developer.organizations.common.back' })}
            </Button>
          </div>
      </div>
    );
  }



  const pendingInvitations = invitations.filter(inv => inv.status === 'pending');
  const userRole = organization.user_role || 'member';
  const canManage = userRole === 'owner' || userRole === 'admin';

  // Access control based on system role and organization role
  if (organization) {
    // System admins can access any organization
    if (systemUser?.role === 'admin') {
      // Allow access - system admins can oversee all organizations
    }
    // Developers can access organizations they have admin/owner role in
    else if (systemUser?.role === 'developer' && !canManage) {
      return (
        <div className="flex flex-1 flex-col gap-6 p-6 w-full">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Access Denied
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You need admin or owner role in this organization to manage it.
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Organizations
            </Button>
          </div>
        </div>
      );
    }
    // Regular users cannot access organization management at all
    else if (systemUser?.role === 'user') {
      return (
        <div className="flex flex-1 flex-col gap-6 p-6 w-full">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Access Denied
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Organization management is only available to developers and system administrators.
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to User Portal
            </Button>
          </div>
        </div>
      );
    }
  }



  // Handle tab access control without useEffect to prevent infinite loops
  const handleTabChange = (newTab: string) => {
    // Prevent non-admin users from accessing restricted tabs
    if (!canManage && (newTab === 'invitations' || newTab === 'settings')) {
      toast({
        title: "Access Denied",
        description: "You need admin or owner role to access this tab",
        variant: "destructive",
      });
      return;
    }
    setActiveTab(newTab);
  };

  // Ensure non-admin users don't see restricted tabs in their current tab
  const safeActiveTab = (!canManage && (activeTab === 'invitations' || activeTab === 'settings'))
    ? 'overview'
    : activeTab;

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Building2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {organization.name}
                </h1>
                <div className="flex items-center space-x-4 mt-1">
                  <p className="text-gray-600 dark:text-gray-400">
                    @{organization.slug}
                  </p>
                  <Badge variant={organization.is_public ? 'default' : 'secondary'}>
                    {intl.formatMessage({
                      id: organization.is_public ? 'developer.organizations.status.public' : 'developer.organizations.status.private'
                    })}
                  </Badge>
                  <Badge variant="outline">
                    {intl.formatMessage({
                      id: `developer.organizations.tier.${organization.subscription_tier}`
                    })}
                  </Badge>
                </div>
              </div>
            </div>
            
            {canManage && (
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={handleInviteMembers}
                  className="btn-animate shimmer-effect"
                  size="default"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.actions.inviteMembers' })}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleManageApplications}
                  className="btn-animate scale-effect"
                  size="default"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.actions.manageApplications' })}
                </Button>
              </div>
            )}
          </div>
          
          {organization.description && (
            <p className="mt-4 text-gray-600 dark:text-gray-400 max-w-3xl">
              {organization.description}
            </p>
          )}
          
          {organization.website && (
            <div className="mt-2">
              <a
                href={organization.website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline"
              >
                <Globe className="w-4 h-4 mr-1" />
                {organization.website}
              </a>
            </div>
          )}
        </div>

        {/* Dashboard Tabs */}
        <div className="space-y-6">
          <StyledTabs
            tabs={[
              { id: 'overview', label: intl.formatMessage({ id: 'developer.organizations.navigation.overview' }) },
              { id: 'members', label: intl.formatMessage({ id: 'developer.organizations.navigation.members' }) },
              { id: 'applications', label: intl.formatMessage({ id: 'developer.organizations.navigation.applications' }) },
              ...(canManage ? [{
                id: 'invitations',
                label: `${intl.formatMessage({ id: 'developer.organizations.navigation.invitations' })}${pendingInvitations.length > 0 ? ` (${pendingInvitations.length})` : ''}`
              }] : []),
              ...(canManage ? [{ id: 'settings', label: intl.formatMessage({ id: 'developer.organizations.navigation.settings' }) }] : [])
            ]}
            activeTab={safeActiveTab}
            onTabChange={handleTabChange}
            size="md"
            variant="default"
          />

          {/* Tab Content */}
          {safeActiveTab === 'overview' && (
            <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Members Card */}
              <Card className="shadow-sm border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Users className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.members.title' })}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {members.length}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {intl.formatMessage(
                      { id: 'developer.organizations.dashboard.overview.members.count' },
                      { count: members.length }
                    )}
                  </p>
                  <Button asChild className="w-full">
                    <button onClick={() => setActiveTab('members')}>
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.members.viewAll' })}
                    </button>
                  </Button>
                </CardContent>
              </Card>

              {/* Applications Card */}
              <Card className="shadow-sm border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Building2 className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.applications.title' })}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    0
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {intl.formatMessage(
                      { id: 'developer.organizations.dashboard.overview.applications.count' },
                      { count: 0 }
                    )}
                  </p>
                  <Button asChild className="w-full">
                    <button onClick={() => setActiveTab('applications')}>
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.applications.viewAll' })}
                    </button>
                  </Button>
                </CardContent>
              </Card>

              {/* Invitations Card */}
              <Card className="shadow-sm border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Mail className="w-5 h-5 mr-2 text-orange-600 dark:text-orange-400" />
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.invitations.title' })}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {pendingInvitations.length}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {intl.formatMessage(
                      { id: 'developer.organizations.dashboard.overview.invitations.count' },
                      { count: pendingInvitations.length }
                    )}
                  </p>
                  <Button asChild className="w-full">
                    <button onClick={() => setActiveTab('invitations')}>
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.overview.invitations.viewAll' })}
                    </button>
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Organization Info */}
            <Card className="shadow-sm border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.info.generalInformation' })}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.info.organizationName' })}
                    </p>
                    <p className="text-gray-900 dark:text-white">{organization.name}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.info.maximumMembers' })}
                    </p>
                    <p className="text-gray-900 dark:text-white">
                      {members.length} / {organization.max_members}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.info.created' })}
                    </p>
                    <p className="text-gray-900 dark:text-white flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(organization.created_at).toLocaleDateString()}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.info.yourRole' })}
                    </p>
                    <Badge variant="outline" className="capitalize">
                      <Shield className="w-3 h-3 mr-1" />
                      {intl.formatMessage({ id: `developer.organizations.role.${userRole}` })}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
            </div>
          )}

          {/* Members Tab */}
          {safeActiveTab === 'members' && (
            <div className="space-y-6">
              {/* Members Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.members.title' })}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.members.subtitle' })}
                  </p>
                </div>
                {canManage && (
                  <button
                    onClick={handleInviteMembers}
                    className="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.members.invite' })}
                  </button>
                )}
              </div>

              {/* Members Filter and Sort */}
              <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex-1">
                  <Input
                    placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.members.search' })}
                    value={memberFilter}
                    onChange={(e) => setMemberFilter(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={memberSort} onValueChange={(value: any) => setMemberSort(value)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.members.sortBy' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.sortByName' })}</SelectItem>
                    <SelectItem value="role">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.sortByRole' })}</SelectItem>
                    <SelectItem value="joined">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.sortByJoined' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Members List */}
              <div className="rounded-lg border bg-card">
                <div className="p-6">
                  <div className="space-y-4">
                    {filteredMembers.map((member, index) => (
                      <div key={index} className="flex items-center justify-between p-4 rounded-lg border bg-muted/50">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <Users className="w-5 h-5 text-primary" />
                          </div>
                          <div>
                            <p className="font-medium">{member.user_name || member.user_email || 'Current User'}</p>
                            <p className="text-sm text-muted-foreground">
                              {member.user_email && member.user_name && `${member.user_email}`}
                              {member.created_at && ` • Joined ${new Date(member.created_at).toLocaleDateString()}`}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                            (member.role || userRole) === 'owner'
                              ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-200'
                              : (member.role || userRole) === 'admin'
                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-200'
                              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                          }`}>
                            {(member.role || userRole) === 'owner' ? (
                              <Crown className="h-3 w-3" />
                            ) : (member.role || userRole) === 'admin' ? (
                              <Shield className="h-3 w-3" />
                            ) : (
                              <Users className="h-3 w-3" />
                            )}
                            {intl.formatMessage({ id: `developer.organizations.role.${member.role || userRole}` })}
                          </span>

                          {/* Member Actions */}
                          {userRole === 'owner' && (member.role || userRole) !== 'owner' && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <button className="rounded-md border border-input p-2 hover:bg-accent transition-colors">
                                  <MoreVertical className="h-4 w-4" />
                                </button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {(member.role || userRole) === 'member' && (
                                  <DropdownMenuItem onClick={() => handleChangeRole(member, 'admin')}>
                                    <UserCog className="mr-2 h-4 w-4" />
                                    {intl.formatMessage({ id: 'developer.organizations.dashboard.members.actions.makeAdmin' })}
                                  </DropdownMenuItem>
                                )}
                                {(member.role || userRole) === 'admin' && (
                                  <DropdownMenuItem onClick={() => handleChangeRole(member, 'member')}>
                                    <Users className="mr-2 h-4 w-4" />
                                    {intl.formatMessage({ id: 'developer.organizations.dashboard.members.actions.makeMember' })}
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleRemoveMember(member)}
                                  className="text-red-600 dark:text-red-400"
                                >
                                  <UserMinus className="mr-2 h-4 w-4" />
                                  {intl.formatMessage({ id: 'developer.organizations.dashboard.members.actions.removeMember' })}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Applications Tab */}
          {safeActiveTab === 'applications' && (
            <div className="space-y-6">
              {/* Applications Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.title' })}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.subtitle' })}
                  </p>
                </div>
                {canManage && (
                  <button
                    onClick={handleAddApplication}
                    className="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.add' })}
                  </button>
                )}
              </div>

              {/* Applications Filter and Sort */}
              <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex-1">
                  <Input
                    placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.applications.search' })}
                    value={applicationFilter}
                    onChange={(e) => setApplicationFilter(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={applicationSort} onValueChange={(value: any) => setApplicationSort(value)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.applications.sortBy' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.sortByName' })}</SelectItem>
                    <SelectItem value="status">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.sortByStatus' })}</SelectItem>
                    <SelectItem value="added">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.sortByAdded' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Applications List */}
              <div className="rounded-lg border bg-card">
                <div className="p-6">
                  {applications.length === 0 ? (
                    <div className="text-center py-12">
                      <Building2 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.empty.title' })}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.empty.description' })}
                      </p>
                      {canManage && (
                        <button
                          onClick={handleAddApplication}
                          className="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                        >
                          <Plus className="h-4 w-4" />
                          {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.add' })}
                        </button>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredApplications.map((app) => (
                        <div key={app.id} className="flex items-center justify-between p-4 rounded-lg border bg-muted/50">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                              <Building2 className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium">{app.application_name || `Application ${app.application_id.slice(0, 8)}`}</p>
                              <p className="text-sm text-muted-foreground">
                                {app.application_client_id && `Client ID: ${app.application_client_id.slice(0, 12)}... • `}
                                {app.description || 'No description'}
                                {app.created_at && ` • Added ${new Date(app.created_at).toLocaleDateString()}`}
                                {app.role && ` • Role: ${app.role}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                              app.is_active
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-200'
                                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                            }`}>
                              {intl.formatMessage({
                                id: app.is_active
                                  ? 'developer.organizations.dashboard.applications.status.active'
                                  : 'developer.organizations.dashboard.applications.status.inactive'
                              })}
                            </span>

                            {/* Application Actions */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <button className="rounded-md border border-input p-2 hover:bg-accent transition-colors">
                                  <MoreVertical className="h-4 w-4" />
                                </button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditApplication(app)}>
                                  <Settings className="mr-2 h-4 w-4" />
                                  {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.actions.edit' })}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleRemoveApplication(app)}
                                  className="text-red-600 dark:text-red-400"
                                >
                                  <Building2 className="mr-2 h-4 w-4" />
                                  {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.actions.removeFromOrganization' })}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Invitations Tab */}
          {safeActiveTab === 'invitations' && (
            <div className="space-y-6">
              {/* Invitations Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.title' })}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.subtitle' })}
                  </p>
                </div>
                <button
                  onClick={handleSendInvitation}
                  className="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                >
                  <Mail className="h-4 w-4" />
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.send' })}
                </button>
              </div>

              {/* Invitations List */}
              <div className="rounded-lg border bg-card">
                <div className="p-6">
                  {invitations.length === 0 ? (
                    <div className="text-center py-12">
                      <Mail className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.empty.title' })}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.empty.description' })}
                      </p>
                      <button
                        onClick={handleSendInvitation}
                        className="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                      >
                        <Mail className="h-4 w-4" />
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.send' })}
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {invitations.map((invitation) => (
                        <div key={invitation.id} className="flex items-center justify-between p-4 rounded-lg border bg-muted/50">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                              <Mail className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium">{invitation.email}</p>
                              <p className="text-sm text-muted-foreground">
                                {intl.formatMessage({ id: `developer.organizations.role.${invitation.role}` })}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                              invitation.status === 'pending'
                                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-200'
                                : invitation.status === 'accepted'
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-200'
                                : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-200'
                            }`}>
                              {intl.formatMessage({ id: `developer.organizations.dashboard.invitations.status.${invitation.status}` })}
                            </span>
                            {invitation.status === 'pending' && (
                              <button
                                onClick={() => handleCancelInvitation(invitation.id)}
                                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1 rounded transition-colors"
                                title={intl.formatMessage({ id: 'developer.organizations.dashboard.invitations.cancel' })}
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {safeActiveTab === 'settings' && (
            <div className="space-y-6">
              {/* Settings Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.title' })}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.subtitle' })}
                  </p>
                </div>
                {!editingSettings ? (
                  <Button onClick={handleEditSettings}>
                    {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.edit' })}
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={handleCancelSettings}>
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.cancel' })}
                    </Button>
                    <Button onClick={handleSaveSettings}>
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.save' })}
                    </Button>
                  </div>
                )}
              </div>

              {/* General Settings */}
              <div className="rounded-lg border bg-card p-6">
                <h4 className="text-lg font-semibold mb-4">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.title' })}
                </h4>

                <div className="space-y-6">
                  {/* Organization Name */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.name' })}
                    </label>
                    <input
                      type="text"
                      value={editingSettings ? settingsForm.name : organization.name}
                      onChange={(e) => editingSettings && setSettingsForm(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full rounded-md border px-3 py-2 text-sm border-input bg-background"
                      disabled={!editingSettings}
                    />
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.description' })}
                    </label>
                    <textarea
                      value={editingSettings ? settingsForm.description : (organization.description || '')}
                      onChange={(e) => editingSettings && setSettingsForm(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full rounded-md border px-3 py-2 text-sm border-input bg-background"
                      rows={3}
                      disabled={!editingSettings}
                    />
                  </div>

                  {/* Website */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.website' })}
                    </label>
                    <input
                      type="url"
                      value={editingSettings ? settingsForm.website : (organization.website || '')}
                      onChange={(e) => editingSettings && setSettingsForm(prev => ({ ...prev, website: e.target.value }))}
                      className="w-full rounded-md border px-3 py-2 text-sm border-input bg-background"
                      disabled={!editingSettings}
                    />
                  </div>

                  {/* Public Organization */}
                  <div className="flex items-center justify-between py-2">
                    <div className="space-y-1">
                      <label className="text-sm font-medium">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.isPublic' })}
                      </label>
                      <p className="text-xs text-muted-foreground">
                        {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.isPublicHelp' })}
                      </p>
                    </div>
                    <Switch
                      checked={editingSettings ? settingsForm.is_public : organization.is_public}
                      onCheckedChange={(checked) => editingSettings && setSettingsForm(prev => ({ ...prev, is_public: checked }))}
                      disabled={!editingSettings}
                    />
                  </div>

                  {/* Divider */}
                  <div className="border-t border-border"></div>

                  {/* Max Members */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers' })}
                    </label>
                    <Select
                      value={organization.max_members.toString()}
                      disabled
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.10' })}</SelectItem>
                        <SelectItem value="25">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.25' })}</SelectItem>
                        <SelectItem value="50">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.50' })}</SelectItem>
                        <SelectItem value="100">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.100' })}</SelectItem>
                        <SelectItem value="250">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.250' })}</SelectItem>
                        <SelectItem value="500">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.500' })}</SelectItem>
                        <SelectItem value="1000">{intl.formatMessage({ id: 'developer.organizations.dashboard.settings.general.maxMembers.1000' })}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Danger Zone */}
              <div className="rounded-lg border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50 p-6">
                <h4 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.danger.title' })}
                </h4>
                <p className="text-sm text-red-700 dark:text-red-300 mb-4">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.danger.description' })}
                </p>
                <button
                  onClick={handleDeleteOrganization}
                  className="inline-flex items-center gap-2 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 transition-colors"
                >
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.settings.danger.delete' })}
                </button>
              </div>
            </div>
          )}
        </div>

      {/* Add Application Dialog */}
      <Dialog open={addApplicationDialog} onOpenChange={setAddApplicationDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.title' })}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="applicationId">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.selectApplication' })}</Label>
              {loadingApplications ? (
                <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.loadingApplications' })}</div>
              ) : availableApplications.length === 0 ? (
                <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.noApplications' })}</div>
              ) : (
                <Select value={applicationId} onValueChange={setApplicationId}>
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.selectPlaceholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableApplications.map((app) => (
                      <SelectItem key={app.id} value={app.id}>
                        {app.application_name} ({app.client_id})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="applicationDescription">{intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.description' })}</Label>
              <Input
                id="applicationDescription"
                value={applicationDescription}
                onChange={(e) => setApplicationDescription(e.target.value)}
                placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.descriptionPlaceholder' })}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setAddApplicationDialog(false)}>
                {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.cancel' })}
              </Button>
              <Button onClick={handleSubmitApplication} disabled={!applicationId || loadingApplications}>
                {intl.formatMessage({ id: 'developer.organizations.dashboard.applications.addApplicationDialog.addApplication' })}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Send Invitation Dialog */}
      <Dialog open={sendInvitationDialog} onOpenChange={setSendInvitationDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.title' })}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="userSearch">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.searchUser' })}</Label>
              <Input
                id="userSearch"
                value={invitationEmail}
                onChange={(e) => handleSearchUsers(e.target.value)}
                placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.searchPlaceholder' })}
              />
              {searchingUsers && (
                <div className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.searching' })}
                </div>
              )}
              {searchError && (
                <div className="text-sm text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700">
                  {searchError}
                </div>
              )}
              {availableUsers.length > 0 && (
                <div className="max-h-40 overflow-y-auto border rounded-md">
                  {availableUsers.map((user) => (
                    <div
                      key={user.id}
                      className={`p-2 cursor-pointer hover:bg-muted ${selectedUser?.id === user.id ? 'bg-primary/10 border-l-2 border-primary' : ''}`}
                      onClick={() => {
                        const newUser = selectedUser?.id === user.id ? null : user;
                        setSelectedUser(newUser);
                        // Reset role to member if selecting a system user
                        if (newUser && newUser.role === 'user' && invitationRole === 'admin') {
                          setInvitationRole('member');
                        }
                      }}
                    >
                      <div className="font-medium flex items-center justify-between">
                        <span>{user.first_name} {user.last_name}</span>
                        {selectedUser?.id === user.id && (
                          <span className="text-primary text-sm">✓ Selected</span>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="invitationRole">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.role' })}</Label>
              <Select value={invitationRole} onValueChange={(value: 'admin' | 'member') => setInvitationRole(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="member">{intl.formatMessage({ id: 'developer.organizations.role.member' })}</SelectItem>
                  {/* Only allow admin role for developers, not system users */}
                  {selectedUser && selectedUser.role === 'developer' && (
                    <SelectItem value="admin">{intl.formatMessage({ id: 'developer.organizations.role.admin' })}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {selectedUser && selectedUser.role === 'user' && (
                <p className="text-sm text-amber-600">
                  {intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.userCannotBeAdmin' })}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="invitationMessage">{intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.message' })}</Label>
              <Input
                id="invitationMessage"
                value={invitationMessage}
                onChange={(e) => setInvitationMessage(e.target.value)}
                placeholder={intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.messagePlaceholder' })}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setSendInvitationDialog(false)}>
                {intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.cancel' })}
              </Button>
              <Button onClick={handleSubmitInvitation} disabled={!selectedUser}>
                {intl.formatMessage({ id: 'developer.organizations.dashboard.members.addUserDialog.sendInvitation' })}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Loading skeleton component
const OrganizationDashboardSkeleton: React.FC = () => {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      <div className="mb-8">
          <Skeleton className="h-10 w-20 mb-4" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-lg" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>
        
        <div className="space-y-6">
          <Skeleton className="h-12 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="shadow-sm">
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-4 w-24 mb-4" />
                  <Skeleton className="h-8 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
    </div>
  );
};

export default OrganizationDashboard;
