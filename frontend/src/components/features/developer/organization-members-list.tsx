import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useToast } from '@/hooks/use-toast';

// UI Components
import { But<PERSON> } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Avatar, AvatarFallback } from '@/components/ui/shadcn/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/shadcn/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/shadcn/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';

// Icons
import { 
  MoreHorizontal, 
  UserMinus, 
  UserCog, 
  Shield, 
  Crown, 
  User,
  Plus,
  Calendar
} from 'lucide-react';

// Services
import { organizationApi, OrganizationMembership } from '@/services/organization-api';

interface OrganizationMembersListProps {
  organizationId: string;
  members: OrganizationMembership[];
  currentUserRole: string;
  onMembersChange: () => void;
}

interface RoleChangeDialog {
  isOpen: boolean;
  member: OrganizationMembership | null;
  newRole: string;
}

interface RemoveMemberDialog {
  isOpen: boolean;
  member: OrganizationMembership | null;
}

export const OrganizationMembersList: React.FC<OrganizationMembersListProps> = ({
  organizationId,
  members,
  currentUserRole,
  onMembersChange,
}) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [roleChangeDialog, setRoleChangeDialog] = useState<RoleChangeDialog>({
    isOpen: false,
    member: null,
    newRole: '',
  });
  
  const [removeMemberDialog, setRemoveMemberDialog] = useState<RemoveMemberDialog>({
    isOpen: false,
    member: null,
  });
  
  const [isUpdating, setIsUpdating] = useState(false);

  const canManageMembers = currentUserRole === 'owner' || currentUserRole === 'admin';

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      default:
        return <User className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const canManageMember = (member: OrganizationMembership): boolean => {
    if (!canManageMembers) return false;
    
    // Owners can manage everyone
    if (currentUserRole === 'owner') return true;
    
    // Admins can only manage members, not owners or other admins
    if (currentUserRole === 'admin') {
      return member.role === 'member';
    }
    
    return false;
  };

  const handleRoleChange = (member: OrganizationMembership) => {
    setRoleChangeDialog({
      isOpen: true,
      member,
      newRole: member.role,
    });
  };

  const handleRemoveMember = (member: OrganizationMembership) => {
    setRemoveMemberDialog({
      isOpen: true,
      member,
    });
  };

  const confirmRoleChange = async () => {
    if (!roleChangeDialog.member || !roleChangeDialog.newRole) return;
    
    setIsUpdating(true);
    
    try {
      await organizationApi.updateOrganizationMember(
        organizationId,
        roleChangeDialog.member.user_id,
        { role: roleChangeDialog.newRole as 'owner' | 'admin' | 'member' }
      );
      
      toast({
        title: 'Member role updated successfully',
      });
      onMembersChange();
      setRoleChangeDialog({ isOpen: false, member: null, newRole: '' });
    } catch (error: any) {
      console.error('Failed to update member role:', error);
      toast({
        title: 'Failed to update member role',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const confirmRemoveMember = async () => {
    if (!removeMemberDialog.member) return;
    
    setIsUpdating(true);
    
    try {
      await organizationApi.removeOrganizationMember(
        organizationId,
        removeMemberDialog.member.user_id
      );
      
      toast({
        title: 'Member removed successfully',
      });
      onMembersChange();
      setRemoveMemberDialog({ isOpen: false, member: null });
    } catch (error: any) {
      console.error('Failed to remove member:', error);
      toast({
        title: 'Failed to remove member',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getInitials = (name: string, email: string): string => {
    if (name && name.trim()) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return email.slice(0, 2).toUpperCase();
  };

  return (
    <>
      <Card className="shadow-sm border-gray-200 dark:border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl text-gray-900 dark:text-white">
                {intl.formatMessage({ id: 'developer.organizations.members.title' })}
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                {intl.formatMessage({ id: 'developer.organizations.members.subtitle' })}
              </CardDescription>
            </div>
            
            {canManageMembers && (
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                {intl.formatMessage({ id: 'developer.organizations.members.actions.inviteMore' })}
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md border border-gray-200 dark:border-gray-700">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">
                    {intl.formatMessage({ id: 'developer.organizations.members.table.name' })}
                  </TableHead>
                  <TableHead>
                    {intl.formatMessage({ id: 'developer.organizations.members.table.role' })}
                  </TableHead>
                  <TableHead>
                    {intl.formatMessage({ id: 'developer.organizations.members.table.joinedAt' })}
                  </TableHead>
                  <TableHead className="w-[100px]">
                    {intl.formatMessage({ id: 'developer.organizations.members.table.actions' })}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {getInitials(member.user_name || '', member.user_email || '')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {member.user_name || 'Unknown User'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {member.user_email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getRoleIcon(member.role)}
                        <Badge variant={getRoleBadgeVariant(member.role)} className="capitalize">
                          {intl.formatMessage({ id: `developer.organizations.members.roles.${member.role}` })}
                        </Badge>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(member.joined_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      {canManageMember(member) && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleRoleChange(member)}>
                              <UserCog className="mr-2 h-4 w-4" />
                              {intl.formatMessage({ id: 'developer.organizations.members.actions.changeRole' })}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleRemoveMember(member)}
                              className="text-red-600 dark:text-red-400"
                            >
                              <UserMinus className="mr-2 h-4 w-4" />
                              {intl.formatMessage({ id: 'developer.organizations.members.actions.removeMember' })}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {members.length === 0 && (
            <div className="text-center py-12">
              <User className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                No members found
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Change Dialog */}
      <Dialog 
        open={roleChangeDialog.isOpen} 
        onOpenChange={(open) => !open && setRoleChangeDialog({ isOpen: false, member: null, newRole: '' })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {intl.formatMessage({ id: 'developer.organizations.members.changeRole.title' })}
            </DialogTitle>
            <DialogDescription>
              {roleChangeDialog.member && intl.formatMessage(
                { id: 'developer.organizations.members.changeRole.currentRole' },
                { role: roleChangeDialog.member.role }
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                {intl.formatMessage({ id: 'developer.organizations.members.changeRole.newRole' })}
              </label>
              <Select
                value={roleChangeDialog.newRole}
                onValueChange={(value) => setRoleChangeDialog(prev => ({ ...prev, newRole: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currentUserRole === 'owner' && (
                    <SelectItem value="owner">
                      <div className="flex items-center space-x-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span>{intl.formatMessage({ id: 'developer.organizations.members.roles.owner' })}</span>
                      </div>
                    </SelectItem>
                  )}
                  <SelectItem value="admin">
                    <div className="flex items-center space-x-2">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span>{intl.formatMessage({ id: 'developer.organizations.members.roles.admin' })}</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="member">
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-600" />
                      <span>{intl.formatMessage({ id: 'developer.organizations.members.roles.member' })}</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Role descriptions */}
            <div className="space-y-2 text-sm">
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <p className="font-medium text-gray-900 dark:text-white mb-1">
                  {intl.formatMessage({ id: `developer.organizations.members.roles.${roleChangeDialog.newRole}` })}
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  {intl.formatMessage({ id: `developer.organizations.members.roleDescriptions.${roleChangeDialog.newRole}` })}
                </p>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRoleChangeDialog({ isOpen: false, member: null, newRole: '' })}
              disabled={isUpdating}
            >
              {intl.formatMessage({ id: 'developer.organizations.members.changeRole.cancel' })}
            </Button>
            <Button
              onClick={confirmRoleChange}
              disabled={isUpdating || !roleChangeDialog.newRole || roleChangeDialog.newRole === roleChangeDialog.member?.role}
            >
              {intl.formatMessage({ id: 'developer.organizations.members.changeRole.confirm' })}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <AlertDialog 
        open={removeMemberDialog.isOpen}
        onOpenChange={(open) => !open && setRemoveMemberDialog({ isOpen: false, member: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {intl.formatMessage({ id: 'developer.organizations.members.confirmRemove.title' })}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {removeMemberDialog.member && intl.formatMessage(
                { id: 'developer.organizations.members.confirmRemove.message' },
                { name: removeMemberDialog.member.user_name || removeMemberDialog.member.user_email }
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>
              {intl.formatMessage({ id: 'developer.organizations.members.confirmRemove.cancel' })}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveMember}
              disabled={isUpdating}
              className="bg-red-600 hover:bg-red-700"
            >
              {intl.formatMessage({ id: 'developer.organizations.members.confirmRemove.confirm' })}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
