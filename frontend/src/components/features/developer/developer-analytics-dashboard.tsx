/**
 * Developer Analytics Dashboard Component
 *
 * Enhanced developer dashboard with real-time analytics,
 * application performance metrics, and user engagement data.
 */

import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hnutChart } from '@/components/charts';
import { useDeveloperAnalytics, useDeveloperApplications } from '@/hooks/use-developer-api';
import { 
  Users, 
  Activity, 
  TrendingUp, 
  RefreshCw,
  Eye,
  BarChart3,
  Zap,
  Target
} from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  onClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  icon, 
  description,
  trend = 'neutral',
  onClick 
}) => {
  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = () => {
    if (change === undefined) return null;
    return (
      <span className={`text-sm ${getTrendColor()}`}>
        {change > 0 ? '+' : ''}{change}%
      </span>
    );
  };

  return (
    <Card className={onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''} onClick={onClick}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
        <div className="flex items-center justify-between">
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {getTrendIcon()}
        </div>
      </CardContent>
    </Card>
  );
};

export const DeveloperAnalyticsDashboard: React.FC = () => {
  const intl = useIntl();
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const { analytics, isLoading, error, refreshData } = useDeveloperAnalytics();
  const { applications } = useDeveloperApplications();

  const handleRefresh = () => {
    refreshData();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-red-600">
              <Activity className="h-4 w-4" />
              <span>{intl.formatMessage({ id: 'developer.analytics.error.failedToLoad' })}</span>
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.analytics.error.retry' })}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) return null;

  // Safe access to analytics data with null checks
  const recentActivity = analytics.recent_activity || [];
  const topApplications = analytics.top_applications || [];

  // Calculate growth rates with null checks
  const userGrowth = recentActivity.length > 1 
    ? ((recentActivity[recentActivity.length - 1]?.new_users || 0) - 
        (recentActivity[0]?.new_users || 0)) / Math.max(recentActivity[0]?.new_users || 1, 1) * 100
    : 0;

  const loginGrowth = recentActivity.length > 1
    ? ((recentActivity[recentActivity.length - 1]?.logins || 0) - 
        (recentActivity[0]?.logins || 0)) / Math.max(recentActivity[0]?.logins || 1, 1) * 100
    : 0;

  // Prepare chart data with safe access
  const activityChartData = recentActivity.map(item => ({
    date: item?.date ? new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : intl.formatMessage({ id: 'developer.analytics.common.unknown' }),
    logins: item?.logins || 0,
    newUsers: item?.new_users || 0,
  }));

  const applicationPerformanceData = topApplications.map(item => ({
    label: item?.application_name || intl.formatMessage({ id: 'developer.analytics.common.unknown' }),
    value: item?.user_count || 0,
    color: `hsl(${Math.random() * 360}, 70%, 50%)`,
  }));

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{intl.formatMessage({ id: 'developer.analytics.title' })}</h2>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'developer.analytics.description' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">{intl.formatMessage({ id: 'developer.analytics.period.1d' })}</SelectItem>
              <SelectItem value="7d">{intl.formatMessage({ id: 'developer.analytics.period.7d' })}</SelectItem>
              <SelectItem value="30d">{intl.formatMessage({ id: 'developer.analytics.period.30d' })}</SelectItem>
              <SelectItem value="90d">{intl.formatMessage({ id: 'developer.analytics.period.90d' })}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'developer.analytics.refresh' })}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title={intl.formatMessage({ id: 'developer.analytics.metrics.totalUsers' })}
          value={analytics.total_users || 0}
          change={userGrowth}
          trend={userGrowth > 0 ? 'up' : userGrowth < 0 ? 'down' : 'neutral'}
          icon={<Users className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'developer.analytics.metrics.totalUsersDesc' })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'developer.analytics.metrics.activeUsers' })}
          value={analytics.active_users || 0}
          icon={<Zap className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'developer.analytics.metrics.activeUsersDesc' })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'developer.analytics.metrics.totalLogins' })}
          value={analytics.total_logins || 0}
          change={loginGrowth}
          trend={loginGrowth > 0 ? 'up' : loginGrowth < 0 ? 'down' : 'neutral'}
          icon={<Activity className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'developer.analytics.metrics.totalLoginsDesc' })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'developer.analytics.metrics.applications' })}
          value={analytics.total_applications || 0}
          icon={<Target className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'developer.analytics.metrics.applicationsDesc' })}
        />
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* User Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>{intl.formatMessage({ id: 'developer.analytics.charts.userActivity' })}</span>
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'developer.analytics.charts.userActivityDesc' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LineChart
              labels={activityChartData.map(item => item.date)}
              datasets={[
                {
                  label: intl.formatMessage({ id: 'developer.analytics.charts.logins' }),
                  data: activityChartData.map(item => item.logins),
                  borderColor: '#50C878',
                  backgroundColor: 'rgba(80, 200, 120, 0.1)',
                  fill: true,
                },
                {
                  label: intl.formatMessage({ id: 'developer.analytics.charts.newUsers' }),
                  data: activityChartData.map(item => item.newUsers),
                  borderColor: '#4F46E5',
                  backgroundColor: 'rgba(79, 70, 229, 0.1)',
                  fill: false,
                },
              ]}
              height={300}
            />
          </CardContent>
        </Card>

        {/* Application Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>{intl.formatMessage({ id: 'developer.analytics.charts.applicationPerformance' })}</span>
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'developer.analytics.charts.applicationPerformanceDesc' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DoughnutChart
              data={applicationPerformanceData}
              height={300}
              centerText={intl.formatMessage({ id: 'developer.analytics.common.users' })}
              showPercentages={true}
            />
          </CardContent>
        </Card>
      </div>

      {/* Application Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>{intl.formatMessage({ id: 'developer.analytics.charts.applicationBreakdown' })}</span>
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'developer.analytics.charts.applicationBreakdownDesc' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BarChart
            labels={topApplications.map(item => item?.application_name || intl.formatMessage({ id: 'developer.analytics.common.unknown' }))}
            datasets={[
              {
                label: intl.formatMessage({ id: 'developer.analytics.charts.users' }),
                data: topApplications.map(item => item?.user_count || 0),
                backgroundColor: 'rgba(80, 200, 120, 0.8)',
                borderColor: '#50C878',
              },
              {
                label: intl.formatMessage({ id: 'developer.analytics.charts.logins' }),
                data: topApplications.map(item => item?.login_count || 0),
                backgroundColor: 'rgba(79, 70, 229, 0.8)',
                borderColor: '#4F46E5',
              },
            ]}
            height={300}
          />
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'developer.analytics.quickActions.title' })}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Users className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.analytics.quickActions.viewAllUsers' })}
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Activity className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.analytics.quickActions.activityLogs' })}
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <BarChart3 className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.analytics.quickActions.detailedReports' })}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'developer.analytics.topPerforming.title' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {topApplications.slice(0, 3).map((application, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm truncate">{application?.application_name || intl.formatMessage({ id: 'developer.analytics.common.unknown' })}</span>
                  <Badge variant="secondary">{application?.user_count || 0} {intl.formatMessage({ id: 'developer.analytics.topPerforming.users' })}</Badge>
                </div>
              ))}
              {topApplications.length === 0 && (
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.analytics.topPerforming.noData' })}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'developer.analytics.recentActivity.title' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recentActivity.slice(-3).map((activity, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm">{activity?.date ? new Date(activity.date).toLocaleDateString() : intl.formatMessage({ id: 'developer.analytics.common.unknown' })}</span>
                  <div className="text-right">
                    <div className="text-sm font-medium">{activity?.logins || 0} {intl.formatMessage({ id: 'developer.analytics.recentActivity.logins' })}</div>
                    <div className="text-xs text-muted-foreground">{activity?.new_users || 0} {intl.formatMessage({ id: 'developer.analytics.recentActivity.new' })}</div>
                  </div>
                </div>
              ))}
              {recentActivity.length === 0 && (
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.analytics.recentActivity.noData' })}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DeveloperAnalyticsDashboard;
