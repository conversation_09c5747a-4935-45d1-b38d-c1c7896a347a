import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

// UI Components
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';

// Icons
import { ArrowLeft, Building2, Globe, Users, Settings } from 'lucide-react';

// Services
import { organizationApi, OrganizationCreate } from '@/services/organization-api';

interface OrganizationCreateFormData {
  name: string;
  slug: string;
  description: string;
  website: string;
  isPublic: boolean;
  maxMembers: number;
}

interface OrganizationCreateFormErrors {
  name?: string;
  slug?: string;
  description?: string;
  website?: string;
  maxMembers?: string;
}

export const OrganizationCreatePage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<OrganizationCreateFormData>({
    name: '',
    slug: '',
    description: '',
    website: '',
    isPublic: false,
    maxMembers: 50,
  });
  
  const [errors, setErrors] = useState<OrganizationCreateFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
    }));
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: undefined }));
    }
  };

  const handleSlugChange = (slug: string) => {
    // Only allow lowercase letters, numbers, and hyphens
    const cleanSlug = slug.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setFormData(prev => ({ ...prev, slug: cleanSlug }));
    if (errors.slug) {
      setErrors(prev => ({ ...prev, slug: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: OrganizationCreateFormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = intl.formatMessage({ id: 'developer.organizations.create.errors.nameRequired' });
    }

    if (!formData.slug.trim()) {
      newErrors.slug = intl.formatMessage({ id: 'developer.organizations.create.errors.slugRequired' });
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = intl.formatMessage({ id: 'developer.organizations.create.errors.slugInvalid' });
    } else if (formData.slug.startsWith('-') || formData.slug.endsWith('-') || formData.slug.includes('--')) {
      newErrors.slug = intl.formatMessage({ id: 'developer.organizations.create.errors.slugInvalid' });
    }

    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = intl.formatMessage({ id: 'developer.organizations.create.errors.websiteInvalid' });
    }

    if (formData.maxMembers < 1 || formData.maxMembers > 1000) {
      newErrors.maxMembers = intl.formatMessage({ id: 'developer.organizations.create.errors.maxMembersInvalid' });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const createData: OrganizationCreate = {
        name: formData.name.trim(),
        slug: formData.slug.trim(),
        description: formData.description.trim() || undefined,
        website: formData.website.trim() || undefined,
        is_public: formData.isPublic,
        max_members: formData.maxMembers,
      };

      const organization = await organizationApi.createOrganization(createData);

      toast({
        title: intl.formatMessage({ id: 'developer.organizations.create.success' }),
      });
      navigate(`/developer/organizations/${organization.id}`);
    } catch (error: any) {
      console.error('Failed to create organization:', error);
      
      if (error.response?.data?.detail?.includes('slug')) {
        setErrors({ slug: intl.formatMessage({ id: 'developer.organizations.create.errors.slugTaken' }) });
      } else {
        toast({
          title: intl.formatMessage({ id: 'developer.organizations.create.errors.createFailed' }),
          variant: 'destructive',
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/developer/organizations');
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'developer.organizations.create.title' })}</h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'developer.organizations.create.subtitle' })}
        </p>
      </div>

      {/* Organization Form */}
      <div className="w-full">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
            <h2 className="text-lg font-semibold mb-4">{intl.formatMessage({ id: 'developer.organizations.create.form.basicInfo' })}</h2>

            <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2">
              {/* Organization Name */}
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.name.label' })} *
                </label>
                <input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  className={`w-full rounded-md border px-3 py-2 text-sm ${
                    errors.name ? 'border-red-500' : 'border-input'
                  } bg-background`}
                  placeholder={intl.formatMessage({ id: 'developer.organizations.create.form.name.placeholder' })}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.name.help' })}
                </p>
              </div>

              {/* Organization Slug */}
              <div className="space-y-2">
                <label htmlFor="slug" className="text-sm font-medium">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.slug.label' })} *
                </label>
                <input
                  id="slug"
                  type="text"
                  value={formData.slug}
                  onChange={(e) => handleSlugChange(e.target.value)}
                  className={`w-full rounded-md border px-3 py-2 text-sm ${
                    errors.slug ? 'border-red-500' : 'border-input'
                  } bg-background`}
                  placeholder={intl.formatMessage({ id: 'developer.organizations.create.form.slug.placeholder' })}
                  disabled={isSubmitting}
                />
                {errors.slug && (
                  <p className="text-sm text-red-500">{errors.slug}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.slug.help' })}
                </p>
              </div>
            </div>
          </div>

          {/* Additional Details */}
          <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
            <h2 className="text-lg font-semibold mb-4">{intl.formatMessage({ id: 'developer.organizations.create.form.additionalDetails' })}</h2>

            <div className="space-y-4">
              {/* Description */}
              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.description.label' })}
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full rounded-md border px-3 py-2 text-sm border-input bg-background"
                  placeholder={intl.formatMessage({ id: 'developer.organizations.create.form.description.placeholder' })}
                  rows={3}
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.description.help' })}
                </p>
              </div>

              {/* Website */}
              <div className="space-y-2">
                <label htmlFor="website" className="text-sm font-medium">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.website.label' })}
                </label>
                <input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                  className={`w-full rounded-md border px-3 py-2 text-sm ${
                    errors.website ? 'border-red-500' : 'border-input'
                  } bg-background`}
                  placeholder={intl.formatMessage({ id: 'developer.organizations.create.form.website.placeholder' })}
                  disabled={isSubmitting}
                />
                {errors.website && (
                  <p className="text-sm text-red-500">{errors.website}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.website.help' })}
                </p>
              </div>
            </div>
          </div>

          {/* Organization Settings */}
          <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
            <h2 className="text-lg font-semibold mb-6">{intl.formatMessage({ id: 'developer.organizations.create.form.settings' })}</h2>

            <div className="space-y-6">
              {/* Public Organization */}
              <div className="flex items-center justify-between py-2">
                <div className="space-y-1">
                  <label className="text-sm font-medium">
                    {intl.formatMessage({ id: 'developer.organizations.create.form.isPublic.label' })}
                  </label>
                  <p className="text-xs text-muted-foreground">
                    {intl.formatMessage({ id: 'developer.organizations.create.form.isPublic.help' })}
                  </p>
                </div>
                <Switch
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPublic: checked }))}
                  disabled={isSubmitting}
                />
              </div>

              {/* Divider */}
              <div className="border-t border-border"></div>

              {/* Max Members */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.maxMembers.label' })}
                </label>
                <Select
                  value={formData.maxMembers.toString()}
                  disabled={true}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="50 members (fixed)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="50">50 members</SelectItem>
                  </SelectContent>
                </Select>
                {errors.maxMembers && (
                  <p className="text-sm text-red-500">{errors.maxMembers}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.organizations.create.form.maxMembers.help' })}
                </p>
              </div>
            </div>
          </div>

          {/* Submit Actions */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="rounded-md border border-input px-6 py-2 text-sm font-medium hover:bg-accent transition-colors"
            >
              {intl.formatMessage({ id: 'developer.organizations.create.actions.cancel' })}
            </button>

            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center gap-2 rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 disabled:opacity-50 transition-colors"
            >
              {isSubmitting ? (
                <>
                  <Settings className="h-4 w-4 animate-spin" />
                  {intl.formatMessage({ id: 'developer.organizations.create.actions.creating' })}
                </>
              ) : (
                <>
                  <Building2 className="h-4 w-4" />
                  {intl.formatMessage({ id: 'developer.organizations.create.actions.create' })}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
