"use client"

import * as React from "react"
import { useIntl } from 'react-intl'
import { MoreVertical, Eye, Edit, RefreshCw, Power, PowerOff, Trash2, BarChart3 } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/shadcn/card'
import { Badge } from '@/components/ui/shadcn/badge'
import { Button } from '@/components/ui/shadcn/button'
import { Input } from '@/components/ui/shadcn/input'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/shadcn/dropdown-menu'
import { CopyButton } from '@/components/ui/animate-ui/button/copy'
import { useToast } from '@/hooks/use-toast'

interface ApplicationCardProps {
  application: {
    id: string
    application_name: string
    client_id: string
    is_active: boolean
    created_at: string
    stats?: {
      total_users: number
      total_logins: number
    }
  }
  onViewDetails: (application: any) => void
  onAnalytics: (application: any) => void
  formatDate: (date: string) => string
}

export function ApplicationCard({ application, onViewDetails, onAnalytics, formatDate }: ApplicationCardProps) {
  const intl = useIntl()
  const { toast } = useToast()

  return (
    <Card className="bg-card border border-border shadow-none">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1">
            <div className={`w-3 h-3 rounded-full ${application.is_active ? 'bg-green-500' : 'bg-muted-foreground/40'}`} />
            <h3 className="font-medium text-base text-foreground/90 truncate">{application.application_name}</h3>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onViewDetails(application)}>
                <Eye className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'developer.applications.actions.viewDetails' })}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'developer.applications.edit' })}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <RefreshCw className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'developer.applications.actions.regenerateSecret' })}
              </DropdownMenuItem>
              <DropdownMenuItem>
                {application.is_active ? (
                  <>
                    <PowerOff className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'developer.applications.disable' })}
                  </>
                ) : (
                  <>
                    <Power className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'developer.applications.enable' })}
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600 dark:text-red-400">
                <Trash2 className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'developer.applications.delete' })}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div className="bg-muted/30 p-2 rounded text-center">
            <div className="font-bold text-blue-600 dark:text-blue-400">{application.stats?.total_users || 0}</div>
            <div className="text-muted-foreground text-xs">{intl.formatMessage({ id: 'developer.applications.users' })}</div>
          </div>
          <div className="bg-muted/30 p-2 rounded text-center">
            <div className="font-bold text-green-600 dark:text-green-400">{application.stats?.total_logins || 0}</div>
            <div className="text-muted-foreground text-xs">{intl.formatMessage({ id: 'developer.applications.logins' })}</div>
          </div>
        </div>

        <div className="flex items-center space-x-2 mb-3">
          <Input
            readOnly
            value={application.client_id}
            className="font-mono text-xs bg-muted/30 border-muted h-10 flex-1"
          />
          <CopyButton
            size="sm"
            variant="outline"
            content={application.client_id}
            className="h-10 w-10 p-0"
            onCopy={() => {
              toast({
                title: intl.formatMessage({ id: 'developer.applications.toast.copied' }),
                description: intl.formatMessage({ id: 'developer.applications.toast.copiedDesc' }),
              });
            }}
          />
        </div>
        <div className="flex space-x-2">
          <Button size="sm" variant="outline" className="flex-1 h-10 text-xs" onClick={() => onViewDetails(application)}>
            {intl.formatMessage({ id: 'developer.applications.details' })}
          </Button>
          <Button size="sm" variant="outline" className="flex-1 h-10 text-xs" onClick={() => onAnalytics(application)}>
            {intl.formatMessage({ id: 'developer.applications.analytics' })}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
