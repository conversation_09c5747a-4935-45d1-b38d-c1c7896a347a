/**
 * Role Transition Application Form Component
 * 
 * Form for submitting developer role applications
 * Follows the existing design patterns and supports dark/light mode
 */

import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  UserCheck, 
  Code, 
  Send, 
  AlertTriangle,
  Info,
  ArrowLeft
} from 'lucide-react';
import { 
  roleTransitionApi, 
  validateApplicationForm,
  type ApplicationSubmissionRequest 
} from '@/services/role-transition-api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';

interface RoleTransitionFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const RoleTransitionForm: React.FC<RoleTransitionFormProps> = ({ onSuccess, onCancel }) => {
  const intl = useIntl();
  const { toast } = useToast();
  const { user } = useAuth();
  
  const [formData, setFormData] = useState<ApplicationSubmissionRequest>({
    application_reason: '',
    technical_background: '',
    intended_use: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const handleInputChange = (field: keyof ApplicationSubmissionRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = validateApplicationForm(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      await roleTransitionApi.submitApplication(formData);
      
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.submitted' }),
        variant: 'default',
      });
      
      onSuccess?.();
    } catch (err: any) {
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.submit.error' }),
        description: err.message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-sm">
      <CardHeader>
        <div className="flex items-center gap-3">
          <Code className="h-6 w-6 text-primary" />
          <div>
            <CardTitle>{intl.formatMessage({ id: 'user.role.transition.application.form.title' })}</CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.role.transition.application.form.subtitle' })}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Current User Info */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              {intl.formatMessage({ id: 'user.role.transition.applicant.info' })}
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.applicant.name' })}:
                </span>
                <span className="ml-2 font-medium">
                  {user?.first_name} {user?.last_name}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.applicant.email' })}:
                </span>
                <span className="ml-2 font-medium">{user?.email}</span>
              </div>
              <div>
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.current.role' })}:
                </span>
                <Badge variant="secondary" className="ml-2">
                  {user?.role || 'user'}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.target.role' })}:
                </span>
                <Badge variant="default" className="ml-2">
                  developer
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Application Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason" className="text-base font-semibold">
              {intl.formatMessage({ id: 'user.role.transition.form.reason.label' })} *
            </Label>
            <Textarea
              id="reason"
              placeholder={intl.formatMessage({ id: 'user.role.transition.form.reason.placeholder' })}
              value={formData.application_reason}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('application_reason', e.target.value)}
              className="min-h-[120px] resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{intl.formatMessage({ id: 'user.role.transition.form.reason.help' })}</span>
              <span>{formData.application_reason.length}/1000</span>
            </div>
          </div>

          {/* Technical Background */}
          <div className="space-y-2">
            <Label htmlFor="technical" className="text-base font-semibold">
              {intl.formatMessage({ id: 'user.role.transition.form.technical.label' })}
            </Label>
            <Textarea
              id="technical"
              placeholder={intl.formatMessage({ id: 'user.role.transition.form.technical.placeholder' })}
              value={formData.technical_background}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('technical_background', e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{intl.formatMessage({ id: 'user.role.transition.form.technical.help' })}</span>
              <span>{formData.technical_background?.length || 0}/1000</span>
            </div>
          </div>

          {/* Intended Use */}
          <div className="space-y-2">
            <Label htmlFor="intended" className="text-base font-semibold">
              {intl.formatMessage({ id: 'user.role.transition.form.intended.label' })}
            </Label>
            <Textarea
              id="intended"
              placeholder={intl.formatMessage({ id: 'user.role.transition.form.intended.placeholder' })}
              value={formData.intended_use}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('intended_use', e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{intl.formatMessage({ id: 'user.role.transition.form.intended.help' })}</span>
              <span>{formData.intended_use?.length || 0}/1000</span>
            </div>
          </div>

          {/* Info Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              {intl.formatMessage({ id: 'user.role.transition.description' })}
            </AlertDescription>
          </Alert>

          {/* Validation Errors */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>
                      {intl.formatMessage({ id: error })}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button 
                type="button"
                variant="outline" 
                onClick={onCancel}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'user.role.transition.form.cancel' })}
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading || formData.application_reason.trim().length < 50}
              className="flex-1"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {intl.formatMessage({ id: 'common.loading' })}
                </div>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  {intl.formatMessage({ id: 'user.role.transition.form.submit' })}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
