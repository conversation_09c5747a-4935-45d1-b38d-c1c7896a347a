import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SessionInfo {
  id: string;
  device_fingerprint: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  last_activity: string;
  expires_at: string;
  is_active: boolean;
  is_current: boolean;
  location?: string;
  device_type: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  browser: string;
  os: string;
  connected_apps: Array<{
    id: string;
    name: string;
    last_used: string;
  }>;
}

interface ActiveSessionCardProps {
  session: SessionInfo;
  onTerminate?: (sessionId: string) => void;
  onExtend?: (sessionId: string) => void;
  isTerminating?: boolean;
  isExtending?: boolean;
  compact?: boolean;
}

/**
 * ActiveSessionCard Component
 * 
 * Displays detailed information about an active user session including:
 * - Device information with appropriate icons
 * - Location and IP address
 * - Session timing and activity status
 * - Connected applications
 * - Session management actions (terminate, extend)
 * - Current session highlighting
 */
const ActiveSessionCard: React.FC<ActiveSessionCardProps> = ({
  session,
  onTerminate,
  onExtend,
  isTerminating = false,
  isExtending = false,
  compact = false,
}) => {
  const intl = useIntl();

  // Get device icon based on device type
  const getDeviceIcon = (deviceType: string) => {
    const iconClass = compact ? "h-3 w-3" : "h-4 w-4";
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className={iconClass} />;
      case 'tablet':
        return <Tablet className={iconClass} />;
      case 'desktop':
        return <Monitor className={iconClass} />;
      default:
        return <Globe className={iconClass} />;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Calculate time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return intl.formatMessage({ id: 'user.sessions.time.now', defaultMessage: 'Just now' });
    if (diffMins < 60) return intl.formatMessage({ id: 'user.sessions.time.minutes', defaultMessage: '{minutes}m ago' }, { minutes: diffMins });
    if (diffHours < 24) return intl.formatMessage({ id: 'user.sessions.time.hours', defaultMessage: '{hours}h ago' }, { hours: diffHours });
    return intl.formatMessage({ id: 'user.sessions.time.days', defaultMessage: '{days}d ago' }, { days: diffDays });
  };

  // Check if session is expiring soon (within 24 hours)
  const isExpiringSoon = () => {
    const now = new Date();
    const expiresAt = new Date(session.expires_at);
    const diffHours = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);
    return diffHours < 24;
  };

  // Get session status color
  const getStatusColor = () => {
    if (!session.is_active) return 'text-red-500';
    if (isExpiringSoon()) return 'text-amber-500';
    return 'text-green-500';
  };

  // Get connection status icon
  const getConnectionIcon = () => {
    const iconClass = "h-3 w-3";
    if (!session.is_active) return <WifiOff className={cn(iconClass, "text-red-500")} />;
    if (isExpiringSoon()) return <Wifi className={cn(iconClass, "text-amber-500")} />;
    return <Wifi className={cn(iconClass, "text-green-500")} />;
  };

  if (compact) {
    return (
      <Card className={cn(
        "transition-colors",
        session.is_current && "border-primary/50 bg-primary/5"
      )}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getDeviceIcon(session.device_type)}
              <div className="flex flex-col">
                <span className="text-sm font-medium">
                  {session.browser} on {session.os}
                </span>
                <span className="text-xs text-muted-foreground">
                  {session.location || session.ip_address} • {getTimeAgo(session.last_activity)}
                </span>
              </div>
              {session.is_current && (
                <Badge variant="default" className="text-xs">
                  {intl.formatMessage({ id: 'user.sessions.current', defaultMessage: 'Current' })}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              {getConnectionIcon()}
              {!session.is_current && onTerminate && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onTerminate(session.id)}
                  disabled={isTerminating}
                  className="h-6 w-6 p-0"
                >
                  {isTerminating ? (
                    <RefreshCw className="h-3 w-3 animate-spin" />
                  ) : (
                    <LogOut className="h-3 w-3" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "transition-colors",
      session.is_current && "border-primary/50 bg-primary/5"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {getDeviceIcon(session.device_type)}
            <span>{session.browser} on {session.os}</span>
            {session.is_current && (
              <Badge variant="default" className="ml-2">
                {intl.formatMessage({ id: 'user.sessions.current', defaultMessage: 'Current' })}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={session.is_active ? "default" : "secondary"} className={getStatusColor()}>
              {session.is_active ? (
                <CheckCircle className="h-3 w-3 mr-1" />
              ) : (
                <XCircle className="h-3 w-3 mr-1" />
              )}
              {session.is_active 
                ? intl.formatMessage({ id: 'user.sessions.active', defaultMessage: 'Active' })
                : intl.formatMessage({ id: 'user.sessions.inactive', defaultMessage: 'Inactive' })
              }
            </Badge>
            {isExpiringSoon() && session.is_active && (
              <Badge variant="outline" className="text-amber-600 border-amber-300">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {intl.formatMessage({ id: 'user.sessions.expiring', defaultMessage: 'Expiring Soon' })}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Session Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.sessions.location', defaultMessage: 'Location:' })}
            </span>
            <span>{session.location || session.ip_address}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.sessions.lastActivity', defaultMessage: 'Last Activity:' })}
            </span>
            <span>{getTimeAgo(session.last_activity)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.sessions.created', defaultMessage: 'Created:' })}
            </span>
            <span>{formatDate(session.created_at)}</span>
          </div>
          <div className="flex items-center gap-2">
            <AlertTriangle className={cn("h-4 w-4", isExpiringSoon() ? "text-amber-500" : "text-muted-foreground")} />
            <span className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.sessions.expires', defaultMessage: 'Expires:' })}
            </span>
            <span className={isExpiringSoon() ? "text-amber-600 font-medium" : ""}>
              {formatDate(session.expires_at)}
            </span>
          </div>
        </div>

        {/* Connected Applications */}
        {session.connected_apps.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">
              {intl.formatMessage({ 
                id: 'user.sessions.connectedApps', 
                defaultMessage: 'Connected Applications' 
              })}
            </h4>
            <div className="flex flex-wrap gap-2">
              {session.connected_apps.map((app) => (
                <Badge key={app.id} variant="outline" className="text-xs">
                  {app.name}
                  <span className="ml-1 text-muted-foreground">
                    ({getTimeAgo(app.last_used)})
                  </span>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {!session.is_current && (
          <div className="flex gap-2 pt-2">
            {onExtend && session.is_active && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExtend(session.id)}
                disabled={isExtending}
              >
                {isExtending ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                ) : (
                  <Clock className="h-3 w-3 mr-2" />
                )}
                {intl.formatMessage({ id: 'user.sessions.extend', defaultMessage: 'Extend' })}
              </Button>
            )}
            {onTerminate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTerminate(session.id)}
                disabled={isTerminating}
              >
                {isTerminating ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                ) : (
                  <LogOut className="h-3 w-3 mr-2" />
                )}
                {intl.formatMessage({ id: 'user.sessions.terminate', defaultMessage: 'Terminate' })}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveSessionCard;
