/**
 * Role Transition Applications List Component
 * 
 * Displays user's role transition applications with status and actions
 * Follows the existing design patterns and supports dark/light mode
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/shadcn/table';
import { 
  FileText, 
  Plus, 
  Eye, 
  Trash2, 
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  RotateCcw
} from 'lucide-react';
import { 
  roleTransitionApi, 
  getStatusColor,
  formatApplicationDate,
  type DeveloperApplication 
} from '@/services/role-transition-api';
import { useToast } from '@/hooks/use-toast';

interface RoleTransitionListProps {
  onCreateNew?: () => void;
  onViewDetails?: (application: DeveloperApplication) => void;
}

export const RoleTransitionList: React.FC<RoleTransitionListProps> = ({ 
  onCreateNew, 
  onViewDetails 
}) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [applications, setApplications] = useState<DeveloperApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [applicationToWithdraw, setApplicationToWithdraw] = useState<DeveloperApplication | null>(null);
  const [isWithdrawing, setIsWithdrawing] = useState(false);

  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setIsLoading(true);
      const applicationList = await roleTransitionApi.getMyApplications();
      setApplications(applicationList);
    } catch (error) {
      console.error('Failed to load applications:', error);
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.error.network' }),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleWithdrawApplication = async () => {
    if (!applicationToWithdraw) return;

    setIsWithdrawing(true);
    try {
      await roleTransitionApi.withdrawApplication(applicationToWithdraw.id);
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.withdrawn' }),
        variant: 'default',
      });
      setApplicationToWithdraw(null);
      loadApplications();
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.withdraw.error' }),
        variant: 'destructive',
      });
    } finally {
      setIsWithdrawing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'withdrawn':
        return <RotateCcw className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const canWithdraw = (application: DeveloperApplication) => {
    return application.status === 'pending';
  };

  const hasPendingApplication = applications.some(app => app.status === 'pending');

  return (
    <div className="space-y-6">
      <Card className="shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-primary" />
              <div>
                <CardTitle>
                  {intl.formatMessage({ id: 'user.role.transition.my.applications.title' })}
                </CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.role.transition.my.applications.subtitle' })}
                </CardDescription>
              </div>
            </div>
            <Button 
              onClick={onCreateNew}
              disabled={hasPendingApplication}
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              {intl.formatMessage({ id: 'user.role.transition.my.applications.create' })}
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {hasPendingApplication && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {intl.formatMessage({ id: 'user.role.transition.message.pending.exists' })}
              </AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : applications.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-4">
                {intl.formatMessage({ id: 'user.role.transition.my.applications.empty' })}
              </p>
              <Button onClick={onCreateNew}>
                <Plus className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'user.role.transition.my.applications.create' })}
              </Button>
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      {intl.formatMessage({ id: 'user.role.transition.application.id' })}
                    </TableHead>
                    <TableHead>
                      {intl.formatMessage({ id: 'user.role.transition.application.status' })}
                    </TableHead>
                    <TableHead>
                      {intl.formatMessage({ id: 'user.role.transition.application.submitted' })}
                    </TableHead>
                    <TableHead>
                      {intl.formatMessage({ id: 'user.role.transition.application.reviewed' })}
                    </TableHead>
                    <TableHead className="w-[150px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell className="font-mono text-sm">
                        {application.id.slice(0, 8)}...
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant="secondary" 
                          className={`${getStatusColor(application.status)} border-0`}
                        >
                          <div className="flex items-center gap-1">
                            {getStatusIcon(application.status)}
                            {intl.formatMessage({ 
                              id: `user.role.transition.status.${application.status}` 
                            })}
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatApplicationDate(application.created_at)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {application.reviewed_at 
                          ? formatApplicationDate(application.reviewed_at)
                          : '-'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDetails?.(application)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {canWithdraw(application) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setApplicationToWithdraw(application)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdraw Application Dialog */}
      <Dialog open={!!applicationToWithdraw} onOpenChange={() => setApplicationToWithdraw(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {intl.formatMessage({ id: 'user.role.transition.confirm.withdraw.title' })}
            </DialogTitle>
            <DialogDescription>
              {intl.formatMessage({ id: 'user.role.transition.confirm.withdraw.message' })}
            </DialogDescription>
          </DialogHeader>
          
          {applicationToWithdraw && (
            <div className="py-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <FileText className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {intl.formatMessage({ id: 'user.role.transition.application.id' })}: {applicationToWithdraw.id.slice(0, 8)}...
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'user.role.transition.application.submitted' })}: {formatApplicationDate(applicationToWithdraw.created_at)}
                  </p>
                </div>
              </div>
              
              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {intl.formatMessage({ id: 'user.role.transition.confirm.withdraw.warning' })}
                </AlertDescription>
              </Alert>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setApplicationToWithdraw(null)}>
              {intl.formatMessage({ id: 'common.cancel' })}
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleWithdrawApplication}
              disabled={isWithdrawing}
            >
              {isWithdrawing ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {intl.formatMessage({ id: 'common.loading' })}
                </div>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  {intl.formatMessage({ id: 'user.role.transition.application.withdraw' })}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
