/**
 * Invitation Dashboard Widget Component
 * 
 * Shows pending invitations summary on user dashboard
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Mail, Users, ArrowRight, Clock } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { getUserInvitations } from '@/services/user-api';
import useAuth from '@/hooks/use-auth';
import { useInvitationContext } from '@/contexts/invitation-context';

interface OrganizationInvitation {
  id: string;
  organization_name: string;
  role: string;
  invited_at: string;
  expires_at: string;
}

/**
 * Invitation Dashboard Widget
 * 
 * Displays a summary of pending organization invitations on the user dashboard.
 * Shows invitation count and quick access to manage invitations.
 */
export const InvitationDashboardWidget: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { refreshTrigger } = useInvitationContext();

  const [invitations, setInvitations] = useState<OrganizationInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadInvitations = async () => {
    if (!isAuthenticated || !user || user.role === 'admin') {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await getUserInvitations({ 
        status_filter: 'pending',
        limit: 5,
        offset: 0 
      });
      setInvitations(response.invitations || []);
    } catch (err: any) {
      console.error('Failed to load invitations:', err);
      setError('Failed to load invitations');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, [isAuthenticated, user, refreshTrigger]); // Add refreshTrigger to dependencies

  // Don't show widget for admin users or if no invitations
  if (!isAuthenticated || !user || user.role === 'admin') {
    return null;
  }

  const handleViewAllInvitations = () => {
    navigate('/user/invitations');
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const isExpired = (expiresAt: string) => {
    try {
      return new Date(expiresAt) < new Date();
    } catch {
      return false;
    }
  };

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {intl.formatMessage({ id: 'user.dashboard.invitations.title' })}
          </CardTitle>
          <Mail className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            {intl.formatMessage({ id: 'user.dashboard.invitations.loading' })}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {intl.formatMessage({ id: 'user.dashboard.invitations.title' })}
          </CardTitle>
          <Mail className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-sm text-red-600">
            {intl.formatMessage({ id: 'user.dashboard.invitations.error' })}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (invitations.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {intl.formatMessage({ id: 'user.dashboard.invitations.title' })}
          </CardTitle>
          <Mail className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">0</div>
          <p className="text-xs text-muted-foreground">
            {intl.formatMessage({ id: 'user.dashboard.invitations.noPending' })}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {intl.formatMessage({ id: 'user.dashboard.invitations.title' })}
        </CardTitle>
        <Mail className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{invitations.length}</div>
        <p className="text-xs text-muted-foreground">
          {intl.formatMessage({
            id: invitations.length === 1
              ? 'user.dashboard.invitations.pendingCount.one'
              : 'user.dashboard.invitations.pendingCount.other'
          })}
        </p>
        
        {/* Show first few invitations */}
        <div className="mt-4 space-y-2">
          {invitations.slice(0, 3).map((invitation) => (
            <div key={invitation.id} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Users className="h-3 w-3 text-muted-foreground" />
                <span className="font-medium">{invitation.organization_name}</span>
                <Badge variant="outline" className="text-xs">
                  {invitation.role}
                </Badge>
              </div>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>
                  {isExpired(invitation.expires_at)
                    ? intl.formatMessage({ id: 'user.dashboard.invitations.expired' })
                    : formatDate(invitation.expires_at)
                  }
                </span>
              </div>
            </div>
          ))}
          
          {invitations.length > 3 && (
            <div className="text-xs text-muted-foreground">
              +{invitations.length - 3} more invitations
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          className="w-full mt-4"
          onClick={handleViewAllInvitations}
        >
          {intl.formatMessage({ id: 'user.dashboard.invitations.manageButton' })}
          <ArrowRight className="h-3 w-3 ml-1" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default InvitationDashboardWidget;
