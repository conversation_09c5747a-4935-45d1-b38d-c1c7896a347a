import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { 
  Shield, 
  AlertTriangle,
  Clock,
  Settings,
  ExternalLink,
  RefreshCw,
  ChevronRight,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConsentSummary {
  total_consents: number;
  active_consents: number;
  expiring_soon: number;
  recent_activity: Array<{
    application_name: string;
    action: string;
    timestamp: string;
  }>;
}

interface ConsentManagementWidgetProps {
  className?: string;
  showRecentActivity?: boolean;
  maxRecentItems?: number;
}

/**
 * ConsentManagementWidget Component
 * 
 * A dashboard widget that provides quick access to consent management:
 * - Summary of active consents and permissions
 * - Alerts for expiring consents
 * - Recent consent activity
 * - Quick actions for consent management
 * - Navigation to detailed consent pages
 */
const ConsentManagementWidget: React.FC<ConsentManagementWidgetProps> = ({
  className,
  showRecentActivity = true,
  maxRecentItems = 3,
}) => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [summary, setSummary] = useState<ConsentSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  // Fetch consent summary
  const fetchConsentSummary = async () => {
    try {
      setIsLoading(true);
      
      // Fetch active consents
      const consentsResponse = await fetch('/api/v1/consent/my-consents?active_only=true', {
        credentials: 'include',
      });
      
      let consentData = [];
      if (consentsResponse.ok) {
        consentData = await consentsResponse.json();
      }

      // Fetch recent audit log
      const auditResponse = await fetch(`/api/v1/consent/audit-log?limit=${maxRecentItems}`, {
        credentials: 'include',
      });
      
      let auditData = [];
      if (auditResponse.ok) {
        auditData = await auditResponse.json();
      }

      // Calculate summary
      const now = new Date();
      const expiringSoon = consentData.filter((consent: any) => {
        if (!consent.expires_at) return false;
        const expiresAt = new Date(consent.expires_at);
        const diffDays = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
        return diffDays < 30 && diffDays > 0;
      }).length;

      setSummary({
        total_consents: consentData.length,
        active_consents: consentData.filter((c: any) => c.is_valid).length,
        expiring_soon: expiringSoon,
        recent_activity: auditData.slice(0, maxRecentItems),
      });
    } catch (error) {
      console.error('Failed to fetch consent summary:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get action display
  const getActionDisplay = (action: string) => {
    switch (action) {
      case 'granted':
        return {
          color: 'text-green-600',
          label: intl.formatMessage({ id: 'user.consent.action.granted', defaultMessage: 'Granted' }),
        };
      case 'revoked':
        return {
          color: 'text-red-600',
          label: intl.formatMessage({ id: 'user.consent.action.revoked', defaultMessage: 'Revoked' }),
        };
      case 'expired':
        return {
          color: 'text-amber-600',
          label: intl.formatMessage({ id: 'user.consent.action.expired', defaultMessage: 'Expired' }),
        };
      case 'updated':
        return {
          color: 'text-blue-600',
          label: intl.formatMessage({ id: 'user.consent.action.updated', defaultMessage: 'Updated' }),
        };
      default:
        return {
          color: 'text-gray-600',
          label: action,
        };
    }
  };

  // Format time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return intl.formatMessage({ id: 'time.now', defaultMessage: 'Just now' });
    if (diffHours < 24) return intl.formatMessage({ id: 'time.hours', defaultMessage: '{hours}h ago' }, { hours: diffHours });
    return intl.formatMessage({ id: 'time.days', defaultMessage: '{days}d ago' }, { days: diffDays });
  };

  // Navigation handlers
  const handleViewAllConsents = () => {
    navigate('/user/consent/history');
  };

  const handleManageConsents = () => {
    navigate('/user/consent/history');
  };

  const handleViewSessions = () => {
    navigate('/user/sessions');
  };

  useEffect(() => {
    fetchConsentSummary();
  }, []);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (!summary) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {intl.formatMessage({ 
                id: 'user.consent.widget.error', 
                defaultMessage: 'Unable to load consent information' 
              })}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("transition-all duration-200", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Shield className="h-5 w-5 text-primary" />
            {intl.formatMessage({ 
              id: 'user.consent.widget.title', 
              defaultMessage: 'App Permissions' 
            })}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{summary.active_consents}</div>
            <div className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.consent.widget.active', defaultMessage: 'Active' })}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">{summary.total_consents}</div>
            <div className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.consent.widget.total', defaultMessage: 'Total' })}
            </div>
          </div>
          <div className="text-center">
            <div className={cn(
              "text-2xl font-bold",
              summary.expiring_soon > 0 ? "text-amber-600" : "text-muted-foreground"
            )}>
              {summary.expiring_soon}
            </div>
            <div className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.consent.widget.expiring', defaultMessage: 'Expiring' })}
            </div>
          </div>
        </div>

        {/* Expiring Soon Alert */}
        {summary.expiring_soon > 0 && (
          <div className="flex items-center gap-2 p-3 rounded-lg bg-amber-50 border border-amber-200 dark:bg-amber-950 dark:border-amber-800">
            <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            <div className="flex-1 text-sm">
              <span className="font-medium text-amber-800 dark:text-amber-200">
                {intl.formatMessage(
                  { 
                    id: 'user.consent.widget.expiring.message',
                    defaultMessage: '{count} permissions expiring soon'
                  },
                  { count: summary.expiring_soon }
                )}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleManageConsents}
              className="text-amber-700 hover:text-amber-800 dark:text-amber-300"
            >
              <Settings className="h-3 w-3 mr-1" />
              {intl.formatMessage({ id: 'user.consent.widget.manage', defaultMessage: 'Manage' })}
            </Button>
          </div>
        )}

        {/* Recent Activity */}
        {showRecentActivity && isExpanded && summary.recent_activity.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">
              {intl.formatMessage({ 
                id: 'user.consent.widget.recent', 
                defaultMessage: 'Recent Activity' 
              })}
            </h4>
            <div className="space-y-2">
              {summary.recent_activity.map((activity, index) => {
                const actionDisplay = getActionDisplay(activity.action);
                return (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-primary" />
                      <span className="font-medium">{activity.application_name}</span>
                      <span className={cn("text-xs", actionDisplay.color)}>
                        {actionDisplay.label}
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {getTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewAllConsents}
            className="flex-1"
          >
            <Shield className="h-3 w-3 mr-2" />
            {intl.formatMessage({ id: 'user.consent.widget.viewAll', defaultMessage: 'View All' })}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewSessions}
            className="flex-1"
          >
            <Clock className="h-3 w-3 mr-2" />
            {intl.formatMessage({ id: 'user.consent.widget.sessions', defaultMessage: 'Sessions' })}
          </Button>
        </div>

        {/* Manage Link */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManageConsents}
          className="w-full justify-between text-muted-foreground hover:text-foreground"
        >
          <span>
            {intl.formatMessage({ 
              id: 'user.consent.widget.manageAll', 
              defaultMessage: 'Manage all permissions' 
            })}
          </span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default ConsentManagementWidget;
