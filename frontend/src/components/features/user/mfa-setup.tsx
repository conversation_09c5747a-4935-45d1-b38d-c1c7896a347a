/**
 * MFA Setup Component
 * 
 * Multi-step wizard for setting up Multi-Factor Authentication
 * Follows the existing design patterns and supports dark/light mode
 */

import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  CheckCircle, 
  Copy, 
  Download, 
  QrCode, 
  Shield, 
  Smartphone,
  AlertTriangle,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { mfaApi, downloadBackupCodes, copyBackupCodes } from '@/services/mfa-api';
import { useToast } from '@/hooks/use-toast';

interface MFASetupProps {
  onComplete?: () => void;
  onCancel?: () => void;
}

type SetupStep = 'device-name' | 'qr-code' | 'verify' | 'backup-codes' | 'complete';

export const MFASetup: React.FC<MFASetupProps> = ({ onComplete, onCancel }) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [currentStep, setCurrentStep] = useState<SetupStep>('device-name');
  const [deviceName, setDeviceName] = useState('');
  const [setupData, setSetupData] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDeviceNameSubmit = async () => {
    if (!deviceName.trim()) {
      setError(intl.formatMessage({ id: 'user.twofa.error.device.name.required' }));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await mfaApi.setupDevice({
        device_name: deviceName.trim(),
        issuer_name: 'GeNieGO SSO'
      });
      
      setSetupData(response);
      setCurrentStep('qr-code');
    } catch (err: any) {
      setError(err.message || intl.formatMessage({ id: 'user.twofa.error.setup.failed' }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationSubmit = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError(intl.formatMessage({ id: 'user.twofa.error.invalid.code' }));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await mfaApi.verifySetup({
        device_id: setupData.device_id,
        code: verificationCode
      });

      if (response.valid) {
        setCurrentStep('backup-codes');
      } else {
        setError(response.message || intl.formatMessage({ id: 'user.twofa.error.verification.failed' }));
      }
    } catch (err: any) {
      setError(err.message || intl.formatMessage({ id: 'user.twofa.error.verification.failed' }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackupCodesDownload = () => {
    if (setupData?.backup_codes) {
      downloadBackupCodes(setupData.backup_codes);
      toast({
        title: intl.formatMessage({ id: 'user.twofa.message.backup.download' }),
        variant: 'default',
      });
    }
  };

  const handleBackupCodesCopy = async () => {
    if (setupData?.backup_codes) {
      const success = await copyBackupCodes(setupData.backup_codes);
      if (success) {
        toast({
          title: intl.formatMessage({ id: 'user.twofa.message.backup.copied' }),
          variant: 'default',
        });
      }
    }
  };

  const handleComplete = () => {
    setCurrentStep('complete');
    toast({
      title: intl.formatMessage({ id: 'user.twofa.message.setup.success' }),
      variant: 'default',
    });
    onComplete?.();
  };

  const renderDeviceNameStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">{intl.formatMessage({ id: 'user.twofa.setup.title' })}</h2>
        <p className="text-muted-foreground mt-2">{intl.formatMessage({ id: 'user.twofa.setup.subtitle' })}</p>
      </div>

      <Alert>
        <Smartphone className="h-4 w-4" />
        <AlertDescription>
          {intl.formatMessage({ id: 'user.twofa.setup.description' })}
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div>
          <Label htmlFor="device-name">{intl.formatMessage({ id: 'user.twofa.setup.device.name' })}</Label>
          <Input
            id="device-name"
            type="text"
            placeholder={intl.formatMessage({ id: 'user.twofa.setup.device.name.placeholder' })}
            value={deviceName}
            onChange={(e) => setDeviceName(e.target.value)}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            {intl.formatMessage({ id: 'user.twofa.setup.device.name.help' })}
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-3">
          <Button variant="outline" onClick={onCancel} className="flex-1">
            {intl.formatMessage({ id: 'common.cancel' })}
          </Button>
          <Button 
            onClick={handleDeviceNameSubmit} 
            disabled={isLoading || !deviceName.trim()}
            className="flex-1"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {intl.formatMessage({ id: 'common.loading' })}
              </div>
            ) : (
              <>
                {intl.formatMessage({ id: 'common.next' })}
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderQRCodeStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <QrCode className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">{intl.formatMessage({ id: 'user.twofa.setup.qr.title' })}</h2>
        <p className="text-muted-foreground mt-2">{intl.formatMessage({ id: 'user.twofa.setup.qr.description' })}</p>
      </div>

      {setupData?.qr_code && (
        <div className="flex justify-center">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <img 
              src={`data:image/png;base64,${setupData.qr_code}`}
              alt="MFA QR Code"
              className="w-48 h-48"
            />
          </div>
        </div>
      )}

      <Separator />

      <div className="space-y-4">
        <h3 className="font-semibold">{intl.formatMessage({ id: 'user.twofa.setup.manual.title' })}</h3>
        <p className="text-sm text-muted-foreground">
          {intl.formatMessage({ id: 'user.twofa.setup.manual.description' })}
        </p>
        
        <div className="bg-muted p-3 rounded-md">
          <Label className="text-xs font-medium text-muted-foreground">
            {intl.formatMessage({ id: 'user.twofa.setup.manual.key' })}
          </Label>
          <div className="flex items-center gap-2 mt-1">
            <code className="flex-1 text-sm font-mono bg-background px-2 py-1 rounded border">
              {setupData?.manual_entry_key}
            </code>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigator.clipboard.writeText(setupData?.manual_entry_key || '')}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => setCurrentStep('device-name')}
            className="flex-1"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({ id: 'common.back' })}
          </Button>
          <Button 
            onClick={() => setCurrentStep('verify')}
            className="flex-1"
          >
            {intl.formatMessage({ id: 'common.next' })}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  const renderVerifyStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">{intl.formatMessage({ id: 'user.twofa.setup.verify.title' })}</h2>
        <p className="text-muted-foreground mt-2">{intl.formatMessage({ id: 'user.twofa.setup.verify.description' })}</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="verification-code">{intl.formatMessage({ id: 'user.twofa.setup.verify.code' })}</Label>
          <Input
            id="verification-code"
            type="text"
            placeholder={intl.formatMessage({ id: 'user.twofa.setup.verify.code.placeholder' })}
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            className="mt-1 text-center text-lg font-mono tracking-widest"
            maxLength={6}
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => setCurrentStep('qr-code')}
            className="flex-1"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({ id: 'common.back' })}
          </Button>
          <Button 
            onClick={handleVerificationSubmit}
            disabled={isLoading || verificationCode.length !== 6}
            className="flex-1"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {intl.formatMessage({ id: 'common.loading' })}
              </div>
            ) : (
              <>
                {intl.formatMessage({ id: 'common.next' })}
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderBackupCodesStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">{intl.formatMessage({ id: 'user.twofa.setup.backup.title' })}</h2>
        <p className="text-muted-foreground mt-2">{intl.formatMessage({ id: 'user.twofa.setup.backup.description' })}</p>
      </div>

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {intl.formatMessage({ id: 'user.twofa.setup.backup.warning' })}
        </AlertDescription>
      </Alert>

      <div className="bg-muted p-4 rounded-lg">
        <div className="grid grid-cols-2 gap-2">
          {setupData?.backup_codes?.map((code: string, index: number) => (
            <div key={index} className="bg-background p-2 rounded border">
              <code className="text-sm font-mono">{code}</code>
            </div>
          ))}
        </div>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handleBackupCodesDownload} className="flex-1">
          <Download className="mr-2 h-4 w-4" />
          {intl.formatMessage({ id: 'user.twofa.setup.backup.download' })}
        </Button>
        <Button variant="outline" onClick={handleBackupCodesCopy} className="flex-1">
          <Copy className="mr-2 h-4 w-4" />
          {intl.formatMessage({ id: 'user.twofa.setup.backup.copy' })}
        </Button>
      </div>

      <Button onClick={handleComplete} className="w-full">
        {intl.formatMessage({ id: 'user.twofa.setup.complete' })}
      </Button>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6 text-center">
      <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
      <div>
        <h2 className="text-2xl font-bold">{intl.formatMessage({ id: 'user.twofa.message.setup.success' })}</h2>
        <p className="text-muted-foreground mt-2">
          {intl.formatMessage({ id: 'user.twofa.setup.description' })}
        </p>
      </div>
      <Badge variant="secondary" className="text-sm">
        <Shield className="mr-2 h-4 w-4" />
        {intl.formatMessage({ id: 'user.twofa.manage.status.enabled' })}
      </Badge>
    </div>
  );

  return (
    <Card className="w-full max-w-md mx-auto shadow-sm">
      <CardHeader className="text-center">
        <div className="flex justify-center space-x-2 mb-4">
          {['device-name', 'qr-code', 'verify', 'backup-codes'].map((step, index) => (
            <div
              key={step}
              className={`w-3 h-3 rounded-full ${
                ['device-name', 'qr-code', 'verify', 'backup-codes'].indexOf(currentStep) >= index
                  ? 'bg-primary'
                  : 'bg-muted'
              }`}
            />
          ))}
        </div>
      </CardHeader>
      
      <CardContent>
        {currentStep === 'device-name' && renderDeviceNameStep()}
        {currentStep === 'qr-code' && renderQRCodeStep()}
        {currentStep === 'verify' && renderVerifyStep()}
        {currentStep === 'backup-codes' && renderBackupCodesStep()}
        {currentStep === 'complete' && renderCompleteStep()}
      </CardContent>
    </Card>
  );
};
