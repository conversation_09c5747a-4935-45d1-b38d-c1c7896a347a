import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Shield, 
  TrendingUp,
  TrendingDown,
  BarChart3,
  Pie<PERSON>hart,
  Activity,
  Clock,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Users,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConsentAnalytics {
  summary: {
    total_consents: number;
    active_consents: number;
    expired_consents: number;
    revoked_consents: number;
    expiring_soon: number;
    recent_activity_7d: number;
    health_score: number;
  };
  trends: {
    consents_granted_7d: number;
    consents_revoked_7d: number;
    consents_expired_7d: number;
    trend_direction: 'up' | 'down' | 'stable';
  };
  top_applications: Array<{
    application_name: string;
    consent_count: number;
    last_activity: string;
  }>;
  scope_distribution: Array<{
    scope: string;
    count: number;
    percentage: number;
  }>;
  activity_timeline: Array<{
    date: string;
    granted: number;
    revoked: number;
    expired: number;
  }>;
}

interface ConsentAnalyticsDashboardProps {
  className?: string;
  timeRange?: '7d' | '30d' | '90d';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d') => void;
}

/**
 * ConsentAnalyticsDashboard Component
 * 
 * Comprehensive analytics dashboard for consent management:
 * - Consent summary statistics and health metrics
 * - Trend analysis with directional indicators
 * - Top applications by consent usage
 * - Scope distribution analysis
 * - Activity timeline visualization
 * - Health score monitoring
 * - GDPR compliance insights
 */
const ConsentAnalyticsDashboard: React.FC<ConsentAnalyticsDashboardProps> = ({
  className,
  timeRange = '7d',
  onTimeRangeChange,
}) => {
  const intl = useIntl();
  const [analytics, setAnalytics] = useState<ConsentAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  // Fetch consent analytics
  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Fetch consent summary (from cleanup service)
      const summaryResponse = await fetch('/api/v1/consent/analytics/summary', {
        credentials: 'include',
      });
      
      let summary = {};
      if (summaryResponse.ok) {
        summary = await summaryResponse.json();
      }

      // Fetch user's consents for detailed analysis
      const consentsResponse = await fetch('/api/v1/consent/my-consents', {
        credentials: 'include',
      });
      
      let consents = [];
      if (consentsResponse.ok) {
        consents = await consentsResponse.json();
      }

      // Fetch audit log for trends
      const auditResponse = await fetch('/api/v1/consent/audit-log?limit=100', {
        credentials: 'include',
      });
      
      let auditLog = [];
      if (auditResponse.ok) {
        auditLog = await auditResponse.json();
      }

      // Process analytics data
      const processedAnalytics = processAnalyticsData(summary, consents, auditLog);
      setAnalytics(processedAnalytics);
      
    } catch (error) {
      console.error('Failed to fetch consent analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Process raw data into analytics format
  const processAnalyticsData = (summary: any, consents: any[], auditLog: any[]): ConsentAnalytics => {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Calculate summary stats
    const activeConsents = consents.filter(c => c.is_valid).length;
    const expiredConsents = consents.filter(c => c.is_expired).length;
    const revokedConsents = consents.filter(c => !c.is_active).length;
    
    const expiringSoon = consents.filter(c => {
      if (!c.expires_at || !c.is_valid) return false;
      const expiresAt = new Date(c.expires_at);
      const diffDays = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
      return diffDays > 0 && diffDays <= 30;
    }).length;

    // Calculate trends
    const recentAudit = auditLog.filter(log => 
      new Date(log.timestamp) >= sevenDaysAgo
    );
    
    const granted7d = recentAudit.filter(log => log.action === 'granted').length;
    const revoked7d = recentAudit.filter(log => log.action === 'revoked').length;
    const expired7d = recentAudit.filter(log => log.action === 'expired').length;

    // Determine trend direction
    const trendDirection = granted7d > revoked7d + expired7d ? 'up' : 
                          granted7d < revoked7d + expired7d ? 'down' : 'stable';

    // Top applications
    const appCounts: Record<string, number> = {};
    consents.forEach(consent => {
      appCounts[consent.application_name] = (appCounts[consent.application_name] || 0) + 1;
    });
    
    const topApplications = Object.entries(appCounts)
      .map(([name, count]) => ({
        application_name: name,
        consent_count: count,
        last_activity: consents
          .filter(c => c.application_name === name)
          .sort((a, b) => new Date(b.granted_at).getTime() - new Date(a.granted_at).getTime())[0]?.granted_at || '',
      }))
      .sort((a, b) => b.consent_count - a.consent_count)
      .slice(0, 5);

    // Scope distribution
    const scopeCounts: Record<string, number> = {};
    consents.forEach(consent => {
      consent.scopes.forEach((scope: string) => {
        scopeCounts[scope] = (scopeCounts[scope] || 0) + 1;
      });
    });
    
    const totalScopes = Object.values(scopeCounts).reduce((sum, count) => sum + count, 0);
    const scopeDistribution = Object.entries(scopeCounts)
      .map(([scope, count]) => ({
        scope,
        count,
        percentage: totalScopes > 0 ? (count / totalScopes) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count);

    // Activity timeline (simplified)
    const activityTimeline = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
      
      const dayAudit = auditLog.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= dayStart && logDate < dayEnd;
      });
      
      activityTimeline.push({
        date: date.toISOString().split('T')[0],
        granted: dayAudit.filter(log => log.action === 'granted').length,
        revoked: dayAudit.filter(log => log.action === 'revoked').length,
        expired: dayAudit.filter(log => log.action === 'expired').length,
      });
    }

    // Calculate health score
    const healthScore = activeConsents > 0 ? 
      Math.min(100, Math.max(0, (activeConsents / (activeConsents + expiredConsents + revokedConsents)) * 100)) : 
      100;

    return {
      summary: {
        total_consents: consents.length,
        active_consents: activeConsents,
        expired_consents: expiredConsents,
        revoked_consents: revokedConsents,
        expiring_soon: expiringSoon,
        recent_activity_7d: recentAudit.length,
        health_score: healthScore,
      },
      trends: {
        consents_granted_7d: granted7d,
        consents_revoked_7d: revoked7d,
        consents_expired_7d: expired7d,
        trend_direction: trendDirection,
      },
      top_applications: topApplications,
      scope_distribution: scopeDistribution,
      activity_timeline: activityTimeline,
    };
  };

  // Handle time range change
  const handleTimeRangeChange = (range: '7d' | '30d' | '90d') => {
    setSelectedTimeRange(range);
    if (onTimeRangeChange) {
      onTimeRangeChange(range);
    }
    // In a real implementation, you would refetch data with the new time range
  };

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  };

  // Get trend icon
  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  useEffect(() => {
    fetchAnalytics();
  }, [selectedTimeRange]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {intl.formatMessage({ 
                id: 'user.consent.analytics.error', 
                defaultMessage: 'Unable to load analytics data' 
              })}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
          <BarChart3 className="h-6 w-6 text-primary" />
          {intl.formatMessage({ 
            id: 'user.consent.analytics.title', 
            defaultMessage: 'Consent Analytics' 
          })}
        </h2>
        <div className="flex gap-2">
          {(['7d', '30d', '90d'] as const).map((range) => (
            <Button
              key={range}
              variant={selectedTimeRange === range ? "default" : "outline"}
              size="sm"
              onClick={() => handleTimeRangeChange(range)}
            >
              {range}
            </Button>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchAnalytics}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
            {intl.formatMessage({ id: 'user.consent.analytics.refresh', defaultMessage: 'Refresh' })}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.consent.analytics.total', defaultMessage: 'Total Consents' })}
                </p>
                <p className="text-2xl font-bold">{analytics.summary.total_consents}</p>
              </div>
              <Shield className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.consent.analytics.active', defaultMessage: 'Active' })}
                </p>
                <p className="text-2xl font-bold text-green-600">{analytics.summary.active_consents}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.consent.analytics.expiring', defaultMessage: 'Expiring Soon' })}
                </p>
                <p className="text-2xl font-bold text-amber-600">{analytics.summary.expiring_soon}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-amber-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.consent.analytics.health', defaultMessage: 'Health Score' })}
                </p>
                <p className={cn("text-2xl font-bold", getHealthScoreColor(analytics.summary.health_score))}>
                  {analytics.summary.health_score.toFixed(0)}%
                </p>
              </div>
              <Activity className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Trends and Top Applications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTrendIcon(analytics.trends.trend_direction)}
              {intl.formatMessage({ id: 'user.consent.analytics.trends', defaultMessage: 'Recent Trends' })}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'user.consent.analytics.granted', defaultMessage: 'Granted (7d)' })}
              </span>
              <Badge variant="outline" className="text-green-600">
                +{analytics.trends.consents_granted_7d}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'user.consent.analytics.revoked', defaultMessage: 'Revoked (7d)' })}
              </span>
              <Badge variant="outline" className="text-red-600">
                -{analytics.trends.consents_revoked_7d}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'user.consent.analytics.expired', defaultMessage: 'Expired (7d)' })}
              </span>
              <Badge variant="outline" className="text-amber-600">
                -{analytics.trends.consents_expired_7d}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Top Applications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.consent.analytics.topApps', defaultMessage: 'Top Applications' })}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.top_applications.map((app, index) => (
                <div key={app.application_name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                    <span className="text-sm font-medium">{app.application_name}</span>
                  </div>
                  <Badge variant="secondary">{app.consent_count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scope Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            {intl.formatMessage({ id: 'user.consent.analytics.scopes', defaultMessage: 'Permission Distribution' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.scope_distribution.map((scope) => (
              <div key={scope.scope} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-primary" />
                  <span className="text-sm font-medium capitalize">{scope.scope}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">{scope.count}</span>
                  <Badge variant="outline">{formatPercentage(scope.percentage)}</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConsentAnalyticsDashboard;
