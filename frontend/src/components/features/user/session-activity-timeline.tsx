import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  MapPin, 
  Clock, 
  Shield, 
  LogIn,
  LogOut,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity,
  Eye,
  Settings,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SessionActivity {
  session_id: string;
  device_fingerprint: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  last_activity: string;
  expires_at: string;
  is_active: boolean;
  device_info: {
    device_type: 'desktop' | 'mobile' | 'tablet' | 'unknown';
    browser: string;
    os: string;
  };
  location: {
    city: string;
    country: string;
    region: string;
  };
  connected_apps: Array<{
    application_id: string;
    connected_at: string;
    last_used?: string;
  }>;
}

interface SessionActivityTimelineProps {
  userId?: string;
  limit?: number;
  showInactive?: boolean;
  className?: string;
  onSessionTerminate?: (sessionId: string) => void;
}

/**
 * SessionActivityTimeline Component
 * 
 * Displays a chronological timeline of user session activities:
 * - Visual timeline with device icons and activity indicators
 * - Session creation, activity, and termination events
 * - Connected application tracking
 * - Device and location information
 * - Security event highlighting
 * - Interactive session management
 */
const SessionActivityTimeline: React.FC<SessionActivityTimelineProps> = ({
  userId,
  limit = 20,
  showInactive = true,
  className,
  onSessionTerminate,
}) => {
  const intl = useIntl();
  const [activities, setActivities] = useState<SessionActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTerminating, setIsTerminating] = useState<string | null>(null);

  // Fetch session activity timeline
  const fetchActivityTimeline = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/v1/sessions/activity-timeline?limit=${limit}`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setActivities(data);
      }
    } catch (error) {
      console.error('Failed to fetch session activity timeline:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle session termination
  const handleTerminateSession = async (sessionId: string) => {
    try {
      setIsTerminating(sessionId);
      
      if (onSessionTerminate) {
        onSessionTerminate(sessionId);
      } else {
        const response = await fetch(`/api/v1/sessions/${sessionId}`, {
          method: 'DELETE',
          credentials: 'include',
        });
        
        if (response.ok) {
          await fetchActivityTimeline(); // Refresh timeline
        }
      }
    } catch (error) {
      console.error('Failed to terminate session:', error);
    } finally {
      setIsTerminating(null);
    }
  };

  // Get device icon
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'tablet':
        return <Tablet className="h-4 w-4" />;
      case 'desktop':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  // Get activity status
  const getActivityStatus = (activity: SessionActivity) => {
    const now = new Date();
    const lastActivity = new Date(activity.last_activity);
    const expiresAt = new Date(activity.expires_at);
    
    if (!activity.is_active) {
      return {
        icon: <LogOut className="h-4 w-4 text-red-500" />,
        label: intl.formatMessage({ id: 'session.status.terminated', defaultMessage: 'Terminated' }),
        color: 'text-red-600',
        bgColor: 'bg-red-50 dark:bg-red-950',
        borderColor: 'border-red-200',
      };
    }
    
    if (expiresAt < now) {
      return {
        icon: <Clock className="h-4 w-4 text-amber-500" />,
        label: intl.formatMessage({ id: 'session.status.expired', defaultMessage: 'Expired' }),
        color: 'text-amber-600',
        bgColor: 'bg-amber-50 dark:bg-amber-950',
        borderColor: 'border-amber-200',
      };
    }
    
    const diffHours = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);
    if (diffHours < 1) {
      return {
        icon: <Wifi className="h-4 w-4 text-green-500" />,
        label: intl.formatMessage({ id: 'session.status.active', defaultMessage: 'Active' }),
        color: 'text-green-600',
        bgColor: 'bg-green-50 dark:bg-green-950',
        borderColor: 'border-green-200',
      };
    } else if (diffHours < 24) {
      return {
        icon: <WifiOff className="h-4 w-4 text-gray-500" />,
        label: intl.formatMessage({ id: 'session.status.idle', defaultMessage: 'Idle' }),
        color: 'text-gray-600',
        bgColor: 'bg-gray-50 dark:bg-gray-950',
        borderColor: 'border-gray-200',
      };
    } else {
      return {
        icon: <WifiOff className="h-4 w-4 text-gray-400" />,
        label: intl.formatMessage({ id: 'session.status.inactive', defaultMessage: 'Inactive' }),
        color: 'text-gray-500',
        bgColor: 'bg-gray-50 dark:bg-gray-950',
        borderColor: 'border-gray-200',
      };
    }
  };

  // Format time
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Get time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return intl.formatMessage({ id: 'time.now', defaultMessage: 'Just now' });
    if (diffHours < 24) return intl.formatMessage({ id: 'time.hours', defaultMessage: '{hours}h ago' }, { hours: diffHours });
    return intl.formatMessage({ id: 'time.days', defaultMessage: '{days}d ago' }, { days: diffDays });
  };

  // Check if session is current
  const isCurrentSession = (activity: SessionActivity) => {
    // This would need to be determined by comparing with current session
    // For now, we'll use a simple heuristic
    const now = new Date();
    const lastActivity = new Date(activity.last_activity);
    const diffMinutes = (now.getTime() - lastActivity.getTime()) / (1000 * 60);
    return activity.is_active && diffMinutes < 5;
  };

  // Filter activities
  const filteredActivities = showInactive 
    ? activities 
    : activities.filter(activity => activity.is_active);

  useEffect(() => {
    fetchActivityTimeline();
  }, [limit, showInactive]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary" />
            {intl.formatMessage({ 
              id: 'session.timeline.title', 
              defaultMessage: 'Session Activity Timeline' 
            })}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchActivityTimeline}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
            {intl.formatMessage({ id: 'session.timeline.refresh', defaultMessage: 'Refresh' })}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {filteredActivities.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {intl.formatMessage({ 
                id: 'session.timeline.empty', 
                defaultMessage: 'No session activity found' 
              })}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredActivities.map((activity, index) => {
              const status = getActivityStatus(activity);
              const isCurrent = isCurrentSession(activity);
              
              return (
                <div key={activity.session_id} className="relative">
                  {/* Timeline line */}
                  {index < filteredActivities.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border" />
                  )}
                  
                  <div className={cn(
                    "flex gap-4 p-4 rounded-lg border transition-colors",
                    status.borderColor,
                    status.bgColor,
                    isCurrent && "ring-2 ring-primary/20"
                  )}>
                    {/* Device Icon */}
                    <div className="flex-shrink-0">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center",
                        status.bgColor,
                        "border-2",
                        status.borderColor
                      )}>
                        {getDeviceIcon(activity.device_info.device_type)}
                      </div>
                    </div>
                    
                    {/* Activity Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-foreground">
                            {activity.device_info.browser} on {activity.device_info.os}
                          </h4>
                          {isCurrent && (
                            <Badge variant="default" className="text-xs">
                              {intl.formatMessage({ id: 'session.current', defaultMessage: 'Current' })}
                            </Badge>
                          )}
                          <Badge variant="outline" className={cn("text-xs", status.color)}>
                            {status.icon}
                            <span className="ml-1">{status.label}</span>
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {getTimeAgo(activity.last_activity)}
                          </span>
                          {activity.is_active && !isCurrent && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleTerminateSession(activity.session_id)}
                              disabled={isTerminating === activity.session_id}
                              className="h-6 w-6 p-0"
                            >
                              {isTerminating === activity.session_id ? (
                                <RefreshCw className="h-3 w-3 animate-spin" />
                              ) : (
                                <LogOut className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {/* Session Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-muted-foreground mb-3">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>
                            {activity.location.city !== 'Unknown' 
                              ? `${activity.location.city}, ${activity.location.country}`
                              : activity.ip_address
                            }
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            {intl.formatMessage({ id: 'session.created', defaultMessage: 'Created' })}: {formatTime(activity.created_at)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Activity className="h-3 w-3" />
                          <span>
                            {intl.formatMessage({ id: 'session.lastActivity', defaultMessage: 'Last Activity' })}: {formatTime(activity.last_activity)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          <span>
                            {intl.formatMessage({ id: 'session.expires', defaultMessage: 'Expires' })}: {formatTime(activity.expires_at)}
                          </span>
                        </div>
                      </div>
                      
                      {/* Connected Applications */}
                      {activity.connected_apps.length > 0 && (
                        <div>
                          <h5 className="text-xs font-medium text-muted-foreground mb-1">
                            {intl.formatMessage({ 
                              id: 'session.connectedApps', 
                              defaultMessage: 'Connected Applications' 
                            })}
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {activity.connected_apps.map((app) => (
                              <Badge key={app.application_id} variant="secondary" className="text-xs">
                                {app.application_id}
                                {app.last_used && (
                                  <span className="ml-1 text-muted-foreground">
                                    ({getTimeAgo(app.last_used)})
                                  </span>
                                )}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SessionActivityTimeline;
