/**
 * Invitation Notification Badge Component
 * 
 * Shows a notification badge with pending invitation count
 */

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/shadcn/badge';
import { getUserInvitations } from '@/services/user-api';
import useAuth from '@/hooks/use-auth';
import { useInvitationContext } from '@/contexts/invitation-context';

interface InvitationNotificationBadgeProps {
  className?: string;
}

/**
 * Invitation Notification Badge
 * 
 * Displays a badge with the count of pending invitations for the current user.
 * Updates automatically when invitations change.
 */
export const InvitationNotificationBadge: React.FC<InvitationNotificationBadgeProps> = ({ 
  className = "" 
}) => {
  const { user, isAuthenticated } = useAuth();
  const { refreshTrigger, updateInvitationCount } = useInvitationContext();
  const [pendingCount, setPendingCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const loadPendingCount = async () => {
    if (!isAuthenticated || !user || user.role === 'admin') {
      setPendingCount(0);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getUserInvitations({ 
        status_filter: 'pending',
        limit: 100,
        offset: 0 
      });
      const count = response.total || 0;
      setPendingCount(count);
      updateInvitationCount(count);
    } catch (error) {
      console.error('Failed to load pending invitations count:', error);
      setPendingCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPendingCount();

    // Set up polling to check for new invitations every 30 seconds
    const interval = setInterval(loadPendingCount, 30000);

    return () => clearInterval(interval);
  }, [isAuthenticated, user, refreshTrigger]); // Add refreshTrigger to dependencies

  // Don't show badge for admin users or if no pending invitations
  if (!isAuthenticated || !user || user.role === 'admin' || pendingCount === 0) {
    return null;
  }

  return (
    <Badge 
      variant="destructive" 
      className={`ml-auto h-5 w-5 shrink-0 items-center justify-center rounded-full text-xs ${className}`}
    >
      {pendingCount > 99 ? '99+' : pendingCount}
    </Badge>
  );
};

export default InvitationNotificationBadge;
