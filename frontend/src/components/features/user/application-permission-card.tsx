import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Shield, 
  User, 
  Mail, 
  Globe, 
  Eye,
  Settings,
  Clock,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ApplicationPermission {
  id: string;
  user_id: string;
  application_id: string;
  application_name: string;
  application_description?: string;
  application_url?: string;
  scopes: string[];
  granted_at: string;
  expires_at?: string;
  is_active: boolean;
  is_expired: boolean;
  is_valid: boolean;
  last_used?: string;
}

interface ApplicationPermissionCardProps {
  permission: ApplicationPermission;
  onRevoke?: (applicationId: string, applicationName: string) => void;
  onModify?: (applicationId: string, currentScopes: string[]) => void;
  onViewDetails?: (applicationId: string) => void;
  isRevoking?: boolean;
  isModifying?: boolean;
  compact?: boolean;
}

/**
 * ApplicationPermissionCard Component
 * 
 * Displays detailed information about user permissions for a specific application:
 * - Application information and branding
 * - Granted scopes with clear descriptions
 * - Permission status and expiration
 * - Last usage information
 * - Management actions (revoke, modify, view details)
 * - GDPR-compliant permission display
 */
const ApplicationPermissionCard: React.FC<ApplicationPermissionCardProps> = ({
  permission,
  onRevoke,
  onModify,
  onViewDetails,
  isRevoking = false,
  isModifying = false,
  compact = false,
}) => {
  const intl = useIntl();

  // Get scope icon
  const getScopeIcon = (scope: string) => {
    const iconClass = compact ? "h-3 w-3" : "h-4 w-4";
    switch (scope) {
      case 'openid':
        return <Shield className={iconClass} />;
      case 'profile':
        return <User className={iconClass} />;
      case 'email':
        return <Mail className={iconClass} />;
      case 'read':
        return <Eye className={iconClass} />;
      case 'write':
        return <Settings className={iconClass} />;
      default:
        return <Globe className={iconClass} />;
    }
  };

  // Get scope display name
  const getScopeName = (scope: string) => {
    const scopeNames: Record<string, string> = {
      'openid': intl.formatMessage({ id: 'user.consent.scope.openid', defaultMessage: 'Basic Authentication' }),
      'profile': intl.formatMessage({ id: 'user.consent.scope.profile', defaultMessage: 'Profile Information' }),
      'email': intl.formatMessage({ id: 'user.consent.scope.email', defaultMessage: 'Email Address' }),
      'read': intl.formatMessage({ id: 'user.consent.scope.read', defaultMessage: 'Read Access' }),
      'write': intl.formatMessage({ id: 'user.consent.scope.write', defaultMessage: 'Write Access' }),
    };
    return scopeNames[scope] || scope.charAt(0).toUpperCase() + scope.slice(1);
  };

  // Get scope description
  const getScopeDescription = (scope: string) => {
    const descriptions: Record<string, string> = {
      'openid': intl.formatMessage({ id: 'user.consent.scope.openid.desc', defaultMessage: 'Verify your identity' }),
      'profile': intl.formatMessage({ id: 'user.consent.scope.profile.desc', defaultMessage: 'Access your name and username' }),
      'email': intl.formatMessage({ id: 'user.consent.scope.email.desc', defaultMessage: 'Access your email address' }),
      'read': intl.formatMessage({ id: 'user.consent.scope.read.desc', defaultMessage: 'Read your data and settings' }),
      'write': intl.formatMessage({ id: 'user.consent.scope.write.desc', defaultMessage: 'Modify your data and settings' }),
    };
    return descriptions[scope] || intl.formatMessage({ id: 'user.consent.scope.custom.desc', defaultMessage: 'Custom application permission' });
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return intl.formatMessage({ id: 'time.today', defaultMessage: 'Today' });
    if (diffDays === 1) return intl.formatMessage({ id: 'time.yesterday', defaultMessage: 'Yesterday' });
    if (diffDays < 30) return intl.formatMessage({ id: 'time.days', defaultMessage: '{days} days ago' }, { days: diffDays });
    if (diffDays < 365) return intl.formatMessage({ id: 'time.months', defaultMessage: '{months} months ago' }, { months: Math.floor(diffDays / 30) });
    return intl.formatMessage({ id: 'time.years', defaultMessage: '{years} years ago' }, { years: Math.floor(diffDays / 365) });
  };

  // Check if permission is expiring soon (within 30 days)
  const isExpiringSoon = () => {
    if (!permission.expires_at) return false;
    const now = new Date();
    const expiresAt = new Date(permission.expires_at);
    const diffDays = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    return diffDays < 30;
  };

  // Get status color and icon
  const getStatusDisplay = () => {
    if (!permission.is_active) {
      return {
        icon: <XCircle className="h-4 w-4 text-red-500" />,
        color: 'text-red-700',
        bg: 'bg-red-50 border-red-200',
        label: intl.formatMessage({ id: 'permission.status.revoked', defaultMessage: 'Revoked' }),
      };
    }
    if (permission.is_expired) {
      return {
        icon: <Clock className="h-4 w-4 text-amber-500" />,
        color: 'text-amber-700',
        bg: 'bg-amber-50 border-amber-200',
        label: intl.formatMessage({ id: 'permission.status.expired', defaultMessage: 'Expired' }),
      };
    }
    if (isExpiringSoon()) {
      return {
        icon: <AlertTriangle className="h-4 w-4 text-amber-500" />,
        color: 'text-amber-700',
        bg: 'bg-amber-50 border-amber-200',
        label: intl.formatMessage({ id: 'permission.status.expiring', defaultMessage: 'Expiring Soon' }),
      };
    }
    return {
      icon: <CheckCircle className="h-4 w-4 text-green-500" />,
      color: 'text-green-700',
      bg: 'bg-green-50 border-green-200',
      label: intl.formatMessage({ id: 'permission.status.active', defaultMessage: 'Active' }),
    };
  };

  const statusDisplay = getStatusDisplay();

  if (compact) {
    return (
      <Card className="transition-colors hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-primary" />
              <div>
                <h4 className="font-medium text-sm">{permission.application_name}</h4>
                <p className="text-xs text-muted-foreground">
                  {permission.scopes.length} {intl.formatMessage({ 
                    id: 'permission.scopes.count', 
                    defaultMessage: 'permissions' 
                  })}
                  {permission.last_used && ` • ${getTimeAgo(permission.last_used)}`}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={permission.is_valid ? "default" : "secondary"} className="text-xs">
                {statusDisplay.label}
              </Badge>
              {onRevoke && permission.is_valid && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRevoke(permission.application_id, permission.application_name)}
                  disabled={isRevoking}
                  className="h-6 w-6 p-0"
                >
                  {isRevoking ? (
                    <RefreshCw className="h-3 w-3 animate-spin" />
                  ) : (
                    <Trash2 className="h-3 w-3" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-colors hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            <div>
              <span className="text-lg">{permission.application_name}</span>
              {permission.application_url && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(permission.application_url, '_blank')}
                  className="ml-2 h-6 w-6 p-0"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={permission.is_valid ? "default" : "secondary"} className={statusDisplay.color}>
              {statusDisplay.icon}
              <span className="ml-1">{statusDisplay.label}</span>
            </Badge>
          </div>
        </div>
        {permission.application_description && (
          <p className="text-sm text-muted-foreground mt-2">
            {permission.application_description}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Granted Permissions */}
        <div>
          <h4 className="font-medium text-sm mb-3">
            {intl.formatMessage({ 
              id: 'permission.granted.title', 
              defaultMessage: 'Granted Permissions' 
            })}
          </h4>
          <div className="space-y-2">
            {permission.scopes.map((scope) => (
              <div key={scope} className="flex items-start gap-3 p-2 rounded-lg bg-muted/50">
                <div className="text-primary mt-0.5">
                  {getScopeIcon(scope)}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{getScopeName(scope)}</div>
                  <div className="text-xs text-muted-foreground">
                    {getScopeDescription(scope)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Permission Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {intl.formatMessage({ id: 'permission.granted', defaultMessage: 'Granted:' })}
            </span>
            <span>{formatDate(permission.granted_at)}</span>
          </div>
          {permission.expires_at && (
            <div className="flex items-center gap-2">
              <AlertTriangle className={cn("h-4 w-4", isExpiringSoon() ? "text-amber-500" : "text-muted-foreground")} />
              <span className="text-muted-foreground">
                {intl.formatMessage({ id: 'permission.expires', defaultMessage: 'Expires:' })}
              </span>
              <span className={isExpiringSoon() ? "text-amber-600 font-medium" : ""}>
                {formatDate(permission.expires_at)}
              </span>
            </div>
          )}
          {permission.last_used && (
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                {intl.formatMessage({ id: 'permission.lastUsed', defaultMessage: 'Last Used:' })}
              </span>
              <span>{getTimeAgo(permission.last_used)}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        {permission.is_valid && (
          <div className="flex gap-2 pt-2">
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(permission.application_id)}
              >
                <Eye className="h-3 w-3 mr-2" />
                {intl.formatMessage({ id: 'permission.viewDetails', defaultMessage: 'View Details' })}
              </Button>
            )}
            {onModify && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onModify(permission.application_id, permission.scopes)}
                disabled={isModifying}
              >
                {isModifying ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                ) : (
                  <Edit className="h-3 w-3 mr-2" />
                )}
                {intl.formatMessage({ id: 'permission.modify', defaultMessage: 'Modify' })}
              </Button>
            )}
            {onRevoke && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRevoke(permission.application_id, permission.application_name)}
                disabled={isRevoking}
                className="text-red-600 hover:text-red-700 hover:border-red-300"
              >
                {isRevoking ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                ) : (
                  <Trash2 className="h-3 w-3 mr-2" />
                )}
                {intl.formatMessage({ id: 'permission.revoke', defaultMessage: 'Revoke' })}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ApplicationPermissionCard;
