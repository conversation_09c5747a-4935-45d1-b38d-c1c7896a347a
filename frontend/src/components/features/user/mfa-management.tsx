/**
 * MFA Management Component
 * 
 * Component for managing MFA devices and settings
 * Displays devices, allows adding/removing devices, and shows backup codes
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/shadcn/table';
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  Plus, 
  Trash2, 
  Smartphone, 
  Key,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { mfaApi, type MFADevice } from '@/services/mfa-api';
import { MFASetup } from './mfa-setup';
import { useToast } from '@/hooks/use-toast';

interface MFAManagementProps {
  className?: string;
}

export const MFAManagement: React.FC<MFAManagementProps> = ({ className }) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [devices, setDevices] = useState<MFADevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showSetup, setShowSetup] = useState(false);
  const [deviceToRemove, setDeviceToRemove] = useState<MFADevice | null>(null);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    loadDevices();
  }, []);

  const loadDevices = async () => {
    try {
      setIsLoading(true);
      const deviceList = await mfaApi.getDevices();
      setDevices(deviceList);
    } catch (error) {
      console.error('Failed to load MFA devices:', error);
      toast({
        title: intl.formatMessage({ id: 'user.twofa.error.network' }),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetupComplete = () => {
    setShowSetup(false);
    loadDevices();
  };

  const handleRemoveDevice = async () => {
    if (!deviceToRemove) return;

    setIsRemoving(true);
    try {
      await mfaApi.removeDevice(deviceToRemove.id);
      toast({
        title: intl.formatMessage({ id: 'user.twofa.message.device.removed' }),
        variant: 'default',
      });
      setDeviceToRemove(null);
      loadDevices();
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'user.twofa.message.device.remove.error' }),
        variant: 'destructive',
      });
    } finally {
      setIsRemoving(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return intl.formatMessage({ id: 'common.never' });
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return intl.formatMessage({ id: 'common.never' });
    return new Date(dateString).toLocaleString();
  };

  const getDeviceTypeIcon = (type: string) => {
    switch (type) {
      case 'totp':
        return <Smartphone className="h-4 w-4" />;
      default:
        return <Key className="h-4 w-4" />;
    }
  };

  const getDeviceTypeName = (type: string) => {
    switch (type) {
      case 'totp':
        return intl.formatMessage({ id: 'user.twofa.type.totp' });
      case 'sms':
        return intl.formatMessage({ id: 'user.twofa.type.sms' });
      case 'email':
        return intl.formatMessage({ id: 'user.twofa.type.email' });
      default:
        return type;
    }
  };

  const mfaEnabled = devices.some(device => device.is_active && device.is_verified);

  if (showSetup) {
    return (
      <div className={className}>
        <MFASetup
          onComplete={handleSetupComplete}
          onCancel={() => setShowSetup(false)}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <Card className="shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {mfaEnabled ? (
                <ShieldCheck className="h-6 w-6 text-green-500" />
              ) : (
                <ShieldX className="h-6 w-6 text-muted-foreground" />
              )}
              <div>
                <CardTitle>{intl.formatMessage({ id: 'user.twofa.manage.title' })}</CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'user.twofa.manage.subtitle' })}</CardDescription>
              </div>
            </div>
            <Badge variant={mfaEnabled ? 'default' : 'secondary'}>
              {mfaEnabled ? intl.formatMessage({ id: 'user.twofa.manage.status.enabled' }) : intl.formatMessage({ id: 'user.twofa.manage.status.disabled' })}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {!mfaEnabled && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                {intl.formatMessage({ id: 'user.twofa.setup.description' })}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">{intl.formatMessage({ id: 'user.twofa.manage.devices.title' })}</h3>
            <Button onClick={() => setShowSetup(true)} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              {intl.formatMessage({ id: 'user.twofa.manage.devices.add' })}
            </Button>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : devices.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">{intl.formatMessage({ id: 'user.twofa.manage.devices.empty' })}</p>
              <Button onClick={() => setShowSetup(true)} className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'user.twofa.manage.devices.add' })}
              </Button>
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{intl.formatMessage({ id: 'user.twofa.manage.device.name' })}</TableHead>
                    <TableHead>{intl.formatMessage({ id: 'user.twofa.manage.device.type' })}</TableHead>
                    <TableHead>{intl.formatMessage({ id: 'user.twofa.manage.device.status' })}</TableHead>
                    <TableHead>{intl.formatMessage({ id: 'user.twofa.manage.device.last.used' })}</TableHead>
                    <TableHead className="w-[100px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {getDeviceTypeIcon(device.device_type)}
                          {device.device_name}
                        </div>
                      </TableCell>
                      <TableCell>{getDeviceTypeName(device.device_type)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {device.is_verified ? (
                            <Badge variant="default" className="text-xs">
                              <CheckCircle className="mr-1 h-3 w-3" />
                              {intl.formatMessage({ id: 'user.twofa.manage.device.verified' })}
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="text-xs">
                              <Clock className="mr-1 h-3 w-3" />
                              {intl.formatMessage({ id: 'user.twofa.manage.device.unverified' })}
                            </Badge>
                          )}
                          {device.is_active && (
                            <Badge variant="outline" className="text-xs">
                              {intl.formatMessage({ id: 'user.twofa.manage.device.active' })}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatDateTime(device.last_used_at)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeviceToRemove(device)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {mfaEnabled && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{intl.formatMessage({ id: 'user.twofa.manage.backup.codes' })}</h3>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Key className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">
                        {devices.reduce((total, device) => total + device.backup_codes_count, 0)} {intl.formatMessage({ id: 'user.twofa.manage.backup.remaining' })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({ id: 'user.twofa.setup.backup.description' })}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      {intl.formatMessage({ id: 'user.twofa.manage.backup.view' })}
                    </Button>
                    <Button variant="outline" size="sm">
                      {intl.formatMessage({ id: 'user.twofa.manage.backup.regenerate' })}
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Remove Device Dialog */}
      <Dialog open={!!deviceToRemove} onOpenChange={() => setDeviceToRemove(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{intl.formatMessage({ id: 'user.twofa.manage.device.remove' })}</DialogTitle>
            <DialogDescription>
              {intl.formatMessage({ id: 'user.twofa.manage.device.remove.confirm' })}
            </DialogDescription>
          </DialogHeader>
          
          {deviceToRemove && (
            <div className="py-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                {getDeviceTypeIcon(deviceToRemove.device_type)}
                <div>
                  <p className="font-medium">{deviceToRemove.device_name}</p>
                  <p className="text-sm text-muted-foreground">
                    {getDeviceTypeName(deviceToRemove.device_type)} • {intl.formatMessage({ id: 'user.twofa.manage.device.created' })}: {formatDate(deviceToRemove.created_at)}
                  </p>
                </div>
              </div>
              
              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {intl.formatMessage({ id: 'user.twofa.manage.device.remove.warning' })}
                </AlertDescription>
              </Alert>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDeviceToRemove(null)}>
              {intl.formatMessage({ id: 'common.cancel' })}
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleRemoveDevice}
              disabled={isRemoving}
            >
              {isRemoving ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {intl.formatMessage({ id: 'common.loading' })}
                </div>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  {intl.formatMessage({ id: 'user.twofa.manage.device.remove' })}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
