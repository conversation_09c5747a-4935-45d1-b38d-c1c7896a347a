import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { 
  AlertTriangle,
  Clock,
  Shield,
  RefreshCw,
  X,
  CheckCircle,
  ExternalLink,
  Bell,
  BellOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ExpiringConsent {
  id: string;
  application_id: string;
  application_name: string;
  application_url?: string;
  scopes: string[];
  expires_at: string;
  days_until_expiry: number;
  is_critical: boolean;
}

interface ConsentRenewalNotificationProps {
  className?: string;
  autoCheck?: boolean;
  checkInterval?: number; // minutes
  onRenew?: (applicationId: string) => void;
  onDismiss?: (consentId: string) => void;
  compact?: boolean;
}

/**
 * ConsentRenewalNotification Component
 * 
 * Displays notifications for consents that are expiring soon:
 * - Automatic checking for expiring consents
 * - Prioritized display based on criticality
 * - Quick renewal actions
 * - Dismissible notifications
 * - Configurable check intervals
 * - GDPR-compliant renewal process
 */
const ConsentRenewalNotification: React.FC<ConsentRenewalNotificationProps> = ({
  className,
  autoCheck = true,
  checkInterval = 60, // 1 hour default
  onRenew,
  onDismiss,
  compact = false,
}) => {
  const intl = useIntl();
  const [expiringConsents, setExpiringConsents] = useState<ExpiringConsent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRenewing, setIsRenewing] = useState<string | null>(null);
  const [dismissedConsents, setDismissedConsents] = useState<Set<string>>(new Set());
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Fetch expiring consents
  const fetchExpiringConsents = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/v1/consent/my-consents?active_only=true', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const consents = await response.json();
        const now = new Date();
        
        // Filter consents expiring within 30 days
        const expiring = consents
          .filter((consent: any) => {
            if (!consent.expires_at || !consent.is_valid) return false;
            
            const expiresAt = new Date(consent.expires_at);
            const diffDays = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
            return diffDays > 0 && diffDays <= 30;
          })
          .map((consent: any) => {
            const expiresAt = new Date(consent.expires_at);
            const diffDays = Math.ceil((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            
            return {
              id: consent.id,
              application_id: consent.application_id,
              application_name: consent.application_name,
              application_url: consent.application_url,
              scopes: consent.scopes,
              expires_at: consent.expires_at,
              days_until_expiry: diffDays,
              is_critical: diffDays <= 7, // Critical if expiring within 7 days
            };
          })
          .sort((a: ExpiringConsent, b: ExpiringConsent) => a.days_until_expiry - b.days_until_expiry);

        setExpiringConsents(expiring);
      }
    } catch (error) {
      console.error('Failed to fetch expiring consents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle consent renewal
  const handleRenew = async (applicationId: string, applicationName: string) => {
    try {
      setIsRenewing(applicationId);
      
      if (onRenew) {
        onRenew(applicationId);
      } else {
        // Default renewal action - redirect to consent page
        window.location.href = `/oauth2/authorize?client_id=${applicationId}&response_type=code&scope=openid profile email&redirect_uri=${encodeURIComponent(window.location.origin)}&state=renewal`;
      }
    } catch (error) {
      console.error('Failed to initiate renewal:', error);
    } finally {
      setIsRenewing(null);
    }
  };

  // Handle notification dismissal
  const handleDismiss = (consentId: string) => {
    setDismissedConsents(prev => new Set([...prev, consentId]));
    if (onDismiss) {
      onDismiss(consentId);
    }
  };

  // Get urgency level styling
  const getUrgencyStyle = (daysUntilExpiry: number, isCritical: boolean) => {
    if (isCritical) {
      return {
        borderColor: 'border-red-200',
        bgColor: 'bg-red-50 dark:bg-red-950',
        textColor: 'text-red-800 dark:text-red-200',
        iconColor: 'text-red-600 dark:text-red-400',
      };
    } else if (daysUntilExpiry <= 14) {
      return {
        borderColor: 'border-amber-200',
        bgColor: 'bg-amber-50 dark:bg-amber-950',
        textColor: 'text-amber-800 dark:text-amber-200',
        iconColor: 'text-amber-600 dark:text-amber-400',
      };
    } else {
      return {
        borderColor: 'border-blue-200',
        bgColor: 'bg-blue-50 dark:bg-blue-950',
        textColor: 'text-blue-800 dark:text-blue-200',
        iconColor: 'text-blue-600 dark:text-blue-400',
      };
    }
  };

  // Format expiry message
  const getExpiryMessage = (daysUntilExpiry: number) => {
    if (daysUntilExpiry <= 1) {
      return intl.formatMessage({ 
        id: 'user.consent.renewal.expires.today', 
        defaultMessage: 'Expires today' 
      });
    } else if (daysUntilExpiry <= 7) {
      return intl.formatMessage(
        { 
          id: 'user.consent.renewal.expires.days', 
          defaultMessage: 'Expires in {days} days' 
        },
        { days: daysUntilExpiry }
      );
    } else {
      return intl.formatMessage(
        { 
          id: 'user.consent.renewal.expires.weeks', 
          defaultMessage: 'Expires in {weeks} weeks' 
        },
        { weeks: Math.ceil(daysUntilExpiry / 7) }
      );
    }
  };

  // Auto-check effect
  useEffect(() => {
    if (autoCheck && notificationsEnabled) {
      fetchExpiringConsents();

      const interval = setInterval(fetchExpiringConsents, checkInterval * 60 * 1000);
      return () => clearInterval(interval);
    }
    // Return empty cleanup function if conditions not met
    return () => {};
  }, [autoCheck, checkInterval, notificationsEnabled]);

  // Filter out dismissed consents
  const visibleConsents = expiringConsents.filter(
    consent => !dismissedConsents.has(consent.id)
  );

  if (!notificationsEnabled || visibleConsents.length === 0) {
    return null;
  }

  if (compact) {
    const criticalCount = visibleConsents.filter(c => c.is_critical).length;
    const totalCount = visibleConsents.length;

    return (
      <Alert className={cn("border-amber-200 bg-amber-50 dark:bg-amber-950", className)}>
        <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertDescription className="flex items-center justify-between">
          <span className="text-amber-800 dark:text-amber-200">
            {criticalCount > 0 
              ? intl.formatMessage(
                  { 
                    id: 'user.consent.renewal.compact.critical', 
                    defaultMessage: '{critical} critical, {total} total permissions expiring' 
                  },
                  { critical: criticalCount, total: totalCount }
                )
              : intl.formatMessage(
                  { 
                    id: 'user.consent.renewal.compact.normal', 
                    defaultMessage: '{total} permissions expiring soon' 
                  },
                  { total: totalCount }
                )
            }
          </span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotificationsEnabled(false)}
            >
              <BellOff className="h-3 w-3" />
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-foreground flex items-center gap-2">
          <Bell className="h-4 w-4" />
          {intl.formatMessage({ 
            id: 'user.consent.renewal.title', 
            defaultMessage: 'Permission Renewals' 
          })}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setNotificationsEnabled(false)}
        >
          <BellOff className="h-3 w-3" />
        </Button>
      </div>

      {/* Expiring Consents */}
      {visibleConsents.map((consent) => {
        const urgencyStyle = getUrgencyStyle(consent.days_until_expiry, consent.is_critical);
        
        return (
          <Card 
            key={consent.id} 
            className={cn(
              "transition-colors",
              urgencyStyle.borderColor,
              urgencyStyle.bgColor
            )}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <AlertTriangle className={cn("h-5 w-5 mt-0.5", urgencyStyle.iconColor)} />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className={cn("font-medium", urgencyStyle.textColor)}>
                        {consent.application_name}
                      </h4>
                      {consent.is_critical && (
                        <Badge variant="destructive" className="text-xs">
                          {intl.formatMessage({ 
                            id: 'user.consent.renewal.critical', 
                            defaultMessage: 'Critical' 
                          })}
                        </Badge>
                      )}
                    </div>
                    <p className={cn("text-sm mb-2", urgencyStyle.textColor)}>
                      {getExpiryMessage(consent.days_until_expiry)}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {consent.scopes.slice(0, 3).map((scope) => (
                        <Badge key={scope} variant="outline" className="text-xs">
                          {scope}
                        </Badge>
                      ))}
                      {consent.scopes.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{consent.scopes.length - 3} more
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleRenew(consent.application_id, consent.application_name)}
                        disabled={isRenewing === consent.application_id}
                        className="text-xs"
                      >
                        {isRenewing === consent.application_id ? (
                          <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                        ) : (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        )}
                        {intl.formatMessage({ 
                          id: 'user.consent.renewal.renew', 
                          defaultMessage: 'Renew' 
                        })}
                      </Button>
                      {consent.application_url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(consent.application_url, '_blank')}
                          className="text-xs"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          {intl.formatMessage({ 
                            id: 'user.consent.renewal.visit', 
                            defaultMessage: 'Visit App' 
                          })}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDismiss(consent.id)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      })}

      {/* Re-enable notifications */}
      {!notificationsEnabled && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setNotificationsEnabled(true)}
          className="w-full"
        >
          <Bell className="h-3 w-3 mr-2" />
          {intl.formatMessage({ 
            id: 'user.consent.renewal.enable', 
            defaultMessage: 'Enable renewal notifications' 
          })}
        </Button>
      )}
    </div>
  );
};

export default ConsentRenewalNotification;
