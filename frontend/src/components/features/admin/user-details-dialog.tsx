import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  User,
  Mail,
  Calendar,
  Activity,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Download,
  Key,
  UserCheck,
  UserX,
  Phone,
  MapPin,
  Clock,
} from 'lucide-react';

interface AdminUser {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  lastLogin: string | null;
  connectedApps: string[];
  loginAttempts: number;
  role: 'user' | 'developer' | 'admin';
  location?: string;
  phone?: string;
}

interface UserDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  user: AdminUser | null;
  onUserAction: (action: string, userId: string) => void;
}

/**
 * User Details Dialog
 * 
 * Admin dialog component for viewing detailed user information and performing
 * user management actions. Follows project standards with dark/light mode support,
 * responsive design, and i18n.
 */
export const UserDetailsDialog: React.FC<UserDetailsDialogProps> = ({
  isOpen,
  onClose,
  user,
  onUserAction,
}) => {
  const intl = useIntl();
  const [isPerformingAction, setIsPerformingAction] = useState(false);

  if (!user) return null;

  const handleAction = async (action: string) => {
    setIsPerformingAction(true);
    try {
      await onUserAction(action, user.id);
    } finally {
      setIsPerformingAction(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default';
      case 'developer':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusBadgeVariant = (isActive: boolean, isVerified: boolean) => {
    if (!isActive) return 'destructive';
    if (!isVerified) return 'secondary';
    return 'default';
  };

  const getStatusIcon = (isActive: boolean, isVerified: boolean) => {
    if (!isActive) return <XCircle className="h-3 w-3" />;
    if (!isVerified) return <AlertTriangle className="h-3 w-3" />;
    return <CheckCircle className="h-3 w-3" />;
  };

  const getStatusText = (isActive: boolean, isVerified: boolean) => {
    if (!isActive) return intl.formatMessage({ id: 'admin.users.status.disabled' });
    if (!isVerified) return intl.formatMessage({ id: 'admin.users.status.unverified' });
    return intl.formatMessage({ id: 'admin.users.status.active' });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.users.details.title' })}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage({ id: 'admin.users.details.description' })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Basic Info */}
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">{user.firstName} {user.lastName}</h3>
                <p className="text-sm text-muted-foreground">@{user.username}</p>
              </div>
              <div className="flex gap-2">
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {user.role}
                </Badge>
                <Badge variant={getStatusBadgeVariant(user.isActive, user.isVerified)}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(user.isActive, user.isVerified)}
                    {getStatusText(user.isActive, user.isVerified)}
                  </div>
                </Badge>
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{user.email}</span>
              </div>
              {user.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{user.phone}</span>
                </div>
              )}
              {user.location && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{user.location}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Account Details */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {intl.formatMessage({ id: 'admin.users.details.accountDetails' })}
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.users.details.registered' })}:
                </span>
                <span>{formatDate(user.createdAt)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.users.details.lastLogin' })}:
                </span>
                <span>
                  {user.lastLogin ? formatDateTime(user.lastLogin) : intl.formatMessage({ id: 'admin.users.details.never' })}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.users.details.failedAttempts' })}:
                </span>
                <span>{user.loginAttempts}</span>
              </div>
            </div>
          </div>

          {/* Connected Applications */}
          {user.connectedApps.length > 0 && (
            <>
              <Separator />
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">
                  {intl.formatMessage({ id: 'admin.users.details.connectedApps' })}
                </h4>
                
                <div className="flex flex-wrap gap-2">
                  {user.connectedApps.map((app) => (
                    <Badge key={app} variant="outline">
                      {app}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('toggle_status')}
              disabled={isPerformingAction}
              className="flex items-center gap-2"
            >
              {user.isActive ? (
                <>
                  <UserX className="h-4 w-4" />
                  {intl.formatMessage({ id: 'admin.users.actions.disableAccount' })}
                </>
              ) : (
                <>
                  <UserCheck className="h-4 w-4" />
                  {intl.formatMessage({ id: 'admin.users.actions.enableAccount' })}
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('reset_password')}
              disabled={isPerformingAction}
              className="flex items-center gap-2"
            >
              <Key className="h-4 w-4" />
              {intl.formatMessage({ id: 'admin.users.actions.resetPassword' })}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('export_data')}
              disabled={isPerformingAction}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {intl.formatMessage({ id: 'admin.users.actions.exportData' })}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
