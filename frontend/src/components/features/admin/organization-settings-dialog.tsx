import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Switch } from '@/components/ui/shadcn/switch';
import { Label } from '@/components/ui/shadcn/label';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Settings,
  Save,
  X,
  Globe,
  Lock,
  Users,
  AlertTriangle,
  Loader2,
} from 'lucide-react';

interface OrganizationSettings {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_public: boolean;
  is_active: boolean;
  max_members: number;
  settings: Record<string, any>;
}

interface OrganizationSettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string | null;
  onSettingsUpdated: () => void;
}

/**
 * Organization Settings Dialog
 * 
 * Admin dialog component for editing organization settings.
 * Follows project standards with dark/light mode support, responsive design, and i18n.
 */
export const OrganizationSettingsDialog: React.FC<OrganizationSettingsDialogProps> = ({
  isOpen,
  onClose,
  organizationId,
  onSettingsUpdated,
}) => {
  const intl = useIntl();
  const [settings, setSettings] = useState<OrganizationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_public: false,
    is_active: true,
    max_members: 50,
  });

  // Load organization settings when dialog opens
  useEffect(() => {
    if (isOpen && organizationId) {
      loadSettings();
    }
  }, [isOpen, organizationId]);

  // Update form data when settings change
  useEffect(() => {
    if (settings) {
      setFormData({
        name: settings.name,
        description: settings.description || '',
        is_public: settings.is_public,
        is_active: settings.is_active,
        max_members: settings.max_members,
      });
      setHasChanges(false);
    }
  }, [settings]);

  const loadSettings = async () => {
    if (!organizationId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/admin/organizations/${organizationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load organization settings: ${response.status}`);
      }

      const data = await response.json();
      setSettings(data);
    } catch (err) {
      console.error('Failed to load organization settings:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Fallback to mock data
      setSettings({
        id: organizationId,
        name: 'Test Organization',
        slug: 'test-org',
        description: 'A test organization for demonstration',
        is_public: false,
        is_active: true,
        max_members: 50,
        settings: {}
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!organizationId || !hasChanges) return;

    setIsSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/admin/organizations/${organizationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update organization settings: ${response.status}`);
      }

      const updatedData = await response.json();
      setSettings(updatedData);
      setHasChanges(false);
      onSettingsUpdated();
      
      // Show success message (you could add a toast here)
      console.log('Organization settings updated successfully');
    } catch (err) {
      console.error('Failed to update organization settings:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    if (hasChanges) {
      const confirmClose = window.confirm(
        intl.formatMessage({ id: 'admin.organizations.settings.confirmClose' })
      );
      if (!confirmClose) return;
    }
    
    setSettings(null);
    setError(null);
    setHasChanges(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.organizations.settings.title' })}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage({ id: 'admin.organizations.settings.description' })}
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            {intl.formatMessage({ id: 'common.loading' })}
          </div>
        )}

        {error && (
          <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSettings}
              className="mt-2"
            >
              {intl.formatMessage({ id: 'common.retry' })}
            </Button>
          </div>
        )}

        {!isLoading && settings && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                {intl.formatMessage({ id: 'admin.organizations.settings.basicInfo' })}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">
                    {intl.formatMessage({ id: 'admin.organizations.settings.name' })}
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'admin.organizations.settings.namePlaceholder' })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">
                    {intl.formatMessage({ id: 'admin.organizations.settings.description' })}
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'admin.organizations.settings.descriptionPlaceholder' })}
                    rows={3}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Visibility & Access */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                {intl.formatMessage({ id: 'admin.organizations.settings.visibility' })}
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      {intl.formatMessage({ id: 'admin.organizations.settings.publicOrganization' })}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {intl.formatMessage({ id: 'admin.organizations.settings.publicDescription' })}
                    </p>
                  </div>
                  <Switch
                    checked={formData.is_public}
                    onCheckedChange={(checked) => handleInputChange('is_public', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      {intl.formatMessage({ id: 'admin.organizations.settings.activeOrganization' })}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {intl.formatMessage({ id: 'admin.organizations.settings.activeDescription' })}
                    </p>
                  </div>
                  <Switch
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Member Limits */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                {intl.formatMessage({ id: 'admin.organizations.settings.memberLimits' })}
              </h3>
              
              <div>
                <Label htmlFor="max_members">
                  {intl.formatMessage({ id: 'admin.organizations.settings.maxMembers' })}
                </Label>
                <Input
                  id="max_members"
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.max_members}
                  onChange={(e) => handleInputChange('max_members', parseInt(e.target.value) || 50)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {intl.formatMessage({ id: 'admin.organizations.settings.maxMembersDescription' })}
                </p>
              </div>
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isSaving}
              >
                <X className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'common.cancel' })}
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={!hasChanges || isSaving}
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {intl.formatMessage({ id: 'common.save' })}
              </Button>
            </div>

            {hasChanges && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertTriangle className="h-4 w-4" />
                {intl.formatMessage({ id: 'admin.organizations.settings.unsavedChanges' })}
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
