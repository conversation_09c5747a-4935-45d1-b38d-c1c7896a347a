import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Users,
  UserCheck,
  UserX,
  Mail,
  Calendar,
  Crown,
  Shield,
  User,
  Loader2,
  MoreVertical,
} from 'lucide-react';

interface OrganizationMember {
  id: string;
  user_id: string;
  username: string;
  email: string;
  role: string;
  is_active: boolean;
  joined_at: string;
  invited_at?: string;
  permissions: Record<string, any>;
}

interface OrganizationMembersDialogProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string | null;
  organizationName: string;
}

/**
 * Organization Members Dialog
 * 
 * Admin dialog component for viewing and managing organization members.
 * Follows project standards with dark/light mode support, responsive design, and i18n.
 */
export const OrganizationMembersDialog: React.FC<OrganizationMembersDialogProps> = ({
  isOpen,
  onClose,
  organizationId,
  organizationName,
}) => {
  const intl = useIntl();
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  // Load organization members when dialog opens
  useEffect(() => {
    if (isOpen && organizationId) {
      loadMembers();
    }
  }, [isOpen, organizationId]);

  const loadMembers = async () => {
    if (!organizationId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/admin/organizations/${organizationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load organization details: ${response.status}`);
      }

      const data = await response.json();
      setMembers(data.members || []);
    } catch (err) {
      console.error('Failed to load organization members:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Fallback to mock data
      setMembers([
        {
          id: 'member1',
          user_id: 'user1',
          username: 'john_doe',
          email: '<EMAIL>',
          role: 'owner',
          is_active: true,
          joined_at: '2024-01-15T10:30:00Z',
          permissions: {}
        },
        {
          id: 'member2',
          user_id: 'user2',
          username: 'jane_smith',
          email: '<EMAIL>',
          role: 'admin',
          is_active: true,
          joined_at: '2024-02-10T09:15:00Z',
          permissions: {}
        },
        {
          id: 'member3',
          user_id: 'user3',
          username: 'mike_wilson',
          email: '<EMAIL>',
          role: 'member',
          is_active: false,
          joined_at: '2024-03-05T11:20:00Z',
          permissions: {}
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMemberAction = async (action: string, memberId: string) => {
    setActionMenuOpen(null);
    
    try {
      switch (action) {
        case 'toggle_status':
          // TODO: Implement member status toggle
          console.log('Toggle member status:', memberId);
          break;
        case 'change_role':
          // TODO: Implement role change
          console.log('Change member role:', memberId);
          break;
        case 'remove_member':
          // TODO: Implement member removal
          console.log('Remove member:', memberId);
          break;
      }
    } catch (error) {
      console.error('Failed to perform member action:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusBadgeVariant = (isActive: boolean) => {
    return isActive ? 'default' : 'destructive';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.organizations.members.title' })}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage({ id: 'admin.organizations.members.description' }, { name: organizationName })}
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            {intl.formatMessage({ id: 'common.loading' })}
          </div>
        )}

        {error && (
          <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={loadMembers}
              className="mt-2"
            >
              {intl.formatMessage({ id: 'common.retry' })}
            </Button>
          </div>
        )}

        {!isLoading && (
          <div className="space-y-6">
            {/* Members Summary */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">
                  {intl.formatMessage({ id: 'admin.organizations.members.summary' })}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.organizations.members.count' }, { count: members.length })}
                </p>
              </div>
            </div>

            <Separator />

            {/* Members List */}
            <div className="space-y-4">
              {members.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {intl.formatMessage({ id: 'admin.organizations.members.empty.title' })}
                  </h3>
                  <p className="text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.members.empty.description' })}
                  </p>
                </div>
              ) : (
                members.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getRoleIcon(member.role)}
                        <div>
                          <div className="font-medium">{member.username}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant={getRoleBadgeVariant(member.role)}>
                            {member.role}
                          </Badge>
                          <Badge variant={getStatusBadgeVariant(member.is_active)}>
                            {member.is_active 
                              ? intl.formatMessage({ id: 'admin.organizations.members.status.active' })
                              : intl.formatMessage({ id: 'admin.organizations.members.status.inactive' })
                            }
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {intl.formatMessage({ id: 'admin.organizations.members.joinedOn' })}: {formatDate(member.joined_at)}
                        </div>
                      </div>
                      
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setActionMenuOpen(actionMenuOpen === member.id ? null : member.id)}
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                        
                        {actionMenuOpen === member.id && (
                          <div className="absolute right-0 top-full mt-1 w-48 rounded-md border bg-popover p-1 shadow-sm z-10">
                            <button
                              onClick={() => handleMemberAction('toggle_status', member.id)}
                              className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                            >
                              {member.is_active ? (
                                <>
                                  <UserX className="h-4 w-4" />
                                  {intl.formatMessage({ id: 'admin.organizations.members.actions.deactivate' })}
                                </>
                              ) : (
                                <>
                                  <UserCheck className="h-4 w-4" />
                                  {intl.formatMessage({ id: 'admin.organizations.members.actions.activate' })}
                                </>
                              )}
                            </button>
                            
                            {member.role !== 'owner' && (
                              <>
                                <button
                                  onClick={() => handleMemberAction('change_role', member.id)}
                                  className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                                >
                                  <Shield className="h-4 w-4" />
                                  {intl.formatMessage({ id: 'admin.organizations.members.actions.changeRole' })}
                                </button>
                                
                                <button
                                  onClick={() => handleMemberAction('remove_member', member.id)}
                                  className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-destructive hover:bg-destructive/10"
                                >
                                  <UserX className="h-4 w-4" />
                                  {intl.formatMessage({ id: 'admin.organizations.members.actions.remove' })}
                                </button>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
