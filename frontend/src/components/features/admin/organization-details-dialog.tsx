import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Building2,
  Users,
  Calendar,
  Globe,
  Lock,
  Mail,
  UserCheck,
  Settings,
  Activity,
  X,
  Loader2,
} from 'lucide-react';

interface OrganizationMember {
  id: string;
  user_id: string;
  username: string;
  email: string;
  role: string;
  is_active: boolean;
  joined_at: string;
  invited_at?: string;
  permissions: Record<string, any>;
}

interface OrganizationInvitation {
  id: string;
  email: string;
  role: string;
  status: string;
  created_at: string;
  expires_at: string;
  message?: string;
}

interface OrganizationDetails {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_public: boolean;
  is_active: boolean;
  max_members: number;
  created_at: string;
  updated_at: string;
  settings: Record<string, any>;
  members: OrganizationMember[];
  pending_invitations: OrganizationInvitation[];
  stats: {
    total_members: number;
    active_members: number;
    pending_invitations: number;
  };
}

interface OrganizationDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string | null;
}

/**
 * Organization Details Dialog
 * 
 * Admin dialog component for viewing detailed organization information,
 * including members, invitations, and statistics. Follows project standards
 * with dark/light mode support, responsive design, and i18n.
 */
export const OrganizationDetailsDialog: React.FC<OrganizationDetailsDialogProps> = ({
  isOpen,
  onClose,
  organizationId,
}) => {
  const intl = useIntl();
  const [organizationDetails, setOrganizationDetails] = useState<OrganizationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load organization details when dialog opens
  useEffect(() => {
    if (isOpen && organizationId) {
      loadOrganizationDetails();
    }
  }, [isOpen, organizationId]);

  const loadOrganizationDetails = async () => {
    if (!organizationId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/admin/organizations/${organizationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load organization details: ${response.status}`);
      }

      const data = await response.json();
      setOrganizationDetails(data);
    } catch (err) {
      console.error('Failed to load organization details:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setOrganizationDetails(null);
    setError(null);
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeVariant = (isActive: boolean) => {
    return isActive ? 'default' : 'secondary';
  };

  const getVisibilityBadgeVariant = (isPublic: boolean) => {
    return isPublic ? 'outline' : 'secondary';
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.organizations.details.title' })}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage({ id: 'admin.organizations.details.description' })}
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            {intl.formatMessage({ id: 'common.loading' })}
          </div>
        )}

        {error && (
          <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={loadOrganizationDetails}
              className="mt-2"
            >
              {intl.formatMessage({ id: 'common.retry' })}
            </Button>
          </div>
        )}

        {organizationDetails && (
          <div className="space-y-6">
            {/* Organization Info */}
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">{organizationDetails.name}</h3>
                  <p className="text-sm text-muted-foreground">@{organizationDetails.slug}</p>
                  {organizationDetails.description && (
                    <p className="text-sm">{organizationDetails.description}</p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Badge variant={getStatusBadgeVariant(organizationDetails.is_active)}>
                    {organizationDetails.is_active 
                      ? intl.formatMessage({ id: 'admin.organizations.status.active' })
                      : intl.formatMessage({ id: 'admin.organizations.status.inactive' })
                    }
                  </Badge>
                  <Badge variant={getVisibilityBadgeVariant(organizationDetails.is_public)}>
                    {organizationDetails.is_public 
                      ? intl.formatMessage({ id: 'admin.organizations.visibility.public' })
                      : intl.formatMessage({ id: 'admin.organizations.visibility.private' })
                    }
                  </Badge>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 rounded-lg bg-muted">
                  <div className="text-2xl font-bold">{organizationDetails.stats.total_members}</div>
                  <div className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.totalMembers' })}
                  </div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted">
                  <div className="text-2xl font-bold">{organizationDetails.stats.active_members}</div>
                  <div className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.activeMembers' })}
                  </div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted">
                  <div className="text-2xl font-bold">{organizationDetails.stats.pending_invitations}</div>
                  <div className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.pendingInvitations' })}
                  </div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted">
                  <div className="text-2xl font-bold">{organizationDetails.max_members}</div>
                  <div className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.maxMembers' })}
                  </div>
                </div>
              </div>

              {/* Metadata */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.created' })}:
                  </span>
                  <span>{formatDate(organizationDetails.created_at)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.organizations.details.updated' })}:
                  </span>
                  <span>{formatDate(organizationDetails.updated_at)}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Members Section */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold flex items-center gap-2">
                <Users className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.organizations.details.members' })}
              </h4>
              
              {organizationDetails.members.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.organizations.details.noMembers' })}
                </p>
              ) : (
                <div className="space-y-2">
                  {organizationDetails.members.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <UserCheck className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{member.username}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getRoleBadgeVariant(member.role)}>
                          {member.role}
                        </Badge>
                        {!member.is_active && (
                          <Badge variant="secondary">
                            {intl.formatMessage({ id: 'admin.organizations.details.inactive' })}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Pending Invitations */}
            {organizationDetails.pending_invitations.length > 0 && (
              <>
                <Separator />
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    {intl.formatMessage({ id: 'admin.organizations.details.pendingInvitations' })}
                  </h4>
                  
                  <div className="space-y-2">
                    {organizationDetails.pending_invitations.map((invitation) => (
                      <div key={invitation.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{invitation.email}</div>
                            <div className="text-sm text-muted-foreground">
                              {intl.formatMessage({ id: 'admin.organizations.details.invitedOn' })}: {formatDate(invitation.created_at)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={getRoleBadgeVariant(invitation.role)}>
                            {invitation.role}
                          </Badge>
                          <Badge variant="outline">
                            {invitation.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
