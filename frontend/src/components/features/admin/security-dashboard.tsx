/**
 * Security Dashboard Component
 * 
 * Main security monitoring dashboard for administrators
 * Displays threat overview, recent activity, and key metrics
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Shield, 
  ShieldAlert, 
  ShieldCheck, 
  RefreshCw, 
  TrendingUp, 
  Activity,
  AlertTriangle,
  Eye,
  Clock
} from 'lucide-react';
import { 
  securityApi, 
  getThreatLevelColor,
  getSeverityIcon,
  formatSecurityDate,
  type ThreatDashboard 
} from '@/services/security-api';
import { useToast } from '@/hooks/use-toast';

interface SecurityDashboardProps {
  className?: string;
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({ className }) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [dashboardData, setDashboardData] = useState<ThreatDashboard | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await securityApi.getThreatDashboard();
      setDashboardData(data);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load security dashboard:', error);
      toast({
        title: intl.formatMessage({ id: 'admin.security.message.data.error' }),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getThreatLevelIcon = (level: string) => {
    switch (level) {
      case 'critical':
        return <ShieldAlert className="h-5 w-5 text-red-500" />;
      case 'high':
        return <ShieldAlert className="h-5 w-5 text-orange-500" />;
      case 'medium':
        return <Shield className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <ShieldCheck className="h-5 w-5 text-green-500" />;
      default:
        return <Shield className="h-5 w-5 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-muted-foreground">
            {intl.formatMessage({ id: 'admin.security.common.loading' })}
          </span>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {intl.formatMessage({ id: 'admin.security.error.data.unavailable' })}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {intl.formatMessage({ id: 'admin.security.dashboard.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.security.dashboard.subtitle' })}
          </p>
        </div>
        <div className="flex items-center gap-3">
          {lastRefresh && (
            <span className="text-sm text-muted-foreground">
              {intl.formatMessage({ id: 'admin.security.dashboard.last.updated' })}: {formatSecurityDate(lastRefresh.toISOString())}
            </span>
          )}
          <Button onClick={loadDashboardData} disabled={isLoading} size="sm">
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {intl.formatMessage({ id: 'admin.security.dashboard.refresh' })}
          </Button>
        </div>
      </div>

      {/* Security Overview */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getThreatLevelIcon(dashboardData.overview.threat_level)}
            {intl.formatMessage({ id: 'admin.security.dashboard.overview' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{dashboardData.overview.total_threats}</div>
              <div className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.dashboard.total.threats' })}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{dashboardData.overview.critical_threats}</div>
              <div className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.dashboard.critical.threats' })}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{dashboardData.overview.high_threats}</div>
              <div className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.dashboard.high.threats' })}
              </div>
            </div>
            <div className="text-center">
              <Badge 
                variant="secondary" 
                className={`${getThreatLevelColor(dashboardData.overview.threat_level)} border-0 text-lg px-4 py-2`}
              >
                {intl.formatMessage({ id: `admin.security.threat.level.${dashboardData.overview.threat_level}` })}
              </Badge>
              <div className="text-sm text-muted-foreground mt-1">
                {intl.formatMessage({ id: 'admin.security.dashboard.threat.level' })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Threats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Brute Force Attacks */}
        <Card className="shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5 text-red-500" />
              {intl.formatMessage({ id: 'admin.security.threat.type.brute.force' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'admin.security.time.window.30m' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">{dashboardData.threat_counts.brute_force_30m}</div>
            {dashboardData.recent_threats.brute_force_attacks.length > 0 ? (
              <div className="space-y-2">
                {dashboardData.recent_threats.brute_force_attacks.slice(0, 3).map((attack, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="font-mono">{attack.ip_address}</span>
                    <Badge variant="outline" className="text-xs">
                      {attack.attempt_count} attempts
                    </Badge>
                  </div>
                ))}
                {dashboardData.recent_threats.brute_force_attacks.length > 3 && (
                  <Button variant="link" size="sm" className="p-0 h-auto">
                    {intl.formatMessage({ id: 'admin.security.dashboard.view.all' })}
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.brute.force.empty' })}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Account Takeover Attempts */}
        <Card className="shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Eye className="h-5 w-5 text-orange-500" />
              {intl.formatMessage({ id: 'admin.security.threat.type.account.takeover' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'admin.security.time.window.6h' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">{dashboardData.threat_counts.takeover_attempts_6h}</div>
            {dashboardData.recent_threats.account_takeover_attempts.length > 0 ? (
              <div className="space-y-2">
                {dashboardData.recent_threats.account_takeover_attempts.slice(0, 3).map((attempt, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="font-mono">{attempt.user_id.slice(0, 8)}...</span>
                    <Badge variant="outline" className="text-xs">
                      {attempt.unique_ip_count} IPs
                    </Badge>
                  </div>
                ))}
                {dashboardData.recent_threats.account_takeover_attempts.length > 3 && (
                  <Button variant="link" size="sm" className="p-0 h-auto">
                    {intl.formatMessage({ id: 'admin.security.dashboard.view.all' })}
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.takeover.empty' })}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Suspicious Logins */}
        <Card className="shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              {intl.formatMessage({ id: 'admin.security.threat.type.suspicious.login' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'admin.security.time.window.24h' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">{dashboardData.threat_counts.suspicious_logins_24h}</div>
            {dashboardData.recent_threats.suspicious_logins.length > 0 ? (
              <div className="space-y-2">
                {dashboardData.recent_threats.suspicious_logins.slice(0, 3).map((login, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="font-mono">{login.ip_address}</span>
                    <Badge variant="outline" className="text-xs">
                      {login.failed_attempts} failures
                    </Badge>
                  </div>
                ))}
                {dashboardData.recent_threats.suspicious_logins.length > 3 && (
                  <Button variant="link" size="sm" className="p-0 h-auto">
                    {intl.formatMessage({ id: 'admin.security.dashboard.view.all' })}
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'admin.security.suspicious.login.empty' })}
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      {dashboardData.overview.threat_level === 'low' && (
        <Alert>
          <ShieldCheck className="h-4 w-4" />
          <AlertDescription>
            {intl.formatMessage({ id: 'admin.security.alert.system.healthy' })}
          </AlertDescription>
        </Alert>
      )}

      {dashboardData.overview.threat_level === 'critical' && (
        <Alert variant="destructive">
          <ShieldAlert className="h-4 w-4" />
          <AlertDescription>
            {intl.formatMessage({ id: 'admin.security.alert.critical.threat' })}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
