/**
 * Admin Analytics Dashboard Component
 *
 * Professional admin dashboard with comprehensive data visualization,
 * real-time metrics, and system analytics for GeNieGO SSO Server.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hnut<PERSON><PERSON> } from '@/components/charts';
import { useAdminSystemAnalytics } from '@/hooks/use-admin-api';
import { useIntl } from 'react-intl';
import {
  Users,
  Building2,
  Activity,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  icon, 
  description,
  trend = 'neutral' 
}) => {
  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = () => {
    if (change === undefined) return null;
    return (
      <span className={`text-sm ${getTrendColor()}`}>
        {change > 0 ? '+' : ''}{change}%
      </span>
    );
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
        <div className="flex items-center justify-between">
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {getTrendIcon()}
        </div>
      </CardContent>
    </Card>
  );
};

export const AdminAnalyticsDashboard: React.FC = () => {
  const { analytics, isLoading, error } = useAdminSystemAnalytics();
  const intl = useIntl();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-4 w-4" />
            <span>Failed to load analytics data: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-gray-600">
            <AlertTriangle className="h-4 w-4" />
            <span>No analytics data available</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare chart data with comprehensive null checks
  let activityChartData: any[] = [];
  let applicationStatusData: any[] = [];
  let topApplicationsData: any[] = [];

  try {
    activityChartData = (analytics.activity_timeline || []).map(item => ({
      date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      logins: item.total_logins || 0,
      users: item.new_users || 0,
      applications: item.new_applications || 0,
    }));

    applicationStatusData = [
      { label: intl.formatMessage({ id: 'admin.dashboard.analytics.activeStatus' }), value: analytics.active_applications || 0, color: '#50C878' },
      { label: intl.formatMessage({ id: 'admin.dashboard.analytics.pendingStatus' }), value: analytics.pending_approvals || 0, color: '#FFA500' },
      { label: intl.formatMessage({ id: 'admin.dashboard.analytics.inactiveStatus' }), value: (analytics.total_applications || 0) - (analytics.active_applications || 0) - (analytics.pending_approvals || 0), color: '#DC143C' },
    ];

    topApplicationsData = (analytics.top_applications || []).slice(0, 5);
  } catch (error) {
    console.error('Error preparing chart data:', error);
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-4 w-4" />
            <span>Error processing analytics data</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title={intl.formatMessage({ id: 'admin.dashboard.analytics.totalUsers' })}
          value={analytics.total_users || 0}
          change={analytics.growth_metrics?.users_growth || 0}
          trend={(analytics.growth_metrics?.users_growth || 0) > 0 ? 'up' : 'down'}
          icon={<Users className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'admin.dashboard.analytics.registeredUsers' })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'admin.dashboard.analytics.activeApplications' })}
          value={analytics.active_applications || 0}
          change={analytics.growth_metrics?.applications_growth || 0}
          trend={(analytics.growth_metrics?.applications_growth || 0) > 0 ? 'up' : 'down'}
          icon={<Building2 className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'admin.dashboard.analytics.totalCount' }, { count: analytics.total_applications || 0 })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'admin.dashboard.analytics.dailyLogins' })}
          value={analytics.total_logins_today || 0}
          change={analytics.growth_metrics?.logins_growth || 0}
          trend={(analytics.growth_metrics?.logins_growth || 0) > 0 ? 'up' : 'down'}
          icon={<Activity className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'admin.dashboard.analytics.todaysActivity' })}
        />
        <MetricCard
          title={intl.formatMessage({ id: 'admin.dashboard.analytics.pendingApprovals' })}
          value={analytics.pending_approvals || 0}
          icon={<Clock className="h-4 w-4" />}
          description={intl.formatMessage({ id: 'admin.dashboard.analytics.awaitingReview' })}
        />
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>{intl.formatMessage({ id: 'admin.dashboard.analytics.activityTimeline' })}</span>
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'admin.dashboard.analytics.activityTrends' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LineChart
              labels={activityChartData.map(item => item.date)}
              datasets={[
                {
                  label: intl.formatMessage({ id: 'admin.dashboard.analytics.totalLogins' }),
                  data: activityChartData.map(item => item.logins),
                  borderColor: '#50C878',
                  backgroundColor: 'rgba(80, 200, 120, 0.1)',
                  fill: true,
                },
                {
                  label: intl.formatMessage({ id: 'admin.dashboard.analytics.newUsers' }),
                  data: activityChartData.map(item => item.users),
                  borderColor: '#4F46E5',
                  backgroundColor: 'rgba(79, 70, 229, 0.1)',
                  fill: false,
                },
              ]}
              height={300}
            />
          </CardContent>
        </Card>

        {/* Application Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>{intl.formatMessage({ id: 'admin.dashboard.analytics.applicationStatus' })}</span>
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'admin.dashboard.analytics.statusDistribution' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DoughnutChart
              data={applicationStatusData}
              height={300}
              centerText={intl.formatMessage({ id: 'admin.dashboard.analytics.total' })}
              showPercentages={true}
            />
          </CardContent>
        </Card>
      </div>

      {/* Top Applications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>{intl.formatMessage({ id: 'admin.dashboard.analytics.topApplications' })}</span>
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'admin.dashboard.analytics.topApplicationsDesc' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BarChart
            labels={topApplicationsData.map(item => item.application_name)}
            datasets={[
              {
                label: 'Users',
                data: topApplicationsData.map(item => item.user_count),
                backgroundColor: 'rgba(80, 200, 120, 0.8)',
                borderColor: '#50C878',
              },
              {
                label: 'Logins',
                data: topApplicationsData.map(item => item.login_count),
                backgroundColor: 'rgba(79, 70, 229, 0.8)',
                borderColor: '#4F46E5',
              },
            ]}
            height={300}
          />
        </CardContent>
      </Card>

      {/* System Health Summary */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.weeklyGrowth' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.users' })}</span>
                <Badge variant={(analytics.growth_metrics?.users_growth || 0) > 0 ? "default" : "secondary"}>
                  {(analytics.growth_metrics?.users_growth || 0) > 0 ? '+' : ''}{analytics.growth_metrics?.users_growth || 0}%
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.applications' })}</span>
                <Badge variant={(analytics.growth_metrics?.applications_growth || 0) > 0 ? "default" : "secondary"}>
                  {(analytics.growth_metrics?.applications_growth || 0) > 0 ? '+' : ''}{analytics.growth_metrics?.applications_growth || 0}%
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.logins' })}</span>
                <Badge variant={(analytics.growth_metrics?.logins_growth || 0) > 0 ? "default" : "secondary"}>
                  {(analytics.growth_metrics?.logins_growth || 0) > 0 ? '+' : ''}{analytics.growth_metrics?.logins_growth || 0}%
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.systemStatus' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.apiServicesOnline' })}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.databaseConnected' })}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.authenticationActive' })}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{intl.formatMessage({ id: 'admin.dashboard.analytics.quickStats' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.developers' })}</span>
                <span className="text-sm font-medium">{analytics.total_developers || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.weekLogins' })}</span>
                <span className="text-sm font-medium">{(analytics.total_logins_week || 0).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.analytics.monthLogins' })}</span>
                <span className="text-sm font-medium">{(analytics.total_logins_month || 0).toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminAnalyticsDashboard;
