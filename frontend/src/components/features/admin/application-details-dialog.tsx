import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Badge } from '@/components/ui/shadcn/badge';
import { Button } from '@/components/ui/shadcn/button';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Monitor,
  User,
  Calendar,
  Activity,
  CheckCircle,
  XCircle,
  Power,
  PowerOff,
  Trash2,
  Globe,
  Lock,
  Key,
  Settings,
  AlertTriangle,
} from 'lucide-react';

interface AdminApplication {
  id: string;
  name: string;
  clientId: string;
  description: string;
  redirectUris: string[];
  scopes: string[];
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  developer: {
    id: string;
    username: string;
    email: string;
  };
  usage: {
    totalUsers: number;
    activeUsers: number;
    requestsToday: number;
    requestsThisMonth: number;
  };
}

interface ApplicationDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  application: AdminApplication | null;
  onApplicationAction: (action: string, applicationId: string) => void;
}

/**
 * Application Details Dialog
 * 
 * Admin dialog component for viewing detailed application information and performing
 * application management actions. Follows project standards with dark/light mode support,
 * responsive design, and i18n.
 */
export const ApplicationDetailsDialog: React.FC<ApplicationDetailsDialogProps> = ({
  isOpen,
  onClose,
  application,
  onApplicationAction,
}) => {
  const intl = useIntl();
  const [isPerformingAction, setIsPerformingAction] = useState(false);

  if (!application) return null;

  const handleAction = async (action: string) => {
    setIsPerformingAction(true);
    try {
      await onApplicationAction(action, application.id);
      if (action !== 'view_details') {
        onClose(); // Close dialog after actions except view details
      }
    } finally {
      setIsPerformingAction(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'rejected':
        return 'destructive';
      case 'suspended':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-3 w-3" />;
      case 'pending':
        return <AlertTriangle className="h-3 w-3" />;
      case 'rejected':
        return <XCircle className="h-3 w-3" />;
      case 'suspended':
        return <PowerOff className="h-3 w-3" />;
      default:
        return <AlertTriangle className="h-3 w-3" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return intl.formatMessage({ id: 'admin.applications.status.approved' });
      case 'pending':
        return intl.formatMessage({ id: 'admin.applications.status.pending' });
      case 'rejected':
        return intl.formatMessage({ id: 'admin.applications.status.rejected' });
      case 'suspended':
        return intl.formatMessage({ id: 'admin.applications.status.suspended' });
      default:
        return status;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.applications.details.title' })}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage({ id: 'admin.applications.details.description' })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Application Basic Info */}
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">{application.name}</h3>
                <p className="text-sm text-muted-foreground">{application.description}</p>
              </div>
              <div className="flex gap-2">
                <Badge variant={getStatusBadgeVariant(application.status)}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(application.status)}
                    {getStatusText(application.status)}
                  </div>
                </Badge>
                {!application.isActive && (
                  <Badge variant="destructive">
                    {intl.formatMessage({ id: 'admin.applications.status.inactive' })}
                  </Badge>
                )}
              </div>
            </div>

            {/* Application Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Key className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.clientId' })}:
                </span>
                <code className="bg-muted px-2 py-1 rounded text-xs">{application.clientId}</code>
              </div>
              
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.developer' })}:
                </span>
                <span>{application.developer.username}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.created' })}:
                </span>
                <span>{formatDate(application.createdAt)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.updated' })}:
                </span>
                <span>{formatDate(application.updatedAt)}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Usage Statistics */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {intl.formatMessage({ id: 'admin.applications.details.usage' })}
            </h4>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 rounded-lg bg-muted">
                <div className="text-2xl font-bold">{application.usage.totalUsers}</div>
                <div className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.totalUsers' })}
                </div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted">
                <div className="text-2xl font-bold">{application.usage.activeUsers}</div>
                <div className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.activeUsers' })}
                </div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted">
                <div className="text-2xl font-bold">{application.usage.requestsToday}</div>
                <div className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.requestsToday' })}
                </div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted">
                <div className="text-2xl font-bold">{application.usage.requestsThisMonth}</div>
                <div className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.requestsThisMonth' })}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Technical Details */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {intl.formatMessage({ id: 'admin.applications.details.technical' })}
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.redirectUris' })}
                </label>
                <div className="mt-1 space-y-1">
                  {application.redirectUris.map((uri, index) => (
                    <code key={index} className="block bg-muted px-3 py-2 rounded text-xs">
                      {uri}
                    </code>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.applications.details.scopes' })}
                </label>
                <div className="mt-1 flex flex-wrap gap-2">
                  {application.scopes.map((scope) => (
                    <Badge key={scope} variant="outline">
                      {scope}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            {application.status === 'pending' && (
              <>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleAction('approve')}
                  disabled={isPerformingAction}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  {intl.formatMessage({ id: 'admin.applications.actions.approve' })}
                </Button>
                
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleAction('reject')}
                  disabled={isPerformingAction}
                  className="flex items-center gap-2"
                >
                  <XCircle className="h-4 w-4" />
                  {intl.formatMessage({ id: 'admin.applications.actions.reject' })}
                </Button>
              </>
            )}
            
            {application.status === 'approved' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAction('suspend')}
                disabled={isPerformingAction}
                className="flex items-center gap-2"
              >
                <PowerOff className="h-4 w-4" />
                {intl.formatMessage({ id: 'admin.applications.actions.suspend' })}
              </Button>
            )}
            
            {application.status === 'suspended' && (
              <Button
                variant="default"
                size="sm"
                onClick={() => handleAction('reactivate')}
                disabled={isPerformingAction}
                className="flex items-center gap-2"
              >
                <Power className="h-4 w-4" />
                {intl.formatMessage({ id: 'admin.applications.actions.reactivate' })}
              </Button>
            )}
            
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleAction('delete')}
              disabled={isPerformingAction}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              {intl.formatMessage({ id: 'admin.applications.actions.delete' })}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
