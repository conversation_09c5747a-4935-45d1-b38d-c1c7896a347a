/**
 * MFA Verification Component
 * 
 * Component for verifying <PERSON><PERSON> during login flow
 * Supports TOTP and backup code verification
 */

import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Shield, 
  Smartphone, 
  Key, 
  ArrowLeft,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MFAVerificationProps {
  sessionToken: string;
  availableMethods?: string[];
  onSuccess: (response: any) => void;
  onCancel?: () => void;
}

type VerificationMethod = 'totp' | 'backup_code';

export const MFAVerification: React.FC<MFAVerificationProps> = ({
  sessionToken,
  availableMethods,
  onSuccess,
  onCancel
}) => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [method, setMethod] = useState<VerificationMethod>('totp');
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      setError(intl.formatMessage({ id: 'user.twofa.error.invalid.code' }));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Mock API call - replace with actual MFA verification
      const response = { success: true, token: 'mock-token' };
      
      if (response.success) {
        toast({
          title: intl.formatMessage({ id: 'user.twofa.message.verify.success' }),
          variant: 'default',
        });
        onSuccess(response);
      } else {
        setError(intl.formatMessage({ id: 'user.twofa.error.verification.failed' }));
      }
    } catch (err: any) {
      setError(err.message || intl.formatMessage({ id: 'user.twofa.error.verification.failed' }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCodeChange = (value: string) => {
    setCode(value);
    if (error) setError(null);
  };

  const switchMethod = () => {
    setMethod(method === 'totp' ? 'backup_code' : 'totp');
    setCode('');
    setError(null);
  };

  const renderTOTPVerification = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Smartphone className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-xl font-semibold">{intl.formatMessage({ id: 'user.twofa.verify.title' })}</h2>
        <p className="text-muted-foreground text-sm mt-1">
          {intl.formatMessage({ id: 'user.twofa.verify.description' })}
        </p>
      </div>

      <div>
        <Label htmlFor="totp-code">{intl.formatMessage({ id: 'user.twofa.verify.code' })}</Label>
        <Input
          id="totp-code"
          type="text"
          placeholder={intl.formatMessage({ id: 'user.twofa.verify.code.placeholder' })}
          value={code}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCodeChange(e.target.value)}
          className="mt-1 text-center text-lg font-mono tracking-widest"
          maxLength={6}
          autoComplete="one-time-code"
        />
      </div>

      {availableMethods?.includes('backup_code') && (
        <>
          <Separator />
          <Button
            type="button"
            variant="link"
            onClick={switchMethod}
            className="w-full text-sm"
          >
            {intl.formatMessage({ id: 'user.twofa.verify.backup.link' })}
          </Button>
        </>
      )}
    </div>
  );

  const renderBackupCodeVerification = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Key className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-xl font-semibold">{intl.formatMessage({ id: 'user.twofa.verify.backup.title' })}</h2>
        <p className="text-muted-foreground text-sm mt-1">
          {intl.formatMessage({ id: 'user.twofa.verify.backup.description' })}
        </p>
      </div>

      <div>
        <Label htmlFor="backup-code">{intl.formatMessage({ id: 'user.twofa.verify.backup.code' })}</Label>
        <Input
          id="backup-code"
          type="text"
          placeholder={intl.formatMessage({ id: 'user.twofa.verify.backup.code.placeholder' })}
          value={code}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCodeChange(e.target.value)}
          className="mt-1 text-center text-lg font-mono tracking-widest"
          maxLength={8}
          autoComplete="one-time-code"
        />
      </div>

      <Button
        type="button"
        variant="link"
        onClick={switchMethod}
        className="w-full text-sm"
      >
        {intl.formatMessage({ id: 'user.twofa.verify.totp.link' })}
      </Button>
    </div>
  );

  return (
    <Card className="w-full max-w-md mx-auto shadow-sm">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-2">
          <Shield className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.twofa.verify.title' })}</CardTitle>
        <CardDescription>{intl.formatMessage({ id: 'user.twofa.verify.subtitle' })}</CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {method === 'totp' ? renderTOTPVerification() : renderBackupCodeVerification()}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button
              type="submit"
              disabled={isLoading || !code.trim()}
              className="w-full"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {intl.formatMessage({ id: 'common.loading' })}
                </div>
              ) : (
                method === 'totp' ? intl.formatMessage({ id: 'user.twofa.verify.submit' }) : intl.formatMessage({ id: 'user.twofa.verify.backup.submit' })
              )}
            </Button>

            {onCancel && (
              <Button 
                type="button"
                variant="outline"
                onClick={onCancel}
                className="w-full"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {intl.formatMessage({ id: 'common.cancel' })}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
