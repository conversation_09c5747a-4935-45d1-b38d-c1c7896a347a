import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { ScrollArea } from '@/components/ui/shadcn/scroll-area';
import { 
  LogOut, 
  Monitor, 
  Smartphone, 
  Globe, 
  AlertTriangle,
  Loader2 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConnectedApp {
  id: string;
  name: string;
  icon?: string;
  lastAccessed?: string;
}

interface ActiveSession {
  id: string;
  device_fingerprint?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  last_activity: string;
  connected_applications: number;
  applications: Array<{
    id: string;
    oauth_token_id: string;
    created_at: string;
  }>;
}

interface LogoutConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
}

/**
 * LogoutConfirmationModal Component
 * 
 * Displays a confirmation modal before logout that shows:
 * - Connected applications that will be logged out
 * - Active sessions information
 * - Warning about Single Logout (SLO) behavior
 */
export const LogoutConfirmationModal: React.FC<LogoutConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}) => {
  const intl = useIntl();
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [loadingSessions, setLoadingSessions] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch active sessions when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchActiveSessions();
    }
  }, [isOpen]);

  const fetchActiveSessions = async () => {
    setLoadingSessions(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/sessions/active', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const sessions = await response.json();
        setActiveSessions(sessions);
      } else {
        console.warn('Failed to fetch active sessions');
        setActiveSessions([]);
      }
    } catch (error) {
      console.error('Error fetching active sessions:', error);
      setError('Failed to load session information');
      setActiveSessions([]);
    } finally {
      setLoadingSessions(false);
    }
  };

  const getDeviceIcon = (userAgent?: string) => {
    if (!userAgent) return <Monitor className="h-4 w-4" />;
    
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return <Smartphone className="h-4 w-4" />;
    }
    return <Monitor className="h-4 w-4" />;
  };

  const getDeviceName = (userAgent?: string) => {
    if (!userAgent) return 'Unknown Device';
    
    const ua = userAgent.toLowerCase();
    if (ua.includes('chrome')) return 'Chrome Browser';
    if (ua.includes('firefox')) return 'Firefox Browser';
    if (ua.includes('safari')) return 'Safari Browser';
    if (ua.includes('edge')) return 'Edge Browser';
    return 'Web Browser';
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Unknown';
    }
  };

  const totalConnectedApps = activeSessions.reduce(
    (total, session) => total + session.connected_applications, 
    0
  );

  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <LogOut className="h-5 w-5" />
            {intl.formatMessage(
              { id: 'auth.logout.confirm.title' },
              { defaultMessage: 'Confirm Logout' }
            )}
          </DialogTitle>
          <DialogDescription>
            {intl.formatMessage(
              { id: 'auth.logout.confirm.description' },
              { 
                defaultMessage: 'You will be logged out from all connected applications and devices.',
              }
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning about SLO */}
          <div className="flex items-start gap-3 rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950">
            <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-amber-800 dark:text-amber-200">
                {intl.formatMessage(
                  { id: 'auth.logout.slo.warning.title' },
                  { defaultMessage: 'Single Logout (SLO)' }
                )}
              </p>
              <p className="text-amber-700 dark:text-amber-300 mt-1">
                {intl.formatMessage(
                  { id: 'auth.logout.slo.warning.description' },
                  { 
                    defaultMessage: 'This will log you out from all applications and devices using this SSO account.',
                  }
                )}
              </p>
            </div>
          </div>

          {/* Session Information */}
          {loadingSessions ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">
                {intl.formatMessage(
                  { id: 'auth.logout.loading.sessions' },
                  { defaultMessage: 'Loading session information...' }
                )}
              </span>
            </div>
          ) : error ? (
            <div className="text-sm text-muted-foreground text-center py-2">
              {error}
            </div>
          ) : activeSessions.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {intl.formatMessage(
                    { id: 'auth.logout.active.sessions' },
                    { defaultMessage: 'Active Sessions' }
                  )}
                </span>
                <Badge variant="secondary">
                  {activeSessions.length}
                </Badge>
              </div>

              <ScrollArea className="max-h-32">
                <div className="space-y-2">
                  {activeSessions.map((session) => (
                    <div
                      key={session.id}
                      className="flex items-center gap-3 rounded-md border p-2"
                    >
                      {getDeviceIcon(session.user_agent)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {getDeviceName(session.user_agent)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {session.ip_address} • {formatDate(session.last_activity)}
                        </p>
                      </div>
                      {session.connected_applications > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {session.connected_applications} apps
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {totalConnectedApps > 0 && (
                <>
                  <Separator />
                  <div className="flex items-center gap-2 text-sm">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {intl.formatMessage(
                        { 
                          id: 'auth.logout.connected.apps.count',
                          defaultMessage: '{count} connected {count, plural, one {application} other {applications}}',
                        },
                        { count: totalConnectedApps }
                      )}
                    </span>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="text-sm text-muted-foreground text-center py-2">
              {intl.formatMessage(
                { id: 'auth.logout.no.active.sessions' },
                { defaultMessage: 'No active sessions found' }
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {intl.formatMessage(
              { id: 'common.cancel' },
              { defaultMessage: 'Cancel' }
            )}
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isLoading}
            className="min-w-[100px]"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {intl.formatMessage(
                  { id: 'auth.logout.logging.out' },
                  { defaultMessage: 'Logging out...' }
                )}
              </>
            ) : (
              <>
                <LogOut className="h-4 w-4 mr-2" />
                {intl.formatMessage(
                  { id: 'auth.logout.confirm.button' },
                  { defaultMessage: 'Logout' }
                )}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
