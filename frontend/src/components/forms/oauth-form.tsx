import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { Shield, ExternalLink, AlertTriangle, CheckCircle, X } from 'lucide-react';

interface OAuthScope {
  name: string;
  description: string;
  required: boolean;
}

interface OAuthClient {
  clientId: string;
  name: string;
  description: string;
  website?: string;
  logoUrl?: string;
  scopes: OAuthScope[];
}

interface OAuthFormProps {
  onAuthorize?: (clientId: string, scopes: string[]) => void;
  onDeny?: () => void;
}

export const OAuthForm: React.FC<OAuthFormProps> = ({ onAuthorize, onDeny }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [client, setClient] = useState<OAuthClient | null>(null);
  const [selectedScopes, setSelectedScopes] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Extract OAuth parameters from URL
  const clientId = searchParams.get('client_id');
  const redirectUri = searchParams.get('redirect_uri');
  const scope = searchParams.get('scope');
  const state = searchParams.get('state');
  const responseType = searchParams.get('response_type');

  useEffect(() => {
    const fetchClientInfo = async () => {
      if (!clientId) {
        setError('Missing client_id parameter');
        setIsLoading(false);
        return;
      }

      if (!redirectUri) {
        setError('Missing redirect_uri parameter');
        setIsLoading(false);
        return;
      }

      if (responseType !== 'code') {
        setError('Invalid response_type. Only "code" is supported');
        setIsLoading(false);
        return;
      }

      try {
        // TODO: Replace with actual API call when backend endpoints are implemented
        // const response = await fetch(`/api/v1/oauth2/clients/${clientId}`);
        // if (!response.ok) throw new Error('Client not found');
        // const clientData = await response.json();

        // Client data loaded from OAuth2 request parameters
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockClient: OAuthClient = {
          clientId: clientId,
          name: clientId === 'genie-move-web' ? 'GenieMove' : 
                clientId === 'genie-chat-app' ? 'GenieChat' : 
                clientId === 'genie-analytics-beta' ? 'GenieAnalytics' : 'Unknown Application',
          description: clientId === 'genie-move-web' ? 'Transportation and logistics management platform' :
                      clientId === 'genie-chat-app' ? 'Real-time messaging and communication platform' :
                      clientId === 'genie-analytics-beta' ? 'Business intelligence and analytics dashboard' : 
                      'Third-party application requesting access',
          website: clientId === 'genie-move-web' ? 'https://move.genieland.ai' : 
                  clientId === 'genie-chat-app' ? 'https://chat.genieland.ai' : 
                  clientId === 'genie-analytics-beta' ? 'https://analytics.genieland.ai' : undefined,
          scopes: [
            { name: 'openid', description: 'Access your basic identity information', required: true },
            { name: 'profile', description: 'Access your profile information (name, username)', required: true },
            { name: 'email', description: 'Access your email address', required: false }
          ]
        };

        setClient(mockClient);
        
        // Set initial selected scopes (all required scopes + requested scopes)
        const requestedScopes = scope ? scope.split(' ') : [];
        const requiredScopes = mockClient.scopes.filter(s => s.required).map(s => s.name);
        const initialScopes = [...new Set([...requiredScopes, ...requestedScopes])];
        setSelectedScopes(initialScopes);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load client information');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientInfo();
  }, [clientId, redirectUri, responseType, scope]);

  const handleScopeToggle = (scopeName: string) => {
    const scopeInfo = client?.scopes.find(s => s.name === scopeName);
    if (scopeInfo?.required) return; // Can't toggle required scopes

    setSelectedScopes(prev => 
      prev.includes(scopeName) 
        ? prev.filter(s => s !== scopeName)
        : [...prev, scopeName]
    );
  };

  const handleAuthorize = async () => {
    if (!client || !clientId) return;

    try {
      setIsLoading(true);
      
      // TODO: Replace with actual API call when backend endpoints are implemented
      // const response = await fetch('/api/v1/oauth2/authorize', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     client_id: clientId,
      //     redirect_uri: redirectUri,
      //     scope: selectedScopes.join(' '),
      //     state: state,
      //     response_type: responseType
      //   })
      // });

      // if (!response.ok) throw new Error('Authorization failed');
      // const { authorization_code, redirect_url } = await response.json();

      // Mock authorization
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockAuthCode = 'auth_' + Math.random().toString(36).substring(2, 15);
      const redirectUrl = `${redirectUri}?code=${mockAuthCode}${state ? `&state=${state}` : ''}`;
      
      onAuthorize?.(clientId, selectedScopes);
      window.location.href = redirectUrl;
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Authorization failed');
      setIsLoading(false);
    }
  };

  const handleDeny = () => {
    onDeny?.();
    const errorUrl = `${redirectUri}?error=access_denied${state ? `&state=${state}` : ''}`;
    window.location.href = errorUrl;
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading application details...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Authorization Error</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={() => navigate('/login')} className="w-full">
            Back to Login
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!client) return null;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <Shield className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-xl">Authorize Application</CardTitle>
        <CardDescription>
          <strong>{client.name}</strong> is requesting access to your account
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Application Info */}
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">Application Details:</div>
          <div className="rounded-lg border p-3 space-y-2">
            <div className="font-medium">{client.name}</div>
            <div className="text-sm text-muted-foreground">{client.description}</div>
            {client.website && (
              <div className="flex items-center space-x-1 text-sm">
                <ExternalLink className="h-3 w-3" />
                <a href={client.website} target="_blank" rel="noopener noreferrer" 
                   className="text-primary hover:underline">
                  {client.website}
                </a>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Permissions */}
        <div className="space-y-3">
          <div className="text-sm font-medium">This application will be able to:</div>
          <div className="space-y-2">
            {client.scopes.map((scope) => (
              <div key={scope.name} className="flex items-start space-x-3">
                <div className="flex items-center space-x-2 flex-1">
                  {scope.required ? (
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  ) : (
                    <button
                      onClick={() => handleScopeToggle(scope.name)}
                      className="mt-0.5"
                    >
                      {selectedScopes.includes(scope.name) ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <div className="h-4 w-4 border-2 border-muted-foreground rounded-sm" />
                      )}
                    </button>
                  )}
                  <div className="flex-1">
                    <div className="text-sm font-medium">{scope.description}</div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {scope.name}
                      </Badge>
                      {scope.required && (
                        <Badge variant="secondary" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button 
            onClick={handleAuthorize} 
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? 'Authorizing...' : 'Authorize'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleDeny}
            className="w-full"
            disabled={isLoading}
          >
            <X className="mr-2 h-4 w-4" />
            Deny Access
          </Button>
        </div>

        <div className="text-xs text-muted-foreground text-center">
          By authorizing, you allow this application to access your account according to the permissions above.
        </div>
      </CardContent>
    </Card>
  );
};

export default OAuthForm;
