import React, { useState } from 'react';
import { useAuth } from '../../contexts/auth-context';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { Button } from '../ui/shadcn/button';


interface LoginFormProps {
  redirectTo?: string;
}

export function LoginForm({ redirectTo = '/' }: LoginFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const intl = useIntl();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login(email, password);
      navigate(redirectTo);
    } catch (err) {
      setError(err instanceof Error ? err.message : intl.formatMessage({ id: 'auth.loginForm.loginFailed' }));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google OAuth login
  const handleGoogleLogin = () => {
    // For regular login page, we don't have OAuth2 parameters, so use simple Google login
    const googleLoginUrl = `/api/v1/auth/google`;
    window.location.href = googleLoginUrl;
  };

  // Quick login buttons for demo
  const handleQuickLogin = async (userType: 'admin' | 'developer' | 'user') => {
    setError('');
    setIsLoading(true);

    try {
      if (userType === 'admin') {
        await login('<EMAIL>', 'GenieAdmin123!');
        navigate('/admin'); // Admin should go to /admin
      } else if (userType === 'developer') {
        await login('<EMAIL>', 'GenieDev123!');
        navigate('/developer'); // Developer should go to /developer
      } else {
        await login('<EMAIL>', 'GenieUser123!');
        navigate('/dashboard'); // User should go to /dashboard
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : intl.formatMessage({ id: 'auth.loginForm.loginFailed' }));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="animate-fadeIn relative mx-auto w-full rounded-lg border bg-card text-card-foreground shadow-sm">
      <div className="flex flex-col space-y-1 p-6 sm:p-8 pt-8">
        <h3 className="animate-slideUp text-2xl font-semibold leading-none tracking-tight">
          {intl.formatMessage({ id: 'auth.signIn' })}
        </h3>
        <p className="animate-slideUp animation-delay-100 text-sm text-muted-foreground mt-1">
          {intl.formatMessage({ id: 'auth.welcomeDescription' })}
        </p>
      </div>
      <div className="p-6 sm:p-8 pt-2">
        <form id="login-form" onSubmit={handleSubmit} className="grid gap-3">
          {error && (
            <div className="animate-fadeIn relative w-full rounded-lg border border-destructive/50 p-4 text-destructive [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg+div]:translate-y-[-3px] [&:has(svg)]:pl-11">
              <h5 className="mb-1 font-medium leading-none tracking-tight">{error}</h5>
            </div>
          )}
          <div className="animate-fadeIn animation-delay-100 grid gap-2">
            <label htmlFor="email" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {intl.formatMessage({ id: 'auth.loginForm.emailLabel' })}
            </label>
            <input
              id="email"
              type="email"
              placeholder={intl.formatMessage({ id: 'auth.loginForm.emailPlaceholder' })}
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
              className="flex h-11 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
          </div>
          <div className="animate-fadeIn animation-delay-200 grid gap-2">
            <label htmlFor="password" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {intl.formatMessage({ id: 'auth.loginForm.passwordLabel' })}
            </label>
            <input
              id="password"
              type="password"
              placeholder={intl.formatMessage({ id: 'auth.loginForm.passwordPlaceholder' })}
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              className="flex h-11 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
          </div>
          <button
            type="submit"
            className="shimmer-effect animate-fadeIn animation-delay-200 w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 px-4 py-2 mt-2"
            disabled={isLoading}
          >
            {isLoading
              ? intl.formatMessage({ id: 'auth.loginForm.signingIn' })
              : intl.formatMessage({ id: 'auth.loginForm.signIn' })
            }
          </button>
        </form>

        {/* Google Login Section */}
        <div className="animate-fadeIn animation-delay-300 relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">{intl.formatMessage({ id: 'auth.orContinueWith' })}</span>
          </div>
        </div>

        <button
          type="button"
          onClick={handleGoogleLogin}
          className="animate-fadeIn animation-delay-350 w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 px-4 py-2 mb-6"
          disabled={isLoading}
        >
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {intl.formatMessage({ id: 'auth.continueWithGoogle' })}
        </button>

        <div className="animate-fadeIn animation-delay-400 relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">Quick Login for Demo</span>
          </div>
        </div>

        <div className="animate-fadeIn animation-delay-400 grid gap-2">
          <Button
            type="button"
            variant="outline"
            className="w-full h-11"
            onClick={() => handleQuickLogin('admin')}
            disabled={isLoading}
          >
            Login as Admin
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full h-11"
            onClick={() => handleQuickLogin('developer')}
            disabled={isLoading}
          >
            Login as Developer
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full h-11"
            onClick={() => handleQuickLogin('user')}
            disabled={isLoading}
          >
            Login as User
          </Button>
        </div>
      </div>
    </div>
  );
}
