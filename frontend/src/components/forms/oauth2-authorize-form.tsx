import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Shield, User, Mail, CheckCircle } from 'lucide-react';
import { useIntl } from 'react-intl';

/**
 * OAuth2 Authorize Form Component
 *
 * Clean design for OAuth2 authorization consent with proper i18n support
 * for English and Chinese.
 *
 * @returns {JSX.Element} The OAuth2 Authorize Form component
 */
export function OAuth2AuthorizeForm() {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<{
    firstName: string;
    lastName: string;
    email: string;
    username: string;
  } | null>(null);

  // Extract OAuth2 parameters from URL
  const clientId = searchParams.get('client_id');
  const redirectUri = searchParams.get('redirect_uri');
  const responseType = searchParams.get('response_type');
  const scope = searchParams.get('scope') || 'openid profile';
  const state = searchParams.get('state');
  const codeChallenge = searchParams.get('code_challenge');
  const codeChallengeMethod = searchParams.get('code_challenge_method');

  // Parse scopes
  const scopes = scope.split(' ').filter(Boolean);

  // Mock user info - in real implementation, this would come from the session
  useEffect(() => {
    // Simulate fetching current user info
    setUserInfo({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      username: 'johndoe'
    });
  }, []);

  const getScopeDescription = (scopeName: string) => {
    switch (scopeName) {
      case 'openid':
        return intl.formatMessage({ id: 'auth.scope.openid' });
      case 'profile':
        return intl.formatMessage({ id: 'auth.scope.profile' });
      case 'email':
        return intl.formatMessage({ id: 'auth.scope.email' });
      default:
        return `${scopeName} access`;
    }
  };

  const getScopeIcon = (scopeName: string) => {
    switch (scopeName) {
      case 'openid':
        return <Shield className="h-4 w-4" />;
      case 'profile':
        return <User className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const handleAuthorize = async () => {
    setIsLoading(true);

    try {
      // Use traditional form submission
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/oauth2/authorize';
      
      // Add form fields
      const fields: Record<string, string> = {
        'authorize': 'true',
      };

      // Add OAuth2 parameters
      if (clientId) fields['client_id'] = clientId;
      if (redirectUri) fields['redirect_uri'] = redirectUri;
      if (responseType) fields['response_type'] = responseType;
      if (scope) fields['scope'] = scope;
      if (state) fields['state'] = state;
      if (codeChallenge) fields['code_challenge'] = codeChallenge;
      if (codeChallengeMethod) fields['code_challenge_method'] = codeChallengeMethod;

      // Create hidden inputs
      Object.entries(fields).forEach(([name, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      
    } catch (err) {
      setIsLoading(false);
    }
  };

  const handleDeny = async () => {
    setIsLoading(true);

    try {
      // Redirect back with error
      const errorParams = new URLSearchParams({
        error: 'access_denied',
        error_description: 'User denied the request',
        ...(state && { state })
      });

      if (redirectUri) {
        window.location.href = `${redirectUri}?${errorParams.toString()}`;
      }
      
    } catch (err) {
      setIsLoading(false);
    }
  };

  if (!clientId || !redirectUri) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-card rounded-lg border p-6 shadow-sm">
          <div className="text-center">
            <div className="text-destructive mb-4">
              <Shield className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-2">
              {intl.formatMessage({ id: 'auth.error.invalidRequest' })}
            </h2>
            <p className="text-sm text-muted-foreground">
              {intl.formatMessage({ id: 'auth.error.missingParameters' })}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-card rounded-lg border p-6 shadow-sm">
        {/* Header */}
        <div className="mb-6 text-center">
          <div className="text-primary mb-4">
            <Shield className="h-12 w-12 mx-auto" />
          </div>
          <h2 className="text-xl font-semibold text-foreground">
            {intl.formatMessage({ id: 'auth.authorizationRequest' })}
          </h2>
          <p className="text-sm text-muted-foreground mt-2">
            {intl.formatMessage({ id: 'auth.applicationRequestsAccess' })}
          </p>
        </div>

        {/* Application Info */}
        <div className="mb-6 p-4 bg-muted/50 rounded-lg">
          <h3 className="font-medium text-foreground mb-2">
            {intl.formatMessage({ id: 'auth.application' })}
          </h3>
          <p className="text-sm text-muted-foreground">
            <strong>{clientId}</strong>
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            {redirectUri}
          </p>
        </div>

        {/* User Info */}
        {userInfo && (
          <div className="mb-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
            <h3 className="font-medium text-foreground mb-2">
              {intl.formatMessage({ id: 'auth.signedInAs' })}
            </h3>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm font-medium text-foreground">
                  {userInfo.firstName} {userInfo.lastName}
                </p>
                <p className="text-xs text-muted-foreground">
                  {userInfo.email}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Permissions */}
        <div className="mb-6">
          <h3 className="font-medium text-foreground mb-3">
            {intl.formatMessage({ id: 'auth.requestedPermissions' })}
          </h3>
          <div className="space-y-2">
            {scopes.map((scopeName) => (
              <div key={scopeName} className="flex items-center space-x-3 p-2 rounded-md bg-muted/30">
                <div className="text-primary">
                  {getScopeIcon(scopeName)}
                </div>
                <span className="text-sm text-foreground">
                  {getScopeDescription(scopeName)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Security Notice */}
        <div className="mb-6 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <p className="text-xs text-amber-800">
            <strong>{intl.formatMessage({ id: 'auth.securityNotice' })}:</strong>{' '}
            {intl.formatMessage({ id: 'auth.onlyAuthorizeAppsYouTrust' })}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleDeny}
            disabled={isLoading}
            className="flex-1 bg-secondary text-secondary-foreground hover:bg-secondary/80 disabled:opacity-50 disabled:cursor-not-allowed py-2 px-4 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {intl.formatMessage({ id: 'auth.deny' })}
          </button>
          <button
            type="button"
            onClick={handleAuthorize}
            disabled={isLoading}
            className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed py-2 px-4 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {isLoading 
              ? intl.formatMessage({ id: 'auth.authorizing' })
              : intl.formatMessage({ id: 'auth.authorize' })
            }
          </button>
        </div>
      </div>
    </div>
  );
}
