import React, { useState } from 'react';
import { useSearch<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';
import { useIntl } from 'react-intl';


interface OAuth2LoginFormProps {
  showBackButton?: boolean;
}

/**
 * OAuth2 Login Form Component
 *
 * Clean design without inappropriate system information,
 * with proper i18n support for English and Chinese.
 * Enhanced with increased vertical spacing for better UX.
 *
 * @param {OAuth2LoginFormProps} props - Component props
 * @returns {JSX.Element} The OAuth2 Login Form component
 */
export function OAuth2LoginForm({ showBackButton = true }: OAuth2LoginFormProps) {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const [username, setUsername] = useState('<EMAIL>');
  const [password, setPassword] = useState('GenieAdmin123!');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Extract OAuth2 parameters from URL - provide defaults for testing
  // Use 'system' as default client for GeNieGO SSO system's own demo login
  const clientId = searchParams.get('client_id') || 'system';
  const redirectUri = searchParams.get('redirect_uri') || 'http://localhost:5550/auth/callback';
  const responseType = searchParams.get('response_type') || 'code';
  const scope = searchParams.get('scope') || 'openid profile';
  const state = searchParams.get('state') || 'test-state-' + Date.now();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // First authenticate the user using the auth API
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: username, // OAuth2 form uses username field for email
          password: password
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Login failed');
      }

      await response.json(); // Consume response

      // After successful authentication, redirect to OAuth2 authorize endpoint
      const params = new URLSearchParams({
        response_type: responseType,
        client_id: clientId,
        redirect_uri: redirectUri,
        scope: scope,
        state: state
      });

      // Redirect to OAuth2 authorize endpoint (GET request)
      window.location.href = `/oauth2/authorize?${params.toString()}`;

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Login failed');
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    window.history.back(); // Navigate to the previous page in history
  };



  // Handle Google OAuth login
  const handleGoogleLogin = () => {
    const params = new URLSearchParams();
    
    // Pass through OAuth2 parameters if present
    if (clientId !== 'system') params.append('client_id', clientId);
    if (redirectUri !== 'http://localhost:5550/auth/callback') params.append('redirect_uri', redirectUri);
    if (state !== `test-state-${Date.now()}`) params.append('state', state);
    
    const googleLoginUrl = `/api/v1/auth/google${params.toString() ? '?' + params.toString() : ''}`;
    window.location.href = googleLoginUrl;
  };

  return (
    <div className="animate-fadeIn relative mx-auto w-full rounded-lg border bg-card text-card-foreground shadow-sm">
      {showBackButton && (
        <button
          type="button"
          className="animate-fadeIn absolute left-2 top-2 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
          onClick={handleBack}
          aria-label={intl.formatMessage({ id: 'common.back' })}
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
      )}
      <div className="flex flex-col space-y-1 p-6 sm:p-8 pt-8">
        <h3 className="animate-slideUp text-2xl font-semibold leading-none tracking-tight">
          {intl.formatMessage({ id: 'auth.signIn' })}
        </h3>
        <p className="animate-slideUp animation-delay-100 text-sm text-muted-foreground mt-1">
          {intl.formatMessage({ id: 'auth.welcomeDescription' })}
        </p>
      </div>
      <div className="p-6 sm:p-8 pt-2">
        <form id="oauth2-form" onSubmit={handleSubmit} className="grid gap-3">
          {error && (
            <div className="animate-fadeIn relative w-full rounded-lg border border-destructive/50 p-4 text-destructive [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg+div]:translate-y-[-3px] [&:has(svg)]:pl-11">
              <h5 className="mb-1 font-medium leading-none tracking-tight">{error}</h5>
            </div>
          )}
          <div className="animate-fadeIn animation-delay-100 grid gap-2">
            <label htmlFor="username" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {intl.formatMessage({ id: 'auth.username' })}
            </label>
            <input
              id="username"
              type="text"
              placeholder={intl.formatMessage({ id: 'auth.usernamePlaceholder' })}
              value={username}
              onChange={e => setUsername(e.target.value)}
              className="flex h-11 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
          </div>
          <div className="animate-fadeIn animation-delay-200 grid gap-2">
            <label htmlFor="password" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {intl.formatMessage({ id: 'auth.password' })}
            </label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder={intl.formatMessage({ id: 'auth.passwordPlaceholder' })}
                value={password}
                onChange={e => setPassword(e.target.value)}
                className="flex h-11 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-3 text-muted-foreground hover:text-foreground transition-colors"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>
          <button
            type="submit"
            className="shimmer-effect animate-fadeIn animation-delay-200 w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 px-4 py-2 mt-2"
            disabled={isLoading}
          >
            {isLoading 
              ? intl.formatMessage({ id: 'auth.authorizing' })
              : intl.formatMessage({ id: 'auth.loginAuthorize' })
            }
          </button>

          {/* Google Login Button */}
          <div className="animate-fadeIn animation-delay-300 relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">{intl.formatMessage({ id: 'auth.orContinueWith' })}</span>
            </div>
          </div>
          
          <button
            type="button"
            onClick={handleGoogleLogin}
            className="animate-fadeIn animation-delay-400 w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 px-4 py-2"
            disabled={isLoading}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            {intl.formatMessage({ id: 'auth.continueWithGoogle' })}
          </button>



        </form>

        {/* Register Link */}
        <div className="animate-fadeIn animation-delay-700 mt-6 text-center text-sm">
          <span className="text-muted-foreground">{intl.formatMessage({ id: 'auth.noAccount' })} </span>
          <Link 
            to="/auth/register" 
            className="text-primary hover:underline font-medium"
          >
            {intl.formatMessage({ id: 'auth.signUp' })}
          </Link>
        </div>

        {/* Secure Authentication Badge - moved to bottom below sign-up link */}
        <div className="animate-fadeIn animation-delay-800 mt-6 text-center">
          <span className="text-xs text-muted-foreground px-3 py-2 rounded-full bg-muted/30 border">
            {intl.formatMessage({ id: 'auth.secureAuth' })}
          </span>
        </div>
      </div>
    </div>
  );
} 