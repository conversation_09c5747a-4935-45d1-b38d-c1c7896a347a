import React, { useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { <PERSON><PERSON>eft, Eye, EyeOff } from 'lucide-react';
import { useIntl } from 'react-intl';

interface OAuth2RegisterFormProps {
  showBackButton?: boolean;
}

/**
 * OAuth2 Register Form Component
 *
 * Clean design for user registration with proper i18n support
 * for English and Chinese.
 *
 * @param {OAuth2RegisterFormProps} props - Component props
 * @returns {JSX.Element} The OAuth2 Register Form component
 */
export function OAuth2RegisterForm({ showBackButton = true }: OAuth2RegisterFormProps) {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Extract OAuth2 parameters from URL
  const clientId = searchParams.get('client_id');
  const redirectUri = searchParams.get('redirect_uri');
  const responseType = searchParams.get('response_type');
  const scope = searchParams.get('scope');
  const state = searchParams.get('state');
  const codeChallenge = searchParams.get('code_challenge');
  const codeChallengeMethod = searchParams.get('code_challenge_method');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      setError(intl.formatMessage({ id: 'auth.error.firstNameRequired' }));
      return false;
    }
    if (!formData.lastName.trim()) {
      setError(intl.formatMessage({ id: 'auth.error.lastNameRequired' }));
      return false;
    }
    if (!formData.username.trim()) {
      setError(intl.formatMessage({ id: 'auth.error.usernameRequired' }));
      return false;
    }
    if (!formData.email.trim()) {
      setError(intl.formatMessage({ id: 'auth.error.emailRequired' }));
      return false;
    }
    if (!formData.password) {
      setError(intl.formatMessage({ id: 'auth.error.passwordRequired' }));
      return false;
    }
    if (formData.password.length < 8) {
      setError(intl.formatMessage({ id: 'auth.error.passwordTooShort' }));
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError(intl.formatMessage({ id: 'auth.error.passwordMismatch' }));
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Use traditional form submission
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/api/v1/auth/register';
      
      // Add form fields
      const fields: Record<string, string> = {
        'first_name': formData.firstName,
        'last_name': formData.lastName,
        'username': formData.username,
        'email': formData.email,
        'password': formData.password,
        'confirm_password': formData.confirmPassword,
      };

      // Add OAuth2 parameters if present
      if (clientId) fields['client_id'] = clientId;
      if (redirectUri) fields['redirect_uri'] = redirectUri;
      if (responseType) fields['response_type'] = responseType;
      if (scope) fields['scope'] = scope;
      if (state) fields['state'] = state;
      if (codeChallenge) fields['code_challenge'] = codeChallenge;
      if (codeChallengeMethod) fields['code_challenge_method'] = codeChallengeMethod;

      // Create hidden inputs
      Object.entries(fields).forEach(([name, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      
    } catch (err) {
      setError(intl.formatMessage({ id: 'auth.error.registrationFailed' }));
      setIsLoading(false);
    }
  };

  // Handle Google OAuth registration
  const handleGoogleRegister = () => {
    // For OAuth2 registration, pass OAuth2 parameters to Google OAuth
    const params = new URLSearchParams();
    if (clientId) params.append('client_id', clientId);
    if (redirectUri) params.append('redirect_uri', redirectUri);
    if (responseType) params.append('response_type', responseType);
    if (scope) params.append('scope', scope);
    if (state) params.append('state', state);
    if (codeChallenge) params.append('code_challenge', codeChallenge);
    if (codeChallengeMethod) params.append('code_challenge_method', codeChallengeMethod);

    const googleLoginUrl = `/api/v1/auth/google?${params.toString()}`;
    window.location.href = googleLoginUrl;
  };

  // Build login URL with OAuth2 parameters
  const loginUrl = `/oauth2/login${clientId ? `?${searchParams.toString()}` : ''}`;

  return (
    <div className="w-full mx-auto">
      <div className="bg-card rounded-lg border p-6 sm:p-8 shadow-sm">
        {/* Header */}
        <div className="mb-6 text-center">
          <h2 className="text-xl font-semibold text-foreground">
            {intl.formatMessage({ id: 'auth.createAccount' })}
          </h2>
          {clientId && (
            <p className="text-sm text-muted-foreground mt-2">
              {intl.formatMessage({ id: 'auth.registerToContinue' })}
            </p>
          )}
        </div>

        {/* Google Registration Button */}
        <button
          type="button"
          onClick={handleGoogleRegister}
          className="w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 px-4 py-2 mb-6"
          disabled={isLoading}
        >
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {intl.formatMessage({ id: 'auth.continueWithGoogle' })}
        </button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">{intl.formatMessage({ id: 'auth.orContinueWith' })}</span>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
            {error}
          </div>
        )}

        {/* Registration Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-foreground mb-1">
                {intl.formatMessage({ id: 'auth.firstName' })}
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                required
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder={intl.formatMessage({ id: 'auth.firstName' })}
              />
            </div>
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-foreground mb-1">
                {intl.formatMessage({ id: 'auth.lastName' })}
              </label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                required
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder={intl.formatMessage({ id: 'auth.lastName' })}
              />
            </div>
          </div>

          {/* Username */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-foreground mb-1">
              {intl.formatMessage({ id: 'auth.username' })}
            </label>
            <input
              id="username"
              name="username"
              type="text"
              required
              value={formData.username}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder={intl.formatMessage({ id: 'auth.username' })}
            />
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-foreground mb-1">
              {intl.formatMessage({ id: 'auth.email' })}
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder={intl.formatMessage({ id: 'auth.email' })}
            />
          </div>

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-foreground mb-1">
              {intl.formatMessage({ id: 'auth.password' })}
            </label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                required
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-3 py-2 pr-10 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder={intl.formatMessage({ id: 'auth.password' })}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {intl.formatMessage({ id: 'auth.passwordRequirement' })}
            </p>
          </div>

          {/* Confirm Password */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-foreground mb-1">
              {intl.formatMessage({ id: 'auth.confirmPassword' })}
            </label>
            <div className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                required
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className="w-full px-3 py-2 pr-10 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder={intl.formatMessage({ id: 'auth.confirmPassword' })}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground"
              >
                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed py-2 px-4 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {isLoading 
              ? intl.formatMessage({ id: 'auth.creatingAccount' })
              : intl.formatMessage({ id: 'auth.createAccount' })
            }
          </button>
        </form>

        {/* Login Link */}
        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            {intl.formatMessage({ id: 'auth.alreadyHaveAccount' })}{' '}
            <Link 
              to={loginUrl}
              className="text-primary hover:text-primary/80 font-medium"
            >
              {intl.formatMessage({ id: 'auth.signIn' })}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
