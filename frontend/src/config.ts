/**
 * Application Configuration
 * Central configuration file for the SSO frontend application
 */

// Dashboard path for redirects after login
export const DASHBOARD_PATH = '/dashboard';

// API Configuration
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5550',
  timeout: 10000,
};

// OAuth2 Configuration (Frontend Auth UI routes - separate from backend OAuth2 API)
export const OAUTH2_CONFIG = {
  loginPath: '/auth/login',
  registerPath: '/auth/register',
  authorizePath: '/auth/authorize',
  callbackPath: '/auth/callback',
  errorPath: '/auth/error',
};

// Application Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  ADMIN: '/admin',
  DEVELOPER: '/developer',
};

// Feature Flags
export const FEATURES = {
  enableDarkMode: true,
  enableMultiLanguage: true,
  enableNotifications: true,
  enableAnalytics: false,
  enableTwoFactorAuth: true,
};

// Default export
const config = {
  DASHBOARD_PATH,
  API_CONFIG,
  OAUTH2_CONFIG,
  ROUTES,
  FEATURES,
};

export default config;
