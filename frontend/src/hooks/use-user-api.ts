/**
 * End User API Hooks
 *
 * Custom hooks for end user API interactions with real-time data fetching,
 * state management, and error handling.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { UserApiService } from '@/services/user-api';
import type {
  ConnectedApplication,
  UserActivity,
  UserPermissions,
  UserProfileResponse,
  UserSecurityOverviewResponse,
} from '@/types/api/user';
import type { PaginationParams } from '@/types/api/common';

/**
 * Hook for managing user's connected applications
 */
export function useUserConnectedApplications(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [applications, setApplications] = useState<ConnectedApplication[]>([]);
  const [total, setTotal] = useState(0);
  const [userId, setUserId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadApplications = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...params, ...newParams };
      const data = await UserApiService.getConnectedApplications(finalParams);
      setApplications(data?.applications || []);
      setTotal(data?.total || 0);
      setUserId(data?.user_id || '');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load connected applications';
      setError(errorMessage);
      console.error('Error loading connected applications:', err);
      // Reset to safe defaults on error
      setApplications([]);
      setTotal(0);
      setUserId('');
    } finally {
      setIsLoading(false);
    }
  }, [JSON.stringify(params)]);

  const revokeAccess = useCallback(async (applicationId: string) => {
    try {
      setError(null);
      await UserApiService.revokeApplicationAccess(applicationId);
      // Remove from local state
      setApplications(prev => prev.filter(app => app.application_id !== applicationId));
      setTotal(prev => prev - 1);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to revoke application access';
      setError(errorMessage);
      throw err;
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadApplications();
    }
  }, [autoLoad, loadApplications]);

  return {
    applications,
    total,
    userId,
    isLoading,
    error,
    loadApplications,
    revokeAccess,
    refreshData: loadApplications,
  };
}

/**
 * Hook for specific application details
 */
export function useUserApplicationDetails(applicationId: string, autoLoad: boolean = true) {
  const [application, setApplication] = useState<ConnectedApplication | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadApplication = useCallback(async () => {
    if (!applicationId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const data = await UserApiService.getApplicationDetails(applicationId);
      setApplication(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load application details';
      setError(errorMessage);
      console.error('Error loading application details:', err);
    } finally {
      setIsLoading(false);
    }
  }, [applicationId]);

  const revokeAccess = useCallback(async () => {
    if (!applicationId) return;
    
    try {
      setError(null);
      await UserApiService.revokeApplicationAccess(applicationId);
      // Update local state to reflect revoked access
      setApplication(prev => prev ? { ...prev, is_active: false } : null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to revoke application access';
      setError(errorMessage);
      throw err;
    }
  }, [applicationId]);

  useEffect(() => {
    if (autoLoad && applicationId) {
      loadApplication();
    }
  }, [autoLoad, applicationId, loadApplication]);

  return {
    application,
    isLoading,
    error,
    loadApplication,
    revokeAccess,
    refreshData: loadApplication,
  };
}

/**
 * Hook for user activity logs
 */
export function useUserActivity(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [activity, setActivity] = useState<UserActivity[]>([]);
  const [total, setTotal] = useState(0);
  const [userId, setUserId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadActivity = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...params, ...newParams };
      const data = await UserApiService.getUserActivity(finalParams);
      setActivity(data?.activity || []);
      setTotal(data?.total || 0);
      setUserId(data?.user_id || '');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load user activity';
      setError(errorMessage);
      console.error('Error loading user activity:', err);
      // Reset to safe defaults on error
      setActivity([]);
      setTotal(0);
      setUserId('');
    } finally {
      setIsLoading(false);
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    if (autoLoad) {
      loadActivity();
    }
  }, [autoLoad, loadActivity]);

  return {
    activity,
    total,
    userId,
    isLoading,
    error,
    loadActivity,
    refreshData: loadActivity,
  };
}

/**
 * Hook for user permissions overview
 */
export function useUserPermissions(autoLoad: boolean = true) {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await UserApiService.getUserPermissions();
      setPermissions(data || null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load user permissions';
      setError(errorMessage);
      console.error('Error loading user permissions:', err);
      // Reset to safe default on error
      setPermissions(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadPermissions();
    }
  }, [autoLoad, loadPermissions]);

  return {
    permissions,
    isLoading,
    error,
    loadPermissions,
    refreshData: loadPermissions,
  };
}

/**
 * Hook for user dashboard statistics
 */
export function useUserDashboardStats(autoLoad: boolean = true) {
  const [stats, setStats] = useState<{
    activeSessions: number;
    securityAlerts: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const [sessionsData, alertsData] = await Promise.all([
        UserApiService.getActiveSessionsCount(),
        UserApiService.getSecurityAlertsCount()
      ]);
      
      setStats({
        activeSessions: sessionsData.active_sessions_count,
        securityAlerts: alertsData.security_alerts_count,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard statistics';
      setError(errorMessage);
      console.error('Error loading dashboard statistics:', err);
      // Reset to safe default on error
      setStats({ activeSessions: 0, securityAlerts: 0 });
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadStats();
    }
  }, [autoLoad, loadStats]);

  return {
    stats,
    isLoading,
    error,
    loadStats,
    refreshData: loadStats,
  };
}

/**
 * Hook for user profile management
 */
export function useUserProfile(autoLoad: boolean = true) {
  const [profile, setProfile] = useState<UserProfileResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const profileData = await UserApiService.getUserProfile();
      setProfile(profileData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load user profile';
      setError(errorMessage);
      console.error('Error loading user profile:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateProfile = useCallback(async (profileData: Partial<UserProfileResponse>) => {
    try {
      setIsLoading(true);
      setError(null);
      const updatedProfile = await UserApiService.updateUserProfile(profileData);
      setProfile(updatedProfile);
      return updatedProfile;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user profile';
      setError(errorMessage);
      console.error('Error updating user profile:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadProfile();
    }
  }, [autoLoad, loadProfile]);

  return {
    profile,
    isLoading,
    error,
    loadProfile,
    updateProfile,
    refreshData: loadProfile,
  };
}

/**
 * Hook for user security overview
 */
export function useUserSecurityOverview(autoLoad: boolean = true) {
  const [securityOverview, setSecurityOverview] = useState<UserSecurityOverviewResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSecurityOverview = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const securityData = await UserApiService.getUserSecurityOverview();
      setSecurityOverview(securityData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load security overview';
      setError(errorMessage);
      console.error('Error loading security overview:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadSecurityOverview();
    }
  }, [autoLoad, loadSecurityOverview]);

  return {
    securityOverview,
    isLoading,
    error,
    loadSecurityOverview,
    refreshData: loadSecurityOverview,
  };
}

/**
 * Hook for comprehensive user dashboard data
 */
export function useUserDashboard(autoLoad: boolean = true) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Memoize params to prevent infinite re-renders
  const emptyParams = useMemo(() => ({}), []);
  const activityParams = useMemo(() => ({ limit: 10 }), []);

  const { applications, loadApplications } = useUserConnectedApplications(emptyParams, false);
  const { activity, loadActivity } = useUserActivity(activityParams, false);
  const { permissions, loadPermissions } = useUserPermissions(false);

  const loadDashboardData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await Promise.all([
        loadApplications(),
        loadActivity(),
        loadPermissions(),
      ]);
      
      setLastUpdate(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard data';
      setError(errorMessage);
      console.error('Error loading user dashboard data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [loadApplications, loadActivity, loadPermissions]);

  useEffect(() => {
    if (autoLoad) {
      loadDashboardData();
    }
  }, [autoLoad, loadDashboardData]);

  return {
    applications,
    activity,
    permissions,
    isLoading,
    error,
    lastUpdate,
    loadDashboardData,
    refreshData: loadDashboardData,
  };
}

/**
 * Hook for real-time user data updates with polling
 */
export function useUserRealTimeData(
  refreshInterval: number = 60000, // 1 minute (less frequent for end users)
  enabled: boolean = true
) {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Memoize params to prevent infinite re-renders
  const emptyParams = useMemo(() => ({}), []);
  const activityParams = useMemo(() => ({ limit: 20 }), []);

  const { applications, loadApplications } = useUserConnectedApplications(emptyParams, false);
  const { activity, loadActivity } = useUserActivity(activityParams, false);
  const { permissions, loadPermissions } = useUserPermissions(false);

  const refreshAllData = useCallback(async () => {
    try {
      await Promise.all([
        loadApplications(),
        loadActivity(),
        loadPermissions(),
      ]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error refreshing user real-time data:', error);
    }
  }, [loadApplications, loadActivity, loadPermissions]);

  useEffect(() => {
    if (!enabled) return;

    // Initial load
    refreshAllData();

    // Set up polling
    const interval = setInterval(refreshAllData, refreshInterval);

    return () => clearInterval(interval);
  }, [enabled, refreshInterval, refreshAllData]);

  return {
    applications,
    activity,
    permissions,
    lastUpdate,
    refreshAllData,
    isRealTimeEnabled: enabled,
  };
}

/**
 * Hook for filtering and searching user data
 */
export function useUserDataFilters() {
  const [filters, setFilters] = useState({
    applicationSearch: '',
    activityFilter: 'all', // 'all', 'success', 'failed'
    dateRange: '7d', // '1d', '7d', '30d', 'all'
    permissionScope: 'all', // 'all', 'openid', 'profile', 'email', etc.
  });

  const updateFilter = useCallback((key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      applicationSearch: '',
      activityFilter: 'all',
      dateRange: '7d',
      permissionScope: 'all',
    });
  }, []);

  const filterApplications = useCallback((applications: ConnectedApplication[]) => {
    return applications.filter(app => {
      if (filters.applicationSearch) {
        const searchLower = filters.applicationSearch.toLowerCase();
        return (
          app.application_name.toLowerCase().includes(searchLower) ||
          app.developer_email.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [filters.applicationSearch]);

  const filterActivity = useCallback((activity: UserActivity[]) => {
    return activity.filter(log => {
      // Filter by success status
      if (filters.activityFilter === 'success' && !log.success) return false;
      if (filters.activityFilter === 'failed' && log.success) return false;
      
      // Filter by date range
      if (filters.dateRange !== 'all') {
        const logDate = new Date(log.timestamp);
        const now = new Date();
        const daysDiff = Math.floor((now.getTime() - logDate.getTime()) / (1000 * 60 * 60 * 24));
        
        switch (filters.dateRange) {
          case '1d':
            if (daysDiff > 1) return false;
            break;
          case '7d':
            if (daysDiff > 7) return false;
            break;
          case '30d':
            if (daysDiff > 30) return false;
            break;
        }
      }
      
      return true;
    });
  }, [filters.activityFilter, filters.dateRange]);

  return {
    filters,
    updateFilter,
    resetFilters,
    filterApplications,
    filterActivity,
  };
}

/**
 * Combined hook for all user API functionality
 * Provides a unified interface for user data management
 */
export function useUserApi(params: PaginationParams = {}) {
  // Memoize params to prevent infinite re-renders
  const memoizedParams = useMemo(() => params, [JSON.stringify(params)]);

  const applications = useUserConnectedApplications(memoizedParams);
  const activity = useUserActivity(memoizedParams);
  const permissions = useUserPermissions();
  const dashboard = useUserDashboard();

  const revokeAccess = async (applicationId: string) => {
    await applications.revokeAccess(applicationId);
  };

  const refreshAll = async () => {
    await Promise.all([
      applications.refreshData(),
      activity.refreshData(),
      permissions.refreshData(),
      dashboard.refreshData(),
    ]);
  };

  return {
    // Applications
    applications: {
      applications: applications.applications,
      total: applications.total,
      isLoading: applications.isLoading,
      error: applications.error,
    },

    // Activity
    activity: {
      activity: activity.activity,
      total: activity.total,
      isLoading: activity.isLoading,
      error: activity.error,
    },

    // Permissions
    permissions: {
      permissions: permissions.permissions,
      isLoading: permissions.isLoading,
      error: permissions.error,
    },

    // Dashboard
    dashboard: {
      applications: dashboard.applications,
      activity: dashboard.activity,
      permissions: dashboard.permissions,
      isLoading: dashboard.isLoading,
      error: dashboard.error,
    },

    // Actions
    revokeAccess,
    refreshAll,

    // Individual refresh functions
    refreshApplications: applications.refreshData,
    refreshActivity: activity.refreshData,
    refreshPermissions: permissions.refreshData,
    refreshDashboard: dashboard.refreshData,

    // Overall loading state
    isLoading: applications.isLoading || activity.isLoading || permissions.isLoading || dashboard.isLoading,

    // Overall error state
    hasError: !!(applications.error || activity.error || permissions.error || dashboard.error),
    errors: {
      applications: applications.error,
      activity: activity.error,
      permissions: permissions.error,
      dashboard: dashboard.error,
    },
  };
}
