import { useLocation } from 'react-router-dom';
import { useMemo } from 'react';

export function useActiveRoute() {
  const location = useLocation();

  return useMemo(
    () => ({
      isActive: (url: string) => {
        if (url === '#') return false;
        // Exact match
        if (location.pathname === url) return true;
        // Dashboard routes should only be active for exact matches
        // No special sub-route logic for dashboard items
        return false;
      },
      currentPath: location.pathname,
    }),
    [location.pathname]
  );
}
