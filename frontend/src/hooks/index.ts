/**
 * Hooks Barrel Exports
 *
 * This file serves as the main entry point for all custom hooks.
 */

// Core hooks
export { default as useAuth } from './use-auth';
export { default as useConfig } from './use-config';
export { useIsMobile } from './use-mobile';
export { default as useScriptRef } from './use-script-ref';
export { useToast } from './use-toast';
export { useActiveRoute } from './use-active-route';
export { useImagePreload } from './use-image-preload';
export { default as useLocalStorage } from './use-localStorage';

export {
  useDeveloperApplications,
  useDeveloperAnalytics,
  useApplicationAnalytics,
  useDeveloperUsers,
  useApplicationUsers,
  useDeveloperActivityLogs,
  useDeveloperUsageStats,
  useDeveloperRealTimeData,
} from './use-developer-api';

export {
  useAdminApplications,
  useAdminUsers,
  useAdminDeveloperUsers,
  useAdminRegularUsers,
  useAdminUserDetails,
  useAdminSystemAnalytics,
  useApplicationsByDeveloper,
  useAdminRealTimeData,
} from './use-admin-api';

export {
  useUserConnectedApplications,
  useUserApplicationDetails,
  useUserActivity,
  useUserPermissions,
  useUserProfile,
  useUserSecurityOverview,
  useUserDashboard,
  useUserRealTimeData,
  useUserDataFilters,
} from './use-user-api';
