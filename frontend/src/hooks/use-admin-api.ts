/**
 * Admin API Hooks
 *
 * Custom hooks for admin portal API interactions with real-time data fetching,
 * state management, and error handling.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { AdminApiService } from '@/services/admin-api';
import type {
  AdminApplicationResponse,
  AdminUserResponse,
  SystemAnalytics,
  UserApplicationConnection,
  PasswordResetRequest,
  ApplicationApprovalRequest,
} from '@/types/api/admin';
import type { UserActivityLog } from '@/types/api/developer';
import type { PaginationParams } from '@/types/api/common';

/**
 * Hook for managing admin applications oversight
 */
export function useAdminApplications(
  params: {
    is_active?: boolean;
    is_approved?: boolean;
    search?: string;
    page?: number;
    per_page?: number;
  } = {},
  autoLoad: boolean = true
) {
  const [applications, setApplications] = useState<AdminApplicationResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create stable params reference
  const stableParams = useMemo(() => params, [JSON.stringify(params)]);

  const loadApplications = useCallback(async (newParams?: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...stableParams, ...newParams };
      const data = await AdminApiService.getAllApplications(finalParams);
      setApplications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load applications';
      setError(errorMessage);
      console.error('Error loading admin applications:', err);
    } finally {
      setIsLoading(false);
    }
  }, [stableParams]);

  const approveApplication = useCallback(async (id: string, data: ApplicationApprovalRequest) => {
    try {
      setError(null);
      await AdminApiService.approveApplication(id, data);
      // Update local state
      setApplications(prev => 
        prev.map(sub => 
          sub.id === id 
            ? { ...sub, is_approved: data.approved, status: data.approved ? 'active' : 'rejected' }
            : sub
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to approve application';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const rejectApplication = useCallback(async (id: string, data: ApplicationApprovalRequest) => {
    try {
      setError(null);
      await AdminApiService.rejectApplication(id, data);
      // Update local state
      setApplications(prev => 
        prev.map(sub => 
          sub.id === id 
            ? { ...sub, is_approved: false, status: 'rejected' }
            : sub
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject application';
      setError(errorMessage);
      throw err;
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadApplications();
    }
  }, [autoLoad, loadApplications]);

  return {
    applications,
    isLoading,
    error,
    loadApplications,
    approveApplication,
    rejectApplication,
    refreshData: loadApplications,
  };
}

/**
 * Hook for admin user management
 */
export function useAdminUsers(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [users, setUsers] = useState<AdminUserResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create stable params reference
  const stableParams = useMemo(() => params, [JSON.stringify(params)]);

  const loadUsers = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...stableParams, ...newParams };
      const data = await AdminApiService.getAllUsers(finalParams);
      setUsers(data.users);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load users';
      setError(errorMessage);
      console.error('Error loading admin users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [stableParams]);

  const resetUserPassword = useCallback(async (userId: string, data: PasswordResetRequest) => {
    try {
      setError(null);
      await AdminApiService.resetUserPassword(userId, data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset password';
      setError(errorMessage);
      throw err;
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadUsers();
    }
  }, [autoLoad, loadUsers]);

  return {
    users,
    total,
    isLoading,
    error,
    loadUsers,
    resetUserPassword,
    refreshData: loadUsers,
  };
}

/**
 * Hook for developer users management
 */
export function useAdminDeveloperUsers(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [developers, setDevelopers] = useState<AdminUserResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create stable params reference
  const stableParams = useMemo(() => params, [JSON.stringify(params)]);

  const loadDevelopers = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...stableParams, ...newParams };
      const data = await AdminApiService.getDeveloperUsers(finalParams);
      setDevelopers(data.users);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load developers';
      setError(errorMessage);
      console.error('Error loading admin developers:', err);
    } finally {
      setIsLoading(false);
    }
  }, [stableParams]);

  useEffect(() => {
    if (autoLoad) {
      loadDevelopers();
    }
  }, [autoLoad, loadDevelopers]);

  return {
    developers,
    total,
    isLoading,
    error,
    loadDevelopers,
    refreshData: loadDevelopers,
  };
}

/**
 * Hook for regular users management
 */
export function useAdminRegularUsers(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [regularUsers, setRegularUsers] = useState<AdminUserResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create stable params reference
  const stableParams = useMemo(() => params, [JSON.stringify(params)]);

  const loadRegularUsers = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...stableParams, ...newParams };
      const data = await AdminApiService.getRegularUsers(finalParams);
      setRegularUsers(data.users);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load regular users';
      setError(errorMessage);
      console.error('Error loading admin regular users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [stableParams]);

  useEffect(() => {
    if (autoLoad) {
      loadRegularUsers();
    }
  }, [autoLoad, loadRegularUsers]);

  return {
    regularUsers,
    total,
    isLoading,
    error,
    loadRegularUsers,
    refreshData: loadRegularUsers,
  };
}

/**
 * Hook for user details and connections
 */
export function useAdminUserDetails(userId: string, autoLoad: boolean = true) {
  const [user, setUser] = useState<AdminUserResponse | null>(null);
  const [applications, setApplications] = useState<UserApplicationConnection[]>([]);
  const [activity, setActivity] = useState<UserActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadUserDetails = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const [userDetails, userApplications, userActivity] = await Promise.all([
        AdminApiService.getUser(userId),
        AdminApiService.getUserApplications(userId),
        AdminApiService.getUserActivity(userId, { limit: 50 }),
      ]);
      
      setUser(userDetails);
      setApplications(userApplications);
      setActivity(userActivity.logs);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load user details';
      setError(errorMessage);
      console.error('Error loading user details:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (autoLoad && userId) {
      loadUserDetails();
    }
  }, [autoLoad, userId, loadUserDetails]);

  return {
    user,
    applications,
    activity,
    isLoading,
    error,
    loadUserDetails,
    refreshData: loadUserDetails,
  };
}

/**
 * Hook for system analytics
 */
export function useAdminSystemAnalytics(autoLoad: boolean = true) {
  const [analytics, setAnalytics] = useState<SystemAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAnalytics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await AdminApiService.getSystemAnalytics();
      setAnalytics(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load system analytics';
      setError(errorMessage);
      console.error('Error loading system analytics:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadAnalytics();
    }
  }, [autoLoad, loadAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    loadAnalytics,
    refreshData: loadAnalytics,
  };
}

/**
 * Hook for applications by developer
 */
export function useApplicationsByDeveloper(developerId: string, autoLoad: boolean = true) {
  const [applications, setApplications] = useState<AdminApplicationResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadApplications = useCallback(async () => {
    if (!developerId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const data = await AdminApiService.getApplicationsByDeveloper(developerId);
      setApplications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load developer applications';
      setError(errorMessage);
      console.error('Error loading developer applications:', err);
    } finally {
      setIsLoading(false);
    }
  }, [developerId]);

  useEffect(() => {
    if (autoLoad && developerId) {
      loadApplications();
    }
  }, [autoLoad, developerId, loadApplications]);

  return {
    applications,
    isLoading,
    error,
    loadApplications,
    refreshData: loadApplications,
  };
}

/**
 * Hook for real-time admin data updates with polling
 */
export function useAdminRealTimeData(
  refreshInterval: number = 30000, // 30 seconds
  enabled: boolean = true
) {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const { analytics, loadAnalytics } = useAdminSystemAnalytics(false);
  const { applications, loadApplications } = useAdminApplications({}, false);
  const { users, loadUsers } = useAdminUsers({}, false);

  const refreshAllData = useCallback(async () => {
    try {
      await Promise.all([
        loadAnalytics(),
        loadApplications(),
        loadUsers(),
      ]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error refreshing admin real-time data:', error);
    }
  }, [loadAnalytics, loadApplications, loadUsers]);

  useEffect(() => {
    if (!enabled) return;

    // Initial load
    refreshAllData();

    // Set up polling
    const interval = setInterval(refreshAllData, refreshInterval);

    return () => clearInterval(interval);
  }, [enabled, refreshInterval, refreshAllData]);

  return {
    analytics,
    applications,
    users,
    lastUpdate,
    refreshAllData,
    isRealTimeEnabled: enabled,
  };
}
