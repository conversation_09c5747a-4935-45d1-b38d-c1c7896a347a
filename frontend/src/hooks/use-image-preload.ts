import { useEffect, useState } from 'react';

export function useImagePreload(imageSources: string[]) {
  const [imagesLoaded, setImagesLoaded] = useState(false);

  useEffect(() => {
    const preloadImages = async () => {
      const loadImage = (src: string): Promise<void> => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.src = src;
          img.onload = () => resolve();
          img.onerror = reject;
        });
      };

      try {
        await Promise.all(imageSources.map(src => loadImage(src)));
        setImagesLoaded(true);
      } catch (error) {
        console.error('Error preloading images:', error);
        setImagesLoaded(true); // Still set to true to not block rendering
      }
    };

    preloadImages();
  }, [imageSources]);

  return imagesLoaded;
}
