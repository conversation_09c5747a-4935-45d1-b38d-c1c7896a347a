/**
 * Developer API Hooks
 *
 * Custom hooks for developer portal API interactions with real-time data fetching,
 * state management, and error handling.
 */

import { useState, useEffect, useCallback } from 'react';
import { DeveloperApiService } from '@/services/developer-api';
import type {
  ApplicationResponse,
  ApplicationCreateRequest,
  ApplicationUpdateRequest,
  DeveloperAnalyticsOverview,
  DeveloperUser,
  UserActivityLog,
  UsageStatistics,
} from '@/types/api/developer';
import type { PaginationParams } from '@/types/api/common';

/**
 * Hook for managing developer applications
 */
export function useDeveloperApplications(autoLoad: boolean = true) {
  const [applications, setApplications] = useState<ApplicationResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadApplications = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await DeveloperApiService.getApplications();
      setApplications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load applications';
      setError(errorMessage);
      console.error('Error loading applications:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createApplication = useCallback(async (data: ApplicationCreateRequest) => {
    try {
      setError(null);
      const newApplication = await DeveloperApiService.createApplication(data);
      setApplications(prev => [...prev, newApplication]);
      return newApplication;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create application';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const updateApplication = useCallback(async (id: string, data: ApplicationUpdateRequest) => {
    try {
      setError(null);
      const updatedApplication = await DeveloperApiService.updateApplication(id, data);
      setApplications(prev => 
        prev.map(sub => sub.id === id ? updatedApplication : sub)
      );
      return updatedApplication;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update application';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const deleteApplication = useCallback(async (id: string) => {
    try {
      setError(null);
      await DeveloperApiService.deleteApplication(id);
      setApplications(prev => prev.filter(sub => sub.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete application';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const regenerateSecret = useCallback(async (id: string) => {
    try {
      setError(null);
      const updatedApplication = await DeveloperApiService.regenerateSecret(id);
      setApplications(prev => 
        prev.map(sub => sub.id === id ? updatedApplication : sub)
      );
      return updatedApplication;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to regenerate secret';
      setError(errorMessage);
      throw err;
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadApplications();
    }
  }, [autoLoad, loadApplications]);

  return {
    applications,
    isLoading,
    error,
    loadApplications,
    createApplication,
    updateApplication,
    deleteApplication,
    regenerateSecret,
    refreshData: loadApplications,
  };
}

/**
 * Hook for developer analytics data
 */
export function useDeveloperAnalytics(autoLoad: boolean = true) {
  const [analytics, setAnalytics] = useState<DeveloperAnalyticsOverview | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAnalytics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await DeveloperApiService.getAnalyticsOverview();
      setAnalytics(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics';
      setError(errorMessage);
      console.error('Error loading analytics:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadAnalytics();
    }
  }, [autoLoad, loadAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    loadAnalytics,
    refreshData: loadAnalytics,
  };
}

/**
 * Hook for application-specific analytics
 */
export function useApplicationAnalytics(applicationId: string, autoLoad: boolean = true) {
  const [analytics, setAnalytics] = useState<DeveloperAnalyticsOverview | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAnalytics = useCallback(async () => {
    if (!applicationId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const data = await DeveloperApiService.getApplicationAnalytics(applicationId);
      setAnalytics(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load application analytics';
      setError(errorMessage);
      console.error('Error loading application analytics:', err);
    } finally {
      setIsLoading(false);
    }
  }, [applicationId]);

  useEffect(() => {
    if (autoLoad && applicationId) {
      loadAnalytics();
    }
  }, [autoLoad, applicationId, loadAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    loadAnalytics,
    refreshData: loadAnalytics,
  };
}

/**
 * Hook for developer users management
 */
export function useDeveloperUsers(params: PaginationParams = {}, autoLoad: boolean = true) {
  const [users, setUsers] = useState<DeveloperUser[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadUsers = useCallback(async (newParams?: PaginationParams) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...params, ...newParams };
      const data = await DeveloperApiService.getUsers(finalParams);
      setUsers(data.users);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load users';
      setError(errorMessage);
      console.error('Error loading users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [params]);

  useEffect(() => {
    if (autoLoad) {
      loadUsers();
    }
  }, [autoLoad, loadUsers]);

  return {
    users,
    total,
    isLoading,
    error,
    loadUsers,
    refreshData: loadUsers,
  };
}

/**
 * Hook for application users
 */
export function useApplicationUsers(
  applicationId: string, 
  params: PaginationParams = {}, 
  autoLoad: boolean = true
) {
  const [users, setUsers] = useState<DeveloperUser[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadUsers = useCallback(async (newParams?: PaginationParams) => {
    if (!applicationId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...params, ...newParams };
      const data = await DeveloperApiService.getApplicationUsers(applicationId, finalParams);
      setUsers(data.users);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load application users';
      setError(errorMessage);
      console.error('Error loading application users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [applicationId, params]);

  useEffect(() => {
    if (autoLoad && applicationId) {
      loadUsers();
    }
  }, [autoLoad, applicationId, loadUsers]);

  return {
    users,
    total,
    isLoading,
    error,
    loadUsers,
    refreshData: loadUsers,
  };
}

/**
 * Hook for activity logs
 */
export function useDeveloperActivityLogs(
  params: {
    limit?: number;
    offset?: number;
    action?: string;
    success?: boolean;
  } = {},
  autoLoad: boolean = true
) {
  const [logs, setLogs] = useState<UserActivityLog[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadLogs = useCallback(async (newParams?: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalParams = { ...params, ...newParams };
      const data = await DeveloperApiService.getActivityLogs(finalParams);
      setLogs(data.logs);
      setTotal(data.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load activity logs';
      setError(errorMessage);
      console.error('Error loading activity logs:', err);
    } finally {
      setIsLoading(false);
    }
  }, [params]);

  useEffect(() => {
    if (autoLoad) {
      loadLogs();
    }
  }, [autoLoad, loadLogs]);

  return {
    logs,
    total,
    isLoading,
    error,
    loadLogs,
    refreshData: loadLogs,
  };
}

/**
 * Hook for usage statistics
 */
export function useDeveloperUsageStats(period: string = '7d', autoLoad: boolean = true) {
  const [stats, setStats] = useState<UsageStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadStats = useCallback(async (newPeriod?: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalPeriod = newPeriod || period;
      const data = await DeveloperApiService.getUsageStatistics(finalPeriod);
      setStats(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load usage statistics';
      setError(errorMessage);
      console.error('Error loading usage statistics:', err);
    } finally {
      setIsLoading(false);
    }
  }, [period]);

  useEffect(() => {
    if (autoLoad) {
      loadStats();
    }
  }, [autoLoad, loadStats]);

  return {
    stats,
    isLoading,
    error,
    loadStats,
    refreshData: loadStats,
  };
}

/**
 * Hook for developer settings data
 */
export function useDeveloperSettings(autoLoad: boolean = true) {
  const [settings, setSettings] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await DeveloperApiService.getSettings();
      setSettings(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load settings';
      setError(errorMessage);
      console.error('Error loading settings:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateSettings = useCallback(async (newSettings: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await DeveloperApiService.updateSettings(newSettings);
      setSettings(data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
      console.error('Error updating settings:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadSettings();
    }
  }, [autoLoad, loadSettings]);

  return {
    settings,
    isLoading,
    error,
    loadSettings,
    updateSettings,
    refreshData: loadSettings,
  };
}

/**
 * Hook for real-time data updates with polling
 */
export function useDeveloperRealTimeData(
  refreshInterval: number = 30000, // 30 seconds
  enabled: boolean = true
) {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const { analytics, loadAnalytics } = useDeveloperAnalytics(false);
  const { applications, loadApplications } = useDeveloperApplications(false);

  const refreshAllData = useCallback(async () => {
    try {
      await Promise.all([
        loadAnalytics(),
        loadApplications(),
      ]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error refreshing real-time data:', error);
    }
  }, [loadAnalytics, loadApplications]);

  useEffect(() => {
    if (!enabled) return;

    // Initial load
    refreshAllData();

    // Set up polling
    const interval = setInterval(refreshAllData, refreshInterval);

    return () => clearInterval(interval);
  }, [enabled, refreshInterval, refreshAllData]);

  return {
    analytics,
    applications,
    lastUpdate,
    refreshAllData,
    isRealTimeEnabled: enabled,
  };
}
