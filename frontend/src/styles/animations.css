/* 
 * Global Animation System
 * 
 * This file contains all shared animations and effects used throughout the application.
 * Import this file in your component's CSS or JS to use these animations.
 */

/* ---------------------------------- */
/* Element Entrance Animations        */
/* ---------------------------------- */

/* Fade In */
.animate-fadeIn {
  opacity: 0;
  animation: fadeIn 0.4s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide Up */
.animate-slideUp {
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale In */
.animate-scaleIn {
  opacity: 0;
  transform: scale(0.9);
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ---------------------------------- */
/* Hero Section Animations           */
/* ---------------------------------- */

/* Hero Container */
.animate-hero-content {
  will-change: transform, opacity;
}

/* Hero Elements */
.hero-element {
  opacity: 0;
  transform: translateY(30px);
  animation: smoothFadeInUp 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

.hero-delay-1 {
  animation-delay: 0.1s;
}

.hero-delay-2 {
  animation-delay: 0.2s;
}

@keyframes smoothFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ---------------------------------- */
/* Animation Delay Utilities          */
/* ---------------------------------- */

.animation-delay-100 {
  animation-delay: 50ms;
}

.animation-delay-200 {
  animation-delay: 100ms;
}

.animation-delay-300 {
  animation-delay: 150ms;
}

.animation-delay-400 {
  animation-delay: 200ms;
}

.animation-delay-500 {
  animation-delay: 250ms;
}

/* ---------------------------------- */
/* Section Reveal Animations          */
/* ---------------------------------- */

.section-reveal {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.section-reveal.visible {
  opacity: 1;
}

/* ---------------------------------- */
/* Interactive Element Animations     */
/* ---------------------------------- */

/* Card Hover Effect */
.stagger-card {
  transition:
    transform 0.2s ease-out,
    box-shadow 0.2s ease-out;
}

.stagger-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ---------------------------------- */
/* Button Effects                     */
/* ---------------------------------- */

/* Base Button Animation */
.btn-animate {
  position: relative;
  transition: all 0.2s ease;
  overflow: hidden;
}

/* Shimmer Effect */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
}

.shimmer-effect:hover::after {
  animation: shimmer 1s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Scale Effect */
.scale-effect {
  transition: transform 0.15s ease;
}

.scale-effect:hover {
  transform: scale(1.05);
}

/* Pulse Effect */
.pulse-effect:hover {
  animation: pulse 0.6s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Glow Effect */
.glow-effect {
  transition: box-shadow 0.2s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 15px rgba(var(--primary-rgb, 80, 70, 230), 0.5);
}

/* ---------------------------------- */
/* Text Animation Effects             */
/* ---------------------------------- */

.text-fade-in {
  opacity: 0;
  animation: textFadeIn 0.5s ease forwards;
}

@keyframes textFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.text-slide-up {
  opacity: 0;
  transform: translateY(20px);
  animation: textSlideUp 0.4s ease forwards;
}

@keyframes textSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
