/**
 * Site Configuration for GeNieGO SSO Server
 * Contains branding and application information
 */

export const SITE_INFO = {
  organization: {
    name: 'Genieland Company Limited',
    displayName: 'GeNieGO',
    displayNameZh: '一元通行',
    description: 'Leading AI technology ecosystem provider',
    descriptionZh: '領先的AI技術生態系統供應商',
    logo: {
      main: '/static/media/gngo-logo-short.png',
      light: '/static/media/gngo-logo-short.png',
      dark: '/static/media/gngo-logo-short.png',
      favicon: '/static/favicon.ico',
    },
    foundedYear: 2018,
    industry: 'AI Technology',
  },
  application: {
    name: 'GeNieGO SSO Server',
    displayName: 'GeNieGO SSO',
    displayNameZh: '一元通行登入',
    description: 'Secure Single Sign-On for Genieland Ecosystem',
    descriptionZh: '一元精靈系統 安全統一登入',
    version: '1.0.0',
    environment: 'production',
    baseUrl: 'https://geniego.genieland.ai',
    apiUrl: 'https://geniego.genieland.ai/api',
    copyright: `© ${new Date().getFullYear()} Genieland Ecosystem. All rights reserved.`,
  },
  branding: {
    primaryColor: '#50C878', // GeNieGO Emerald Green
    secondaryColor: '#22c55e', // Complementary green
    accentColor: '#10b981', // Darker emerald for accents
    logoUrl: '/static/media/gngo-logo-short.png',
    faviconUrl: '/static/favicon.ico',
  },
  contact: {
    email: {
      general: '<EMAIL>',
      support: '<EMAIL>',
      security: '<EMAIL>',
      admin: '<EMAIL>',
      privacy: '<EMAIL>',
    },
    phone: {
      general: '+852-1234-5678',
      support: '+852-1234-5679',
      international: '+852-1234-5680',
    },
    social: {
      github: 'https://github.com/genieland',
      linkedin: 'https://linkedin.com/company/genieland',
      twitter: 'https://twitter.com/genieland',
      facebook: 'https://facebook.com/genieland',
      instagram: 'https://instagram.com/genieland',
    },
    offices: [
      {
        name: 'Hong Kong Headquarters',
        street: 'Central District',
        city: 'Hong Kong',
        state: '',
        zip: '',
        country: 'Hong Kong SAR',
        mapLink: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3691.4936834568!2d114.157!3d22.2783!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjLCsDE2JzQyLjAiTiAxMTTCsDA5JzI1LjIiRQ!5e0!3m2!1sen!2shk!4v1625000000000!5m2!1sen!2shk',
      }
    ],
  },
  legal: {
    companyNumber: 'GLD2018001',
    vatNumber: 'HK-VAT-123456',
    dataProtectionOfficer: '<EMAIL>',
    privacyLastUpdated: '2025-01-01',
    termsLastUpdated: '2025-01-01',
    cookiesLastUpdated: '2025-01-01',
    gdprLastUpdated: '2025-01-01',
  },
} as const;

// Page Metadata
export const PAGE_METADATA = {
  home: {
    title: 'GeNieGO SSO Server - Secure Authentication',
    description: 'Enterprise-grade Single Sign-On for GeNieLand Ecosystem. Secure authentication with OAuth2 and JWT tokens.',
    keywords: ['SSO', 'authentication', 'OAuth2', 'JWT', 'security', 'GeNieLand'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  login: {
    title: 'Sign In - GeNieGO SSO',
    description: 'Sign in to your GeNieLand account securely.',
    keywords: ['login', 'sign in', 'authentication', 'SSO'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  register: {
    title: 'Create Account - GeNieGO SSO',
    description: 'Create your GeNieLand account to access all services.',
    keywords: ['register', 'sign up', 'create account', 'SSO'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  about: {
    title: 'About Us - GeNieGO SSO',
    description: 'Learn about Genieland and our mission to provide secure authentication.',
    keywords: ['about', 'company', 'team', 'mission'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  contact: {
    title: 'Contact Us - GeNieGO SSO',
    description: 'Get in touch with our team for support and inquiries.',
    keywords: ['contact', 'support', 'help'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  privacy: {
    title: 'Privacy Policy - GeNieGO SSO',
    description: 'Our privacy policy and data protection practices.',
    keywords: ['privacy', 'policy', 'data protection'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  terms: {
    title: 'Terms of Service - GeNieGO SSO',
    description: 'Terms and conditions for using our SSO service.',
    keywords: ['terms', 'service', 'legal'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  cookies: {
    title: 'Cookie Policy - GeNieGO SSO',
    description: 'How we use cookies and tracking technologies.',
    keywords: ['cookies', 'tracking', 'policy'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  gdpr: {
    title: 'GDPR Compliance - GeNieGO SSO',
    description: 'Our GDPR compliance and data protection measures.',
    keywords: ['GDPR', 'compliance', 'data protection'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  team: {
    title: 'Our Team - GeNieGO SSO',
    description: 'Meet the team behind GeNieGO SSO service.',
    keywords: ['team', 'staff', 'people'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  careers: {
    title: 'Careers - GeNieGO SSO',
    description: 'Join our team and build the future of authentication.',
    keywords: ['careers', 'jobs', 'employment'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  faq: {
    title: 'FAQ - GeNieGO SSO',
    description: 'Frequently asked questions about our SSO service.',
    keywords: ['FAQ', 'questions', 'help'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  blog: {
    title: 'Blog - GeNieGO SSO',
    description: 'Latest updates and insights from our team.',
    keywords: ['blog', 'news', 'updates'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  help: {
    title: 'Help Center - GeNieGO SSO',
    description: 'Get help and support for using our SSO service.',
    keywords: ['help', 'support', 'documentation'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  resources: {
    title: 'Resources - GeNieGO SSO',
    description: 'Documentation, guides, and resources for developers.',
    keywords: ['resources', 'documentation', 'guides'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  notFound: {
    title: '404 - Page Not Found',
    description: 'The page you are looking for does not exist.',
    keywords: ['404', 'not found', 'error'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  serverError: {
    title: '500 - Server Error',
    description: 'Internal server error occurred.',
    keywords: ['500', 'server error', 'error'],
    ogImage: '/static/media/gngo-og-image.png',
  },
  maintenance: {
    title: 'Maintenance - GeNieGO SSO',
    description: 'Service temporarily unavailable for maintenance.',
    keywords: ['maintenance', 'downtime', 'service'],
    ogImage: '/static/media/gngo-og-image.png',
  },
} as const;

// SSO Service Statistics
export const SSO_STATS = {
  uptime: '99.9%',
  responseTime: '<10ms',
  encryption: '256-bit',
  support: '24/7',
  users: '10K+',
  applications: '50+',
} as const;

// Feature Flags
export const FEATURES = {
  enableDarkMode: true,
  enableMultiLanguage: true,
  enableNotifications: true,
  enableUserRoles: true,
  enableTwoFactorAuth: true,
  maintenanceMode: false,
} as const;

// Content Configuration
export const CONTENT = {
  footerLinks: {
    company: [
      { name: 'About Us', path: '/about' },
      { name: 'Careers', path: '/careers' },
      { name: 'Blog', path: '/blog' },
      { name: 'Contact', path: '/contact' },
      { name: 'Team', path: '/team' },
    ],
    support: [
      { name: 'Help Center', path: '/help' },
      { name: 'FAQ', path: '/faq' },
      { name: 'Resources', path: '/resources' },
    ],
    legal: [
      { name: 'Terms of Service', path: '/terms' },
      { name: 'Privacy Policy', path: '/privacy' },
      { name: 'Cookie Policy', path: '/cookies' },
      { name: 'GDPR', path: '/gdpr' },
    ],
  },
} as const;

// Default export for easy importing
const siteConfig = {
  SITE_INFO,
  PAGE_METADATA,
  SSO_STATS,
  FEATURES,
  CONTENT,
};

export default siteConfig;