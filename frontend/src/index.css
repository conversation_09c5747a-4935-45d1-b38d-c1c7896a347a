@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font Size Control */
.font-size-small {
  font-size: 87.5%; /* 14px if base is 16px */
}

.font-size-default {
  font-size: 100%; /* 16px base */
}

.font-size-large {
  font-size: 112.5%; /* 18px if base is 16px */
}

/* Animation System */
.animate-fadeIn {
  opacity: 0;
  animation: fadeIn 0.4s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-slideUp {
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animation-delay-100 { animation-delay: 50ms; }
.animation-delay-200 { animation-delay: 100ms; }
.animation-delay-300 { animation-delay: 150ms; }
.animation-delay-400 { animation-delay: 200ms; }
.animation-delay-500 { animation-delay: 250ms; }

/* Section Reveal Animations */
.section-reveal {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.section-reveal.visible {
  opacity: 1;
}

/* Card Hover Effects */
.stagger-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.stagger-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-hover-effect {
  transition: all 0.2s ease-out;
}

.card-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
}

.shimmer-effect:hover::after {
  animation: shimmer 1s infinite;
}

@keyframes shimmer {
  100% { transform: translateX(100%); }
}

@layer base {
  :root {
    /* GeNieGO Brand Colors - Light Mode */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 145 63% 49%; /* #50C878 - GeNieGO Emerald Green */
    --primary-foreground: 0 0% 100%;
    --secondary: 145 20% 95%;
    --secondary-foreground: 145 63% 20%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 145 20% 95%;
    --accent-foreground: 145 63% 30%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 145 63% 49%;
    --radius: 0.5rem;
    --primary-rgb: 80, 200, 120; /* RGB values for #50C878 */

    /* Sidebar Colors - GeNieGO Theme */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 145 63% 49%; /* GeNieGO Emerald Green */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 145 63% 49%; /* GeNieGO Emerald Green for active states */
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 145 63% 49%;
  }

  .dark {
    /* GeNieGO Brand Colors - Dark Mode (Greyish Dark) */
    --background: 240 5% 20%; /* More greyish background */
    --foreground: 0 0% 92%; /* Softer white text */
    --card: 240 5% 24%; /* Greyish cards */
    --card-foreground: 0 0% 92%;
    --popover: 240 5% 24%; /* Match card color */
    --popover-foreground: 0 0% 92%;
    --primary: 145 63% 55%; /* Keep the green vibrant */
    --primary-foreground: 240 5% 20%; /* Use background color */
    --secondary: 240 4% 30%; /* Lighter greyish secondary */
    --secondary-foreground: 0 0% 88%;
    --muted: 240 4% 28%; /* Greyish muted background */
    --muted-foreground: 240 3% 70%; /* Readable muted text */
    --accent: 240 4% 30%; /* Match secondary */
    --accent-foreground: 145 63% 55%;
    --destructive: 0 62.8% 45%; /* Balanced red for dark mode */
    --destructive-foreground: 0 0% 92%;
    --border: 240 4% 35%; /* Visible greyish borders */
    --input: 240 4% 26%; /* Input field background */
    --ring: 145 63% 55%;
    --primary-rgb: 90, 220, 135; /* Keep the same RGB for consistency */

    /* Sidebar Colors - GeNieGO Dark Theme (Greyish) */
    --sidebar-background: 240 5% 18%; /* Slightly darker greyish sidebar */
    --sidebar-foreground: 0 0% 88%; /* Readable sidebar text */
    --sidebar-primary: 145 63% 55%; /* Keep green vibrant */
    --sidebar-primary-foreground: 240 5% 18%; /* Use sidebar background */
    --sidebar-accent: 145 63% 55%; /* Keep green vibrant */
    --sidebar-accent-foreground: 240 5% 18%; /* Use sidebar background */
    --sidebar-border: 240 4% 32%; /* Greyish sidebar borders */
    --sidebar-ring: 145 63% 55%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

/* Add these keyframes animations */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

@theme inline {
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
  @keyframes gradient {
    to {
      background-position: var(--bg-size, 300%) 0;
    }
  }
  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }
  @keyframes meteor {
    0% {
      transform: rotate(var(--angle)) translateX(0);
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--angle)) translateX(-500px);
      opacity: 0;
    }
  }
}
