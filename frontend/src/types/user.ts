import { GenericCardProps } from '@/types';
import { PostDataType, UserProfile, Profile } from '@/types/user-profile';

export interface FollowerCardProps {
  avatar: string;
  follow: number;
  location: string;
  name: string;
}

export interface FriendRequestCardProps extends Profile {
  mutual: number;
}

export interface FriendsCardProps {
  avatar: string;
  location: string;
  name: string;
}

export interface UserProfileCardProps extends UserProfile {
  profile: string;
}

export interface UserSimpleCardProps {
  avatar: string;
  name: string;
  status: string;
}

export interface UserStateProps {
  usersS1: UserProfile[];
  usersS2: UserProfileStyle2[];
  followers: FollowerCardProps[];
  friendRequests: FriendRequestCardProps[];
  friends: FriendsCardProps[];
  gallery: GenericCardProps[];
  posts: PostDataType[];
  detailCards: UserProfile[];
  simpleCards: UserSimpleCardProps[];
  profileCards: UserProfileCardProps[];
  error: object | string | null;
}

export type UserProfileStyle2 = {
  image: string;
  name: string;
  designation: string;
  badgeStatus: string;
  subContent: string;
  email: string;
  phone: string;
  location: string;
  progressValue: string;
};

// User profile data types for user pages
export interface UserProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  jobTitle: string;
  company: string;
  bio: string;
  location: string;
  avatarUrl: string | null;
  joinDate: string;
  lastLogin: string;
}

// Notification types
export interface NotificationUser {
  name: string;
  avatar: string;
}

export interface NotificationData {
  id: number;
  type: 'system' | 'activity' | 'mention' | 'security';
  title: string;
  message: string;
  date: string;
  read: boolean;
  actionUrl: string;
  user?: NotificationUser;
}

export interface NotificationSettings {
  email: boolean;
  browser: boolean;
  mobile: boolean;
}

// Security types
export interface ActiveSession {
  id: string;
  device: string;
  location: string;
  ipAddress: string;
  lastActive: string;
  current: boolean;
}

export interface SecurityEvent {
  id: number;
  type: 'login' | 'password_change' | 'login_failed' | 'mfa_disabled';
  description: string;
  ipAddress: string;
  location: string;
  timestamp: string;
  device: string;
}

export interface SecurityData {
  email: string;
  passwordLastChanged: string;
  twoFactorEnabled: boolean;
  recoveryCodesRemaining: number;
  activeSessions: ActiveSession[];
  securityEvents: SecurityEvent[];
}

// Settings types
export interface EmailNotificationSettings {
  productUpdates: boolean;
  securityAlerts: boolean;
  newsletters: boolean;
  usageReports: boolean;
}

export interface InAppNotificationSettings {
  mentions: boolean;
  comments: boolean;
  taskAssignments: boolean;
  statusChanges: boolean;
}

export interface NotificationPreferences {
  email: EmailNotificationSettings;
  inApp: InAppNotificationSettings;
}

export interface ConnectedAccounts {
  google: boolean;
  github: boolean;
  linkedin: boolean;
  slack: boolean;
}

export interface UserSettingsData {
  name: string;
  email: string;
  avatar: string;
  jobTitle: string;
  company: string;
  bio: string;
  location: string;
  timezone: string;
  language: string;
  theme: 'system' | 'light' | 'dark';
  notifications: NotificationPreferences;
  connectedAccounts: ConnectedAccounts;
}
