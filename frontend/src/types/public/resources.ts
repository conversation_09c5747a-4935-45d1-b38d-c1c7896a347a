/**
 * Resources-related type definitions
 */

import { Translation } from '../shared';

/**
 * Resource category structure
 */
export interface ResourceCategory {
  id: string;
  name: Translation | string; // Backward compatible during transition
  description?: Translation | string; // Backward compatible during transition
  icon?: string;
  count?: number;
}

/**
 * Individual resource structure
 */
export interface Resource {
  id: number;
  title: Translation | string; // Backward compatible during transition
  description: Translation | string; // Backward compatible during transition
  category: string;
  downloadUrl?: string;
  externalUrl?: string;
  videoUrl?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  date: string;
  popularityScore?: number;
  featured?: boolean;
  type?: 'pdf' | 'video' | 'document' | 'template' | 'webinar' | 'whitepaper';
  size?: string;
  duration?: number | string; // for videos/webinars - can be number (minutes) or string description
  author?: Translation | string; // Backward compatible during transition
  tags?: string[];
  downloadCount?: number;
  rating?: number;
  language?: string;
  requiresRegistration?: boolean;
}

/**
 * Resource filter options
 */
export interface ResourceFilters {
  category?: string;
  type?: string;
  featured?: boolean;
  query?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'date' | 'popularity' | 'title' | 'downloads';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Resource download tracking
 */
export interface ResourceDownload {
  resourceId: number;
  userId?: string;
  downloadDate: string;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
}

/**
 * Resource analytics
 */
export interface ResourceAnalytics {
  resourceId: number;
  views: number;
  downloads: number;
  uniqueViews: number;
  uniqueDownloads: number;
  averageRating?: number;
  totalRatings?: number;
  lastViewed?: string;
  lastDownloaded?: string;
}
