/**
 * Support-related TypeScript types and interfaces
 *
 * This file contains all TypeScript types used for the Support system,
 * including categories, articles, popular articles, and support options.
 *
 * @module types/public/support
 */

import { Translation } from '../shared';

/**
 * Support category structure
 */
export interface SupportCategory {
  id: string;
  name: Translation;
  description: Translation;
  icon: string;
  articles: SupportArticle[];
}

/**
 * Support article structure
 */
export interface SupportArticle {
  id: string;
  title: Translation;
  summary?: Translation;
  category?: string;
  popular?: boolean;
}

/**
 * Popular article structure
 */
export interface PopularArticle {
  id: string;
  title: Translation;
  description: Translation;
  category: string;
  readTime: Translation;
  featured?: boolean;
}

/**
 * Support option structure
 */
export interface SupportOption {
  id: string;
  title: Translation;
  description: Translation;
  action: Translation;
  icon: string;
  href?: string;
  priority: 'high' | 'medium' | 'low';
}
