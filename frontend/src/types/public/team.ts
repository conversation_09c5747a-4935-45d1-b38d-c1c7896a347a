/**
 * Team-related type definitions
 */

import { Translation } from '../shared';

/**
 * Department structure
 */
export interface Department {
  id?: string;
  name: Translation | string; // Backward compatible during transition
  description?: Translation | string; // Backward compatible during transition
  headId?: number; // Reference to team member who heads the department
  memberCount?: number;
  icon?: string;
  iconType?: string; // Icon type for UI rendering
}

/**
 * Social media links for team members
 */
export interface SocialLinks {
  linkedin?: string;
  twitter?: string;
  github?: string;
  email?: string;
  website?: string;
}

/**
 * Team member structure
 */
export interface TeamMember {
  id: number;
  name: Translation | string; // Backward compatible during transition
  position: Translation | string; // Backward compatible during transition
  bio: Translation | string; // Backward compatible during transition
  imageUrl: string;
  department?: string;
  location?: Translation | string; // Backward compatible during transition
  joinDate?: string;
  socialLinks?: SocialLinks;
  skills?: (Translation | string)[]; // Backward compatible during transition
  achievements?: (Translation | string)[]; // Backward compatible during transition
  education?: {
    degree: Translation | string; // Backward compatible during transition
    institution: Translation | string; // Backward compatible during transition
    year?: number;
  }[];
  experience?: {
    company: Translation | string; // Backward compatible during transition
    position: Translation | string; // Backward compatible during transition
    duration: string;
    description?: string;
  }[];
  isLeadership?: boolean;
  isFounder?: boolean;
  status?: 'active' | 'alumni' | 'contractor';
}

/**
 * Team filters
 */
export interface TeamFilters {
  department?: string;
  location?: string;
  isLeadership?: boolean;
  status?: string;
  query?: string;
}

/**
 * Organization chart node
 */
export interface OrgChartNode {
  id: number;
  memberId: number;
  parentId?: number;
  children?: OrgChartNode[];
  level: number;
}
