/**
 * Milestones-related type definitions
 */

import { Translation } from '../shared';

/**
 * Milestone content structure
 */
export interface MilestoneContent {
  title: Translation;
  description: Translation;
}

/**
 * Year representation - can be number, "foundedYear", "foundedYear+X", "currentYear-X", etc.
 */
export type MilestoneYear = number | string;

/**
 * Individual milestone structure
 */
export interface Milestone {
  year: string; // String representation like "foundedYear", "foundedYear+3", "currentYear-1", etc.
  title: Translation;
  description: Translation;
  icon?: string;
  image?: string;
  featured?: boolean;
}

/**
 * Milestone year structure for grouping
 */
export interface MilestoneYearGroup {
  year: number;
  milestones: Milestone[];
}

/**
 * Timeline configuration
 */
export interface TimelineConfig {
  startYear?: number;
  endYear?: number;
  direction?: 'horizontal' | 'vertical';
  theme?: 'light' | 'dark';
  animationType?: 'fade' | 'slide' | 'scale';
}

/**
 * Milestone display options
 */
export interface MilestoneDisplayOptions {
  showYear?: boolean;
  showIcon?: boolean;
  showImage?: boolean;
  locale?: string;
  maxItems?: number;
  sortOrder?: 'asc' | 'desc';
}
