// project import
import { UserProfile } from '@/types/user-profile';

export interface JWTContextType {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user: UserProfile | null;
  login: (_email: string, _password: string) => Promise<void>;
  logout: () => Promise<void>;
  logoutWithoutCallingAPI: () => void;
  register: (
    _email: string,
    _password: string,
    _firstName: string,
    _lastName: string
  ) => Promise<void>;
  resetPassword: (_email: string) => Promise<void>;
  updateProfile: (_profile: UserProfile) => Promise<void>;
}

export type Auth0ContextType = {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: UserProfile | null | undefined;
  logout: () => void;
  login: () => void;
  resetPassword: (_email: string) => Promise<void>;
  updateProfile: () => void;
};

export interface JWTDataProps {
  userId: string;
}

export interface InitialLoginContextProps {
  isLoggedIn: boolean;
  isInitialized: boolean;
  user: UserProfile | null;
}

export interface User {
  id: string;
  email: string;
  name: string;
}
