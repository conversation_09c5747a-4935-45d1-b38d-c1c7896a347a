/**
 * Developer Portal API Types
 *
 * Type definitions for all developer portal API endpoints including
 * application management, analytics, user management, and activity tracking.
 */

/**
 * Application creation request
 */
export interface ApplicationCreateRequest {
  application_name: string;
  description: string;
  allowed_redirect_uris: string[];
  allowed_scopes?: string[];
}

/**
 * Application update request
 */
export interface ApplicationUpdateRequest {
  application_name?: string;
  description?: string;
  allowed_redirect_uris?: string[];
  allowed_scopes?: string[];
  is_active?: boolean;
}

/**
 * Application response
 */
export interface ApplicationResponse {
  id: string;
  client_id: string;
  client_secret: string;
  application_name: string;
  description: string;
  allowed_redirect_uris: string[];
  allowed_scopes: string[];
  is_active: boolean;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  owner_id: string;
  stats?: {
    total_users: number;
    active_users: number;
    total_logins: number;
    last_activity?: string;
  };
}

/**
 * Developer analytics overview
 */
export interface DeveloperAnalyticsOverview {
  total_applications: number;
  total_users: number;
  active_users: number;
  total_logins: number;
  recent_activity: {
    date: string;
    logins: number;
    new_users: number;
  }[];
  top_applications: {
    application_name: string;
    user_count: number;
    login_count: number;
  }[];
}

/**
 * Developer user data
 */
export interface DeveloperUser {
  id: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  connected_applications: {
    application_id: string;
    application_name: string;
    connected_at: string;
    last_access?: string;
    granted_scopes: string[];
  }[];
  total_logins: number;
}

/**
 * User activity log entry
 */
export interface UserActivityLog {
  id: string;
  user_id: string;
  user_email: string;
  application_id: string;
  application_name: string;
  action: string;
  success: boolean;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  details?: Record<string, any>;
}

/**
 * Usage statistics
 */
export interface UsageStatistics {
  period: string;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  unique_users: number;
  peak_usage_time?: string;
  application_breakdown: {
    application_id: string;
    application_name: string;
    request_count: number;
    user_count: number;
  }[];
}

/**
 * Developer API endpoints response types
 */
export interface DeveloperApplicationsResponse {
  applications: ApplicationResponse[];
  total: number;
}

export interface DeveloperUsersResponse {
  users: DeveloperUser[];
  total: number;
  limit: number;
  offset: number;
}

export interface DeveloperActivityLogsResponse {
  logs: UserActivityLog[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Developer API request parameters
 */
export interface DeveloperUsersParams {
  limit?: number;
  offset?: number;
  search?: string;
  is_active?: boolean;
}

export interface DeveloperActivityParams {
  limit?: number;
  offset?: number;
  action?: string;
  success?: boolean;
  start_date?: string;
  end_date?: string;
}

export interface UsageStatsParams {
  period?: '1d' | '7d' | '30d' | '90d';
  application_id?: string;
}
