/**
 * Common API Types
 *
 * Shared types used across all API endpoints including base responses,
 * pagination, error handling, and health checks.
 */

/**
 * Base API response interface
 */
export interface APIResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
  config: any;
}

/**
 * Standard pagination parameters
 */
export interface PaginationParams {
  page?: number;
  size?: number;
  limit?: number;
  offset?: number;
}

/**
 * Standard paginated response
 */
export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
  limit?: number;
  offset?: number;
  total?: number;
}

/**
 * Standard message response
 */
export interface MessageResponse {
  message: string;
  success?: boolean;
}

/**
 * Health check response
 */
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  module?: string;
  version?: string;
  endpoints?: Record<string, string>;
}

/**
 * Standard error response
 */
export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path?: string;
  details?: any;
}

/**
 * API status for system monitoring
 */
export interface ApiStatus {
  available: boolean;
  lastChecked: Date;
  responseTime: number;
  error?: string;
}
