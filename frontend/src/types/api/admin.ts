/**
 * Admin Portal API Types
 *
 * Type definitions for all admin portal API endpoints including
 * user management, application oversight, and system analytics.
 */

import type { UserActivityLog } from './developer';

/**
 * Admin application response with developer attribution
 */
export interface AdminApplicationResponse {
  id: string;
  client_id: string;
  application_name: string;
  owner_email?: string;
  owner_name?: string;
  allowed_redirect_uris: string[];
  allowed_scopes: string[];
  is_active: boolean;
  is_approved: boolean;
  created_at: string;
  stats: {
    total_users: number;
    active_users: number;
    total_logins: number;
    last_activity?: string;
  };
  status: string;
}

/**
 * Admin user response
 */
export interface AdminUserResponse {
  id: string;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  role?: 'admin' | 'developer' | 'user';
  is_active: boolean;
  is_verified?: boolean;
  created_at: string;
  last_login?: string;
  application_count?: number;
  connected_applications_count?: number;
  total_logins?: number;
  profile_data?: Record<string, any>;
}

/**
 * System analytics for admin dashboard
 */
export interface SystemAnalytics {
  total_users: number;
  total_developers: number;
  total_applications: number;
  active_applications: number;
  pending_approvals: number;
  total_logins_today: number;
  total_logins_week: number;
  total_logins_month: number;
  growth_metrics: {
    users_growth: number;
    applications_growth: number;
    logins_growth: number;
  };
  activity_timeline: {
    date: string;
    new_users: number;
    new_applications: number;
    total_logins: number;
  }[];
  top_applications: {
    application_name: string;
    owner_email: string;
    user_count: number;
    login_count: number;
  }[];
}

/**
 * User application connection details for admin view
 */
export interface UserApplicationConnection {
  application_id: string;
  application_name: string;
  owner_email: string;
  connected_at: string;
  last_access?: string;
  granted_scopes: string[];
  is_active: boolean;
}

/**
 * Password reset request
 */
export interface PasswordResetRequest {
  user_id: string;
  send_email?: boolean;
}

/**
 * Application approval request
 */
export interface ApplicationApprovalRequest {
  approved: boolean;
  admin_notes?: string;
}

/**
 * Admin API response types
 */
export interface AdminApplicationsResponse {
  applications: AdminApplicationResponse[];
  total: number;
  page: number;
  per_page: number;
}

export interface AdminUsersResponse {
  users: AdminUserResponse[];
  total: number;
  limit: number;
  offset: number;
}

export interface AdminUserActivityResponse {
  activity: UserActivityLog[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Admin API request parameters
 */
export interface AdminApplicationsParams {
  is_active?: boolean;
  is_approved?: boolean;
  search?: string;
  page?: number;
  per_page?: number;
}

export interface AdminUsersParams {
  limit?: number;
  offset?: number;
  role?: 'admin' | 'developer' | 'user';
  is_active?: boolean;
  search?: string;
}

export interface AdminUserActivityParams {
  limit?: number;
  offset?: number;
  action?: string;
  success?: boolean;
  start_date?: string;
  end_date?: string;
}

/**
 * Admin bulk operations
 */
export interface BulkUserOperation {
  user_ids: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'reset_password';
  options?: {
    send_email?: boolean;
    reason?: string;
  };
}

export interface BulkApplicationOperation {
  application_ids: string[];
  operation: 'approve' | 'reject' | 'activate' | 'deactivate';
  admin_notes?: string;
}

/**
 * Admin export options
 */
export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx';
  filters?: {
    date_range?: {
      start: string;
      end: string;
    };
    user_roles?: string[];
    application_status?: string[];
  };
  fields?: string[];
}



/**
 * Application approval request
 */
export interface ApplicationApprovalRequest {
  approved: boolean;
  reason?: string;
}

/**
 * Admin applications response
 */
export interface AdminApplicationsResponse {
  applications: AdminApplicationResponse[];
  total: number;
  page: number;
  per_page: number;
}

/**
 * Admin applications params
 */
export interface AdminApplicationsParams {
  is_active?: boolean;
  is_approved?: boolean;
  search?: string;
  page?: number;
  per_page?: number;
}


