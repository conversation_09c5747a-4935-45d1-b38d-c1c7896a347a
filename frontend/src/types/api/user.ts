/**
 * End User API Types
 *
 * Type definitions for end user API endpoints including
 * connected applications management, permission control,
 * and personal activity access.
 */

/**
 * Connected application for end users
 */
export interface ConnectedApplication {
  application_id: string;
  application_name: string;
  description?: string;
  developer_email: string;
  connected_at: string;
  last_access?: string;
  granted_scopes: string[];
  permissions: {
    scope: string;
    description: string;
    granted_at: string;
  }[];
  is_active: boolean;
}

/**
 * User activity entry for end users
 */
export interface UserActivity {
  id: string;
  application_name: string;
  action: string;
  timestamp: string;
  ip_address?: string;
  location?: string;
  success: boolean;
  details?: string;
}

/**
 * User permissions overview
 */
export interface UserPermissions {
  total_applications: number;
  total_permissions: number;
  permissions_by_scope: {
    scope: string;
    description: string;
    applications: string[];
    granted_at: string;
  }[];
  recent_grants: {
    application_name: string;
    scope: string;
    granted_at: string;
  }[];
  security_summary: {
    high_risk_permissions: number;
    data_access_permissions: number;
    last_review_date?: string;
  };
  user_id?: string;
  generated_at?: Date;
}

/**
 * User API response types
 */
export interface UserApplicationsResponse {
  applications: ConnectedApplication[];
  total: number;
  limit: number;
  offset: number;
  user_id: string;
}

export interface UserActivityResponse {
  activity: UserActivity[];
  total: number;
  limit: number;
  offset: number;
  user_id: string;
}

/**
 * User API request parameters
 */
export interface UserApplicationsParams {
  limit?: number;
  offset?: number;
  is_active?: boolean;
  search?: string;
}

export interface UserActivityParams {
  limit?: number;
  offset?: number;
  action?: string;
  success?: boolean;
  start_date?: string;
  end_date?: string;
  application_id?: string;
}

/**
 * Application access revocation
 */
export interface RevokeAccessRequest {
  application_id: string;
  reason?: string;
  revoke_all_tokens?: boolean;
}

/**
 * Permission management
 */
export interface PermissionUpdateRequest {
  application_id: string;
  scopes: string[];
  action: 'grant' | 'revoke';
}

/**
 * User privacy settings
 */
export interface UserPrivacySettings {
  data_sharing_enabled: boolean;
  analytics_tracking_enabled: boolean;
  marketing_communications_enabled: boolean;
  activity_logging_enabled: boolean;
  third_party_access_notifications: boolean;
}

/**
 * User security settings
 */
export interface UserSecuritySettings {
  two_factor_enabled: boolean;
  login_notifications_enabled: boolean;
  suspicious_activity_alerts: boolean;
  session_timeout_minutes: number;
  allowed_ip_ranges?: string[];
}

/**
 * User dashboard summary
 */
export interface UserDashboardSummary {
  connected_applications: number;
  active_sessions: number;
  recent_activity_count: number;
  security_alerts: number;
  last_login: string;
  account_status: 'active' | 'suspended' | 'pending_verification';
}

/**
 * User profile response
 */
export interface UserProfileResponse {
  id: string;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  bio?: string;
  location?: string;
  job_title?: string;
  company?: string;
  avatar_url?: string;
  timezone?: string;
  language?: string;
  is_active: boolean;
  is_verified: boolean;
  role: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

/**
 * User security overview response
 */
export interface UserSecurityOverviewResponse {
  password_last_changed?: string;
  two_factor_enabled: boolean;
  active_sessions_count: number;
  trusted_devices_count: number;
  recent_security_events: number;
  last_login?: string;
  account_created: string;
}

/**
 * Data export request
 */
export interface UserDataExportRequest {
  data_types: ('profile' | 'activity' | 'permissions' | 'applications')[];
  format: 'json' | 'csv';
  date_range?: {
    start: string;
    end: string;
  };
}

/**
 * Account deletion request
 */
export interface AccountDeletionRequest {
  confirmation_password: string;
  reason?: string;
  delete_all_data: boolean;
  scheduled_date?: string;
}
