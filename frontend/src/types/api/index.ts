/**
 * API Types Barrel Exports
 *
 * Centralized exports for all API-related types organized by domain.
 * This provides a clean import structure while maintaining separation of concerns.
 */

// Common API types
export type {
  APIResponse,
  PaginationParams,
  PaginatedResponse,
  MessageResponse,
  HealthCheckResponse,
  ErrorResponse,
  ApiStatus,
} from './common';

// Developer Portal API types
export type {
  ApplicationCreateRequest,
  ApplicationUpdateRequest,
  ApplicationResponse,
  DeveloperAnalyticsOverview,
  DeveloperUser,
  UserActivityLog,
  UsageStatistics,
  DeveloperApplicationsResponse,
  DeveloperUsersResponse,
  DeveloperActivityLogsResponse,
  DeveloperUsersParams,
  DeveloperActivityParams,
  UsageStatsParams,
} from './developer';

// Admin Portal API types
export type {
  AdminApplicationResponse,
  AdminUserResponse,
  SystemAnalytics,
  UserApplicationConnection,
  PasswordResetRequest,
  ApplicationApprovalRequest,
  AdminApplicationsResponse,
  AdminUsersResponse,
  AdminUserActivityResponse,
  AdminApplicationsParams,
  AdminUsersParams,
  AdminUserActivityParams,
  BulkUserOperation,
  BulkApplicationOperation,
  ExportOptions,
} from './admin';

// End User API types
export type {
  ConnectedApplication,
  UserActivity,
  UserPermissions,
  UserApplicationsResponse,
  UserActivityResponse,
  UserApplicationsParams,
  UserActivityParams,
  RevokeAccessRequest,
  PermissionUpdateRequest,
  UserPrivacySettings,
  UserSecuritySettings,
  UserDashboardSummary,
  UserDataExportRequest,
  AccountDeletionRequest,
} from './user';

/**
 * Re-export commonly used types for convenience
 */
// Note: These are already exported above, so we don't need to re-export them

/**
 * Union types for common patterns
 */
export type UserRole = 'admin' | 'developer' | 'user';
export type ApplicationStatus = 'active' | 'inactive' | 'pending_approval' | 'rejected';
export type ActivityAction = 'login' | 'logout' | 'token_refresh' | 'permission_grant' | 'permission_revoke';
export type ExportFormat = 'csv' | 'json' | 'xlsx';

/**
 * Generic API response wrapper
 */
export interface ApiResponseWrapper<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

/**
 * Error handling types
 */
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

/**
 * Filter and search types
 */
export interface SearchFilters {
  query?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  status?: string[];
  roles?: UserRole[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Real-time update types
 */
export interface RealTimeUpdate<T> {
  type: 'create' | 'update' | 'delete';
  entity: string;
  data: T;
  timestamp: string;
}

/**
 * Notification types
 */
export interface ApiNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: {
    label: string;
    action: string;
  }[];
}
