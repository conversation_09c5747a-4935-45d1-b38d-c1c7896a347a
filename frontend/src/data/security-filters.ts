import {
  Shield,
  AlertTriangle,
  Activity,
  Clock,
  Users,
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Lock,
  Unlock
} from 'lucide-react';
import { FilterOption } from '@/components/common/filter-panel';
import { IntlShape } from 'react-intl';

/**
 * Security event filter options and categories for security dashboard
 */
export const getSecurityEventFilterOptions = (intl: IntlShape) => ({
  severity: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allSeverities' }) },
    { id: 'critical', name: intl.formatMessage({ id: 'components.filters.critical' }), icon: AlertTriangle },
    { id: 'high', name: intl.formatMessage({ id: 'components.filters.high' }), icon: AlertCircle },
    { id: 'medium', name: intl.formatMessage({ id: 'components.filters.medium' }), icon: Activity },
    { id: 'low', name: intl.formatMessage({ id: 'components.filters.low' }), icon: CheckCircle }
  ] as FilterOption[],

  type: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTypes' }) },
    { id: 'authentication', name: intl.formatMessage({ id: 'components.filters.authentication' }), icon: Lock },
    { id: 'authorization', name: intl.formatMessage({ id: 'components.filters.authorization' }), icon: Shield },
    { id: 'access', name: intl.formatMessage({ id: 'components.filters.accessControl' }), icon: Eye },
    { id: 'system', name: intl.formatMessage({ id: 'components.filters.system' }), icon: Settings },
    { id: 'user', name: intl.formatMessage({ id: 'components.filters.userActivity' }), icon: Users }
  ] as FilterOption[],

  timeframe: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTime' }) },
    { id: 'hour', name: intl.formatMessage({ id: 'components.filters.lastHour' }), icon: Clock },
    { id: 'day', name: intl.formatMessage({ id: 'components.filters.last24Hours' }), icon: Activity },
    { id: 'week', name: intl.formatMessage({ id: 'components.filters.lastWeek' }), icon: Activity },
    { id: 'month', name: intl.formatMessage({ id: 'components.filters.lastMonth' }), icon: Activity }
  ] as FilterOption[]
});

/**
 * Threat filter options and categories for security dashboard
 */
export const getThreatFilterOptions = (intl: IntlShape) => ({
  status: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allStatus' }) },
    { id: 'active', name: intl.formatMessage({ id: 'components.filters.active' }), icon: AlertTriangle },
    { id: 'mitigated', name: intl.formatMessage({ id: 'components.filters.mitigated' }), icon: CheckCircle },
    { id: 'investigating', name: intl.formatMessage({ id: 'components.filters.investigating' }), icon: Eye },
    { id: 'resolved', name: intl.formatMessage({ id: 'components.filters.resolved' }), icon: CheckCircle }
  ] as FilterOption[],

  risk: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allRiskLevels' }) },
    { id: 'critical', name: intl.formatMessage({ id: 'components.filters.critical' }), icon: AlertTriangle },
    { id: 'high', name: intl.formatMessage({ id: 'components.filters.high' }), icon: AlertCircle },
    { id: 'medium', name: intl.formatMessage({ id: 'components.filters.medium' }), icon: Activity },
    { id: 'low', name: intl.formatMessage({ id: 'components.filters.low' }), icon: CheckCircle }
  ] as FilterOption[]
});

/**
 * Get security event filter categories for security dashboard
 */
export const getSecurityEventFilterCategories = (
  intl: IntlShape,
  severityFilter: string,
  typeFilter: string,
  timeframeFilter: string,
  onSeverityChange: (value: string | string[]) => void,
  onTypeChange: (value: string | string[]) => void,
  onTimeframeChange: (value: string | string[]) => void
) => {
  const securityEventFilterOptions = getSecurityEventFilterOptions(intl);
  return [
    {
      id: 'severity',
      name: intl.formatMessage({ id: 'components.filters.severity' }),
      options: securityEventFilterOptions.severity,
      selected: severityFilter,
      onSelect: onSeverityChange,
      icon: AlertTriangle,
      color: 'destructive' as const
    },
    {
      id: 'type',
      name: intl.formatMessage({ id: 'components.filters.type' }),
      options: securityEventFilterOptions.type,
      selected: typeFilter,
      onSelect: onTypeChange,
      icon: Shield,
      color: 'primary' as const
    },
    {
      id: 'timeframe',
      name: intl.formatMessage({ id: 'components.filters.timeframe' }),
      options: securityEventFilterOptions.timeframe,
      selected: timeframeFilter,
      onSelect: onTimeframeChange,
      icon: Clock,
      color: 'secondary' as const
    }
  ];
};

/**
 * Get threat filter categories for security dashboard
 */
export const getThreatFilterCategories = (
  intl: IntlShape,
  statusFilter: string,
  riskFilter: string,
  onStatusChange: (value: string | string[]) => void,
  onRiskChange: (value: string | string[]) => void
) => {
  const threatFilterOptions = getThreatFilterOptions(intl);
  return [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'components.filters.status' }),
      options: threatFilterOptions.status,
      selected: statusFilter,
      onSelect: onStatusChange,
      icon: Shield,
      color: 'primary' as const
    },
    {
      id: 'risk',
      name: intl.formatMessage({ id: 'components.filters.risk' }),
      options: threatFilterOptions.risk,
      selected: riskFilter,
      onSelect: onRiskChange,
      icon: AlertTriangle,
      color: 'destructive' as const
    }
  ];
};

/**
 * Filter security events based on search term and filters
 */
export const filterSecurityEvents = (
  events: any[],
  searchTerm: string,
  severityFilter: string,
  typeFilter: string,
  timeframeFilter: string
) => {
  if (!events) return [];

  const now = new Date();
  const hour = new Date(now.getTime() - 60 * 60 * 1000);
  const day = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const month = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return events.filter(event => {
    // Search filter
    const matchesSearch = !searchTerm || 
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.source?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.user?.toLowerCase().includes(searchTerm.toLowerCase());

    // Severity filter
    const matchesSeverity = severityFilter === 'all' || event.severity === severityFilter;

    // Type filter
    const matchesType = typeFilter === 'all' || event.type === typeFilter;

    // Timeframe filter
    let matchesTimeframe = true;
    if (timeframeFilter !== 'all' && event.timestamp) {
      const eventDate = new Date(event.timestamp);
      switch (timeframeFilter) {
        case 'hour':
          matchesTimeframe = eventDate >= hour;
          break;
        case 'day':
          matchesTimeframe = eventDate >= day;
          break;
        case 'week':
          matchesTimeframe = eventDate >= week;
          break;
        case 'month':
          matchesTimeframe = eventDate >= month;
          break;
      }
    }

    return matchesSearch && matchesSeverity && matchesType && matchesTimeframe;
  });
};

/**
 * Filter threats based on search term and filters
 */
export const filterThreats = (
  threats: any[],
  searchTerm: string,
  statusFilter: string,
  riskFilter: string
) => {
  if (!threats) return [];

  return threats.filter(threat => {
    // Search filter
    const matchesSearch = !searchTerm || 
      threat.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      threat.source?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      threat.type?.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = statusFilter === 'all' || threat.status === statusFilter;

    // Risk filter
    const matchesRisk = riskFilter === 'all' || threat.risk_level === riskFilter;

    return matchesSearch && matchesStatus && matchesRisk;
  });
};
