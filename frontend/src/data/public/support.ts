/**
 * Support Data
 *
 * This file contains the structured data for the Support page, including
 * categories, articles, popular articles, and support options.
 * All content is organized with translations for supported languages (en, zh, zh-TW).
 *
 * @module data/support
 */

import { Translation } from '@/types/shared';
import type {
  SupportCategory,
  SupportArticle,
  PopularArticle,
  SupportOption,
} from '@/types/public/support';

/**
 * SVG icon paths for support categories and options
 */
export const SUPPORT_ICONS = {
  gettingStarted: 'M13 10V3L4 14h7v7l9-11h-7z',
  features:
    'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
  account:
    'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
  troubleshooting:
    'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
  liveChat:
    'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
  email:
    'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
  phone:
    'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z',
  documentation:
    'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
};

/**
 * Support categories with articles
 */
export const SUPPORT_CATEGORIES: SupportCategory[] = [
  {
    id: 'getting-started',
    name: {
      en: 'Getting Started',
      zh: '入门指南',
      'zh-TW': '入門指南',
    },
    description: {
      en: 'Learn the basics and get up and running quickly',
      zh: '学习基础知识，快速上手',
      'zh-TW': '學習基礎知識，快速上手',
    },
    icon: SUPPORT_ICONS.gettingStarted,
    articles: [
      {
        id: 'quick-start',
        title: {
          en: 'Quick Start Guide',
          zh: '快速入门指南',
          'zh-TW': '快速入門指南',
        },
        summary: {
          en: 'Essential steps to get started with our platform quickly',
          zh: '快速开始使用我们平台的基本步骤',
          'zh-TW': '快速開始使用我們平台的基本步驟',
        },
        popular: true,
      },
      {
        id: 'account-setup',
        title: {
          en: 'Setting Up Your Account',
          zh: '设置您的账户',
          'zh-TW': '設置您的帳戶',
        },
        summary: {
          en: 'Step-by-step guide to configuring your account settings',
          zh: '配置您的账户设置的分步指南',
          'zh-TW': '配置您的帳戶設置的分步指南',
        },
      },
      {
        id: 'first-project',
        title: {
          en: 'Creating Your First Project',
          zh: '创建您的第一个项目',
          'zh-TW': '創建您的第一個項目',
        },
        summary: {
          en: 'How to set up and manage your first project',
          zh: '如何设置和管理您的第一个项目',
          'zh-TW': '如何設置和管理您的第一個項目',
        },
      },
      {
        id: 'user-roles',
        title: {
          en: 'Understanding User Roles',
          zh: '了解用户角色',
          'zh-TW': '了解用戶角色',
        },
        summary: {
          en: 'Learn about different user roles and permissions',
          zh: '了解不同的用户角色和权限',
          'zh-TW': '了解不同的用戶角色和權限',
        },
      },
    ],
  },
  {
    id: 'features',
    name: {
      en: 'Features & Functions',
      zh: '功能与特性',
      'zh-TW': '功能與特性',
    },
    description: {
      en: 'Detailed guides on how to use product features',
      zh: '详细的产品功能使用指南',
      'zh-TW': '詳細的產品功能使用指南',
    },
    icon: SUPPORT_ICONS.features,
    articles: [
      {
        id: 'dashboard',
        title: {
          en: 'Using the Dashboard',
          zh: '使用仪表板',
          'zh-TW': '使用儀表板',
        },
        summary: {
          en: 'Understanding the main dashboard components and metrics',
          zh: '了解主仪表板组件和指标',
          'zh-TW': '了解主儀表板組件和指標',
        },
        popular: true,
      },
      {
        id: 'reports',
        title: {
          en: 'Creating Reports',
          zh: '创建报表',
          'zh-TW': '創建報表',
        },
        summary: {
          en: 'Generate and customize reports for your business needs',
          zh: '生成和自定义满足您业务需求的报表',
          'zh-TW': '生成和自定義滿足您業務需求的報表',
        },
      },
      {
        id: 'integrations',
        title: {
          en: 'Working with Integrations',
          zh: '使用集成功能',
          'zh-TW': '使用集成功能',
        },
        summary: {
          en: 'Connect and manage third-party integrations',
          zh: '连接和管理第三方集成',
          'zh-TW': '連接和管理第三方集成',
        },
      },
      {
        id: 'automation',
        title: {
          en: 'Setting Up Automation',
          zh: '设置自动化',
          'zh-TW': '設置自動化',
        },
        summary: {
          en: 'Streamline your workflow with automation rules',
          zh: '使用自动化规则简化您的工作流程',
          'zh-TW': '使用自動化規則簡化您的工作流程',
        },
      },
    ],
  },
  {
    id: 'account',
    name: {
      en: 'Account & Billing',
      zh: '账户与账单',
      'zh-TW': '帳戶與帳單',
    },
    description: {
      en: 'Manage your account, subscription and billing',
      zh: '管理您的帐户、订阅和账单',
      'zh-TW': '管理您的帳戶、訂閱和帳單',
    },
    icon: SUPPORT_ICONS.account,
    articles: [
      {
        id: 'billing-faq',
        title: {
          en: 'Billing FAQ',
          zh: '账单常见问题',
          'zh-TW': '帳單常見問題',
        },
        summary: {
          en: 'Frequently asked questions about billing and payments',
          zh: '关于账单和付款的常见问题',
          'zh-TW': '關於帳單和付款的常見問題',
        },
        popular: true,
      },
      {
        id: 'subscription',
        title: {
          en: 'Managing Your Subscription',
          zh: '管理您的订阅',
          'zh-TW': '管理您的訂閱',
        },
        summary: {
          en: 'How to upgrade, downgrade, or cancel your subscription',
          zh: '如何升级、降级或取消您的订阅',
          'zh-TW': '如何升級、降級或取消您的訂閱',
        },
      },
      {
        id: 'payment-methods',
        title: {
          en: 'Payment Methods',
          zh: '付款方式',
          'zh-TW': '付款方式',
        },
        summary: {
          en: 'Add, update, or remove payment methods',
          zh: '添加、更新或删除付款方式',
          'zh-TW': '添加、更新或刪除付款方式',
        },
      },
      {
        id: 'invoices',
        title: {
          en: 'Accessing Invoices',
          zh: '访问发票',
          'zh-TW': '訪問發票',
        },
        summary: {
          en: 'Download and manage your billing invoices',
          zh: '下载和管理您的账单发票',
          'zh-TW': '下載和管理您的帳單發票',
        },
      },
    ],
  },
  {
    id: 'troubleshooting',
    name: {
      en: 'Troubleshooting',
      zh: '故障排除',
      'zh-TW': '故障排除',
    },
    description: {
      en: 'Solutions for common issues and problems',
      zh: '常见问题和故障的解决方案',
      'zh-TW': '常見問題和故障的解決方案',
    },
    icon: SUPPORT_ICONS.troubleshooting,
    articles: [
      {
        id: 'common-errors',
        title: {
          en: 'Common Error Messages',
          zh: '常见错误信息',
          'zh-TW': '常見錯誤信息',
        },
        summary: {
          en: 'Explanations for common error messages and how to resolve them',
          zh: '常见错误消息的说明及其解决方法',
          'zh-TW': '常見錯誤消息的說明及其解決方法',
        },
      },
      {
        id: 'login-issues',
        title: {
          en: 'Login Issues',
          zh: '登录问题',
          'zh-TW': '登入問題',
        },
        summary: {
          en: 'Troubleshooting authentication and login problems',
          zh: '身份验证和登录问题的故障排除',
          'zh-TW': '身份驗證和登入問題的故障排除',
        },
      },
      {
        id: 'performance',
        title: {
          en: 'Performance Optimization',
          zh: '性能优化',
          'zh-TW': '性能優化',
        },
        summary: {
          en: 'Tips to improve the performance of your account',
          zh: '提高您账户性能的技巧',
          'zh-TW': '提高您帳戶性能的技巧',
        },
      },
      {
        id: 'data-recovery',
        title: {
          en: 'Data Recovery',
          zh: '数据恢复',
          'zh-TW': '數據恢復',
        },
        summary: {
          en: 'How to recover lost or deleted data',
          zh: '如何恢复丢失或删除的数据',
          'zh-TW': '如何恢復丟失或刪除的數據',
        },
      },
    ],
  },
];

/**
 * Popular articles across all categories
 */
export const POPULAR_ARTICLES: PopularArticle[] = [
  {
    id: 'quick-start',
    title: {
      en: 'Quick Start Guide',
      zh: '快速入门指南',
      'zh-TW': '快速入門指南',
    },
    description: {
      en: 'Everything you need to know to get started in 5 minutes',
      zh: '5分钟内了解您需要知道的一切',
      'zh-TW': '5分鐘內了解您需要知道的一切',
    },
    category: 'getting-started',
    readTime: {
      en: '5 min read',
      zh: '阅读时间 5 分钟',
      'zh-TW': '閱讀時間 5 分鐘',
    },
    featured: true,
  },
  {
    id: 'dashboard',
    title: {
      en: 'Using the Dashboard',
      zh: '使用仪表板',
      'zh-TW': '使用儀表板',
    },
    description: {
      en: 'Master the main dashboard to maximize your productivity',
      zh: '掌握主仪表板以最大化您的生产力',
      'zh-TW': '掌握主儀表板以最大化您的生產力',
    },
    category: 'features',
    readTime: {
      en: '8 min read',
      zh: '阅读时间 8 分钟',
      'zh-TW': '閱讀時間 8 分鐘',
    },
    featured: true,
  },
  {
    id: 'billing-faq',
    title: {
      en: 'Billing FAQ',
      zh: '账单常见问题',
      'zh-TW': '帳單常見問題',
    },
    description: {
      en: 'Common questions about billing, payments, and subscriptions',
      zh: '关于账单、付款和订阅的常见问题',
      'zh-TW': '關於帳單、付款和訂閱的常見問題',
    },
    category: 'account',
    readTime: {
      en: '3 min read',
      zh: '阅读时间 3 分钟',
      'zh-TW': '閱讀時間 3 分鐘',
    },
  },
  {
    id: 'integrations-setup',
    title: {
      en: 'Setting Up Integrations',
      zh: '设置集成',
      'zh-TW': '設置集成',
    },
    description: {
      en: 'Connect your favorite tools and automate your workflow',
      zh: '连接您最喜欢的工具并自动化您的工作流程',
      'zh-TW': '連接您最喜歡的工具並自動化您的工作流程',
    },
    category: 'features',
    readTime: {
      en: '10 min read',
      zh: '阅读时间 10 分钟',
      'zh-TW': '閱讀時間 10 分鐘',
    },
  },
  {
    id: 'account-security',
    title: {
      en: 'Account Security Best Practices',
      zh: '账户安全最佳实践',
      'zh-TW': '帳戶安全最佳實踐',
    },
    description: {
      en: 'Keep your account secure with two-factor authentication and more',
      zh: '使用双因素身份验证等功能保护您的账户安全',
      'zh-TW': '使用雙因素身份驗證等功能保護您的帳戶安全',
    },
    category: 'account',
    readTime: {
      en: '6 min read',
      zh: '阅读时间 6 分钟',
      'zh-TW': '閱讀時間 6 分鐘',
    },
  },
];

/**
 * Support contact options
 */
export const SUPPORT_OPTIONS: SupportOption[] = [
  {
    id: 'live-chat',
    title: {
      en: 'Live Chat',
      zh: '在线聊天',
      'zh-TW': '線上聊天',
    },
    description: {
      en: 'Get instant help from our support team',
      zh: '从我们的支持团队获得即时帮助',
      'zh-TW': '從我們的支持團隊獲得即時幫助',
    },
    action: {
      en: 'Start Chat',
      zh: '开始聊天',
      'zh-TW': '開始聊天',
    },
    icon: SUPPORT_ICONS.liveChat,
    priority: 'high',
  },
  {
    id: 'email-support',
    title: {
      en: 'Email Support',
      zh: '邮件支持',
      'zh-TW': '郵件支持',
    },
    description: {
      en: "Send us a detailed message and we'll get back to you",
      zh: '发送详细信息给我们，我们会回复您',
      'zh-TW': '發送詳細信息給我們，我們會回覆您',
    },
    action: {
      en: 'Send Email',
      zh: '发送邮件',
      'zh-TW': '發送郵件',
    },
    icon: SUPPORT_ICONS.email,
    href: 'mailto:<EMAIL>',
    priority: 'medium',
  },
  {
    id: 'phone-support',
    title: {
      en: 'Phone Support',
      zh: '电话支持',
      'zh-TW': '電話支持',
    },
    description: {
      en: 'Call us for urgent issues (Premium plans only)',
      zh: '紧急问题请致电（仅限高级套餐）',
      'zh-TW': '緊急問題請致電（僅限高級套餐）',
    },
    action: {
      en: 'Call Now',
      zh: '立即致电',
      'zh-TW': '立即致電',
    },
    icon: SUPPORT_ICONS.phone,
    href: 'tel:******-123-4567',
    priority: 'high',
  },
  {
    id: 'documentation',
    title: {
      en: 'Documentation',
      zh: '文档',
      'zh-TW': '文檔',
    },
    description: {
      en: 'Browse our comprehensive documentation and guides',
      zh: '浏览我们全面的文档和指南',
      'zh-TW': '瀏覽我們全面的文檔和指南',
    },
    action: {
      en: 'View Docs',
      zh: '查看文档',
      'zh-TW': '查看文檔',
    },
    icon: SUPPORT_ICONS.documentation,
    href: '/help',
    priority: 'low',
  },
];

/**
 * Get translated text for a specific locale with fallback to English
 * @param text The translation object
 * @param locale The desired locale
 * @returns The translated string in the requested locale or English as fallback
 */
export function getTranslatedText(text: Translation, locale: string): string {
  // If translation for the requested locale exists, return it
  if (text[locale as keyof Translation]) {
    return text[locale as keyof Translation] as string;
  }

  // Otherwise fallback to English
  return text.en;
}

/**
 * Get support category by ID
 * @param categoryId The category ID
 * @returns The support category or undefined if not found
 */
export function getSupportCategory(
  categoryId: string
): SupportCategory | undefined {
  return SUPPORT_CATEGORIES.find(category => category.id === categoryId);
}

/**
 * Get all articles marked as popular
 * @returns Array of popular articles
 */
export function getPopularArticles(): SupportArticle[] {
  const popularArticles: SupportArticle[] = [];

  SUPPORT_CATEGORIES.forEach(category => {
    category.articles.forEach(article => {
      if (article.popular) {
        popularArticles.push({
          ...article,
          category: category.id,
        });
      }
    });
  });

  return popularArticles;
}

/**
 * Search articles by query
 * @param query Search query
 * @param locale Current locale for searching translated content
 * @returns Array of matching articles
 */
export function searchArticles(
  query: string,
  locale: string
): SupportArticle[] {
  if (!query.trim()) {
    return [];
  }

  const searchResults: SupportArticle[] = [];
  const lowercaseQuery = query.toLowerCase();

  SUPPORT_CATEGORIES.forEach(category => {
    category.articles.forEach(article => {
      const title = getTranslatedText(article.title, locale).toLowerCase();
      const summary = article.summary
        ? getTranslatedText(article.summary, locale).toLowerCase()
        : '';

      if (title.includes(lowercaseQuery) || summary.includes(lowercaseQuery)) {
        searchResults.push({
          ...article,
          category: category.id,
        });
      }
    });
  });

  return searchResults;
}

export default SUPPORT_CATEGORIES;
