/**
 * Testimonials Data
 *
 * This file contains customer testimonials data for the company.
 * In a production environment, this would likely be fetched from an API or CMS.
 */

import { Translation } from '@/types/shared';

/**
 * Testimonial interface
 */
export interface Testimonial {
  id: number;
  quote: Translation;
  author: string;
  title: Translation;
  avatar: string;
  company?: Translation;
}

/**
 * Customer testimonials data with multi-language support
 */
export const testimonials: Testimonial[] = [
  {
    id: 1,
    quote: {
      en: 'This platform has transformed how we manage our business operations. The intuitive interface and powerful features have saved us countless hours.',
      zh: '这个平台改变了我们管理业务运营的方式。直观的界面和强大的功能为我们节省了无数小时。',
      'zh-TW':
        '這個平台改變了我們管理業務運營的方式。直觀的界面和強大的功能為我們節省了無數小時。',
    },
    author: '<PERSON>',
    title: {
      en: 'CEO, TechStart Inc.',
      zh: '首席执行官，TechStart Inc.',
      'zh-TW': '執行長，TechStart Inc.',
    },
    avatar: '/images/testimonials/sarah-johnson.jpg',
  },
  {
    id: 2,
    quote: {
      en: "The analytics capabilities have given us insights we never had before. We've been able to optimize our processes and increase efficiency by 35%.",
      zh: '分析功能为我们提供了前所未有的洞察。我们能够优化流程并将效率提高35%。',
      'zh-TW':
        '分析功能為我們提供了前所未有的洞察。我們能夠優化流程並將效率提高35%。',
    },
    author: 'Michael Chen',
    title: {
      en: 'Operations Director, GlobalCorp',
      zh: '运营总监，GlobalCorp',
      'zh-TW': '營運總監，GlobalCorp',
    },
    avatar: '/images/testimonials/michael-chen.jpg',
  },
  {
    id: 3,
    quote: {
      en: "Customer support has been exceptional. Any issues we've had were resolved quickly, and the team is always receptive to feature suggestions.",
      zh: '客户支持非常出色。我们遇到的任何问题都得到了快速解决，团队总是乐于接受功能建议。',
      'zh-TW':
        '客戶支援非常出色。我們遇到的任何問題都得到了快速解決，團隊總是樂於接受功能建議。',
    },
    author: 'Emily Rodriguez',
    title: {
      en: 'Project Manager, InnovateGroup',
      zh: '项目经理，InnovateGroup',
      'zh-TW': '專案經理，InnovateGroup',
    },
    avatar: '/images/testimonials/emily-rodriguez.jpg',
  },
];

/**
 * Get translated text for a specific locale with fallback to English
 * @param text The translation object
 * @param locale The desired locale
 * @returns The translated string in the requested locale or English as fallback
 */
export function getTranslatedText(text: Translation, locale: string): string {
  // If translation for the requested locale exists, return it
  if (text[locale as keyof Translation]) {
    return text[locale as keyof Translation] as string;
  }

  // Otherwise fallback to English
  return text.en;
}

export default testimonials;
