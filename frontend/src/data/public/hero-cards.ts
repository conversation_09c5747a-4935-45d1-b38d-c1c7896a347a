export interface HeroCard {
  id: string;
  titleId: string;
  descriptionId: string;
  iconType: 'shield' | 'grid' | 'users';
  color: string;
  background: string;
}

export const heroCards: HeroCard[] = [
  {
    id: 'security',
    titleId: 'page.landing.feature1.title',
    descriptionId: 'page.landing.feature1.description',
    iconType: 'shield',
    color: '#10B981',
    background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(6, 95, 70, 0.3))',
  },
  {
    id: 'integration',
    titleId: 'page.landing.feature2.title',
    descriptionId: 'page.landing.feature2.description',
    iconType: 'grid',
    color: '#3B82F6',
    background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.3))',
  },
  {
    id: 'management',
    titleId: 'page.landing.feature3.title',
    descriptionId: 'page.landing.feature3.description',
    iconType: 'users',
    color: '#8B5CF6',
    background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(109, 40, 217, 0.3))',
  },
]; 