/**
 * Blog Data
 *
 * This file contains blog posts data for the company.
 * In a production environment, this would likely be fetched from an API or CMS.
 */

import { Translation, BlogPost, Category } from '@/types/public/blog';

/**
 * Available categories for post filtering with translations
 */
export const categories: Category[] = [
  {
    id: 'all',
    name: {
      en: 'All Posts',
      zh: '所有文章',
      'zh-TW': '所有文章',
    },
  },
  {
    id: 'technology',
    name: {
      en: 'Technology',
      zh: '科技',
      'zh-TW': '科技',
    },
  },
  {
    id: 'business',
    name: {
      en: 'Business',
      zh: '商业',
      'zh-TW': '商業',
    },
  },
  {
    id: 'tutorials',
    name: {
      en: 'Tutorials',
      zh: '教程',
      'zh-TW': '教程',
    },
  },
  {
    id: 'company',
    name: {
      en: 'Company News',
      zh: '公司新闻',
      'zh-TW': '公司新聞',
    },
  },
  {
    id: 'industry',
    name: {
      en: 'Industry Insights',
      zh: '行业洞察',
      'zh-TW': '行業洞察',
    },
  },
];

/**
 * Blog posts data with multi-language support
 * Some entries include all translations, some only have English as example
 */
export const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: {
      en: 'How to Improve Your Business Efficiency with Automation',
      zh: '如何通过自动化提高业务效率',
      'zh-TW': '如何通過自動化提高業務效率',
    },
    excerpt: {
      en: 'Explore how automating repetitive tasks can save time and reduce errors in your business operations.',
      zh: '探索如何通过自动化重复性任务来节省时间并减少业务运营中的错误。',
      'zh-TW': '探索如何通過自動化重複性任務來節省時間並減少業務運營中的錯誤。',
    },
    category: 'business',
    author: {
      name: 'Jane Doe',
      avatar: '/images/blog/authors/jane-doe.jpg',
      title: 'Business Strategist',
    },
    publishDate: '2023-11-15',
    readTime: {
      en: '8 min read',
      zh: '阅读时间 8 分钟',
      'zh-TW': '閱讀時間 8 分鐘',
    },
    image: '/images/blog/post-1.jpg',
    featured: true,
    tags: ['automation', 'efficiency', 'business-operations'],
  },
  {
    id: 2,
    title: {
      en: 'The Future of AI in Enterprise Applications',
      zh: '人工智能在企业应用中的未来',
      'zh-TW': '人工智能在企業應用中的未來',
    },
    excerpt: {
      en: 'Discover how artificial intelligence is transforming enterprise software and what to expect in the coming years.',
      zh: '了解人工智能如何改变企业软件以及未来几年的预期发展。',
      'zh-TW': '了解人工智能如何改變企業軟件以及未來幾年的預期發展。',
    },
    category: 'technology',
    author: {
      name: 'John Smith',
      avatar: '/images/blog/authors/john-smith.jpg',
      title: 'Tech Lead',
    },
    publishDate: '2023-11-10',
    readTime: {
      en: '10 min read',
      zh: '阅读时间 10 分钟',
      'zh-TW': '閱讀時間 10 分鐘',
    },
    image: '/images/blog/post-2.jpg',
    featured: true,
    tags: ['ai', 'enterprise', 'future-tech'],
  },
  {
    id: 3,
    title: {
      en: '5 Best Practices for Data Security in 2023',
    },
    excerpt: {
      en: "Learn essential strategies to protect your organization's data in an increasingly complex threat landscape.",
    },
    category: 'technology',
    author: {
      name: 'Sarah Williams',
      avatar: '/images/blog/authors/sarah-williams.jpg',
      title: 'Security Specialist',
    },
    publishDate: '2023-11-05',
    readTime: {
      en: '6 min read',
    },
    image: '/images/blog/post-3.jpg',
    featured: false,
    tags: ['security', 'data-protection', 'best-practices'],
  },
  {
    id: 4,
    title: {
      en: 'How We Built Our New User Dashboard',
      zh: '我们如何构建新的用户仪表板',
      'zh-TW': '我們如何構建新的用戶儀表板',
    },
    excerpt: {
      en: 'A behind-the-scenes look at our design and development process for the latest user dashboard update.',
      zh: '深入了解我们最新用户仪表板更新的设计和开发过程。',
      'zh-TW': '深入了解我們最新用戶儀表板更新的設計和開發過程。',
    },
    category: 'tutorials',
    author: {
      name: 'Michael Chen',
      avatar: '/images/blog/authors/michael-chen.jpg',
      title: 'UI/UX Designer',
    },
    publishDate: '2023-10-28',
    readTime: {
      en: '12 min read',
      zh: '阅读时间 12 分钟',
      'zh-TW': '閱讀時間 12 分鐘',
    },
    image: '/images/blog/post-4.jpg',
    featured: false,
    tags: ['design', 'development', 'user-experience'],
  },
  {
    id: 5,
    title: {
      en: 'Quarterly Update: New Features and Improvements',
    },
    excerpt: {
      en: "Explore all the new capabilities we've added to our platform in the past quarter.",
    },
    category: 'company',
    author: {
      name: 'Emily Johnson',
      avatar: '/images/blog/authors/emily-johnson.jpg',
      title: 'Product Manager',
    },
    publishDate: '2023-10-15',
    readTime: {
      en: '5 min read',
    },
    image: '/images/blog/post-5.jpg',
    featured: false,
    tags: ['product-updates', 'features', 'release-notes'],
  },
  {
    id: 6,
    title: {
      en: "Industry Trends Report: What's Next in Digital Transformation",
    },
    excerpt: {
      en: 'Our analysis of the latest trends and future directions in enterprise digital transformation.',
    },
    category: 'industry',
    author: {
      name: 'David Patel',
      avatar: '/images/blog/authors/david-patel.jpg',
      title: 'Research Analyst',
    },
    publishDate: '2023-10-08',
    readTime: {
      en: '9 min read',
    },
    image: '/images/blog/post-6.jpg',
    featured: false,
    tags: ['digital-transformation', 'trends', 'analysis'],
  },
];

/**
 * Gets the localized text for multilingual content
 * Falls back to English if the requested locale isn't available
 */
export function getTranslatedText(text: Translation, locale: string): string {
  return text[locale as keyof Translation] || text.en;
}

/**
 * Gets the translated category name for a given category ID
 */
export function getCategoryName(categoryId: string, locale: string): string {
  const category = categories.find(cat => cat.id === categoryId);
  if (!category) return '';
  return getTranslatedText(category.name, locale);
}

/**
 * Filters posts by category and search query
 */
export function filterPosts(
  posts: BlogPost[],
  category: string,
  query: string,
  locale: string
): BlogPost[] {
  return posts.filter(post => {
    const matchesCategory = category === 'all' || post.category === category;
    const matchesSearch =
      query === '' ||
      getTranslatedText(post.title, locale)
        .toLowerCase()
        .includes(query.toLowerCase()) ||
      getTranslatedText(post.excerpt, locale)
        .toLowerCase()
        .includes(query.toLowerCase()) ||
      post.tags.some((tag: string) =>
        tag.toLowerCase().includes(query.toLowerCase())
      );

    return matchesCategory && matchesSearch;
  });
}

/**
 * Gets all featured posts
 */
export function getFeaturedPosts(posts: BlogPost[]): BlogPost[] {
  return posts.filter(post => post.featured);
}

/**
 * Gets paginated posts
 */
export function getPaginatedPosts(
  posts: BlogPost[],
  page: number,
  postsPerPage: number
): BlogPost[] {
  return posts.slice((page - 1) * postsPerPage, page * postsPerPage);
}

export default blogPosts;
