/**
 * Careers Data
 *
 * This file contains job openings data for the company.
 * In a production environment, this would likely be fetched from an API or CMS.
 */

import { Translation } from '@/types/public/blog';
import { JobOpening, Department } from '@/types/public/careers';

/**
 * Available departments for job filtering with translations
 */
export const departments: Department[] = [
  {
    id: 'all',
    name: {
      en: 'All Departments',
      zh: '所有部门',
      'zh-TW': '所有部門',
    },
  },
  {
    id: 'engineering',
    name: {
      en: 'Engineering',
      zh: '工程部',
      'zh-TW': '工程部',
    },
  },
  {
    id: 'product',
    name: {
      en: 'Product',
      zh: '产品部',
      'zh-TW': '產品部',
    },
  },
  {
    id: 'marketing',
    name: {
      en: 'Marketing',
      zh: '市场部',
      'zh-TW': '市場部',
    },
  },
  {
    id: 'customer',
    name: {
      en: 'Customer Success',
      zh: '客户成功部',
      'zh-TW': '客戶成功部',
    },
  },
];

/**
 * Job openings data with multi-language support
 * Some entries include all translations, some only have English as example
 */
export const jobOpenings: JobOpening[] = [
  {
    id: 1,
    title: {
      en: 'Senior Frontend Developer',
      zh: '高级前端开发工程师',
      'zh-TW': '高級前端開發工程師',
    },
    department: 'engineering',
    location: {
      en: 'San Francisco, CA (Remote Optional)',
      zh: '旧金山（可远程）',
      'zh-TW': '舊金山（可遠程）',
    },
    type: {
      en: 'Full-time',
      zh: '全职',
      'zh-TW': '全職',
    },
    description: {
      en: "We're looking for a Senior Frontend Developer to help build and improve our user interfaces. The ideal candidate has experience with React, TypeScript, and modern frontend tooling.",
      zh: '我们正在寻找高级前端开发工程师来帮助构建和改进我们的用户界面。理想的候选人应具有React、TypeScript和现代前端工具的经验。',
      'zh-TW':
        '我們正在尋找高級前端開發工程師來幫助構建和改進我們的用戶界面。理想的候選人應具有React、TypeScript和現代前端工具的經驗。',
    },
    responsibilities: [
      {
        en: 'Design, develop, and maintain user interfaces for web applications',
        zh: '设计、开发和维护Web应用程序的用户界面',
        'zh-TW': '設計、開發和維護Web應用程序的用戶界面',
      },
      {
        en: 'Collaborate with backend developers, designers, and product managers',
        zh: '与后端开发人员、设计师和产品经理合作',
        'zh-TW': '與後端開發人員、設計師和產品經理合作',
      },
      {
        en: 'Optimize applications for maximum performance and scalability',
        zh: '优化应用程序以获得最佳性能和可扩展性',
        'zh-TW': '優化應用程序以獲得最佳性能和可擴展性',
      },
      {
        en: 'Implement responsive design and ensure cross-browser compatibility',
        zh: '实现响应式设计并确保跨浏览器兼容性',
        'zh-TW': '實現響應式設計並確保跨瀏覽器兼容性',
      },
    ],
    requirements: [
      {
        en: '5+ years of experience in frontend development',
        zh: '5年以上前端开发经验',
        'zh-TW': '5年以上前端開發經驗',
      },
      {
        en: 'Strong proficiency in React, TypeScript, and modern JavaScript',
        zh: '精通React、TypeScript和现代JavaScript',
        'zh-TW': '精通React、TypeScript和現代JavaScript',
      },
      {
        en: 'Experience with state management solutions (Redux, Context API, etc.)',
        zh: '有状态管理解决方案经验（Redux、Context API等）',
        'zh-TW': '有狀態管理解決方案經驗（Redux、Context API等）',
      },
      {
        en: 'Knowledge of responsive design and CSS frameworks (Tailwind, etc.)',
        zh: '了解响应式设计和CSS框架（Tailwind等）',
        'zh-TW': '了解響應式設計和CSS框架（Tailwind等）',
      },
      {
        en: 'Understanding of web performance optimization',
        zh: '了解Web性能优化',
        'zh-TW': '了解Web性能優化',
      },
    ],
  },
  {
    id: 2,
    title: {
      en: 'Product Manager',
      zh: '产品经理',
      'zh-TW': '產品經理',
    },
    department: 'product',
    location: {
      en: 'New York, NY (Hybrid)',
      zh: '纽约（混合办公）',
      'zh-TW': '紐約（混合辦公）',
    },
    type: {
      en: 'Full-time',
      zh: '全职',
      'zh-TW': '全職',
    },
    description: {
      en: "We're seeking a Product Manager to drive the development of our core product features. You'll work closely with engineering, design, and marketing teams to deliver outstanding user experiences.",
    },
    responsibilities: [
      {
        en: 'Lead the development of product features from conception to launch',
      },
      {
        en: 'Gather and analyze user feedback to inform product decisions',
      },
      {
        en: 'Create and maintain product roadmaps and backlog prioritization',
      },
      {
        en: 'Collaborate with cross-functional teams to ensure successful product delivery',
      },
    ],
    requirements: [
      {
        en: '3+ years of experience in product management',
      },
      {
        en: 'Strong analytical and problem-solving skills',
      },
      {
        en: 'Excellent communication and interpersonal abilities',
      },
      {
        en: 'Experience with agile development methodologies',
      },
      {
        en: 'Technical background or familiarity with SaaS products preferred',
      },
    ],
  },
  {
    id: 3,
    title: {
      en: 'Marketing Specialist',
      zh: '市场专员',
      'zh-TW': '市場專員',
    },
    department: 'marketing',
    location: {
      en: 'Remote',
      zh: '远程',
      'zh-TW': '遠程',
    },
    type: {
      en: 'Full-time',
    },
    description: {
      en: "Join our marketing team to help grow our brand presence and drive user acquisition. You'll be responsible for creating and executing marketing campaigns across various channels.",
    },
    responsibilities: [
      {
        en: 'Plan and execute digital marketing campaigns',
        zh: '规划和执行数字营销活动',
        'zh-TW': '規劃和執行數字營銷活動',
      },
      {
        en: 'Manage social media presence and content creation',
        zh: '管理社交媒体存在和内容创建',
        'zh-TW': '管理社交媒體存在和內容創建',
      },
      {
        en: 'Analyze campaign performance and optimize marketing strategies',
        zh: '分析活动效果并优化营销策略',
        'zh-TW': '分析活動效果並優化營銷策略',
      },
      {
        en: 'Collaborate with product and sales teams on messaging and positioning',
      },
    ],
    requirements: [
      {
        en: '2+ years of experience in digital marketing',
      },
      {
        en: 'Proficiency with marketing analytics tools and social media platforms',
      },
      {
        en: 'Strong written and verbal communication skills',
      },
      {
        en: 'Experience with content creation and email marketing',
      },
      {
        en: 'Data-driven mindset with ability to measure and report on results',
      },
    ],
  },
  {
    id: 4,
    title: {
      en: 'Customer Success Manager',
    },
    department: 'customer',
    location: {
      en: 'Chicago, IL (Remote Optional)',
    },
    type: {
      en: 'Full-time',
    },
    description: {
      en: "We're looking for a Customer Success Manager to ensure our customers get maximum value from our products. You'll build relationships with key accounts and drive customer satisfaction and retention.",
    },
    responsibilities: [
      {
        en: 'Serve as the primary point of contact for assigned customer accounts',
      },
      {
        en: 'Develop and maintain strong relationships with customers',
      },
      {
        en: 'Monitor account health and identify opportunities for growth',
      },
      {
        en: 'Work with product and engineering teams to address customer needs',
      },
    ],
    requirements: [
      {
        en: '3+ years of experience in customer success or account management',
      },
      {
        en: 'Strong interpersonal and communication skills',
      },
      {
        en: 'Problem-solving mindset with ability to navigate complex situations',
      },
      {
        en: 'Experience with CRM systems and tools',
      },
      {
        en: 'Background in SaaS or technology industry preferred',
      },
    ],
  },
];

/**
 * Get translated text for a specific locale with fallback to English
 * @param text The translation object
 * @param locale The desired locale
 * @returns The translated string in the requested locale or English as fallback
 */
export function getTranslatedText(text: Translation, locale: string): string {
  // If translation for the requested locale exists, return it
  if (text[locale as keyof Translation]) {
    return text[locale as keyof Translation] as string;
  }

  // Otherwise fallback to English
  return text.en;
}

/**
 * Get translated array content
 * @param items Array of translation objects
 * @param locale The desired locale
 * @returns Array of translated strings
 */
export function getTranslatedArray(
  items: Translation[],
  locale: string
): string[] {
  return items.map(item => getTranslatedText(item, locale));
}

/**
 * Get department name for a specific locale
 * @param departmentId The department ID
 * @param locale The desired locale
 * @returns The translated department name
 */
export function getTranslatedDepartmentName(
  departmentId: string,
  locale: string
): string {
  const department = departments.find(dept => dept.id === departmentId);
  if (!department) return '';
  return getTranslatedText(department.name, locale);
}

/**
 * Filter job openings by department
 * @param department Department ID to filter by ('all' for all departments)
 * @returns Filtered array of job openings
 */
export function filterJobsByDepartment(department: string): JobOpening[] {
  return department === 'all'
    ? jobOpenings
    : jobOpenings.filter(job => job.department === department);
}

export default jobOpenings;
