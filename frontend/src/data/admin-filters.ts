import {
  Users,
  Shield,
  Settings,
  UserCheck,
  UserX,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity
} from 'lucide-react';
import { FilterOption } from '@/components/common/filter-panel';
import { IntlShape } from 'react-intl';

/**
 * User filter options and categories for admin dashboard
 */
export const getUserFilterOptions = (intl: IntlShape) => ({
  status: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allStatus' }) },
    { id: 'active', name: intl.formatMessage({ id: 'components.filters.active' }), icon: UserCheck },
    { id: 'inactive', name: intl.formatMessage({ id: 'components.filters.inactive' }), icon: UserX }
  ] as FilterOption[],

  role: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allRoles' }) },
    { id: 'user', name: intl.formatMessage({ id: 'components.filters.user' }), icon: Users },
    { id: 'admin', name: intl.formatMessage({ id: 'components.filters.admin' }), icon: Shield },
    { id: 'developer', name: intl.formatMessage({ id: 'components.filters.developer' }), icon: Settings }
  ] as FilterOption[]
});

export const getUserFilterCategories = (
  intl: IntlShape,
  statusSelected: string,
  roleSelected: string[],
  onStatusSelect: (selected: string | string[]) => void,
  onRoleSelect: (selected: string | string[]) => void
) => {
  const userFilterOptions = getUserFilterOptions(intl);
  return [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'components.filters.status' }),
      options: userFilterOptions.status,
      color: 'primary' as const,
      selected: statusSelected,
      onSelect: onStatusSelect,
      icon: Activity,
      multiSelect: false
    },
    {
      id: 'role',
      name: intl.formatMessage({ id: 'components.filters.role' }),
      options: userFilterOptions.role,
      color: 'secondary' as const,
      selected: roleSelected,
      onSelect: onRoleSelect,
      icon: Shield,
      multiSelect: true
    }
  ];
};

/**
 * Application filter options and categories for admin dashboard
 */
export const getApplicationFilterOptions = (intl: IntlShape) => ({
  status: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allStatus' }) },
    { id: 'active', name: intl.formatMessage({ id: 'components.filters.active' }), icon: CheckCircle },
    { id: 'inactive', name: intl.formatMessage({ id: 'components.filters.inactive' }), icon: XCircle }
  ] as FilterOption[],

  approval: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allApprovals' }) },
    { id: 'approved', name: intl.formatMessage({ id: 'components.filters.approved' }), icon: CheckCircle },
    { id: 'pending', name: intl.formatMessage({ id: 'components.filters.pending' }), icon: AlertCircle }
  ] as FilterOption[]
});

export const getApplicationFilterCategories = (
  intl: IntlShape,
  statusSelected: string,
  approvalSelected: string,
  onStatusSelect: (selected: string | string[]) => void,
  onApprovalSelect: (selected: string | string[]) => void
) => {
  const applicationFilterOptions = getApplicationFilterOptions(intl);
  return [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'components.filters.status' }),
      options: applicationFilterOptions.status,
      color: 'primary' as const,
      selected: statusSelected,
      onSelect: onStatusSelect,
      icon: Activity,
      multiSelect: false
    },
    {
      id: 'approval',
      name: intl.formatMessage({ id: 'components.filters.approval' }),
      options: applicationFilterOptions.approval,
      color: 'accent' as const,
      selected: approvalSelected,
      onSelect: onApprovalSelect,
      icon: CheckCircle,
      multiSelect: false
    }
  ];
};

/**
 * Filter logic helpers
 */
export const filterUsers = (
  users: any[],
  searchTerm: string,
  statusFilter: string,
  roleFilters: string[]
) => {
  if (!users || !Array.isArray(users)) return [];
  
  return users.filter(user => {
    if (!user) return false;
    
    // Search filter
    const matchesSearch = !searchTerm ||
      (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    // Status filter (single selection)
    const matchesStatus = !statusFilter || statusFilter === 'all' ||
      (statusFilter === 'active' && user.is_active) ||
      (statusFilter === 'inactive' && !user.is_active);
    
    // Role filter (multi selection)
    const matchesRole = roleFilters.length === 0 ||
      (user.role && roleFilters.includes(user.role.toLowerCase()));
    
    return matchesSearch && matchesStatus && matchesRole;
  });
};

export const filterApplications = (
  applications: any[],
  searchTerm: string,
  statusFilter: string,
  approvalFilter: string
) => {
  if (!applications || !Array.isArray(applications)) return [];
  
  return applications.filter(app => {
    if (!app) return false;
    
    // Search filter
    const matchesSearch = !searchTerm ||
      (app.application_name && app.application_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (app.owner_email && app.owner_email.toLowerCase().includes(searchTerm.toLowerCase()));
    
    // Status filter (single selection)
    const matchesStatus = !statusFilter || statusFilter === 'all' ||
      (statusFilter === 'active' && app.is_active) ||
      (statusFilter === 'inactive' && !app.is_active);
    
    // Approval filter (single selection)
    const matchesApproval = !approvalFilter || approvalFilter === 'all' ||
      (approvalFilter === 'approved' && app.is_approved) ||
      (approvalFilter === 'pending' && !app.is_approved);
    
    return matchesSearch && matchesStatus && matchesApproval;
  });
};
