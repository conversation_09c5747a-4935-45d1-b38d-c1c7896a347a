/**
 * Developer API Endpoints Data
 * 
 * Centralized data for API documentation endpoints, categories, and code examples.
 * This data is used by the developer API documentation page.
 */

export interface APIEndpoint {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  title: string;
  description: string;
  category: string;
  requiresAuth: boolean;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  requestBody?: {
    type: string;
    example: string;
  };
  responses: Array<{
    status: number;
    description: string;
    example: string;
  }>;
}

export interface APICategory {
  id: string;
  nameKey: string; // Translation key
  count: number;
}

// API Categories with translation keys
export const getApiCategories = (): APICategory[] => [
  { id: 'all', nameKey: 'developer.apiDocs.allEndpoints', count: 15 },
  { id: 'authentication', nameKey: 'developer.apiDocs.authentication', count: 4 },
  { id: 'users', nameKey: 'developer.apiDocs.userManagement', count: 6 },
  { id: 'applications', nameKey: 'developer.apiDocs.applications', count: 3 },
  { id: 'oauth', nameKey: 'developer.apiDocs.oauth2', count: 2 }
];

// API Endpoints data
export const getApiEndpoints = (): APIEndpoint[] => [
  {
    id: 'auth-login',
    method: 'POST',
    path: '/api/v1/auth/login',
    title: 'User Login',
    description: 'Authenticate a user and receive access tokens',
    category: 'authentication',
    requiresAuth: false,
    requestBody: {
      type: 'application/json',
      example: JSON.stringify({
        email: '<EMAIL>',
        password: 'securePassword123'
      }, null, 2)
    },
    responses: [
      {
        status: 200,
        description: 'Login successful',
        example: JSON.stringify({
          access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          refresh_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          token_type: 'bearer',
          expires_in: 3600,
          user: {
            id: 'user_123',
            email: '<EMAIL>',
            name: 'John Doe'
          }
        }, null, 2)
      },
      {
        status: 401,
        description: 'Invalid credentials',
        example: JSON.stringify({
          error: 'invalid_credentials',
          message: 'Invalid email or password'
        }, null, 2)
      }
    ]
  },
  {
    id: 'user-profile',
    method: 'GET',
    path: '/api/v1/user/profile',
    title: 'Get User Profile',
    description: 'Retrieve the authenticated user\'s profile information',
    category: 'users',
    requiresAuth: true,
    responses: [
      {
        status: 200,
        description: 'Profile retrieved successfully',
        example: JSON.stringify({
          id: 'user_123',
          email: '<EMAIL>',
          name: 'John Doe',
          phone: '+1234567890',
          avatar_url: 'https://example.com/avatar.jpg',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T12:00:00Z'
        }, null, 2)
      }
    ]
  },
  {
    id: 'oauth-authorize',
    method: 'GET',
    path: '/api/v1/oauth/authorize',
    title: 'OAuth2 Authorization',
    description: 'Initiate OAuth2 authorization flow',
    category: 'oauth',
    requiresAuth: false,
    parameters: [
      { name: 'client_id', type: 'string', required: true, description: 'Your application client ID' },
      { name: 'redirect_uri', type: 'string', required: true, description: 'Callback URL for your application' },
      { name: 'response_type', type: 'string', required: true, description: 'Must be "code"' },
      { name: 'scope', type: 'string', required: false, description: 'Requested permissions (space-separated)' },
      { name: 'state', type: 'string', required: false, description: 'CSRF protection parameter' }
    ],
    responses: [
      {
        status: 302,
        description: 'Redirect to authorization page',
        example: 'Location: /oauth/authorize?client_id=...&redirect_uri=...&response_type=code'
      }
    ]
  }
];

// Code example generators
export const generateCodeExample = (endpoint: APIEndpoint, language: string, intl: any): string => {
  switch (language) {
    case 'javascript':
      return `// Using fetch API
const response = await fetch('${endpoint.path}', {
  method: '${endpoint.method}',
  headers: {
    'Content-Type': 'application/json',
    ${endpoint.requiresAuth ? "'Authorization': 'Bearer YOUR_ACCESS_TOKEN'," : ''}
  },
  ${endpoint.requestBody ? `body: JSON.stringify(${endpoint.requestBody.example})` : ''}
});

const data = await response.json();
console.log(data);`;

    case 'python':
      return `import requests

url = "${endpoint.path}"
headers = {
    "Content-Type": "application/json",
    ${endpoint.requiresAuth ? '"Authorization": "Bearer YOUR_ACCESS_TOKEN",' : ''}
}

${endpoint.requestBody ? `data = ${endpoint.requestBody.example}

response = requests.${endpoint.method.toLowerCase()}(url, headers=headers, json=data)` : `response = requests.${endpoint.method.toLowerCase()}(url, headers=headers)`}
print(response.json())`;

    case 'curl':
      return `curl -X ${endpoint.method} "${endpoint.path}" \\
  -H "Content-Type: application/json" \\
  ${endpoint.requiresAuth ? '-H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\' : ''}
  ${endpoint.requestBody ? `-d '${endpoint.requestBody.example}'` : ''}`;

    default:
      return intl.formatMessage({ id: 'developer.apiDocs.codeNotAvailable' });
  }
};
