import {
  Globe,
  Shield,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Users,
  Settings,
  Building2,
  Key,
  BarChart3
} from 'lucide-react';
import { FilterOption } from '@/components/common/filter-panel';
import { IntlShape } from 'react-intl';

/**
 * Application filter options and categories for developer dashboard
 */
export const getApplicationFilterOptions = (intl: IntlShape) => ({
  status: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allStatus' }) },
    { id: 'active', name: intl.formatMessage({ id: 'components.filters.active' }), icon: CheckCircle },
    { id: 'inactive', name: intl.formatMessage({ id: 'components.filters.inactive' }), icon: XCircle },
    { id: 'pending', name: intl.formatMessage({ id: 'components.filters.pendingReview' }), icon: Clock },
    { id: 'suspended', name: intl.formatMessage({ id: 'components.filters.suspended' }), icon: AlertCircle }
  ] as FilterOption[],

  type: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTypes' }) },
    { id: 'web', name: intl.formatMessage({ id: 'components.filters.webApplication' }), icon: Globe },
    { id: 'mobile', name: intl.formatMessage({ id: 'components.filters.mobileApp' }), icon: Settings },
    { id: 'api', name: intl.formatMessage({ id: 'components.filters.apiService' }), icon: Key },
    { id: 'spa', name: intl.formatMessage({ id: 'components.filters.singlePageApp' }), icon: Building2 }
  ] as FilterOption[],

  environment: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allEnvironments' }) },
    { id: 'development', name: intl.formatMessage({ id: 'components.filters.development' }), icon: Settings },
    { id: 'staging', name: intl.formatMessage({ id: 'components.filters.staging' }), icon: Activity },
    { id: 'production', name: intl.formatMessage({ id: 'components.filters.production' }), icon: Shield }
  ] as FilterOption[]
});

/**
 * Analytics filter options and categories for developer dashboard
 */
export const getAnalyticsFilterOptions = (intl: IntlShape) => ({
  metric: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allMetrics' }) },
    { id: 'users', name: intl.formatMessage({ id: 'components.filters.userMetrics' }), icon: Users },
    { id: 'logins', name: intl.formatMessage({ id: 'components.filters.loginActivity' }), icon: Activity },
    { id: 'performance', name: intl.formatMessage({ id: 'components.filters.performance' }), icon: BarChart3 },
    { id: 'errors', name: intl.formatMessage({ id: 'components.filters.errorRates' }), icon: AlertCircle }
  ] as FilterOption[],

  timeframe: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTime' }) },
    { id: 'today', name: intl.formatMessage({ id: 'components.filters.today' }), icon: Clock },
    { id: 'week', name: intl.formatMessage({ id: 'components.filters.thisWeek' }), icon: Activity },
    { id: 'month', name: intl.formatMessage({ id: 'components.filters.thisMonth' }), icon: BarChart3 },
    { id: 'quarter', name: intl.formatMessage({ id: 'components.filters.thisQuarter' }), icon: BarChart3 }
  ] as FilterOption[]
});

/**
 * Get application filter categories for developer dashboard
 */
export const getApplicationFilterCategories = (
  intl: IntlShape,
  statusFilter: string,
  typeFilter: string,
  environmentFilter: string,
  onStatusChange: (value: string | string[]) => void,
  onTypeChange: (value: string | string[]) => void,
  onEnvironmentChange: (value: string | string[]) => void
) => {
  const applicationFilterOptions = getApplicationFilterOptions(intl);
  return [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'components.filters.status' }),
      options: applicationFilterOptions.status,
      selected: statusFilter,
      onSelect: onStatusChange,
      icon: CheckCircle,
      color: 'primary' as const
    },
    {
      id: 'type',
      name: intl.formatMessage({ id: 'components.filters.type' }),
      options: applicationFilterOptions.type,
      selected: typeFilter,
      onSelect: onTypeChange,
      icon: Building2,
      color: 'secondary' as const
    },
    {
      id: 'environment',
      name: intl.formatMessage({ id: 'components.filters.environment' }),
      options: applicationFilterOptions.environment,
      selected: environmentFilter,
      onSelect: onEnvironmentChange,
      icon: Settings,
      color: 'accent' as const
    }
  ];
};

/**
 * Get analytics filter categories for developer dashboard
 */
export const getAnalyticsFilterCategories = (
  intl: IntlShape,
  metricFilter: string,
  timeframeFilter: string,
  onMetricChange: (value: string | string[]) => void,
  onTimeframeChange: (value: string | string[]) => void
) => {
  const analyticsFilterOptions = getAnalyticsFilterOptions(intl);
  return [
    {
      id: 'metric',
      name: intl.formatMessage({ id: 'components.filters.metrics' }),
      options: analyticsFilterOptions.metric,
      selected: metricFilter,
      onSelect: onMetricChange,
      icon: BarChart3,
      color: 'primary' as const
    },
    {
      id: 'timeframe',
      name: intl.formatMessage({ id: 'components.filters.timeframe' }),
      options: analyticsFilterOptions.timeframe,
      selected: timeframeFilter,
      onSelect: onTimeframeChange,
      icon: Clock,
      color: 'secondary' as const
    }
  ];
};

/**
 * Filter applications based on search term and filters
 */
export const filterApplications = (
  applications: any[],
  searchTerm: string,
  statusFilter: string,
  typeFilter: string,
  environmentFilter: string
) => {
  if (!applications) return [];

  return applications.filter(app => {
    // Search filter
    const matchesSearch = !searchTerm || 
      app.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.client_id?.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;

    // Type filter
    const matchesType = typeFilter === 'all' || app.application_type === typeFilter;

    // Environment filter
    const matchesEnvironment = environmentFilter === 'all' || app.environment === environmentFilter;

    return matchesSearch && matchesStatus && matchesType && matchesEnvironment;
  });
};

/**
 * Filter analytics data based on filters
 */
export const filterAnalytics = (
  analytics: any,
  metricFilter: string,
  timeframeFilter: string
) => {
  if (!analytics) return analytics;

  // This would typically filter the analytics data based on the selected filters
  // For now, we'll return the analytics as-is since the filtering would be done
  // at the API level in a real implementation
  return analytics;
};
