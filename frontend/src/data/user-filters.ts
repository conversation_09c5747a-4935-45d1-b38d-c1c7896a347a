import {
  Globe,
  Shield,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Users,
  Settings
} from 'lucide-react';
import { FilterOption } from '@/components/common/filter-panel';
import { IntlShape } from 'react-intl';

/**
 * Application filter options and categories for user dashboard
 */
export const getApplicationFilterOptions = (intl: IntlShape) => ({
  status: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allStatus' }) },
    { id: 'active', name: intl.formatMessage({ id: 'components.filters.active' }), icon: CheckCircle },
    { id: 'inactive', name: intl.formatMessage({ id: 'components.filters.inactive' }), icon: XCircle },
    { id: 'pending', name: intl.formatMessage({ id: 'components.filters.pending' }), icon: Clock }
  ] as FilterOption[],

  type: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTypes' }) },
    { id: 'web', name: intl.formatMessage({ id: 'components.filters.webApp' }), icon: Globe },
    { id: 'mobile', name: intl.formatMessage({ id: 'components.filters.mobileApp' }), icon: Settings },
    { id: 'api', name: intl.formatMessage({ id: 'components.filters.apiService' }), icon: Activity }
  ] as FilterOption[],

  permissions: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allPermissions' }) },
    { id: 'read', name: intl.formatMessage({ id: 'components.filters.readOnly' }), icon: Users },
    { id: 'write', name: intl.formatMessage({ id: 'components.filters.readWrite' }), icon: Settings },
    { id: 'admin', name: intl.formatMessage({ id: 'components.filters.adminAccess' }), icon: Shield }
  ] as FilterOption[]
});

/**
 * Activity filter options and categories for user dashboard
 */
export const getActivityFilterOptions = (intl: IntlShape) => ({
  type: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allActivities' }) },
    { id: 'login', name: intl.formatMessage({ id: 'components.filters.logins' }), icon: CheckCircle },
    { id: 'permission', name: intl.formatMessage({ id: 'components.filters.permissions' }), icon: Shield },
    { id: 'app_access', name: intl.formatMessage({ id: 'components.filters.appAccess' }), icon: Globe },
    { id: 'security', name: intl.formatMessage({ id: 'components.filters.security' }), icon: AlertCircle }
  ] as FilterOption[],

  timeframe: [
    { id: 'all', name: intl.formatMessage({ id: 'components.filters.allTime' }) },
    { id: 'today', name: intl.formatMessage({ id: 'components.filters.today' }), icon: Clock },
    { id: 'week', name: intl.formatMessage({ id: 'components.filters.thisWeek' }), icon: Activity },
    { id: 'month', name: intl.formatMessage({ id: 'components.filters.thisMonth' }), icon: Activity }
  ] as FilterOption[]
});

/**
 * Get application filter categories for user dashboard
 */
export const getApplicationFilterCategories = (
  intl: IntlShape,
  statusFilter: string,
  typeFilter: string,
  permissionFilter: string,
  onStatusChange: (value: string | string[]) => void,
  onTypeChange: (value: string | string[]) => void,
  onPermissionChange: (value: string | string[]) => void
) => {
  const applicationFilterOptions = getApplicationFilterOptions(intl);
  return [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'components.filters.status' }),
      options: applicationFilterOptions.status,
      selected: statusFilter,
      onSelect: onStatusChange,
      icon: CheckCircle,
      color: 'primary' as const
    },
    {
      id: 'type',
      name: intl.formatMessage({ id: 'components.filters.type' }),
      options: applicationFilterOptions.type,
      selected: typeFilter,
      onSelect: onTypeChange,
      icon: Globe,
      color: 'secondary' as const
    },
    {
      id: 'permissions',
      name: intl.formatMessage({ id: 'components.filters.permissions' }),
      options: applicationFilterOptions.permissions,
      selected: permissionFilter,
      onSelect: onPermissionChange,
      icon: Shield,
      color: 'accent' as const
    }
  ];
};

/**
 * Get activity filter categories for user dashboard
 */
export const getActivityFilterCategories = (
  intl: IntlShape,
  typeFilter: string,
  timeframeFilter: string,
  onTypeChange: (value: string | string[]) => void,
  onTimeframeChange: (value: string | string[]) => void
) => {
  const activityFilterOptions = getActivityFilterOptions(intl);
  return [
    {
      id: 'type',
      name: intl.formatMessage({ id: 'user.dashboard.filters.activity.type' }),
      options: activityFilterOptions.type,
      selected: typeFilter,
      onSelect: onTypeChange,
      icon: Activity,
      color: 'primary' as const
    },
    {
      id: 'timeframe',
      name: intl.formatMessage({ id: 'components.filters.timeframe' }),
      options: activityFilterOptions.timeframe,
      selected: timeframeFilter,
      onSelect: onTimeframeChange,
      icon: Clock,
      color: 'secondary' as const
    }
  ];
};

/**
 * Filter applications based on search term and filters
 */
export const filterApplications = (
  applications: any[],
  searchTerm: string,
  statusFilter: string,
  typeFilter: string,
  permissionFilter: string
) => {
  if (!applications || !Array.isArray(applications)) return [];

  return applications.filter(app => {
    if (!app) return false;

    // Search filter - use correct property names from API
    const matchesSearch = !searchTerm || 
      app.application_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.developer_email?.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter - use is_active property from API
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && app.is_active) ||
      (statusFilter === 'inactive' && !app.is_active);

    // Type filter - default to 'web' if no type specified
    const appType = app.application_type || 'web';
    const matchesType = typeFilter === 'all' || appType === typeFilter;

    // Permission filter - check granted_scopes array
    const hasPermissionLevel = (level: string) => {
      if (!app.granted_scopes || !Array.isArray(app.granted_scopes)) return false;
      const scopes = app.granted_scopes;
      
      switch (level) {
        case 'read':
          return scopes.includes('profile') || scopes.includes('openid');
        case 'write':
          return scopes.includes('email') || scopes.includes('write');
        case 'admin':
          return scopes.includes('admin');
        default:
          return true;
      }
    };

    const matchesPermission = permissionFilter === 'all' || hasPermissionLevel(permissionFilter);

    return matchesSearch && matchesStatus && matchesType && matchesPermission;
  });
};

/**
 * Filter activities based on search term and filters
 */
export const filterActivities = (
  activities: any[],
  searchTerm: string,
  typeFilter: string,
  timeframeFilter: string
) => {
  if (!activities || !Array.isArray(activities)) return [];

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  return activities.filter(activity => {
    if (!activity) return false;

    // Search filter - use correct property names from API
    const matchesSearch = !searchTerm || 
      activity.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.application_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.ip_address?.toLowerCase().includes(searchTerm.toLowerCase());

    // Type filter - map to action types from API
    const matchesType = typeFilter === 'all' || 
      (typeFilter === 'login' && (activity.action === 'login' || activity.action === 'oauth2_authorization')) ||
      (typeFilter === 'permission' && activity.action === 'permission_granted') ||
      (typeFilter === 'app_access' && activity.action === 'application_access') ||
      (typeFilter === 'security' && !activity.success);

    // Timeframe filter
    let matchesTimeframe = true;
    if (timeframeFilter !== 'all' && activity.timestamp) {
      try {
        const activityDate = new Date(activity.timestamp);
        switch (timeframeFilter) {
          case 'today':
            matchesTimeframe = activityDate >= today;
            break;
          case 'week':
            matchesTimeframe = activityDate >= weekAgo;
            break;
          case 'month':
            matchesTimeframe = activityDate >= monthAgo;
            break;
        }
      } catch (error) {
        console.error('Error parsing activity timestamp:', error);
        matchesTimeframe = false;
      }
    }

    return matchesSearch && matchesType && matchesTimeframe;
  });
};
