import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// project imports
import UserLayout from '@/layout/user-layout';
import Loadable from '@/components/base/loadable';
import { AuthGuard } from '@/utils/auth';

// user pages - lazy loading
const UserDashboard = Loadable(lazy(() => import('@/pages/user/dashboard/page')));
const UserApplications = Loadable(lazy(() => import('@/pages/user/applications/page')));
const UserActivityPage = Loadable(lazy(() => import('@/pages/user/activity/page')));
const UserPermissionsPage = Loadable(lazy(() => import('@/pages/user/permissions/page')));
const UserHomePage = Loadable(lazy(() => import('@/pages/user/home/<USER>')));
const UserProfilePage = Loadable(lazy(() => import('@/pages/user/profile/page')));
const UserSecurityOverviewPage = Loadable(lazy(() => import('@/pages/user/security/pages')));
const UserPasswordManagementPage = Loadable(lazy(() => import('@/pages/user/security/password/page')));
const UserPrivacyControlsPage = Loadable(lazy(() => import('@/pages/user/privacy/page')));
const User2FAPage = Loadable(lazy(() => import('@/pages/user/security/2fa/page')));
const UserDataExportPage = Loadable(lazy(() => import('@/pages/user/data-export/page')));
const UserNotificationsPage = Loadable(lazy(() => import('@/pages/user/notifications/page')));
const UserSettingsPage = Loadable(lazy(() => import('@/pages/user/settings/page')));
const UserDeviceManagementPage = Loadable(lazy(() => import('@/pages/user/security/devices/page')));
const UserSharingPage = Loadable(lazy(() => import('@/pages/user/sharing/page')));
const UserAccessibilityPage = Loadable(lazy(() => import('@/pages/user/accessibility/page')));

// Consent Management & Session Tracking
const ConsentHistoryPage = Loadable(lazy(() => import('@/pages/user/consent/page')));
const SessionManagementPage = Loadable(lazy(() => import('@/pages/user/sessions/page')));

// Role Transitions
const UserRoleTransitionsPage = Loadable(lazy(() => import('@/pages/user/role-transitions/page')));

// Organization Invitations
const UserInvitationsPage = Loadable(lazy(() => import('@/pages/user/invitations/page')));
const UserOrganizationsPage = Loadable(lazy(() => import('@/pages/user/organizations/page')));
const UserOrganizationDetailPage = Loadable(lazy(() => import('@/pages/user/organizations/[id]/page')));

// ==============================|| USER ROUTES ||============================== //

const UserRoutes = {
  path: '/user',
  element: (
    <AuthGuard>
      <UserLayout />
    </AuthGuard>
  ),
  children: [
    {
      path: '',
      element: <Navigate to="/user/home" replace />,
    },

    // Profile and Account Management
    {
      path: 'home',
      element: <UserHomePage />,
    },
    {
      path: 'profile',
      element: <UserProfilePage />,
    },
    {
      path: 'privacy',
      element: <UserPrivacyControlsPage />,
    },

    // Security Management
    {
      path: 'security',
      element: <UserSecurityOverviewPage />,
    },
    {
      path: 'security/password',
      element: <UserPasswordManagementPage />,
    },
    {
      path: 'security/2fa',
      element: <User2FAPage />,
    },
    {
      path: 'security/devices',
      element: <UserDeviceManagementPage />,
    },

    // Role Transitions
    {
      path: 'role-transitions',
      element: <UserRoleTransitionsPage />,
    },

    // Organization Invitations
    {
      path: 'invitations',
      element: <UserInvitationsPage />,
    },


    // Consent Management & Session Tracking
    {
      path: 'consent/history',
      element: <ConsentHistoryPage />,
    },
    {
      path: 'sessions',
      element: <SessionManagementPage />,
    },

    // Applications & Data (removed duplicate connected-apps route - use applications instead)
    {
      path: 'data-export',
      element: <UserDataExportPage />,
    },

    // Core User Pages
    {
      path: 'dashboard',
      element: <UserDashboard />,
    },
    {
      path: 'applications',
      element: <UserApplications />,
    },
    {
      path: 'activity',
      element: <UserActivityPage />,
    },
    {
      path: 'permissions',
      element: <UserPermissionsPage />,
    },

    // Sharing & Collaboration
    {
      path: 'sharing',
      element: <UserSharingPage />,
    },

    // Account Preferences
    {
      path: 'accessibility',
      element: <UserAccessibilityPage />,
    },
    {
      path: 'notifications',
      element: <UserNotificationsPage />,
    },
    {
      path: 'settings',
      element: <UserSettingsPage />,
    },
  ],
};

export default UserRoutes;
