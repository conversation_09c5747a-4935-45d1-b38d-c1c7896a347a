import { useRoutes } from 'react-router-dom';
import { lazy } from 'react';

// project imports
import Loadable from '@/components/base/loadable';
import PublicLayout from '@/layout/public-layout';

// routes
import PublicRoutes from './public-routes';
import AuthRoutes from './auth-routes';
import LoginRoutes, { RegisterRoutes } from './login-routes';
import MainRoutes from './main-routes';
import DeveloperRoutes from './developer-routes';
import AdminRoutes from './admin-routes';
import UserRoutes from './user-routes';

// error pages
const NotFoundPage = Loadable(lazy(() => import('@/pages/error/404/page')));

// Global 404 route for unmatched paths
const NotFoundRoutes = {
  path: '*',
  element: <PublicLayout />,
  children: [
    {
      path: '*',
      element: <NotFoundPage />,
    },
  ],
};

// ==============================|| ROUTING RENDER ||============================== //

export default function ThemeRoutes() {
  return useRoutes([
    PublicRoutes,
    LoginRoutes,
    RegisterRoutes,
    AuthRoutes,
    MainRoutes,
    UserRoutes,
    AdminRoutes,
    DeveloperRoutes,
    NotFoundRoutes, // Global 404 fallback
  ]);
}
