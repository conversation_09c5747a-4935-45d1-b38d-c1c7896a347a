import { lazy } from 'react';

// project imports
import GuestLayout from '@/layout/guest-layout';
import Loadable from '@/components/base/loadable';

// auth routing - simple lazy loading
const OAuth2Login = Loadable(lazy(() => import('@/pages/oauth2/login/page')));
const OAuth2Register = Loadable(lazy(() => import('@/pages/oauth2/register/page')));
const OAuth2Authorize = Loadable(lazy(() => import('@/pages/oauth2/authorize/page')));
const OAuth2Error = Loadable(lazy(() => import('@/pages/oauth2/error/page')));
const OAuth2Callback = Loadable(lazy(() => import('@/pages/oauth2/callback/page')));

// logout pages
const LogoutSuccess = Loadable(lazy(() => import('@/pages/auth/logout-success/page')));
const LogoutError = Loadable(lazy(() => import('@/pages/auth/logout-error/page')));

// consent page
const ConsentScreen = Loadable(lazy(() => import('@/pages/auth/consent/page')));

// MFA verification page
const MFAVerifyPage = Loadable(lazy(() => import('@/pages/auth/mfa-verify/page')));

// ==============================|| AUTH ROUTES ||============================== //

const AuthRoutes = {
  path: '/auth',
  element: <GuestLayout />,
  children: [
    {
      path: 'login',
      element: <OAuth2Login />,
    },
    {
      path: 'register',
      element: <OAuth2Register />,
    },
    {
      path: 'authorize',
      element: <OAuth2Authorize />,
    },
    {
      path: 'error',
      element: <OAuth2Error />,
    },
    {
      path: 'callback',
      element: <OAuth2Callback />,
    },
    {
      path: 'logout-success',
      element: <LogoutSuccess />,
    },
    {
      path: 'logout-error',
      element: <LogoutError />,
    },
    {
      path: 'consent',
      element: <ConsentScreen />,
    },
    {
      path: 'mfa-verify',
      element: <MFAVerifyPage />,
    },
  ],
};

export default AuthRoutes;
