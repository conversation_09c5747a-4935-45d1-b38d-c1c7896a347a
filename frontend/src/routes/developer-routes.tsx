import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// project imports
import MainLayout from '@/layout/main-layout';
import Loadable from '@/components/base/loadable';
import { RoleGuard } from '@/utils/auth';

// developer pages - simple lazy loading
const DeveloperDashboard = Loadable(lazy(() => import('@/pages/developer/dashboard/page')));
const ApplicationRegister = Loadable(lazy(() => import('@/pages/developer/register/page')));
const ApplicationManagement = Loadable(lazy(() => import('@/pages/developer/applications/page')));
const DeveloperAPIDocs = Loadable(lazy(() => import('@/pages/developer/docs/api/page')));
const DeveloperSettings = Loadable(lazy(() => import('@/pages/developer/settings/page')));

// organization pages
const OrganizationsPage = Loadable(lazy(() => import('@/pages/developer/organizations/page')));
const OrganizationCreatePage = Loadable(lazy(() => import('@/pages/developer/organizations/create/page')));
const OrganizationDashboardPage = Loadable(lazy(() => import('@/pages/developer/organizations/dashboard/page')));

// ==============================|| DEVELOPER ROUTES ||============================== //

const DeveloperRoutes = {
  path: '/developer',
  element: (
    <RoleGuard requiredRole="developer">
      <MainLayout />
    </RoleGuard>
  ),
  children: [
    {
      path: '',
      element: <Navigate to="/developer/dashboard" replace />,
    },
    {
      path: 'dashboard',
      element: <DeveloperDashboard />,
    },
    {
      path: 'register',
      element: <ApplicationRegister />,
    },
    {
      path: 'applications',
      element: <ApplicationManagement />,
    },
    {
      path: 'docs',
      element: <DeveloperAPIDocs />,
    },
    {
      path: 'settings',
      element: <DeveloperSettings />,
    },
    {
      path: 'organizations',
      element: <OrganizationsPage />,
    },
    {
      path: 'organizations/new',
      element: <OrganizationCreatePage />,
    },
    {
      path: 'organizations/:organizationId',
      element: <OrganizationDashboardPage />,
    },
  ],
};

export default DeveloperRoutes;
