import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// project imports
import MainLayout from '@/layout/main-layout';
import Loadable from '@/components/base/loadable';
import { AuthGuard } from '@/utils/auth';

// error pages
const NotFoundPage = Loadable(lazy(() => import('@/pages/error/404/page')));

// dashboard component that shows appropriate dashboard based on role
const Dashboard = Loadable(lazy(() => import('@/pages/dashboard/redirect')));

// user organization pages (use MainLayout for app-sidebar)
const UserOrganizationsPage = Loadable(lazy(() => import('@/pages/user/organizations/page')));
const UserOrganizationDetailPage = Loadable(lazy(() => import('@/pages/user/organizations/[id]/page')));

// ==============================|| MAIN ROUTES ||============================== //

const MainRoutes = {
  path: '/',
  element: (
    <AuthGuard>
      <MainLayout />
    </AuthGuard>
  ),
  children: [
    {
      path: '',
      element: <Navigate to="/dashboard" replace />,
    },
    {
      path: 'dashboard',
      element: <Dashboard />,
    },
    {
      path: 'profile',
      element: <div className="p-6"><h1 className="text-2xl font-bold">Profile</h1><p>Manage your profile here.</p></div>,
    },
    // User organization routes (use MainLayout for app-sidebar)
    {
      path: 'user/organizations',
      element: <UserOrganizationsPage />,
    },
    {
      path: 'user/organizations/:id',
      element: <UserOrganizationDetailPage />,
    },
    // 404 page for unmatched routes within protected area
    {
      path: '*',
      element: <NotFoundPage />,
    },
  ],
};

export default MainRoutes;
