import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// project imports
import PublicLayout from '../layout/public-layout';
import Loadable from '../components/base/loadable';

// public pages - simple lazy loading
const LandingPage = Loadable(lazy(() => import('../pages/landing')));

// Home page and public pages
const HomePage = Loadable(lazy(() => import('../pages/public/info/home/<USER>')));

// Legal pages
const PrivacyPolicy = Loadable(lazy(() => import('../pages/public/policies/privacy/page')));
const TermsOfService = Loadable(lazy(() => import('../pages/public/policies/terms/page')));
const CookiePolicy = Loadable(lazy(() => import('../pages/public/policies/cookies/page')));
const GDPRCompliance = Loadable(lazy(() => import('../pages/public/policies/gdpr/page')));

// Information pages
const AboutUs = Loadable(lazy(() => import('../pages/public/info/about/page')));
const ContactUs = Loadable(lazy(() => import('../pages/public/info/contact/page')));
const TeamPage = Loadable(lazy(() => import('../pages/public/info/team/page')));
const CareersPage = Loadable(lazy(() => import('../pages/public/info/careers/page')));
const FAQPage = Loadable(lazy(() => import('../pages/public/info/faq/page')));
const BlogPage = Loadable(lazy(() => import('../pages/public/info/blog/page')));
const HelpPage = Loadable(lazy(() => import('../pages/public/info/help/page')));
const ResourcesPage = Loadable(lazy(() => import('../pages/public/info/resources/page')));

// Error and maintenance pages
const NotFoundPage = Loadable(lazy(() => import('../pages/error/404/page')));
const ServerErrorPage = Loadable(lazy(() => import('../pages/error/500/page')));
const MaintenancePage = Loadable(lazy(() => import('../pages/error/maintenance/page')));

// ==============================|| PUBLIC ROUTES ||============================== //

const PublicRoutes = {
  path: '/',
  element: <PublicLayout />,
  children: [
    // Landing page (root)
    {
      path: '',
      element: <LandingPage />,
    },
    // Home page alternative
    {
      path: 'home',
      element: <HomePage />,
    },

    // Legal Pages
    {
      path: 'privacy',
      element: <PrivacyPolicy />,
    },
    {
      path: 'terms',
      element: <TermsOfService />,
    },
    {
      path: 'cookies',
      element: <CookiePolicy />,
    },
    {
      path: 'gdpr',
      element: <GDPRCompliance />,
    },

    // Information Pages
    {
      path: 'about',
      element: <AboutUs />,
    },
    {
      path: 'contact',
      element: <ContactUs />,
    },
    {
      path: 'team',
      element: <TeamPage />,
    },
    {
      path: 'careers',
      element: <CareersPage />,
    },
    {
      path: 'faq',
      element: <FAQPage />,
    },
    {
      path: 'blog',
      element: <BlogPage />,
    },
    {
      path: 'help',
      element: <HelpPage />,
    },
    {
      path: 'resources',
      element: <ResourcesPage />,
    },

    // Error Pages
    {
      path: '404',
      element: <NotFoundPage />,
    },
    {
      path: '500',
      element: <ServerErrorPage />,
    },
    {
      path: 'maintenance',
      element: <MaintenancePage />,
    },

    // Legacy redirects
    {
      path: 'index',
      element: <Navigate to="/" replace />,
    },
    // Legacy OAuth2 redirects for compatibility
    {
      path: 'oauth2/login',
      element: <Navigate to="/login" replace />,
    },
    {
      path: 'oauth2/register',
      element: <Navigate to="/auth/register" replace />,
    },
    // Catch-all route for unmatched paths
    {
      path: '*',
      element: <NotFoundPage />,
    },
  ],
};

export default PublicRoutes;
