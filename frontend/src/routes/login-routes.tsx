import { Navigate } from 'react-router-dom';

// project imports
import GuestLayout from '@/layout/guest-layout';
import Loadable from '@/components/base/loadable';
import { lazy } from 'react';

// login pages - simple lazy loading
const LoginPage = Loadable(lazy(() => import('@/pages/login/page')));
const RegisterPage = Loadable(lazy(() => import('@/pages/register/page')));

// ==============================|| LOGIN ROUTES ||============================== //

const LoginRoutes = {
  path: '/login',
  element: <GuestLayout />,
  children: [
    {
      path: '',
      element: <LoginPage />,
    },
  ],
};

// Separate routes for register and signup
const RegisterRoutes = {
  path: '/',
  element: <GuestLayout />,
  children: [
    {
      path: 'register', 
      element: <RegisterPage />,
    },
    // Redirect signup to register
    {
      path: 'signup',
      element: <Navigate to="/register" replace />,
    },
  ],
};

export default LoginRoutes;
export { RegisterRoutes };
