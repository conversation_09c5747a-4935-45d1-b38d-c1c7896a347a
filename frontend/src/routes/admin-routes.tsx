import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// project imports
import MainLayout from '@/layout/main-layout';
import Loadable from '@/components/base/loadable';
import { RoleGuard } from '@/utils/auth';

// admin pages - simple lazy loading
const AdminDashboard = Loadable(lazy(() => import('@/pages/admin/dashboard/page')));
const AdminUserManagement = Loadable(lazy(() => import('@/pages/admin/users/page')));
const AdminApplicationManagement = Loadable(lazy(() => import('@/pages/admin/applications/page')));
const AdminOrganizationManagement = Loadable(lazy(() => import('@/pages/admin/organizations/page')));
const AdminSystemSettings = Loadable(lazy(() => import('@/pages/admin/system/settings/page')));
const AdminSecurityDashboard = Loadable(lazy(() => import('@/pages/admin/security/dashboard/page')));
const AdminRoleApplications = Loadable(lazy(() => import('@/pages/admin/developer-applications/page')));

// ==============================|| ADMIN ROUTES ||============================== //

const AdminRoutes = {
  path: '/admin',
  element: (
    <RoleGuard requiredRole="admin">
      <MainLayout />
    </RoleGuard>
  ),
  children: [
    {
      path: '',
      element: <Navigate to="/admin/dashboard" replace />,
    },
    {
      path: 'dashboard',
      element: <AdminDashboard />,
    },
    {
      path: 'users',
      element: <AdminUserManagement />,
    },
    {
      path: 'applications',
      element: <AdminApplicationManagement />,
    },
    {
      path: 'organizations',
      element: <AdminOrganizationManagement />,
    },
    {
      path: 'system/settings',
      element: <AdminSystemSettings />,
    },
    {
      path: 'security',
      element: <AdminSecurityDashboard />,
    },
    {
      path: 'developer-applications',
      element: <AdminRoleApplications />,
    },
  ],
};

export default AdminRoutes;
