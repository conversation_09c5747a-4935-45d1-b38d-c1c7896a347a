# Layout-Based Authentication System

## Architecture

Authentication is handled at the **layout level**, not per component. Each layout applies the appropriate guard automatically.

## Layouts

### GuestLayout
- **Used for**: Login, register, auth pages
- **Guard**: `GuestGuard` - redirects authenticated users away
- **Routes**: `/auth/*`, `/login`, `/register`

### ProtectedLayout  
- **Used for**: Main dashboard areas requiring auth
- **Guard**: `AuthGuard` - requires authentication
- **Routes**: `/dashboard`, `/profile`

### AdminLayout
- **Used for**: Admin-only areas
- **Guard**: `RoleGuard` with `requiredRole="admin"`
- **Routes**: `/admin/*`

### DeveloperLayout
- **Used for**: Developer areas 
- **Guard**: `RoleGuard` with `requiredRole="developer"`
- **Routes**: `/developer/*`

## Route Configuration

```tsx
// Example: Admin routes with layout-based protection
const AdminRoutes = {
  path: '/admin',
  element: <AdminLayout />, // Contains RoleGuard
  children: [
    { path: 'dashboard', element: <AdminDashboard /> },
    { path: 'users', element: <UserManagement /> },
  ],
};
```

## Benefits

✅ **No per-page protection needed** - handled at layout level
✅ **Consistent UI** - each area has its own layout/design  
✅ **Clean separation** - auth logic separated from pages
✅ **Easy maintenance** - change protection in one place

## Guard Components Available

- `AuthGuard` - Basic authentication required
- `GuestGuard` - Guest-only access
- `RoleGuard` - Role-based access control

## Role Hierarchy
`guest < user < developer < admin < super_admin`
