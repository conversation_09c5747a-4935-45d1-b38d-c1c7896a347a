import { Separator } from '@/components/ui/shadcn/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/shadcn/sidebar';
import { Outlet } from 'react-router-dom';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { Toaster } from '@/components/ui/shadcn/toaster';
import { NavBreadcrumb } from '@/components/layout/nav-breadcrumb';
import { BreadcrumbProvider, useBreadcrumb } from '@/contexts/breadcrumb-context';
import { InvitationProvider } from '@/contexts/invitation-context';

/**
 * MainLayout Component
 *
 * Standard authenticated layout with sidebar, header, and main content area.
 * Used for all authenticated pages (dashboard, profile, etc.)
 * Guards are applied at the route level.
 */
const MainLayoutContent = () => {
  const { dynamicTitles } = useBreadcrumb();

  return (
    <InvitationProvider>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <NavBreadcrumb dynamicTitles={dynamicTitles} />
            </div>
          </header>
          <Outlet />
        </SidebarInset>
        <Toaster />
      </SidebarProvider>
    </InvitationProvider>
  );
};

const MainLayout = () => {
  return (
    <BreadcrumbProvider>
      <MainLayoutContent />
    </BreadcrumbProvider>
  );
};

export default MainLayout;
