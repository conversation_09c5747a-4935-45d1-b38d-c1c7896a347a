import React from 'react';
import { Outlet } from 'react-router-dom';
import { GuestGuard } from '@/utils/auth';
import { BackgroundBoxes } from '@/components/ui/aceternity/background-boxes';

/**
 * GuestLayout Component
 * 
 * Layout for authentication pages (login, register).
 * Uses GuestGuard to redirect authenticated users away.
 * Features a minimalist background with floating boxes for enterprise-level design.
 */
const GuestLayout: React.FC = () => {
  return (
    <GuestGuard>
      <BackgroundBoxes className="min-h-screen">
        <div className="flex min-h-screen flex-col justify-center py-6 sm:px-6 lg:px-8">
          <Outlet />
        </div>
      </BackgroundBoxes>
    </GuestGuard>
  );
};

export default GuestLayout;
