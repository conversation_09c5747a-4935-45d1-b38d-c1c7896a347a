import { Outlet } from 'react-router-dom';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/shadcn/sidebar';
import { UserSidebar } from '@/components/layout/user-sidebar';
import { Toaster } from '@/components/ui/shadcn/toaster';
import { NavBreadcrumb } from '@/components/layout/nav-breadcrumb';
import { Separator } from '@/components/ui/shadcn/separator';
import { InvitationProvider } from '@/contexts/invitation-context';

/**
 * UserLayout Component
 *
 * Layout component for user-related pages (profile, security, settings, notifications)
 * Provides a specialized sidebar for user-related navigation.
 *
 * This component follows the same pattern as MainLayout but uses a specialized UserSidebar.
 */
const UserLayout = () => {
  return (
    <InvitationProvider>
      <SidebarProvider>
        <UserSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <NavBreadcrumb />
            </div>
          </header>
          <div className="p-6">
            <Outlet />
          </div>
        </SidebarInset>
        <Toaster />
      </SidebarProvider>
    </InvitationProvider>
  );
};

export default UserLayout;
