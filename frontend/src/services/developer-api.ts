/**
 * Developer API Service
 * 
 * This service handles all developer-related API calls for production use.
 */

import axiosServices from '@/utils/api/axios';
import type {
  ApplicationResponse,
  ApplicationCreateRequest,
  ApplicationUpdateRequest,
  DeveloperAnalyticsOverview,
  DeveloperUser,
  UserActivityLog,
  UsageStatistics,
  MessageResponse,
  PaginationParams,
} from '@/types/api';

/**
 * Developer API Service Class
 * Handles all developer portal API operations
 */
export class DeveloperApiService {
  private static readonly BASE_PATH = '/api/v1/developer';

  // ============================================================================
  // Application Management
  // ============================================================================

  /**
   * Get all applications for the current developer
   */
  static async getApplications(): Promise<ApplicationResponse[]> {
    const response = await axiosServices.get<ApplicationResponse[]>(
      `${this.BASE_PATH}/applications`
    );
    return response.data;
  }

  /**
   * Get specific application by ID
   */
  static async getApplication(id: string): Promise<ApplicationResponse | null> {
    const response = await axiosServices.get<ApplicationResponse>(
      `${this.BASE_PATH}/applications/${id}`
    );
    return response.data;
  }

  /**
   * Create a new application
   */
  static async createApplication(data: ApplicationCreateRequest): Promise<ApplicationResponse> {
    const response = await axiosServices.post<ApplicationResponse>(
      `${this.BASE_PATH}/applications`,
      data
    );
    return response.data;
  }

  /**
   * Update an existing application
   */
  static async updateApplication(
    id: string,
    data: ApplicationUpdateRequest
  ): Promise<ApplicationResponse> {
    const response = await axiosServices.put<ApplicationResponse>(
      `${this.BASE_PATH}/applications/${id}`,
      data
    );
    return response.data;
  }

  /**
   * Delete an application
   */
  static async deleteApplication(id: string): Promise<MessageResponse> {
    const response = await axiosServices.delete<MessageResponse>(
      `${this.BASE_PATH}/applications/${id}`
    );
    return response.data;
  }

  /**
   * Regenerate application secret
   */
  static async regenerateSecret(id: string): Promise<ApplicationResponse> {
    const response = await axiosServices.post<ApplicationResponse>(
      `${this.BASE_PATH}/applications/${id}/regenerate-secret`
    );
    return response.data;
  }

  // ============================================================================
  // Analytics
  // ============================================================================

  /**
   * Get analytics overview for developer
   */
  static async getAnalyticsOverview(): Promise<DeveloperAnalyticsOverview> {
    const response = await axiosServices.get<DeveloperAnalyticsOverview>(
      `${this.BASE_PATH}/analytics/overview`
    );
    return response.data;
  }

  /**
   * Get analytics for specific application
   */
  static async getApplicationAnalytics(id: string): Promise<DeveloperAnalyticsOverview> {
    const response = await axiosServices.get<DeveloperAnalyticsOverview>(
      `${this.BASE_PATH}/applications/${id}/analytics`
    );
    return response.data;
  }

  // ============================================================================
  // User Management
  // ============================================================================

  /**
   * Get all users across developer's applications
   */
  static async getUsers(params: PaginationParams = {}): Promise<{
    users: DeveloperUser[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users`,
      { params }
    );
    return response.data;
  }

  /**
   * Get specific user details
   */
  static async getUser(id: string): Promise<DeveloperUser | null> {
    const response = await axiosServices.get<DeveloperUser>(
      `${this.BASE_PATH}/users/${id}`
    );
    return response.data;
  }

  /**
   * Get users for specific application
   */
  static async getApplicationUsers(
    applicationId: string, 
    params: PaginationParams = {}
  ): Promise<{
    users: DeveloperUser[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/applications/${applicationId}/users`,
      { params }
    );
    return response.data;
  }

  // ============================================================================
  // Activity & Usage
  // ============================================================================

  /**
   * Get activity logs
   */
  static async getActivityLogs(params: any = {}): Promise<{
    logs: UserActivityLog[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/activity`,
      { params }
    );
    return response.data;
  }

  /**
   * Get usage statistics
   */
  static async getUsageStatistics(period: string = '7d'): Promise<UsageStatistics> {
    const response = await axiosServices.get<UsageStatistics>(
      `${this.BASE_PATH}/usage`,
      { params: { period } }
    );
    return response.data;
  }

  // ============================================================================
  // Settings Management
  // ============================================================================

  /**
   * Get developer settings
   */
  static async getSettings(): Promise<any> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/settings`
    );
    return response.data;
  }

  /**
   * Update developer settings
   */
  static async updateSettings(settings: any): Promise<any> {
    const response = await axiosServices.put(
      `${this.BASE_PATH}/settings`,
      settings
    );
    return response.data;
  }
}

// Export convenience functions for easier usage
export const getDeveloperApplications = () => DeveloperApiService.getApplications();
export const getDeveloperApplication = (id: string) => DeveloperApiService.getApplication(id);
export const createDeveloperApplication = (data: ApplicationCreateRequest) => 
  DeveloperApiService.createApplication(data);
export const updateDeveloperApplication = (id: string, data: ApplicationUpdateRequest) => 
  DeveloperApiService.updateApplication(id, data);
export const deleteDeveloperApplication = (id: string) => DeveloperApiService.deleteApplication(id);
export const regenerateApplicationSecret = (id: string) => DeveloperApiService.regenerateSecret(id);
export const getDeveloperAnalytics = () => DeveloperApiService.getAnalyticsOverview();
export const getApplicationAnalytics = (id: string) => DeveloperApiService.getApplicationAnalytics(id);
export const getDeveloperUsers = (params?: PaginationParams) => DeveloperApiService.getUsers(params);
export const getDeveloperUser = (id: string) => DeveloperApiService.getUser(id);
export const getApplicationUsers = (applicationId: string, params?: PaginationParams) =>
  DeveloperApiService.getApplicationUsers(applicationId, params);
export const getDeveloperActivityLogs = (params?: any) => DeveloperApiService.getActivityLogs(params);
export const getDeveloperUsageStats = (period?: string) => DeveloperApiService.getUsageStatistics(period);
export const getDeveloperSettings = () => DeveloperApiService.getSettings();
export const updateDeveloperSettings = (settings: any) => DeveloperApiService.updateSettings(settings);
