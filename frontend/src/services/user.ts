/**
 * User Service
 *
 * This service handles user-related API calls for production use.
 */

import axiosServices from '@/utils/api/axios';
import { SystemService } from './system';
import type {
  UserProfileData,
  NotificationData,
  NotificationSettings,
  SecurityData,
  UserSettingsData,
} from '@/types/user';

/**
 * User API Service
 * Provides methods to fetch user data from the backend API
 */
export class UserService {
  /**
   * Get current user profile
   */
  static async getUserProfile(): Promise<UserProfileData> {
    const response = await axiosServices.get('/api/v1/user/profile');

    // Transform API response to match our UserProfileData interface
    const userData = response.data;
    return {
      firstName: userData.firstName || userData.first_name || 'User',
      lastName: userData.lastName || userData.last_name || '',
      email: userData.email,
      phone: userData.phone || '',
      jobTitle: userData.jobTitle || userData.job_title || '',
      company: userData.company || '',
      bio: userData.bio || '',
      location: userData.location || '',
      avatarUrl:
        userData.avatarUrl ||
        userData.avatar_url ||
        userData.avatar ||
        null,
      joinDate:
        userData.createdAt || userData.created_at || userData.joinDate,
      lastLogin: userData.lastLogin || userData.last_login || 'Recently',
    };
  }

  /**
   * Get user notifications
   */
  static async getUserNotifications(): Promise<NotificationData[]> {
    const response = await axiosServices.get('/api/v1/user/notifications');

    // Transform API response to match our NotificationData interface
    return response.data.map((notification: any) => ({
      id: notification.id,
      type: notification.type || 'system',
      title: notification.title,
      message: notification.message || notification.content,
      date:
        notification.createdAt ||
        notification.created_at ||
        notification.date,
      read: notification.read || notification.is_read || false,
      actionUrl: notification.actionUrl || notification.action_url || '#',
      user: notification.user
        ? {
            name:
              notification.user.name ||
              `${notification.user.firstName} ${notification.user.lastName}`,
            avatar:
              notification.user.avatar || notification.user.avatarUrl || '',
          }
        : undefined,
    }));
  }

  /**
   * Get notification settings
   */
  static async getNotificationSettings(): Promise<NotificationSettings> {
    const response = await axiosServices.get('/api/v1/user/settings/notifications');

    return {
      email: response.data.email ?? true,
      browser: response.data.browser ?? true,
      mobile: response.data.mobile ?? true,
    };
  }

  /**
   * Get security data
   */
  static async getSecurityData(): Promise<SecurityData> {
    const response = await axiosServices.get('/api/v1/user/security/overview');
    const data = response.data;

    return {
      email: data.email,
      passwordLastChanged:
        data.passwordLastChanged || data.password_last_changed,
      twoFactorEnabled:
        data.twoFactorEnabled || data.two_factor_enabled || false,
      recoveryCodesRemaining:
        data.recoveryCodesRemaining || data.recovery_codes_remaining || 0,
      activeSessions: data.activeSessions || data.active_sessions || [],
      securityEvents: data.securityEvents || data.security_events || [],
    };
  }

  /**
   * Get user settings
   */
  static async getUserSettings(): Promise<UserSettingsData> {
    const response = await axiosServices.get('/api/v1/user/settings');
    const data = response.data;

    return {
      name: data.name || `${data.firstName} ${data.lastName}`,
      email: data.email,
      avatar: data.avatar || data.avatarUrl || '',
      jobTitle: data.jobTitle || data.job_title || '',
      company: data.company || '',
      bio: data.bio || '',
      location: data.location || '',
      timezone: data.timezone || 'UTC',
      language: data.language || 'en',
      theme: data.theme || 'system',
      notifications: data.notifications || {
        email: {
          productUpdates: true,
          securityAlerts: true,
          newsletters: false,
          usageReports: true,
        },
        inApp: {
          mentions: true,
          comments: true,
          taskAssignments: true,
          statusChanges: true,
        },
      },
      connectedAccounts: data.connectedAccounts || data.connected_accounts || {
        google: false,
        github: false,
        linkedin: false,
        slack: false,
      },
    };
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(
    profileData: Partial<UserProfileData>
  ): Promise<UserProfileData> {
    const response = await axiosServices.put(
      '/api/v1/user/profile',
      profileData
    );
    return response.data;
  }

  /**
   * Reset API availability check
   * Useful for retrying API connection
   * @deprecated Use SystemService.resetApiStatus() instead
   */
  static resetApiCheck(): void {
    SystemService.resetApiStatus();
  }

  /**
   * Get current API status
   * @deprecated Use SystemService.isApiAvailable() instead
   */
  static isApiAvailable(): boolean | null {
    return SystemService.isApiAvailable();
  }
}

// Export individual functions for easier usage
export const getUserProfile = () => UserService.getUserProfile();
export const getUserNotifications = () => UserService.getUserNotifications();
export const getNotificationSettings = () =>
  UserService.getNotificationSettings();
export const getSecurityData = () => UserService.getSecurityData();
export const getUserSettings = () => UserService.getUserSettings();
export const updateUserProfile = (data: Partial<UserProfileData>) =>
  UserService.updateUserProfile(data);
