/**
 * Security Monitoring API Service
 * 
 * Handles security monitoring API operations including
 * threat detection, security metrics, and IP reputation
 */

import { API_CONFIG } from '@/config';

// Types
export interface SecuritySummary {
  summary: {
    threat_level: 'low' | 'medium' | 'high' | 'critical' | 'unknown';
    total_threats: number;
    critical_threats: number;
    high_threats: number;
    generated_at: string;
    time_window_hours: number;
  };
  threats: {
    suspicious_login_activity: SuspiciousLoginActivity[];
    account_takeover_attempts: AccountTakeoverAttempt[];
    brute_force_attacks: BruteForceAttack[];
    unusual_admin_activity: UnusualAdminActivity[];
    mfa_bypass_attempts: MFABypassAttempt[];
  };
}

export interface SuspiciousLoginActivity {
  type: 'suspicious_login_attempts';
  ip_address: string;
  failed_attempts: number;
  unique_users_targeted: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  time_window_hours: number;
}

export interface BruteForceAttack {
  type: 'brute_force_attack';
  ip_address: string;
  attempt_count: number;
  first_attempt: string;
  last_attempt: string;
  rate_per_minute: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  time_window_minutes: number;
}

export interface AccountTakeoverAttempt {
  type: 'potential_account_takeover';
  user_id: string;
  unique_ip_count: number;
  login_count: number;
  ip_addresses: Array<{
    ip: string;
    last_login: string;
  }>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  time_window_hours: number;
}

export interface MFABypassAttempt {
  type: 'mfa_bypass_attempt';
  user_id: string;
  ip_address: string;
  mfa_failure_count: number;
  successful_login_after_failures: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  time_window_hours: number;
}

export interface UnusualAdminActivity {
  type: 'unusual_admin_activity';
  admin_user_id: string;
  total_actions: number;
  unique_action_types: number;
  action_breakdown: Record<string, number>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  time_window_hours: number;
}

export interface IPReputation {
  ip_address: string;
  reputation_score: number;
  reputation_level: 'trusted' | 'good' | 'neutral' | 'suspicious' | 'malicious';
  total_activities: number;
  failed_logins: number;
  successful_logins: number;
  activity_summary: Record<string, number>;
  last_activity: string | null;
  analyzed_at: string;
}

export interface ThreatDashboard {
  overview: SecuritySummary['summary'];
  recent_threats: {
    brute_force_attacks: BruteForceAttack[];
    account_takeover_attempts: AccountTakeoverAttempt[];
    suspicious_logins: SuspiciousLoginActivity[];
  };
  threat_counts: {
    total_24h: number;
    brute_force_30m: number;
    takeover_attempts_6h: number;
    suspicious_logins_24h: number;
  };
  generated_at: string;
}

export interface SecurityMetrics {
  time_window_hours: number;
  threat_metrics: {
    total_threats_detected: number;
    critical_threats: number;
    high_threats: number;
    threat_level: string;
  };
  detection_metrics: {
    suspicious_login_ips: number;
    brute_force_attacks: number;
    takeover_attempts: number;
    mfa_bypass_attempts: number;
    unusual_admin_activities: number;
  };
  generated_at: string;
}

// API Service Class
export class SecurityApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_CONFIG.baseURL}/api/v1/security`;
  }

  /**
   * Get security summary
   */
  async getSecuritySummary(timeWindowHours: number = 24): Promise<SecuritySummary> {
    const response = await fetch(`${this.baseUrl}/security-summary?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get security summary');
    }

    return response.json();
  }

  /**
   * Get suspicious login activity
   */
  async getSuspiciousLoginActivity(timeWindowHours: number = 24): Promise<SuspiciousLoginActivity[]> {
    const response = await fetch(`${this.baseUrl}/suspicious-logins?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get suspicious login activity');
    }

    return response.json();
  }

  /**
   * Get brute force attacks
   */
  async getBruteForceAttacks(timeWindowMinutes: number = 30): Promise<BruteForceAttack[]> {
    const response = await fetch(`${this.baseUrl}/brute-force-attacks?time_window_minutes=${timeWindowMinutes}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get brute force attacks');
    }

    return response.json();
  }

  /**
   * Get account takeover attempts
   */
  async getAccountTakeoverAttempts(timeWindowHours: number = 6): Promise<AccountTakeoverAttempt[]> {
    const response = await fetch(`${this.baseUrl}/account-takeover-attempts?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get account takeover attempts');
    }

    return response.json();
  }

  /**
   * Get MFA bypass attempts
   */
  async getMFABypassAttempts(timeWindowHours: number = 12): Promise<MFABypassAttempt[]> {
    const response = await fetch(`${this.baseUrl}/mfa-bypass-attempts?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get MFA bypass attempts');
    }

    return response.json();
  }

  /**
   * Get unusual admin activity
   */
  async getUnusualAdminActivity(timeWindowHours: number = 24): Promise<UnusualAdminActivity[]> {
    const response = await fetch(`${this.baseUrl}/unusual-admin-activity?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get unusual admin activity');
    }

    return response.json();
  }

  /**
   * Get IP reputation
   */
  async getIPReputation(ipAddress: string): Promise<IPReputation> {
    const response = await fetch(`${this.baseUrl}/ip-reputation/${encodeURIComponent(ipAddress)}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get IP reputation');
    }

    return response.json();
  }

  /**
   * Get threat dashboard data
   */
  async getThreatDashboard(): Promise<ThreatDashboard> {
    const response = await fetch(`${this.baseUrl}/threat-dashboard`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get threat dashboard');
    }

    return response.json();
  }

  /**
   * Get security metrics
   */
  async getSecurityMetrics(timeWindowHours: number = 24): Promise<SecurityMetrics> {
    const response = await fetch(`${this.baseUrl}/security-metrics?time_window_hours=${timeWindowHours}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get security metrics');
    }

    return response.json();
  }
}

// Export singleton instance
export const securityApi = new SecurityApiService();

// Utility functions
export const getThreatLevelColor = (level: string): string => {
  switch (level) {
    case 'low':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'high':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

export const getSeverityIcon = (severity: string): string => {
  switch (severity) {
    case 'low':
      return '🟢';
    case 'medium':
      return '🟡';
    case 'high':
      return '🟠';
    case 'critical':
      return '🔴';
    default:
      return '⚪';
  }
};

export const getReputationColor = (level: string): string => {
  switch (level) {
    case 'trusted':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'good':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'neutral':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    case 'suspicious':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'malicious':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

export const formatSecurityDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const validateIPAddress = (ip: string): boolean => {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
};
