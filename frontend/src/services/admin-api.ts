/**
 * Admin API Service
 * 
 * This service handles all admin-related API calls for production use.
 */

import axiosServices from '@/utils/api/axios';
import type {
  AdminApplicationResponse,
  AdminUserResponse,
  UserApplicationConnection,
  UserActivityLog,
  SystemAnalytics,
  MessageResponse,
  PaginationParams,
} from '@/types/api';

/**
 * Admin API Service Class
 * Handles all admin portal API operations
 */
export class AdminApiService {
  private static readonly BASE_PATH = '/api/v1/admin';

  // ============================================================================
  // Application Management
  // ============================================================================

  /**
   * Get all applications with admin view
   */
  static async getAllApplications(params: PaginationParams = {}): Promise<AdminApplicationResponse[]> {
    const response = await axiosServices.get<AdminApplicationResponse[]>(
      `${this.BASE_PATH}/applications`,
      { params }
    );
    return response.data;
  }

  /**
   * Get specific application by ID
   */
  static async getApplication(id: string): Promise<AdminApplicationResponse | null> {
    const response = await axiosServices.get<AdminApplicationResponse>(
      `${this.BASE_PATH}/applications/${id}`
    );
    return response.data;
  }

  /**
   * Get all developer applications
   */
  static async getDeveloperApplications(): Promise<AdminApplicationResponse[]> {
    const response = await axiosServices.get<AdminApplicationResponse[]>(
      `${this.BASE_PATH}/applications/developers`
    );
    return response.data;
  }

  /**
   * Approve an application
   */
  static async approveApplication(id: string, data?: any): Promise<MessageResponse> {
    const response = await axiosServices.post<MessageResponse>(
      `${this.BASE_PATH}/applications/${id}/approve`,
      data
    );
    return response.data;
  }

  /**
   * Reject an application
   */
  static async rejectApplication(id: string, data?: any): Promise<MessageResponse> {
    const response = await axiosServices.post<MessageResponse>(
      `${this.BASE_PATH}/applications/${id}/reject`,
      data
    );
    return response.data;
  }

  /**
   * Suspend an application
   */
  static async suspendApplication(id: string, reason?: string): Promise<MessageResponse> {
    const response = await axiosServices.put<MessageResponse>(
      `${this.BASE_PATH}/applications/${id}/suspend?reason=${encodeURIComponent(reason || 'Suspended by admin')}`
    );
    return response.data;
  }

  /**
   * Reactivate an application
   */
  static async reactivateApplication(id: string): Promise<MessageResponse> {
    const response = await axiosServices.put<MessageResponse>(
      `${this.BASE_PATH}/applications/${id}/reactivate`
    );
    return response.data;
  }

  // ============================================================================
  // User Management
  // ============================================================================

  /**
   * Get all users with admin view
   */
  static async getAllUsers(params: PaginationParams = {}): Promise<{
    users: AdminUserResponse[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users`,
      { params }
    );
    return response.data;
  }

  /**
   * Get all developers
   */
  static async getDevelopers(params: PaginationParams = {}): Promise<{
    users: AdminUserResponse[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users/developers`,
      { params }
    );
    return response.data;
  }

  /**
   * Get all end users
   */
  static async getEndUsers(params: PaginationParams = {}): Promise<{
    users: AdminUserResponse[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users/end-users`,
      { params }
    );
    return response.data;
  }

  /**
   * Get specific user details
   */
  static async getUser(id: string): Promise<AdminUserResponse | null> {
    const response = await axiosServices.get<AdminUserResponse>(
      `${this.BASE_PATH}/users/${id}`
    );
    return response.data;
  }

  /**
   * Get user's connected applications
   */
  static async getUserApplications(userId: string): Promise<UserApplicationConnection[]> {
    const response = await axiosServices.get<UserApplicationConnection[]>(
      `${this.BASE_PATH}/users/${userId}/applications`
    );
    return response.data;
  }

  /**
   * Get user activity logs
   */
  static async getUserActivity(
    userId: string, 
    params: PaginationParams = {}
  ): Promise<{
    logs: UserActivityLog[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users/${userId}/activity`,
      { params }
    );
    return response.data;
  }

  /**
   * Suspend a user
   */
  static async suspendUser(id: string, reason?: string): Promise<MessageResponse> {
    const response = await axiosServices.put<MessageResponse>(
      `${this.BASE_PATH}/users/${id}/suspend?reason=${encodeURIComponent(reason || 'Suspended by admin')}`
    );
    return response.data;
  }

  /**
   * Reactivate a user
   */
  static async reactivateUser(id: string): Promise<MessageResponse> {
    const response = await axiosServices.put<MessageResponse>(
      `${this.BASE_PATH}/users/${id}/reactivate`
    );
    return response.data;
  }

  /**
   * Reset user password
   */
  static async resetUserPassword(userId: string, data: any): Promise<MessageResponse> {
    const response = await axiosServices.post<MessageResponse>(
      `${this.BASE_PATH}/users/${userId}/reset-password`,
      data
    );
    return response.data;
  }

  /**
   * Get developer users (alias for getDevelopers)
   */
  static async getDeveloperUsers(params: PaginationParams = {}): Promise<{
    users: AdminUserResponse[];
    total: number;
    limit: number;
    offset: number;
  }> {
    return this.getDevelopers(params);
  }

  /**
   * Get regular users (alias for getEndUsers)
   */
  static async getRegularUsers(params: PaginationParams = {}): Promise<{
    users: AdminUserResponse[];
    total: number;
    limit: number;
    offset: number;
  }> {
    return this.getEndUsers(params);
  }

  /**
   * Get applications by developer
   */
  static async getApplicationsByDeveloper(developerId: string): Promise<AdminApplicationResponse[]> {
    const response = await axiosServices.get<AdminApplicationResponse[]>(
      `${this.BASE_PATH}/developers/${developerId}/applications`
    );
    return response.data;
  }

  // ============================================================================
  // System Analytics
  // ============================================================================

  /**
   * Export users data
   */
  static async exportUsers(format: 'csv' | 'json' = 'csv'): Promise<Blob> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/users/export`,
      { 
        params: { format },
        responseType: 'blob'
      }
    );
    return response.data;
  }

  /**
   * Get system analytics
   */
  static async getSystemAnalytics(): Promise<SystemAnalytics> {
    const response = await axiosServices.get<SystemAnalytics>(
      `${this.BASE_PATH}/analytics/system`
    );
    return response.data;
  }
}

// Export convenience functions for easier usage
export const getAdminApplications = () => AdminApiService.getAllApplications();
export const getAdminApplication = (id: string) => AdminApiService.getApplication(id);
export const getAdminDeveloperApplications = () => AdminApiService.getDeveloperApplications();
export const approveAdminApplication = (id: string) => AdminApiService.approveApplication(id);
export const rejectAdminApplication = (id: string, reason?: string) =>
  AdminApiService.rejectApplication(id, reason);
export const suspendAdminApplication = (id: string, reason?: string) => AdminApiService.suspendApplication(id, reason);
export const reactivateAdminApplication = (id: string) => AdminApiService.reactivateApplication(id);
export const getAdminUsers = (params?: PaginationParams) => AdminApiService.getAllUsers(params);
export const getAdminDevelopers = (params?: PaginationParams) => AdminApiService.getDevelopers(params);
export const getAdminEndUsers = (params?: PaginationParams) => AdminApiService.getEndUsers(params);
export const getAdminUser = (id: string) => AdminApiService.getUser(id);
export const getAdminUserApplications = (userId: string) => AdminApiService.getUserApplications(userId);
export const getAdminUserActivity = (userId: string, params?: PaginationParams) => 
  AdminApiService.getUserActivity(userId, params);
export const suspendAdminUser = (id: string, reason?: string) => AdminApiService.suspendUser(id, reason);
export const reactivateAdminUser = (id: string) => AdminApiService.reactivateUser(id);
export const exportAdminUsers = (format?: 'csv' | 'json') => AdminApiService.exportUsers(format);
export const getAdminSystemAnalytics = () => AdminApiService.getSystemAnalytics();
