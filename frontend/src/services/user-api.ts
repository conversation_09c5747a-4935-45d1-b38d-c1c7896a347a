/**
 * User API Service
 * 
 * This service handles all user-related API calls for production use.
 * Provides methods for managing user applications, activity, profile, and security.
 */

import axiosServices from '@/utils/api/axios';
import type {
  ConnectedApplication,
  UserActivity,
  UserPermissions,
  MessageResponse,
  PaginationParams,
} from '@/types/api';
import type {
  UserProfileResponse,
  UserSecurityOverviewResponse,
} from '@/types/api/user';

/**
 * User API Service Class
 * Handles all user portal API operations
 */
export class UserApiService {
  private static readonly BASE_PATH = '/api/v1/user';

  // ============================================================================
  // Connected Applications Management
  // ============================================================================

  /**
   * Get user's connected applications
   */
  static async getConnectedApplications(params: PaginationParams = {}): Promise<{
    applications: ConnectedApplication[];
    total: number;
    limit: number;
    offset: number;
    user_id: string;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/applications`,
      { params }
    );
    return response.data;
  }

  /**
   * Get specific application details
   */
  static async getApplicationDetails(applicationId: string): Promise<ConnectedApplication | null> {
    const response = await axiosServices.get<ConnectedApplication>(
      `${this.BASE_PATH}/applications/${applicationId}`
    );
    return response.data;
  }

  /**
   * Revoke access to an application
   */
  static async revokeApplicationAccess(applicationId: string): Promise<MessageResponse> {
    const response = await axiosServices.delete<MessageResponse>(
      `${this.BASE_PATH}/applications/${applicationId}`
    );
    return response.data;
  }

  // ============================================================================
  // User Activity
  // ============================================================================

  /**
   * Get user activity logs
   */
  static async getUserActivity(params: PaginationParams = {}): Promise<{
    activity: UserActivity[];
    total: number;
    limit: number;
    offset: number;
    user_id: string;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/activity`,
      { params }
    );
    return response.data;
  }

  // ============================================================================
  // User Profile Management
  // ============================================================================

  /**
   * Get user profile
   */
  static async getUserProfile(): Promise<UserProfileResponse> {
    const response = await axiosServices.get<UserProfileResponse>(
      `${this.BASE_PATH}/profile`
    );
    return response.data;
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(profileData: Partial<UserProfileResponse>): Promise<UserProfileResponse> {
    const response = await axiosServices.put<UserProfileResponse>(
      `${this.BASE_PATH}/profile`,
      profileData
    );
    return response.data;
  }

  // ============================================================================
  // Security Management
  // ============================================================================

  /**
   * Get user security overview
   */
  static async getUserSecurityOverview(): Promise<UserSecurityOverviewResponse> {
    const response = await axiosServices.get<UserSecurityOverviewResponse>(
      `${this.BASE_PATH}/security/overview`
    );
    return response.data;
  }

  // ============================================================================
  // Permissions Management
  // ============================================================================

  /**
   * Get user's granted permissions across all applications
   */
  static async getUserPermissions(): Promise<UserPermissions> {
    const response = await axiosServices.get(`${this.BASE_PATH}/permissions`);
    return response.data;
  }

  // ============================================================================
  // Dashboard Statistics
  // ============================================================================

  /**
   * Get user's active sessions count
   */
  static async getActiveSessionsCount(): Promise<{ active_sessions_count: number; user_id: string }> {
    const response = await axiosServices.get(`${this.BASE_PATH}/sessions/active`);
    return response.data;
  }

  /**
   * Get user's security alerts count
   */
  static async getSecurityAlertsCount(): Promise<{ security_alerts_count: number; user_id: string; timeframe: string }> {
    const response = await axiosServices.get(`${this.BASE_PATH}/security/alerts`);
    return response.data;
  }

  // ============================================================================
  // Data Export Management
  // ============================================================================

  /**
   * Get available data categories for export
   */
  static async getExportCategories(): Promise<any[]> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/export/categories`
    );
    return response.data;
  }

  /**
   * List user's data export requests
   */
  static async getExportRequests(): Promise<any[]> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/exports`
    );
    return response.data;
  }

  /**
   * Request data export
   */
  static async requestDataExport(exportType: string = 'full'): Promise<any> {
    const response = await axiosServices.post(
      `${this.BASE_PATH}/export`,
      null,
      { params: { export_type: exportType } }
    );
    return response.data;
  }

  /**
   * Get export status
   */
  static async getExportStatus(exportId: string): Promise<any> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/export/${exportId}/status`
    );
    return response.data;
  }

  /**
   * Download export file
   */
  static async downloadExport(exportId: string): Promise<any> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/export/${exportId}/download`
    );
    return response.data;
  }

  // ============================================================================
  // Organization Invitations Management
  // ============================================================================

  /**
   * Get user's organization invitations
   */
  static async getUserInvitations(params: {
    status_filter?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    invitations: any[];
    total: number;
    limit: number;
    offset: number;
    user_email: string;
  }> {
    const response = await axiosServices.get(
      `${this.BASE_PATH}/invitations`,
      { params }
    );
    return response.data;
  }

  /**
   * Accept organization invitation
   */
  static async acceptInvitation(token: string): Promise<{
    message: string;
    membership_id?: string;
    organization_id?: string;
  }> {
    const response = await axiosServices.post(
      '/api/v1/organizations/invitations/accept',
      { token }
    );
    return response.data;
  }

  /**
   * Decline organization invitation
   */
  static async declineInvitation(token: string, reason?: string): Promise<{
    message: string;
  }> {
    const response = await axiosServices.post(
      '/api/v1/organizations/invitations/reject',
      { token, reason }
    );
    return response.data;
  }
}

// Export convenience functions for easier usage
export const getUserConnectedApplications = (params?: PaginationParams) => 
  UserApiService.getConnectedApplications(params);
export const getUserApplicationDetails = (applicationId: string) => 
  UserApiService.getApplicationDetails(applicationId);
export const revokeUserApplicationAccess = (applicationId: string) => 
  UserApiService.revokeApplicationAccess(applicationId);
export const getUserActivity = (params?: PaginationParams) => 
  UserApiService.getUserActivity(params);
export const getUserProfile = () => UserApiService.getUserProfile();
export const updateUserProfile = (profileData: Partial<UserProfileResponse>) =>
  UserApiService.updateUserProfile(profileData);
export const getUserSecurityOverview = () => UserApiService.getUserSecurityOverview();
export const getUserPermissions = () => UserApiService.getUserPermissions();
export const getActiveSessionsCount = () => UserApiService.getActiveSessionsCount();
export const getSecurityAlertsCount = () => UserApiService.getSecurityAlertsCount();
export const getExportCategories = () => UserApiService.getExportCategories();
export const getExportRequests = () => UserApiService.getExportRequests();
export const requestDataExport = (exportType?: string) => UserApiService.requestDataExport(exportType);
export const getExportStatus = (exportId: string) => UserApiService.getExportStatus(exportId);
export const downloadExport = (exportId: string) => UserApiService.downloadExport(exportId);

// Organization Invitations
export const getUserInvitations = (params?: { status_filter?: string; limit?: number; offset?: number }) =>
  UserApiService.getUserInvitations(params);
export const acceptInvitation = (token: string) => UserApiService.acceptInvitation(token);
export const declineInvitation = (token: string, reason?: string) => UserApiService.declineInvitation(token, reason);
