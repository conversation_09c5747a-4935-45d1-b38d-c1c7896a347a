/**
 * MFA API Service
 * 
 * Handles Multi-Factor Authentication API operations including
 * device setup, verification, and management
 */

import { API_CONFIG } from '@/config';

// Types
export interface MFADevice {
  id: string;
  device_name: string;
  device_type: string;
  is_active: boolean;
  is_verified: boolean;
  last_used_at: string | null;
  created_at: string;
  backup_codes_count: number;
}

export interface MFASetupRequest {
  device_name: string;
  issuer_name?: string;
}

export interface MFASetupResponse {
  device_id: string;
  qr_code: string;
  backup_codes: string[];
  manual_entry_key: string;
}

export interface MFAVerificationRequest {
  code: string;
  device_id?: string;
}

export interface MFAVerificationResponse {
  valid: boolean;
  message: string;
}

export interface BackupCodeVerificationRequest {
  backup_code: string;
}

export interface MFAChallengeResponse {
  challenge_required: boolean;
  available_methods: string[];
  device_count: number;
  backup_codes_available: boolean;
}

// API Service Class
export class MFAApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_CONFIG.baseURL}/api/v1/mfa`;
  }

  /**
   * Set up a new MFA device
   */
  async setupDevice(request: MFASetupRequest): Promise<MFASetupResponse> {
    const response = await fetch(`${this.baseUrl}/setup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to set up MFA device');
    }

    return response.json();
  }

  /**
   * Verify MFA setup with TOTP code
   */
  async verifySetup(request: MFAVerificationRequest): Promise<MFAVerificationResponse> {
    const response = await fetch(`${this.baseUrl}/verify-setup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to verify MFA setup');
    }

    return response.json();
  }

  /**
   * Verify MFA code for authentication
   */
  async verifyCode(request: MFAVerificationRequest): Promise<MFAVerificationResponse> {
    const response = await fetch(`${this.baseUrl}/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to verify MFA code');
    }

    return response.json();
  }

  /**
   * Verify backup code for authentication
   */
  async verifyBackupCode(request: BackupCodeVerificationRequest): Promise<MFAVerificationResponse> {
    const response = await fetch(`${this.baseUrl}/verify-backup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to verify backup code');
    }

    return response.json();
  }

  /**
   * Get user's MFA devices
   */
  async getDevices(): Promise<MFADevice[]> {
    const response = await fetch(`${this.baseUrl}/devices`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get MFA devices');
    }

    return response.json();
  }

  /**
   * Remove an MFA device
   */
  async removeDevice(deviceId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/devices/${deviceId}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to remove MFA device');
    }
  }

  /**
   * Get MFA challenge information
   */
  async getChallenge(): Promise<MFAChallengeResponse> {
    const response = await fetch(`${this.baseUrl}/challenge`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get MFA challenge');
    }

    return response.json();
  }
}

// Export singleton instance
export const mfaApi = new MFAApiService();

// Enhanced Auth Service for MFA Login
export interface MFALoginRequest {
  session_token: string;
  method: 'totp' | 'backup_code';
  code: string;
  device_id?: string;
}

export interface MFALoginResponse {
  success: boolean;
  session_token?: string;
  expires_at?: string;
  user?: any;
  message: string;
}

export interface MFARequiredResponse {
  mfa_required: boolean;
  session_token: string;
  available_methods: string[];
  expires_in: number;
  message: string;
}

export class AuthMFAService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_CONFIG.baseURL}/api/v1/auth`;
  }

  /**
   * Complete MFA login process
   */
  async completeMFALogin(request: MFALoginRequest): Promise<MFALoginResponse> {
    const response = await fetch(`${this.baseUrl}/mfa-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'MFA login failed');
    }

    return response.json();
  }
}

// Export singleton instance
export const authMFAApi = new AuthMFAService();

// Utility functions
export const downloadBackupCodes = (codes: string[], filename: string = 'mfa-backup-codes.txt') => {
  const content = [
    'GeNieGO SSO - MFA Backup Codes',
    '================================',
    '',
    'IMPORTANT: Store these codes in a secure location.',
    'Each code can only be used once.',
    '',
    'Backup Codes:',
    ...codes.map((code, index) => `${index + 1}. ${code}`),
    '',
    `Generated: ${new Date().toLocaleString()}`,
  ].join('\n');

  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const copyBackupCodes = async (codes: string[]): Promise<boolean> => {
  try {
    const content = [
      'GeNieGO SSO - MFA Backup Codes',
      '================================',
      '',
      'IMPORTANT: Store these codes in a secure location.',
      'Each code can only be used once.',
      '',
      'Backup Codes:',
      ...codes.map((code, index) => `${index + 1}. ${code}`),
    ].join('\n');

    await navigator.clipboard.writeText(content);
    return true;
  } catch (error) {
    console.error('Failed to copy backup codes:', error);
    return false;
  }
};
