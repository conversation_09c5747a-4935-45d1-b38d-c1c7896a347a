/**
 * System Service
 *
 * This service handles system-level operations like health checks, status monitoring,
 * and service availability detection. It provides a centralized way to manage
 * API connectivity and system status across the application.
 */

import axiosServices from '@/utils/api/axios';
import type { HealthCheckResponse, ApiStatus } from '@/types/system';

/**
 * System API Service
 * Provides centralized system monitoring and health check functionality
 */
export class SystemService {
  private static apiStatus: ApiStatus | null = null;
  private static readonly HEALTH_ENDPOINT = '/api/health';
  private static readonly DEFAULT_TIMEOUT = 3000;

  /**
   * Check if the API is available by testing the health endpoint
   * This is the primary method for determining API connectivity
   */
  static async checkApiAvailability(
    timeout: number = SystemService.DEFAULT_TIMEOUT
  ): Promise<boolean> {
    const startTime = Date.now();

    try {
      // Try to ping the health endpoint to check if API is available
      await axiosServices.get(SystemService.HEALTH_ENDPOINT, { timeout });
      const responseTime = Date.now() - startTime;

      // Update API status cache
      SystemService.apiStatus = {
        available: true,
        lastChecked: new Date(),
        responseTime,
      };

      return true;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      // Update API status cache with error details
      SystemService.apiStatus = {
        available: false,
        lastChecked: new Date(),
        responseTime,
        error: errorMessage,
      };

      // Only log warnings for unexpected errors, not normal connectivity issues
      if (!errorMessage.includes('Network Error') &&
          !errorMessage.includes('timeout') &&
          !errorMessage.includes('ECONNREFUSED')) {
        console.warn(
          `API health check failed (${responseTime}ms):`,
          errorMessage
        );
      }
      return false;
    }
  }

  /**
   * Get detailed health check information from the system
   * This provides more comprehensive system status information
   */
  static async getHealthStatus(): Promise<HealthCheckResponse | null> {
    try {
      const response = await axiosServices.get<HealthCheckResponse>(
        SystemService.HEALTH_ENDPOINT,
        { timeout: SystemService.DEFAULT_TIMEOUT }
      );

      return {
        ...response.data,
        status: response.data.status || 'healthy',
        timestamp: response.data.timestamp || new Date().toISOString(),
      };
    } catch (error) {
      console.warn('Failed to fetch detailed health status:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get current API status from cache
   * Returns the last known API availability status without making a network request
   */
  static getApiStatus(): ApiStatus | null {
    return SystemService.apiStatus;
  }

  /**
   * Check if API is currently available (from cache)
   * Returns null if no check has been performed yet
   */
  static isApiAvailable(): boolean | null {
    return SystemService.apiStatus?.available ?? null;
  }

  /**
   * Reset API status cache
   * Useful for forcing a fresh API availability check
   */
  static resetApiStatus(): void {
    SystemService.apiStatus = null;
  }

  /**
   * Perform a quick ping to test basic connectivity
   * This is a lightweight alternative to full health checks
   */
  static async ping(timeout: number = 1000): Promise<boolean> {
    try {
      await axiosServices.get(SystemService.HEALTH_ENDPOINT, { timeout });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get API response time from the last check
   * Useful for monitoring API performance
   */
  static getLastResponseTime(): number | null {
    return SystemService.apiStatus?.responseTime ?? null;
  }

  /**
   * Check if the last API check was recent (within specified minutes)
   * Helps determine if a fresh check is needed
   */
  static isStatusFresh(maxAgeMinutes: number = 5): boolean {
    if (!SystemService.apiStatus) return false;

    const ageInMs = Date.now() - SystemService.apiStatus.lastChecked.getTime();
    const ageInMinutes = ageInMs / (1000 * 60);

    return ageInMinutes <= maxAgeMinutes;
  }

  /**
   * Get a cached API status or perform a fresh check if needed
   * This is the recommended method for most use cases
   */
  static async getOrCheckApiAvailability(
    maxCacheAgeMinutes: number = 5
  ): Promise<boolean> {
    // Return cached status if it's fresh
    if (
      SystemService.isStatusFresh(maxCacheAgeMinutes) &&
      SystemService.apiStatus
    ) {
      return SystemService.apiStatus.available;
    }

    // Perform fresh check
    return await SystemService.checkApiAvailability();
  }
}

// Export convenience functions for easier usage
export const checkApiAvailability = (timeout?: number) =>
  SystemService.checkApiAvailability(timeout);
export const getHealthStatus = () => SystemService.getHealthStatus();
export const getApiStatus = () => SystemService.getApiStatus();
export const isApiAvailable = () => SystemService.isApiAvailable();
export const resetApiStatus = () => SystemService.resetApiStatus();
export const ping = (timeout?: number) => SystemService.ping(timeout);
export const getLastResponseTime = () => SystemService.getLastResponseTime();
export const isStatusFresh = (maxAgeMinutes?: number) =>
  SystemService.isStatusFresh(maxAgeMinutes);
export const getOrCheckApiAvailability = (maxCacheAgeMinutes?: number) =>
  SystemService.getOrCheckApiAvailability(maxCacheAgeMinutes);
