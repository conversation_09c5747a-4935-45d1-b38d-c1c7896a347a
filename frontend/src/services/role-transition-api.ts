/**
 * Role Transition API Service
 * 
 * Handles role transition API operations including
 * application submission, review, and management
 */

import { API_CONFIG } from '@/config';

// Types
export interface DeveloperApplication {
  id: string;
  user_id: string;
  application_reason: string;
  technical_background?: string;
  intended_use?: string;
  status: 'pending' | 'approved' | 'rejected' | 'withdrawn';
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DeveloperApplicationWithUser extends DeveloperApplication {
  reason?: string; // Backend uses 'reason' field name
  user: {
    id: string;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    role: string;
    created_at: string;
    last_login?: string; // Backend uses 'last_login' not 'last_login_at'
  };
}

export interface ApplicationSubmissionRequest {
  application_reason: string;
  technical_background?: string;
  intended_use?: string;
}

export interface ApplicationReviewRequest {
  decision: 'approved' | 'rejected';
  admin_notes?: string;
}

export interface ApplicationStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  withdrawn: number;
}

// API Service Class
export class RoleTransitionApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_CONFIG.baseURL}/api/v1/role-transitions`;
  }

  /**
   * Submit a developer role application
   */
  async submitApplication(request: ApplicationSubmissionRequest): Promise<DeveloperApplication> {
    const response = await fetch(`${this.baseUrl}/developer-application`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to submit application');
    }

    return response.json();
  }

  /**
   * Get user's applications
   */
  async getMyApplications(): Promise<DeveloperApplication[]> {
    const response = await fetch(`${this.baseUrl}/my-applications`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get applications');
    }

    return response.json();
  }

  /**
   * Get application by ID
   */
  async getApplication(applicationId: string): Promise<DeveloperApplication> {
    const response = await fetch(`${this.baseUrl}/applications/${applicationId}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get application');
    }

    return response.json();
  }

  /**
   * Withdraw an application
   */
  async withdrawApplication(applicationId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/applications/${applicationId}/withdraw`, {
      method: 'POST',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to withdraw application');
    }
  }

  // Admin methods
  /**
   * Get all applications (admin only)
   */
  async getAllApplications(
    status?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<DeveloperApplicationWithUser[]> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    if (status) {
      params.append('status', status);
    }

    const response = await fetch(`${this.baseUrl}/admin/applications?${params}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get applications');
    }

    return response.json();
  }

  /**
   * Get application with user details (admin only)
   */
  async getApplicationWithUser(applicationId: string): Promise<DeveloperApplicationWithUser> {
    const response = await fetch(`${this.baseUrl}/admin/applications/${applicationId}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get application');
    }

    return response.json();
  }

  /**
   * Review an application (admin only)
   */
  async reviewApplication(
    applicationId: string,
    review: ApplicationReviewRequest
  ): Promise<DeveloperApplication> {
    const response = await fetch(`${this.baseUrl}/admin/applications/${applicationId}/review`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(review),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to review application');
    }

    return response.json();
  }

  /**
   * Get application statistics (admin only)
   */
  async getApplicationStats(): Promise<ApplicationStats> {
    const response = await fetch(`${this.baseUrl}/admin/stats`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get application stats');
    }

    return response.json();
  }
}

// Export singleton instance
export const roleTransitionApi = new RoleTransitionApiService();

// Utility functions
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'withdrawn':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

export const getStatusIcon = (status: string): string => {
  switch (status) {
    case 'pending':
      return '⏳';
    case 'approved':
      return '✅';
    case 'rejected':
      return '❌';
    case 'withdrawn':
      return '↩️';
    default:
      return '❓';
  }
};

export const formatApplicationDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const validateApplicationForm = (data: ApplicationSubmissionRequest): string[] => {
  const errors: string[] = [];

  if (!data.application_reason || data.application_reason.trim().length === 0) {
    errors.push('role.transition.error.reason.required');
  } else if (data.application_reason.trim().length < 50) {
    errors.push('role.transition.error.reason.min.length');
  } else if (data.application_reason.trim().length > 1000) {
    errors.push('role.transition.error.reason.max.length');
  }

  if (data.technical_background && data.technical_background.length > 1000) {
    errors.push('role.transition.error.technical.max.length');
  }

  if (data.intended_use && data.intended_use.length > 1000) {
    errors.push('role.transition.error.intended.max.length');
  }

  return errors;
};
