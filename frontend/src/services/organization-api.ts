/**
 * Organization API Service for GeNieGO SSO Server Frontend
 * 
 * This service provides methods to interact with the organization management API,
 * including CRUD operations, membership management, and invitation handling.
 */

import axiosServices from '@/utils/api/axios';

// Organization Types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  website?: string;
  is_active: boolean;
  is_public: boolean;
  max_members: number;
  subscription_tier: string;
  billing_email?: string;
  settings?: Record<string, any>;
  created_at: string;
  updated_at: string;
  member_count?: number;
  user_role?: string;
}

export interface OrganizationCreate {
  name: string;
  slug: string;
  description?: string;
  website?: string;
  is_public?: boolean;
  max_members?: number;
  settings?: Record<string, any>;
}

export interface OrganizationUpdate {
  name?: string;
  description?: string;
  website?: string;
  is_public?: boolean;
  max_members?: number;
  billing_email?: string;
  settings?: Record<string, any>;
}

// Membership Types
export interface OrganizationMembership {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'member';
  is_active: boolean;
  invited_by?: string;
  invited_at?: string;
  joined_at: string;
  permissions?: Record<string, any>;
  created_at: string;
  updated_at: string;
  user_email?: string;
  user_name?: string;
}

export interface OrganizationMembershipCreate {
  user_id: string;
  role: 'owner' | 'admin' | 'member';
  permissions?: Record<string, any>;
}

export interface OrganizationMembershipUpdate {
  role?: 'owner' | 'admin' | 'member';
  permissions?: Record<string, any>;
}

// Invitation Types
export interface OrganizationInvitation {
  id: string;
  organization_id: string;
  invited_by: string;
  user_id?: string;
  email: string;
  role: 'admin' | 'member';
  token: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  accepted_at?: string;
  rejected_at?: string;
  rejection_reason?: string;
  message?: string;
  created_at: string;
  updated_at: string;
  organization_name?: string;
  organization_slug?: string;
  inviter_name?: string;
  inviter_email?: string;
}

export interface OrganizationInvitationCreate {
  email: string;
  role: 'admin' | 'member';
  message?: string;
  expires_in_days?: number;
}

// Application Organization Types
export interface ApplicationOrganization {
  id: string;
  application_id: string;
  organization_id: string;
  assigned_by: string;
  is_active: boolean;
  role: 'application' | 'service' | 'integration';
  description?: string;
  settings?: Record<string, any>;
  created_at: string;
  updated_at: string;
  application_name?: string;
  application_client_id?: string;
}

export interface ApplicationOrganizationCreate {
  application_id: string;
  role?: 'application' | 'service' | 'integration';
  description?: string;
  settings?: Record<string, any>;
}

export interface ApplicationOrganizationUpdate {
  role?: 'application' | 'service' | 'integration';
  description?: string;
  settings?: Record<string, any>;
  is_active?: boolean;
}

/**
 * Organization API Service Class
 */
export class OrganizationApiService {
  private readonly baseUrl = '/api/v1/organizations';

  // Organization CRUD Operations
  async createOrganization(data: OrganizationCreate): Promise<Organization> {
    const response = await axiosServices.post<Organization>(`${this.baseUrl}/`, data);
    return response.data;
  }

  async getUserOrganizations(): Promise<Organization[]> {
    const response = await axiosServices.get<Organization[]>(`${this.baseUrl}/`);
    return response.data;
  }

  async getPublicOrganizations(limit = 50): Promise<Organization[]> {
    const response = await axiosServices.get<Organization[]>(`${this.baseUrl}/public`, {
      params: { limit }
    });
    return response.data;
  }

  async getOrganizationById(organizationId: string): Promise<Organization> {
    const response = await axiosServices.get<Organization>(`${this.baseUrl}/${organizationId}`);
    return response.data;
  }

  async getOrganizationBySlug(slug: string): Promise<Organization> {
    const response = await axiosServices.get<Organization>(`${this.baseUrl}/slug/${slug}`);
    return response.data;
  }

  async updateOrganization(organizationId: string, data: OrganizationUpdate): Promise<Organization> {
    const response = await axiosServices.put<Organization>(`${this.baseUrl}/${organizationId}`, data);
    return response.data;
  }

  async deleteOrganization(organizationId: string): Promise<void> {
    await axiosServices.delete(`${this.baseUrl}/${organizationId}`);
  }

  // Membership Management
  async getOrganizationMembers(organizationId: string, includeInactive = false): Promise<OrganizationMembership[]> {
    const response = await axiosServices.get<OrganizationMembership[]>(
      `${this.baseUrl}/${organizationId}/members`,
      { params: { include_inactive: includeInactive } }
    );
    return response.data;
  }

  async addOrganizationMember(organizationId: string, data: OrganizationMembershipCreate): Promise<OrganizationMembership> {
    const response = await axiosServices.post<OrganizationMembership>(
      `${this.baseUrl}/${organizationId}/members`,
      data
    );
    return response.data;
  }

  async getOrganizationMember(organizationId: string, userId: string): Promise<OrganizationMembership> {
    const response = await axiosServices.get<OrganizationMembership>(
      `${this.baseUrl}/${organizationId}/members/${userId}`
    );
    return response.data;
  }

  async updateOrganizationMember(
    organizationId: string,
    userId: string,
    data: OrganizationMembershipUpdate
  ): Promise<OrganizationMembership> {
    const response = await axiosServices.put<OrganizationMembership>(
      `${this.baseUrl}/${organizationId}/members/${userId}`,
      data
    );
    return response.data;
  }

  async removeOrganizationMember(organizationId: string, userId: string): Promise<void> {
    await axiosServices.delete(`${this.baseUrl}/${organizationId}/members/${userId}`);
  }

  async addUserToOrganization(organizationId: string, data: { user_id: string; role: string }): Promise<void> {
    await axiosServices.post(`${this.baseUrl}/${organizationId}/members`, data);
  }

  async removeApplicationFromOrganization(organizationId: string, applicationId: string): Promise<void> {
    await axiosServices.delete(`${this.baseUrl}/${organizationId}/applications/${applicationId}`);
  }



  // Invitation Management
  async createOrganizationInvitation(
    organizationId: string,
    data: OrganizationInvitationCreate
  ): Promise<OrganizationInvitation> {
    const response = await axiosServices.post<OrganizationInvitation>(
      `${this.baseUrl}/${organizationId}/invitations`,
      data
    );
    return response.data;
  }

  async getOrganizationInvitations(
    organizationId: string,
    statusFilter = 'pending'
  ): Promise<OrganizationInvitation[]> {
    const response = await axiosServices.get<OrganizationInvitation[]>(
      `${this.baseUrl}/${organizationId}/invitations`,
      { params: { status_filter: statusFilter } }
    );
    return response.data;
  }

  async getInvitationByToken(token: string): Promise<OrganizationInvitation> {
    const response = await axiosServices.get<OrganizationInvitation>(`${this.baseUrl}/invitations/${token}`);
    return response.data;
  }

  async acceptInvitation(token: string): Promise<{ message: string; membership_id?: string; organization_id?: string }> {
    const response = await axiosServices.post(`${this.baseUrl}/invitations/accept`, { token });
    return response.data;
  }

  async rejectInvitation(token: string, reason?: string): Promise<{ message: string }> {
    const response = await axiosServices.post(`${this.baseUrl}/invitations/reject`, { token, reason });
    return response.data;
  }

  async cancelInvitation(organizationId: string, invitationId: string): Promise<void> {
    await axiosServices.delete(`${this.baseUrl}/${organizationId}/invitations/${invitationId}`);
  }

  async leaveOrganization(organizationId: string): Promise<void> {
    await axiosServices.delete(`${this.baseUrl}/${organizationId}/leave`);
  }

  // Application Organization Management
  async getOrganizationApplications(organizationId: string, includeInactive = false): Promise<ApplicationOrganization[]> {
    const response = await axiosServices.get<ApplicationOrganization[]>(
      `${this.baseUrl}/${organizationId}/applications`,
      { params: { include_inactive: includeInactive } }
    );
    return response.data;
  }

  async assignApplicationToOrganization(
    organizationId: string,
    data: ApplicationOrganizationCreate
  ): Promise<ApplicationOrganization> {
    const response = await axiosServices.post<ApplicationOrganization>(
      `${this.baseUrl}/${organizationId}/applications`,
      data
    );
    return response.data;
  }

  async getApplicationOrganizations(applicationId: string): Promise<ApplicationOrganization[]> {
    const response = await axiosServices.get<ApplicationOrganization[]>(
      `${this.baseUrl}/applications/${applicationId}/organizations`
    );
    return response.data;
  }

  async updateApplicationOrganization(
    organizationId: string,
    assignmentId: string,
    data: ApplicationOrganizationUpdate
  ): Promise<ApplicationOrganization> {
    const response = await axiosServices.put<ApplicationOrganization>(
      `${this.baseUrl}/${organizationId}/applications/${assignmentId}`,
      data
    );
    return response.data;
  }


}

// Export singleton instance
export const organizationApi = new OrganizationApiService();
