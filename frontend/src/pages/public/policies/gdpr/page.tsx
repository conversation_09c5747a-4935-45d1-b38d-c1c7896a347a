import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect, useRef, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { gdprContent } from '@/data/public/policies';
import MarkdownRender from '@/components/common/markdown-renderer-lazy';
import useConfig from '@/hooks/use-config';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * GDPR Compliance Page
 *
 * Displays the company's GDPR compliance information using markdown content from the policies file.
 * This page explains how the company adheres to the General Data Protection Regulation.
 * Supports multiple languages based on the user's selected locale.
 * Enhanced with animations for better user experience.
 */
export default function Page() {
  const intl = useIntl();
  const { locale } = useConfig();
  const metadata = PAGE_METADATA.gdpr;
  const companyName = SITE_INFO.organization.name;
  const dataProtectionOfficer = SITE_INFO.legal.dataProtectionOfficer;
  const lastUpdated = SITE_INFO.legal.gdprLastUpdated;

  // State to track if content is visible
  const [contentVisible, setContentVisible] = useState(false);
  const contentRef = useRef(null);

  // Add intersection observer to check when content enters viewport
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setContentVisible(true);
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    if (contentRef.current) {
      observer.observe(contentRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Get the appropriate content based on locale
  const getLocalizedContent = () => {
    // First check if the current locale is available
    if (locale in gdprContent.content) {
      const content =
        gdprContent.content[locale as keyof typeof gdprContent.content];
      if (content) return content;
    }

    // Try to find content for the current locale's language without region
    const language = intl.locale.split('-')[0];
    if (language !== intl.locale && language in gdprContent.content) {
      const content =
        gdprContent.content[language as keyof typeof gdprContent.content];
      if (content) return content;
    }

    // Default to English
    return gdprContent.content.en;
  };

  // Get localized title based on current locale
  const getLocalizedTitle = () => {
    if (gdprContent.titleTranslations) {
      // First check exact locale match
      if (locale in gdprContent.titleTranslations) {
        const title =
          gdprContent.titleTranslations[
            locale as keyof typeof gdprContent.titleTranslations
          ];
        if (title) return title;
      }

      // Try language without region
      const language = intl.locale.split('-')[0];
      if (
        language !== intl.locale &&
        language in gdprContent.titleTranslations
      ) {
        const title =
          gdprContent.titleTranslations[
            language as keyof typeof gdprContent.titleTranslations
          ];
        if (title) return title;
      }
    }

    return gdprContent.title;
  };

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  return (
    <div
      ref={contentRef}
      className={`section-reveal ${contentVisible ? 'visible' : ''}`}
    >
      <div className="animate-fadeIn">
        <MarkdownRender
          content={getLocalizedContent()}
          variables={{
            companyName: companyName,
            dpo: dataProtectionOfficer,
          }}
          title={getLocalizedTitle()}
          lastUpdated={lastUpdated}
          className="animate-fadeIn"
        />
      </div>
    </div>
  );
}
