import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect, useRef, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import companyMilestones, {
  calculateMilestoneYear,
  getMilestoneWithTranslation,
} from '@/data/public/milestones';
import { AnimatedList } from '@/components/ui/magicui/animated-list';
import CountUp from 'react-countup';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * About Us Page
 *
 * Displays comprehensive information about the company,
 * including mission, vision, history, and key achievements.
 *
 * @returns {JSX.Element} The About Us Page component
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.about;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const foundedYear = SITE_INFO.organization.foundedYear || 2010;
  const currentYear = new Date().getFullYear();
  const yearsInOperation = currentYear - foundedYear;
  const locale = intl.locale;

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    mission: false,
    story: false,
    stats: false,
  });

  // Refs for the sections
  const missionRef = useRef(null);
  const storyRef = useRef(null);
  const statsRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (missionRef.current) observer.observe(missionRef.current);
    if (storyRef.current) observer.observe(storyRef.current);
    if (statsRef.current) observer.observe(statsRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Transform milestone data for display using our helper functions
  const milestones = companyMilestones.map(milestone => {
    // Calculate the actual year for this milestone
    const year = calculateMilestoneYear(
      milestone.year,
      foundedYear,
      currentYear
    );

    // Get the milestone content with translations for the current locale
    const translatedContent = getMilestoneWithTranslation(
      milestone,
      locale,
      displayName
    );

    return {
      year,
      title: translatedContent.title,
      description: translatedContent.description,
    };
  });

  // Company values for animated list
  const companyValues = [
    {
      id: 'innovation',
      icon: (
        <svg
          className="mr-2 h-6 w-6 flex-shrink-0 text-primary"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      ),
      title: intl.formatMessage({ id: 'page.about.values.innovation' }),
      description: intl.formatMessage({
        id: 'page.about.values.innovation.description',
      }),
    },
    {
      id: 'quality',
      icon: (
        <svg
          className="mr-2 h-6 w-6 flex-shrink-0 text-primary"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      ),
      title: intl.formatMessage({ id: 'page.about.values.quality' }),
      description: intl.formatMessage({
        id: 'page.about.values.quality.description',
      }),
    },
    {
      id: 'integrity',
      icon: (
        <svg
          className="mr-2 h-6 w-6 flex-shrink-0 text-primary"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      ),
      title: intl.formatMessage({ id: 'page.about.values.integrity' }),
      description: intl.formatMessage({
        id: 'page.about.values.integrity.description',
      }),
    },
    {
      id: 'customer',
      icon: (
        <svg
          className="mr-2 h-6 w-6 flex-shrink-0 text-primary"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      ),
      title: intl.formatMessage({ id: 'page.about.values.customer' }),
      description: intl.formatMessage({
        id: 'page.about.values.customer.description',
      }),
    },
  ];

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-3xl text-center sm:text-left">
            <div className="animate-hero-content">
              <h1 className="hero-element mb-6 text-4xl font-bold md:text-5xl">
                {intl.formatMessage(
                  { id: 'page.about.title' },
                  { company: displayName }
                )}
              </h1>
              <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
                {intl.formatMessage(
                  { id: 'page.about.subtitle' },
                  { years: yearsInOperation }
                )}
              </p>
              <div className="hero-element hero-delay-2 flex flex-col justify-center gap-4 sm:flex-row sm:justify-start">
                <a
                  href="#mission"
                  className="shimmer-effect rounded-md bg-primary px-6 py-3 font-medium text-primary-foreground transition-colors hover:bg-primary/90"
                >
                  {intl.formatMessage({ id: 'page.about.cta.mission' })}
                </a>
                <a
                  href="#story"
                  className="shimmer-effect rounded-md bg-white/10 px-6 py-3 font-medium text-white transition-colors hover:bg-white/20"
                >
                  {intl.formatMessage({ id: 'page.about.cta.story' })}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mission section */}
      <div
        id="mission"
        ref={missionRef}
        data-section="mission"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.mission ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-5xl">
          <div className="flex flex-col gap-12 md:flex-row">
            <div className="md:w-1/2">
              <h2 className="animate-slideUp mb-6 text-3xl font-bold">
                {intl.formatMessage({ id: 'page.about.mission.title' })}
              </h2>
              <p className="animate-slideUp animation-delay-200 mb-4 text-gray-600">
                {intl.formatMessage(
                  { id: 'page.about.mission.description1' },
                  { company: displayName }
                )}
              </p>
              <p className="animate-slideUp animation-delay-300 mb-4 text-muted-foreground">
                {intl.formatMessage({ id: 'page.about.mission.description2' })}
              </p>
              <div className="mt-8">
                <h3 className="mb-4 text-xl font-semibold">
                  {intl.formatMessage({ id: 'page.about.values.title' })}
                </h3>
                <AnimatedList delay={800} className="space-y-3">
                  {companyValues.map(value => (
                    <li
                      key={value.id}
                      className="flex items-start rounded-lg border border-border bg-card p-2 shadow-sm transition-shadow duration-300 hover:shadow-md"
                    >
                      {value.icon}
                      <span>
                        <strong>{value.title}:</strong> {value.description}
                      </span>
                    </li>
                  ))}
                </AnimatedList>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="rounded-lg bg-muted p-8 shadow-sm transition-shadow duration-300 hover:shadow-md">
                <h3 className="animate-slideUp mb-4 text-2xl font-bold">
                  {intl.formatMessage({ id: 'page.about.vision.title' })}
                </h3>
                <p className="animate-fadeIn animation-delay-200 mb-6 text-muted-foreground">
                  {intl.formatMessage({ id: 'page.about.vision.description' })}
                </p>

                <h3 className="animate-slideUp animation-delay-300 mb-4 text-2xl font-bold">
                  {intl.formatMessage({ id: 'page.about.difference.title' })}
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center rounded-lg bg-card p-3 shadow-sm transition-transform duration-300 hover:-translate-y-1">
                    <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {intl.formatMessage({
                          id: 'page.about.difference.design.title',
                        })}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'page.about.difference.design.description',
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center rounded-lg bg-card p-3 shadow-sm transition-transform duration-300 hover:-translate-y-1">
                    <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {intl.formatMessage({
                          id: 'page.about.difference.security.title',
                        })}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'page.about.difference.security.description',
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center rounded-lg bg-card p-3 shadow-sm transition-transform duration-300 hover:-translate-y-1">
                    <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905a3.61 3.61 0 01-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {intl.formatMessage({
                          id: 'page.about.difference.support.title',
                        })}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'page.about.difference.support.description',
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Our story section */}
      <div
        id="story"
        ref={storyRef}
        data-section="story"
        className={`section-reveal bg-muted py-16 ${visibleSections.story ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-5xl">
            <h2 className="mb-12 text-center text-3xl font-bold">
              {intl.formatMessage({ id: 'page.about.story.title' })}
            </h2>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-1/2 h-full w-0.5 -translate-x-1/2 transform bg-border"></div>

              {/* Timeline entries */}
              <div className="space-y-12">
                {milestones.map((milestone, index) => (
                  <div
                    key={index}
                    className={`flex flex-col items-center md:flex-row ${index % 2 === 0 ? 'md:flex-row-reverse' : ''} stagger-card`}
                    style={{ animationDelay: `${index * 300}ms` }}
                  >
                    <div className="p-4 md:w-1/2">
                      <div
                        className={`md:${index % 2 === 0 ? 'text-right' : 'text-left'}`}
                      >
                        <div className="mb-2 inline-block rounded bg-primary/10 px-3 py-1 text-primary">
                          {milestone.year}
                        </div>
                        <h3 className="mb-2 text-xl font-bold">
                          {milestone.title}
                        </h3>
                        <p className="text-muted-foreground">{milestone.description}</p>
                      </div>
                    </div>
                    <div className="flex justify-center md:w-8">
                      <div className="z-10 flex h-8 w-8 items-center justify-center rounded-full bg-primary">
                        <svg
                          className="h-4 w-4 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="hidden md:block md:w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats section */}
      <div
        ref={statsRef}
        data-section="stats"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.stats ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-5xl">
          <h2 className="mb-12 text-center text-3xl font-bold">
            {intl.formatMessage(
              { id: 'page.about.today.title' },
              { company: displayName }
            )}
          </h2>

          <div className="grid grid-cols-2 gap-6 text-center md:grid-cols-4">
            <div className="p-6">
              <div className="mb-2 text-4xl font-bold text-primary">
                <CountUp end={3000} suffix="+" duration={2.5} enableScrollSpy />
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.about.stats.customers' })}
              </div>
            </div>
            <div className="p-6">
              <div className="mb-2 text-4xl font-bold text-primary">
                <CountUp end={150} suffix="+" duration={2.5} enableScrollSpy />
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.about.stats.countries' })}
              </div>
            </div>
            <div className="p-6">
              <div className="mb-2 text-4xl font-bold text-primary">
                <CountUp end={50} suffix="+" duration={2.5} enableScrollSpy />
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.about.stats.team' })}
              </div>
            </div>
            <div className="p-6">
              <div className="mb-2 text-4xl font-bold text-primary">
                <CountUp
                  end={99.9}
                  suffix="%"
                  decimals={1}
                  duration={2.5}
                  enableScrollSpy
                />
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.about.stats.uptime' })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
