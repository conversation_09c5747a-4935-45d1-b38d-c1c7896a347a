import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  blogPosts,
  categories,
  getTranslatedText,
  getCategoryName,
  filterPosts,
  getFeaturedPosts,
  getPaginatedPosts,
} from '@/data/public/blog';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Blog Page Component
 *
 * Displays a collection of blog posts organized by categories.
 * Features include:
 * - Featured/recent blog posts section
 * - Category filtering
 * - Search functionality
 * - Pagination
 * - Related post suggestions
 *
 * @returns {JSX.Element} The Blog Page component
 */
export default function Page() {
  const intl = useIntl();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.blog;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    featuredPosts: false,
    blogGrid: false,
    newsletter: false,
  });

  // Refs for the sections
  const featuredPostsRef = useRef(null);
  const blogGridRef = useRef(null);
  const newsletterRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (featuredPostsRef.current) observer.observe(featuredPostsRef.current);
    if (blogGridRef.current) observer.observe(blogGridRef.current);
    if (newsletterRef.current) observer.observe(newsletterRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Filter posts by category and search query using locale-aware functions
  const filteredPosts = filterPosts(
    blogPosts,
    selectedCategory,
    searchQuery,
    locale
  );

  // Featured posts (for the hero section)
  const featuredPosts = getFeaturedPosts(blogPosts);

  // Pagination logic
  const postsPerPage = 4;
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const paginatedPosts = getPaginatedPosts(
    filteredPosts,
    currentPage,
    postsPerPage
  );

  // Format date function
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="bg-background">
      {/* Hero section with featured posts */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="animate-hero-content mx-auto mb-12 max-w-4xl text-center">
            <h1 className="hero-element mb-4 text-4xl font-bold">
              {intl.formatMessage(
                { id: 'page.blog.title' },
                { defaultMessage: 'Blog & Resources' }
              )}
            </h1>
            <p className="hero-element hero-delay-1 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'page.blog.subtitle' },
                {
                  company: displayName,
                  defaultMessage:
                    'Insights, tutorials, and updates from the {company} team.',
                }
              )}
            </p>
          </div>

          {featuredPosts.length > 0 && (
            <div
              ref={featuredPostsRef}
              data-section="featuredPosts"
              className={`section-reveal mx-auto max-w-6xl ${visibleSections.featuredPosts ? 'visible' : ''}`}
            >
              <div className="grid gap-8 md:grid-cols-2">
                {featuredPosts.slice(0, 2).map((post, index) => (
                  <div
                    key={post.id}
                    className="animate-fadeIn transform overflow-hidden rounded-xl bg-card shadow-sm transition-transform hover:-translate-y-1"
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    <div className="relative h-48 w-full">
                      <div className="absolute inset-0 flex items-center justify-center bg-muted">
                        <span className="text-muted-foreground">
                          {intl.formatMessage({ id: 'blog.featured.image' })}
                        </span>
                      </div>
                      {/* <Image
                        src={post.image}
                        alt={getTranslatedText(post.title, locale)}
                        fill
                        className="object-cover"
                      /> */}
                    </div>
                    <div className="p-6">
                      <div className="mb-2 flex items-center">
                        <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {getCategoryName(post.category, locale)}
                        </span>
                        <span className="ml-2 text-sm text-muted-foreground">
                          {formatDate(post.publishDate)}
                        </span>
                        <span className="ml-2 text-sm text-muted-foreground">
                          • {getTranslatedText(post.readTime, locale)}
                        </span>
                      </div>
                      <h2 className="mb-2 text-xl font-bold text-card-foreground">
                        <a
                          href={`/blog/${post.id}`}
                          className="transition-colors hover:text-primary"
                        >
                          {getTranslatedText(post.title, locale)}
                        </a>
                      </h2>
                      <p className="mb-4 text-muted-foreground">
                        {getTranslatedText(post.excerpt, locale)}
                      </p>
                      <div className="flex items-center">
                        <div className="mr-3 flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-muted">
                          {/* <Image
                            src={post.author.avatar}
                            alt={post.author.name}
                            width={40}
                            height={40}
                            className="object-cover"
                          /> */}
                          <span className="text-xs text-muted-foreground">
                            {post.author.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-card-foreground">
                            {post.author.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {post.author.title}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div
        ref={blogGridRef}
        data-section="blogGrid"
        className={`section-reveal container mx-auto px-4 py-12 ${visibleSections.blogGrid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-6xl">
          {/* Search and filters */}
          <div className="animate-fadeIn mb-10">
            <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="relative md:w-72">
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'page.blog.search',
                    defaultMessage: 'Search articles...',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-input bg-background py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-primary"
                />
                <div className="absolute left-3 top-2.5 text-muted-foreground">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
              </div>

              <div className="flex overflow-x-auto pb-1 md:pb-0">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`mr-4 whitespace-nowrap px-4 py-2 font-medium ${
                      selectedCategory === category.id
                        ? 'border-b-2 border-primary text-primary'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {getTranslatedText(category.name, locale)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Blog posts grid */}
          {paginatedPosts.length > 0 ? (
            <div className="mb-12 grid gap-8 md:grid-cols-2">
              {paginatedPosts.map((post, index) => (
                <div
                  key={post.id}
                  className="stagger-card animate-fadeIn overflow-hidden rounded-lg border border-border bg-card shadow-sm transition-shadow hover:shadow-md"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="relative h-48 w-full">
                    <div className="absolute inset-0 flex items-center justify-center bg-muted">
                      <span className="text-muted-foreground">
                        {intl.formatMessage({ id: 'blog.article.image' })}
                      </span>
                    </div>
                    {/* <Image
                      src={post.image}
                      alt={getTranslatedText(post.title, locale)}
                      fill
                      className="object-cover"
                    /> */}
                  </div>
                  <div className="p-6">
                    <div className="mb-2 flex items-center">
                      <span className="rounded-full bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">
                        {getCategoryName(post.category, locale)}
                      </span>
                      <span className="ml-2 text-sm text-muted-foreground">
                        {formatDate(post.publishDate)}
                      </span>
                      <span className="ml-2 text-sm text-muted-foreground">
                        • {getTranslatedText(post.readTime, locale)}
                      </span>
                    </div>
                    <h2 className="mb-2 text-xl font-bold text-card-foreground">
                      <a
                        href={`/blog/${post.id}`}
                        className="transition-colors hover:text-primary"
                      >
                        {getTranslatedText(post.title, locale)}
                      </a>
                    </h2>
                    <p className="mb-4 text-muted-foreground">
                      {getTranslatedText(post.excerpt, locale)}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="mr-2 flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-muted">
                          {/* <Image
                            src={post.author.avatar}
                            alt={post.author.name}
                            width={32}
                            height={32}
                            className="object-cover"
                          /> */}
                          <span className="text-xs text-muted-foreground">
                            {post.author.name.charAt(0)}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-card-foreground">
                          {post.author.name}
                        </span>
                      </div>
                      <a
                        href={`/blog/${post.id}`}
                        className="shimmer-effect inline-flex items-center text-sm font-medium text-primary hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'page.blog.read.more',
                          defaultMessage: 'Read More',
                        })}
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="animate-fadeIn rounded-lg bg-muted py-12 text-center">
              <p className="mb-2 text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.blog.no.results',
                  defaultMessage: 'No articles found matching your criteria.',
                })}
              </p>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.blog.adjust.search',
                  defaultMessage: 'Try adjusting your search query or',
                })}{' '}
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                  }}
                  className="ml-1 text-primary hover:underline"
                >
                  {intl.formatMessage({
                    id: 'page.blog.view.all',
                    defaultMessage: 'view all articles',
                  })}
                </button>
              </p>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="animate-fadeIn animation-delay-300 flex justify-center">
              <nav className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(p => Math.max(p - 1, 1))}
                  disabled={currentPage === 1}
                  className={`rounded-md px-3 py-1 ${
                    currentPage === 1
                      ? 'cursor-not-allowed text-gray-400'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {intl.formatMessage({
                    id: 'page.blog.previous',
                    defaultMessage: 'Previous',
                  })}
                </button>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`rounded-md px-3 py-1 ${
                        currentPage === page
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  )
                )}

                <button
                  onClick={() =>
                    setCurrentPage(p => Math.min(p + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className={`rounded-md px-3 py-1 ${
                    currentPage === totalPages
                      ? 'cursor-not-allowed text-gray-400'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {intl.formatMessage({
                    id: 'page.blog.next',
                    defaultMessage: 'Next',
                  })}
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>

      {/* Newsletter subscription */}
      <div
        ref={newsletterRef}
        data-section="newsletter"
        className={`section-reveal bg-muted py-16 ${visibleSections.newsletter ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="animate-slideUp mb-4 text-3xl font-bold">
              {intl.formatMessage({
                id: 'page.blog.newsletter.title',
                defaultMessage: 'Stay Updated',
              })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mb-8 text-muted-foreground">
              {intl.formatMessage({
                id: 'page.blog.newsletter.description',
                defaultMessage:
                  "Subscribe to our newsletter for the latest articles, resources, and updates. We'll send you valuable content to help your business grow.",
              })}
            </p>
            <div className="animate-fadeIn animation-delay-300 mx-auto flex max-w-md flex-col gap-3 sm:flex-row">
              <input
                type="email"
                placeholder={intl.formatMessage({
                  id: 'page.blog.newsletter.email',
                  defaultMessage: 'Your email address',
                })}
                className="flex-grow rounded-md border border-input bg-background px-4 py-3 focus:border-transparent focus:ring-2 focus:ring-primary"
              />
              <button className="shimmer-effect rounded-md bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-primary/80">
                {intl.formatMessage({
                  id: 'page.blog.newsletter.subscribe',
                  defaultMessage: 'Subscribe',
                })}
              </button>
            </div>
            <p className="animate-fadeIn animation-delay-400 mt-4 text-sm text-muted-foreground">
              {intl.formatMessage({
                id: 'page.blog.newsletter.privacy',
                defaultMessage:
                  'We respect your privacy. Unsubscribe at any time.',
              })}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
