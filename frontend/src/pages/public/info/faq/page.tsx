import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * FAQ Page Component
 *
 * Displays frequently asked questions organized by categories.
 * Includes a search function and expandable/collapsible answer sections.
 * Enhanced with animations for better user experience.
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.faq;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('general');
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: false,
    search: false,
    faqItems: false,
    contact: false,
  });

  // Refs for the sections
  const headerRef = useRef(null);
  const searchRef = useRef(null);
  const faqItemsRef = useRef(null);
  const contactRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (headerRef.current) observer.observe(headerRef.current);
    if (searchRef.current) observer.observe(searchRef.current);
    if (faqItemsRef.current) observer.observe(faqItemsRef.current);
    if (contactRef.current) observer.observe(contactRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Sample FAQ data organized by categories
  const faqCategories = {
    general: {
      name: intl.formatMessage({ id: 'page.faq.categories.general' }),
      items: [
        {
          id: 'what-is',
          question: intl.formatMessage(
            { id: 'page.faq.general.what.question' },
            { company: displayName }
          ),
          answer: intl.formatMessage(
            { id: 'page.faq.general.what.answer' },
            { company: displayName }
          ),
        },
        {
          id: 'how-started',
          question: intl.formatMessage(
            { id: 'page.faq.general.started.question' },
            { company: displayName }
          ),
          answer: intl.formatMessage(
            { id: 'page.faq.general.started.answer' },
            { company: displayName }
          ),
        },
        {
          id: 'different',
          question: intl.formatMessage(
            { id: 'page.faq.general.different.question' },
            { company: displayName }
          ),
          answer: intl.formatMessage(
            { id: 'page.faq.general.different.answer' },
            { company: displayName }
          ),
        },
      ],
    },
    pricing: {
      name: intl.formatMessage({ id: 'page.faq.categories.pricing' }),
      items: [
        {
          id: 'free-trial',
          question: intl.formatMessage({
            id: 'page.faq.pricing.trial.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.pricing.trial.answer' }),
        },
        {
          id: 'subscription-models',
          question: intl.formatMessage({
            id: 'page.faq.pricing.plans.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.pricing.plans.answer' }),
        },
        {
          id: 'cancel-subscription',
          question: intl.formatMessage({
            id: 'page.faq.pricing.cancel.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.pricing.cancel.answer' }),
        },
      ],
    },
    features: {
      name: intl.formatMessage({ id: 'page.faq.categories.features' }),
      items: [
        {
          id: 'mobile-apps',
          question: intl.formatMessage({
            id: 'page.faq.features.mobile.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.features.mobile.answer' }),
        },
        {
          id: 'integrations',
          question: intl.formatMessage({
            id: 'page.faq.features.integrations.question',
          }),
          answer: intl.formatMessage({
            id: 'page.faq.features.integrations.answer',
          }),
        },
        {
          id: 'data-export',
          question: intl.formatMessage({
            id: 'page.faq.features.export.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.features.export.answer' }),
        },
      ],
    },
    security: {
      name: intl.formatMessage({ id: 'page.faq.categories.security' }),
      items: [
        {
          id: 'data-security',
          question: intl.formatMessage({
            id: 'page.faq.security.data.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.security.data.answer' }),
        },
        {
          id: 'gdpr-compliance',
          question: intl.formatMessage({
            id: 'page.faq.security.gdpr.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.security.gdpr.answer' }),
        },
        {
          id: 'data-ownership',
          question: intl.formatMessage({
            id: 'page.faq.security.ownership.question',
          }),
          answer: intl.formatMessage({
            id: 'page.faq.security.ownership.answer',
          }),
        },
      ],
    },
    support: {
      name: intl.formatMessage({ id: 'page.faq.categories.support' }),
      items: [
        {
          id: 'get-support',
          question: intl.formatMessage({ id: 'page.faq.support.how.question' }),
          answer: intl.formatMessage({ id: 'page.faq.support.how.answer' }),
        },
        {
          id: 'training-options',
          question: intl.formatMessage({
            id: 'page.faq.support.training.question',
          }),
          answer: intl.formatMessage({
            id: 'page.faq.support.training.answer',
          }),
        },
        {
          id: 'support-hours',
          question: intl.formatMessage({
            id: 'page.faq.support.hours.question',
          }),
          answer: intl.formatMessage({ id: 'page.faq.support.hours.answer' }),
        },
      ],
    },
  };

  // Toggle FAQ item expansion
  const toggleItem = (id: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Filter FAQ items based on search query
  const filteredFaqs = Object.entries(faqCategories).flatMap(
    ([categoryId, category]) => {
      if (searchQuery) {
        return category.items
          .filter(
            item =>
              item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.answer.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map(item => ({ ...item, category: categoryId }));
      } else if (categoryId === activeCategory) {
        return category.items;
      }
      return [];
    }
  );

  // Categories for the navigation tabs
  const categories = Object.entries(faqCategories).map(([id, category]) => ({
    id,
    name: category.name,
  }));

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-4xl">
        <div
          ref={headerRef}
          data-section="header"
          className={`section-reveal ${visibleSections.header ? 'visible' : ''}`}
        >
          <h1 className="animate-slideUp mb-2 text-3xl font-bold">
            {intl.formatMessage({ id: 'page.faq.title' })}
          </h1>
          <p className="animate-slideUp animation-delay-100 mb-8 text-lg text-gray-600">
            {intl.formatMessage(
              { id: 'page.faq.subtitle' },
              { company: displayName }
            )}
          </p>
        </div>

        {/* Search bar */}
        <div
          ref={searchRef}
          data-section="search"
          className={`section-reveal mb-8 ${visibleSections.search ? 'visible' : ''}`}
        >
          <div className="animate-fadeIn animation-delay-200 relative">
            <input
              type="text"
              placeholder={intl.formatMessage({
                id: 'page.faq.search.placeholder',
              })}
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="w-full rounded-lg border border-input bg-background px-4 py-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <div className="absolute right-3 top-3 text-muted-foreground">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {!searchQuery && (
          <div className="animate-fadeIn animation-delay-300 mb-6 border-b border-border">
            <div className="flex overflow-x-auto pb-1">
              {categories.map((category, index) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`shimmer-effect mr-4 whitespace-nowrap px-4 py-2 font-medium ${
                    activeCategory === category.id
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* FAQ items */}
        <div
          ref={faqItemsRef}
          data-section="faqItems"
          className={`section-reveal space-y-4 ${visibleSections.faqItems ? 'visible' : ''}`}
        >
          {searchQuery && filteredFaqs.length === 0 ? (
            <div className="animate-fadeIn py-10 text-center">
              <p className="text-muted-foreground">
                No results found for "{searchQuery}"
              </p>
              <p className="mt-2 text-muted-foreground">
                Try using different keywords or
                <button
                  onClick={() => setSearchQuery('')}
                  className="shimmer-effect ml-1 text-primary hover:underline"
                >
                  browse all categories
                </button>
              </p>
            </div>
          ) : (
            filteredFaqs.map((item, index) => (
              <div
                key={item.id}
                className="stagger-card animate-fadeIn overflow-hidden rounded-lg border border-border bg-card shadow-sm"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <button
                  onClick={() => toggleItem(item.id)}
                  className="shimmer-effect flex w-full items-center justify-between px-6 py-4 text-left transition-colors hover:bg-accent"
                >
                  <span className="font-medium text-card-foreground">
                    {item.question}
                  </span>
                  <svg
                    className={`h-5 w-5 transform text-muted-foreground ${expandedItems[item.id] ? 'rotate-180' : ''} transition-transform`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                {expandedItems[item.id] && (
                  <div className="animate-fadeIn border-t border-border bg-muted px-6 py-4">
                    <p className="text-muted-foreground">{item.answer}</p>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Contact support section */}
        <div
          ref={contactRef}
          data-section="contact"
          className={`section-reveal mt-12 rounded-lg border border-primary/10 bg-primary/5 p-6 ${visibleSections.contact ? 'visible' : ''}`}
        >
          <h2 className="animate-slideUp mb-3 text-xl font-semibold">
            {intl.formatMessage({ id: 'page.faq.contact.title' })}
          </h2>
          <p className="animate-slideUp animation-delay-100 mb-4 text-muted-foreground">
            {intl.formatMessage({ id: 'page.faq.contact.subtitle' })}
          </p>
          <div className="animate-fadeIn animation-delay-200 flex flex-col gap-4 sm:flex-row">
            <a
              href="#"
              className="shimmer-effect inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 font-medium text-white transition-colors hover:bg-primary/80"
            >
              <svg
                className="mr-2 h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                />
              </svg>
              {intl.formatMessage({ id: 'page.faq.contact.support' })}
            </a>
            <a
              href="#"
              className="shimmer-effect inline-flex items-center justify-center rounded-md border border-primary/20 bg-white px-6 py-2 font-medium text-primary transition-colors hover:bg-gray-50"
            >
              <svg
                className="mr-2 h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
              {intl.formatMessage({ id: 'page.faq.contact.knowledge' })}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
