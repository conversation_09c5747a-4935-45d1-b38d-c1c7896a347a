import React from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { AlertCircle, ArrowLeft } from 'lucide-react';

interface OAuth2ErrorProps {}

export default function OAuth2Error({}: OAuth2ErrorProps) {
  const [searchParams] = useSearchParams();
  
  const error = searchParams.get('error') || 'unknown_error';
  const errorDescription = searchParams.get('error_description') || 'An unknown error occurred during authentication.';
  const redirectUri = searchParams.get('redirect_uri');

  const getErrorTitle = (errorCode: string): string => {
    switch (errorCode) {
      case 'invalid_client':
        return 'Invalid Client';
      case 'invalid_grant':
        return 'Invalid Grant';
      case 'unauthorized_client':
        return 'Unauthorized Client';
      case 'access_denied':
        return 'Access Denied';
      case 'unsupported_response_type':
        return 'Unsupported Response Type';
      case 'invalid_scope':
        return 'Invalid Scope';
      case 'server_error':
        return 'Server Error';
      case 'temporarily_unavailable':
        return 'Service Temporarily Unavailable';
      default:
        return 'Authentication Error';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <AlertCircle className="mx-auto h-16 w-16 text-red-500 dark:text-red-400" />
          <h2 className="mt-6 text-3xl font-extrabold text-foreground">
            GeNieGO
          </h2>
          <h3 className="mt-2 text-xl font-semibold text-red-600 dark:text-red-400">
            {getErrorTitle(error)}
          </h3>
        </div>

        <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error: {error}
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{errorDescription}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col space-y-3">
          {redirectUri ? (
            <a
              href={redirectUri}
              className="flex items-center justify-center w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Application
            </a>
          ) : (
            <Link
              to="/"
              className="flex items-center justify-center w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          )}
          
          <button
            onClick={() => window.history.back()}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Try Again
          </button>
        </div>

        <div className="text-center text-sm text-gray-500">
          If you continue to experience issues, please contact your system administrator.
        </div>
      </div>
    </div>
  );
} 