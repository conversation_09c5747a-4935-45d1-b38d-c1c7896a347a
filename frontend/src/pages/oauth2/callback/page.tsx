import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';

interface OAuth2CallbackProps {}

export default function OAuth2Callback({}: OAuth2CallbackProps) {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing authorization...');

  useEffect(() => {
    const processCallback = () => {
      const code = searchParams.get('code');
      const error = searchParams.get('error');
      const state = searchParams.get('state');

      if (error) {
        setStatus('error');
        setMessage(`Authorization failed: ${error}`);
        return;
      }

      if (code) {
        setStatus('success');
        setMessage('Authorization successful! You may now close this window.');
        
        // If this is a popup callback, send message to parent window
        if (window.opener) {
          window.opener.postMessage({
            type: 'oauth2_callback',
            success: true,
            code,
            state,
          }, '*');
          
          // Close the popup after a short delay
          setTimeout(() => {
            window.close();
          }, 2000);
        }
      } else {
        setStatus('error');
        setMessage('No authorization code received.');
      }
    };

    processCallback();
  }, [searchParams]);

  const getIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="mx-auto h-16 w-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="mx-auto h-16 w-16 text-green-500" />;
      case 'error':
        return <AlertCircle className="mx-auto h-16 w-16 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {getIcon()}
          <h2 className="mt-6 text-3xl font-extrabold text-foreground">
            GeNieGO
          </h2>
          <h3 className={`mt-2 text-xl font-semibold ${getStatusColor()}`}>
            {status === 'processing' && 'Processing...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Error'}
          </h3>
        </div>

        <div className="text-center">
          <p className="text-muted-foreground">{message}</p>
        </div>

        {status === 'success' && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700 dark:text-green-300">
                  You have been successfully authorized. This window will close automatically.
                </p>
              </div>
            </div>
          </div>
        )}

        {status === 'error' && (
          <div className="flex justify-center">
            <button
              onClick={() => window.close()}
              className="px-4 py-2 border border-input rounded-md shadow-sm text-sm font-medium text-foreground bg-background hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              Close Window
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 