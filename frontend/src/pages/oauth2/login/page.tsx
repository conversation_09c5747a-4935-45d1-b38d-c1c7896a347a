import React, { useEffect, useRef, useState, useMemo } from 'react';
import { OAuth2LoginForm } from '../../../components/forms/oauth2-login-form';
import { LanguageSwitcher } from '../../../components/common/language-switcher';
import { useSearchParams } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '../../../constants/site-config';
import { useConfig } from '../../../contexts/config-context';

/**
 * OAuth2 Login Page Component
 *
 * @description GenieMove OAuth2 login page with proper i18n support
 * and clean design without inappropriate system information
 *
 * @returns {JSX.Element} The OAuth2 Login Page component
 */
export default function OAuth2Login() {
  const intl = useIntl();
  const { locale } = useConfig();
  const [searchParams] = useSearchParams();

  // Memoize computed values to prevent unnecessary re-renders
  const productName = useMemo(() => 
    locale === 'zh' 
    ? SITE_INFO.application.displayNameZh
      : SITE_INFO.application.displayName,
    [locale]
  );
  
  const companyName = useMemo(() => 
    locale === 'zh' 
    ? SITE_INFO.organization.displayNameZh 
      : SITE_INFO.organization.displayName,
    [locale]
  );

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    logo: false,
    form: false,
    footer: false,
  });

  // Refs for the sections
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const footerRef = useRef(null);

  // Test translation
  const signInText = intl.formatMessage({ id: 'auth.signIn' });

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (logoRef.current) observer.observe(logoRef.current);
    if (formRef.current) observer.observe(formRef.current);
    if (footerRef.current) observer.observe(footerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div className="relative h-screen w-full overflow-hidden px-4 md:px-6 lg:px-8">
      {/* Language Switcher */}
      <div className="absolute top-2 right-2 z-10">
        <LanguageSwitcher />
      </div>

      {/* Return to Home Link - Top Left */}
      <div className="absolute top-2 left-2 z-10">
        <a 
          href="/" 
          className="text-sm text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-2"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          {intl.formatMessage({ id: 'auth.returnToHome' })}
        </a>
      </div>

      {/* Centered Content Container */}
      <div className="flex h-full items-center justify-center py-8">
        <div className="w-full max-w-md sm:max-w-lg space-y-6">
          {/* Logo Section */}
          <div
            ref={logoRef}
            data-section="logo"
            className={`section-reveal text-center ${visibleSections.logo ? 'visible' : ''}`}
          >
            <div className="animate-fadeIn inline-flex flex-col items-center transition-opacity hover:opacity-80">
              <div className={`text-2xl text-primary ${locale === 'zh' ? 'font-semibold' : 'font-bold'}`}>
                {productName}
              </div>
              <span className="text-sm text-muted-foreground mt-2">
                {locale === 'zh' ? SITE_INFO.application.descriptionZh : SITE_INFO.application.description}
              </span>
            </div>
          </div>

          {/* Form Section */}
          <div
            ref={formRef}
            data-section="form"
            className={`section-reveal ${visibleSections.form ? 'visible' : ''}`}
          >
            <div className="animate-fadeIn animation-delay-200">
              <OAuth2LoginForm showBackButton={false} />
            </div>
          </div>

          {/* Footer Section - Only Powered by */}
          <div
            ref={footerRef}
            data-section="footer"
            className={`section-reveal text-center mt-8 ${visibleSections.footer ? 'visible' : ''}`}
          >
            <div className="shimmer-effect animate-fadeIn animation-delay-400 text-xs text-muted-foreground px-2">
              {intl.formatMessage({ id: 'auth.poweredBy' })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 