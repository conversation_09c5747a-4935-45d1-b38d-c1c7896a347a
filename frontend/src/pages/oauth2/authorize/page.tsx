import React, { useEffect, useRef, useState, useMemo } from 'react';
import { OAuth2AuthorizeForm } from '../../../components/forms/oauth2-authorize-form';
import { LanguageSwitcher } from '../../../components/common/language-switcher';
import { useSearchParams } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '../../../constants/site-config';
import { useConfig } from '../../../contexts/config-context';

/**
 * OAuth2 Authorize Page Component
 *
 * @description GeNieGO SSO Server authorization page for OAuth2 consent
 * with proper i18n support and clean design
 *
 * @returns {JSX.Element} The OAuth2 Authorize Page component
 */
export default function OAuth2Authorize() {
  const intl = useIntl();
  const { locale } = useConfig();
  const [searchParams] = useSearchParams();

  // Memoize computed values to prevent unnecessary re-renders
  const productName = useMemo(() => 
    locale === 'zh' 
    ? SITE_INFO.application.displayNameZh
      : SITE_INFO.application.displayName,
    [locale]
  );

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    logo: false,
    form: false,
    footer: false,
  });

  // Refs for the sections
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const footerRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (logoRef.current) observer.observe(logoRef.current);
    if (formRef.current) observer.observe(formRef.current);
    if (footerRef.current) observer.observe(footerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center px-4 md:px-6 lg:px-8">
      {/* Language Switcher */}
      <div className="absolute top-6 right-6">
        <LanguageSwitcher />
      </div>

      {/* Logo Section */}
      <div
        ref={logoRef}
        data-section="logo"
        className={`section-reveal mb-8 w-full max-w-md text-center ${visibleSections.logo ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn inline-flex flex-col items-center transition-opacity hover:opacity-80">
          <div className="text-2xl font-bold text-primary">
            {productName}
          </div>
          <span className="text-sm text-muted-foreground mt-2">
            {intl.formatMessage({ id: 'auth.authorizationRequest' })}
          </span>
        </div>
      </div>

      {/* Form Section */}
      <div
        ref={formRef}
        data-section="form"
        className={`section-reveal w-full max-w-md ${visibleSections.form ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn animation-delay-200">
          <OAuth2AuthorizeForm />
        </div>
      </div>

      {/* Footer Section */}
      <div
        ref={footerRef}
        data-section="footer"
        className={`section-reveal mt-4 w-full max-w-md text-center ${visibleSections.footer ? 'visible' : ''}`}
      >
        <div className="shimmer-effect animate-fadeIn animation-delay-300 text-xs text-muted-foreground px-2">
          {intl.formatMessage({ id: 'auth.poweredBy' })}
        </div>
      </div>
    </div>
  );
}
