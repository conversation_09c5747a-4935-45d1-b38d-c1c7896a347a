import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO, SSO_STATS, PAGE_METADATA } from '../constants/site-config';
import { updatePageMetadata } from '@/utils';
import { CardSwap, Card } from '@/components';
import { heroCards } from '@/data/public/hero-cards';
import { getHeroCardIcon } from '@/components/common/hero-card-icons';
import '@/styles/animations.css';

const LandingPage: React.FC = () => {
  const intl = useIntl();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.home;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;

  // State to track which sections are visible for animations
  const [visibleSections, setVisibleSections] = useState({
    features: false,
    statistics: false,
  });

  // Refs for the sections
  const featuresRef = useRef<HTMLDivElement>(null);
  const statisticsRef = useRef<HTMLDivElement>(null);



  // Update document metadata using the metadata helper
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionName = entry.target.getAttribute('data-section');
          if (sectionName) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionName]: true,
            }));
          }
        }
      });
    }, observerOptions);

    if (featuresRef.current) {
      observer.observe(featuresRef.current);
    }
    if (statisticsRef.current) {
      observer.observe(statisticsRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-20 md:py-32">
          <div className="flex flex-col sm:flex-row gap-8 sm:gap-12 lg:gap-16 items-center justify-between min-h-[500px]">
            {/* Text Content */}
            <div className="flex-1 max-w-2xl text-center sm:text-left w-3/5 max-[768px]:w-3/5">
              <div className="mb-8 flex justify-center sm:justify-start animate-fadeIn">
                <img
                  src={SITE_INFO.organization.logo.main}
                  alt={SITE_INFO.organization.displayName}
                  className="h-16 w-auto md:h-20"
                />
              </div>
              <h1 className="mb-6 text-4xl font-bold md:text-6xl animate-slideUp">
                {intl.formatMessage({ id: 'page.landing.title' }, { appName: SITE_INFO.application.displayName })}
              </h1>
              <p className="mb-8 text-xl opacity-90 md:text-2xl animate-slideUp animation-delay-200">
                {intl.formatMessage({ id: 'page.landing.subtitle' }, { company: displayName })}
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row animate-slideUp animation-delay-300">
                <Link
                  to="/login"
                  className="shimmer-effect rounded-md bg-white dark:bg-card px-8 py-3 font-medium text-primary transition-colors hover:bg-gray-100 dark:hover:bg-accent text-center"
                >
                  {intl.formatMessage({ id: 'page.landing.cta.signIn' })}
                </Link>
                <Link
                  to="/login"
                  className="shimmer-effect rounded-md bg-primary/90 px-8 py-3 font-medium text-white transition-colors hover:bg-primary/70 text-center"
                >
                  {intl.formatMessage({ id: 'page.landing.cta.getStarted' })}
                </Link>
              </div>
            </div>

            {/* Card Swap Component */}
            <div className="hidden sm:flex flex-shrink-0 justify-center animate-slideUp animation-delay-400 w-2/5 max-[768px]:w-2/5">
              {/* CardSwap Container - Contains animations within fixed bounds */}
              <div className="relative w-[350px] sm:w-[400px] md:w-[450px] lg:w-[600px] h-[500px] overflow-hidden rounded-xl">
                <CardSwap
                  cardDistance={60}
                  verticalDistance={70}
                  delay={5000}
                  pauseOnHover={false}
                  easing="elastic"
                >
                {heroCards.map((card) => (
                  <Card key={card.id} className="bg-card border border-border shadow-2xl">
                    <div className="w-full h-full flex flex-col">
                      {/* macOS Window Title Bar */}
                      <div className="bg-muted border-b border-border px-4 py-3 flex items-center justify-between rounded-t-xl">
                        <div className="flex items-center space-x-2">
                          {/* Traffic Lights */}
                          <div className="w-3 h-3 rounded-full bg-red-500"></div>
                          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          {card.id}
                        </div>
                        <div className="w-14"></div> {/* Spacer for balance */}
                      </div>

                      {/* Window Content */}
                      <div className="flex-1 p-6 flex flex-col">
                        {/* Header Section */}
                        <div className="flex items-center space-x-4 mb-6">
                          <div 
                            className="w-12 h-12 rounded-xl flex items-center justify-center text-white shadow-sm"
                            style={{ backgroundColor: card.color }}
                          >
                            <div className="w-4 h-4">
                              {getHeroCardIcon(card.iconType, "w-4 h-4")}
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="text-sm font-bold text-card-foreground leading-tight">
                              {intl.formatMessage({ id: card.titleId })}
                            </h3>
                            <div className="text-xs text-muted-foreground mt-1">
                              Enterprise Solution
                            </div>
                          </div>
                        </div>

                        {/* Main Content */}
                        <div className="flex-1 mb-6">
                          <p className="text-card-foreground leading-relaxed text-sm">
                            {intl.formatMessage({ id: card.descriptionId })}
                          </p>
                        </div>

                        {/* Status Bar */}
                        <div className="bg-muted rounded-lg px-4 py-3 border border-border">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div
                                className="w-2 h-2 rounded-full bg-green-500"
                                title="Active"
                              ></div>
                              <span className="text-xs text-muted-foreground font-medium">
                                System Active
                              </span>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Enterprise Ready
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </CardSwap>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div
        className={`section-reveal container mx-auto px-4 py-16 md:py-24 ${visibleSections.features ? 'visible' : ''}`}
        ref={featuresRef}
        data-section="features"
      >
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="animate-fadeIn mb-4 text-3xl font-bold md:text-4xl">
            {intl.formatMessage({ id: 'page.landing.features.title' })}
          </h2>
          <p className="animate-slideUp animation-delay-200 text-xl text-muted-foreground">
            {intl.formatMessage({ id: 'page.landing.features.subtitle' })}
          </p>
        </div>

        <div className="mx-auto grid max-w-5xl gap-8 md:grid-cols-3">
          <div className="animate-slideUp animation-delay-300 stagger-card rounded-xl border border-border bg-card p-8 text-center shadow-sm card-hover-effect">
            <div className="mb-4 inline-flex items-center justify-center text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
            </div>
            <h3 className="mb-3 text-xl font-bold">
              {intl.formatMessage({ id: 'page.landing.feature1.title' })}
            </h3>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'page.landing.feature1.description' })}
            </p>
          </div>

          <div className="animate-slideUp animation-delay-400 stagger-card rounded-xl border border-border bg-card p-8 text-center shadow-sm card-hover-effect">
            <div className="mb-4 inline-flex items-center justify-center text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
                />
              </svg>
            </div>
            <h3 className="mb-3 text-xl font-bold">
              {intl.formatMessage({ id: 'page.landing.feature2.title' })}
            </h3>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'page.landing.feature2.description' })}
            </p>
          </div>

          <div className="animate-slideUp animation-delay-500 stagger-card rounded-xl border border-border bg-card p-8 text-center shadow-sm card-hover-effect">
            <div className="mb-4 inline-flex items-center justify-center text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </div>
            <h3 className="mb-3 text-xl font-bold">
              {intl.formatMessage({ id: 'page.landing.feature3.title' })}
            </h3>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'page.landing.feature3.description' })}
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div
        className={`section-reveal bg-muted py-16 md:py-24 ${visibleSections.statistics ? 'visible' : ''}`}
        ref={statisticsRef}
        data-section="statistics"
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto grid max-w-5xl grid-cols-2 gap-8 text-center md:grid-cols-4">
            <div className="animate-slideUp animation-delay-100 stagger-card rounded-xl bg-card p-6 card-hover-effect">
              <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
                {SSO_STATS.uptime}
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.landing.stats.uptime' })}
              </div>
            </div>
            <div className="animate-slideUp animation-delay-200 stagger-card rounded-xl bg-card p-6 card-hover-effect">
              <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
                {SSO_STATS.responseTime}
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.landing.stats.responseTime' })}
              </div>
            </div>
            <div className="animate-slideUp animation-delay-300 stagger-card rounded-xl bg-card p-6 card-hover-effect">
              <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
                {SSO_STATS.encryption}
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.landing.stats.encryption' })}
              </div>
            </div>
            <div className="animate-slideUp animation-delay-400 stagger-card rounded-xl bg-card p-6 card-hover-effect">
              <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
                {SSO_STATS.support}
              </div>
              <div className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.landing.stats.support' })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 py-16 text-white">
        <div className="container mx-auto px-4">
          <div className="animate-fadeIn mx-auto max-w-3xl text-center">
            <h2 className="animate-slideUp mb-4 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({ id: 'page.landing.cta.title' })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'page.landing.cta.subtitle' },
                { company: displayName }
              )}
            </p>
            <div className="animate-slideUp animation-delay-400 flex flex-col justify-center gap-4 sm:flex-row">
              <Link
                to="/login"
                className="shimmer-effect rounded-md bg-primary px-8 py-3 font-medium text-primary-foreground transition-colors hover:bg-primary/90"
              >
                {intl.formatMessage({ id: 'page.landing.cta.trial' })}
              </Link>
              <Link
                to="/contact"
                className="shimmer-effect rounded-md bg-white/10 px-8 py-3 font-medium text-white transition-colors hover:bg-white/20"
              >
                {intl.formatMessage({ id: 'page.landing.cta.contact' })}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
