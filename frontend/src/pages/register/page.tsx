import React, { useEffect, useRef, useState, useMemo } from 'react';
import { RegisterForm } from '../../components/forms/register-form';
import { LanguageSwitcher } from '../../components/common/language-switcher';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '../../constants/site-config';
import { useConfig } from '../../contexts/config-context';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

/**
 * Register Page Component
 *
 * @description GeNieGO SSO Server registration page with proper i18n support
 * and clean design for user account creation
 *
 * @returns {JSX.Element} The Register Page component
 */
export default function Register() {
  const intl = useIntl();
  const { locale } = useConfig();

  // Memoize computed values to prevent unnecessary re-renders
  const productName = useMemo(() => 
    locale === 'zh' 
    ? SITE_INFO.application.displayNameZh
      : SITE_INFO.application.displayName,
    [locale]
  );
  
  const companyName = useMemo(() => 
    locale === 'zh' 
    ? SITE_INFO.organization.displayNameZh 
      : SITE_INFO.organization.displayName,
    [locale]
  );

  // Intersection Observer for animations
  const [visibleSections, setVisibleSections] = useState({
    logo: false,
    form: false,
    footer: false,
  });

  const logoRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  const footerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const section = entry.target.getAttribute('data-section');
            if (section) {
              setVisibleSections(prev => ({
                ...prev,
                [section]: true
              }));
            }
          }
        });
      },
      { threshold: 0.1 }
    );

    const refs = [logoRef, formRef, footerRef];
    refs.forEach(ref => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      refs.forEach(ref => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, []);

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center px-4 md:px-6 lg:px-8">
      {/* Return to Home Button */}
      <div className="absolute top-6 left-6">
        <Link
          to="/"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          {intl.formatMessage({ id: 'common.returnToHome' })}
        </Link>
      </div>

      {/* Language Switcher */}
      <div className="absolute top-6 right-6">
        <LanguageSwitcher />
      </div>

      {/* Logo Section */}
      <div
        ref={logoRef}
        data-section="logo"
        className={`section-reveal mb-8 w-full max-w-md text-center ${visibleSections.logo ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn inline-flex flex-col items-center transition-opacity hover:opacity-80">
          <div className="text-2xl font-bold text-primary">
            {productName}
          </div>
          <span className="text-sm text-muted-foreground mt-2">
            {intl.formatMessage({ id: 'auth.createAccount' })}
          </span>
        </div>
      </div>

      {/* Form Section */}
      <div
        ref={formRef}
        data-section="form"
        className={`section-reveal w-full max-w-md sm:max-w-lg ${visibleSections.form ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn animation-delay-200">
          <RegisterForm />
        </div>
      </div>

      {/* Footer Section */}
      <div
        ref={footerRef}
        data-section="footer"
        className={`section-reveal mt-4 w-full max-w-md text-center ${visibleSections.footer ? 'visible' : ''}`}
      >
        <div className="shimmer-effect animate-fadeIn animation-delay-300 text-xs text-muted-foreground px-2">
          {intl.formatMessage({ id: 'auth.poweredBy' })} {companyName}
        </div>
      </div>
    </div>
  );
}
