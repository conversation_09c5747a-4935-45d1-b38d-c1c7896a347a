import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { Link } from 'react-router-dom';
import { useEffect } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';

/**
 * 404 Not Found Page
 *
 * Displays when a user tries to access a page that doesn't exist.
 * Provides helpful navigation options to guide users back to valid pages.
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.notFound;
  const companyName = SITE_INFO.organization.name;

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background px-4">
      <div className="w-full max-w-md text-center">
        <h1 className="mb-4 text-9xl font-bold text-primary">404</h1>
        <h2 className="mb-2 text-3xl font-semibold text-foreground">
          {intl.formatMessage({ id: 'error.404.title' })}
        </h2>
        <p className="mb-8 text-muted-foreground">
          {intl.formatMessage({ id: 'error.404.message' })}
        </p>

        <div className="mb-8">
          <p className="mb-4 text-muted-foreground">
            {intl.formatMessage({ id: 'error.404.check.title' })}
          </p>
          <ul className="mb-8 list-disc px-8 text-left text-muted-foreground">
            <li className="mb-2">
              {intl.formatMessage({ id: 'error.404.check.url' })}
            </li>
            <li className="mb-2">
              {intl.formatMessage({ id: 'error.404.check.moved' })}
            </li>
            <li className="mb-2">
              {intl.formatMessage({ id: 'error.404.check.permissions' })}
            </li>
          </ul>
        </div>

        <div className="space-y-4">
          <Link
            to="/"
            className="block w-full rounded-md bg-primary px-4 py-3 font-medium text-primary-foreground hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {intl.formatMessage({ id: 'error.404.return.home' })}
          </Link>

          <Link
            to="/contact"
            className="block w-full rounded-md border border-primary bg-background px-4 py-3 font-medium text-primary hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {intl.formatMessage({ id: 'error.404.contact.support' })}
          </Link>
        </div>

        <div className="mt-8 text-sm text-muted-foreground">
          <p>
            {companyName} - {new Date().getFullYear()}
          </p>
        </div>
      </div>
    </div>
  );
}
