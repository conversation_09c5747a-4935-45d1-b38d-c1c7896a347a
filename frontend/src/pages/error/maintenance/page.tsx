import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';

/**
 * Maintenance Mode Page
 *
 * Displays when the website is undergoing maintenance.
 * Shows a countdown timer and contact information.
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.maintenance;
  const companyName = SITE_INFO.organization.name;
  const supportEmail = SITE_INFO.contact.email.support;
  const social = SITE_INFO.contact.social;

  // Set document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  // Set estimated completion time - 2 hours from now by default
  const [countdown, setCountdown] = useState({
    hours: 2,
    minutes: 0,
    seconds: 0,
  });

  // Countdown timer
  useEffect(() => {
    const endTime = new Date();
    endTime.setHours(endTime.getHours() + 2); // 2 hours from now

    const timer = setInterval(() => {
      const now = new Date();
      const difference = endTime.getTime() - now.getTime();

      if (difference <= 0) {
        clearInterval(timer);
        setCountdown({ hours: 0, minutes: 0, seconds: 0 });
        return;
      }

      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setCountdown({ hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-12">
      <div className="mx-auto w-full max-w-xl rounded-xl bg-card p-8 text-center shadow-sm">
        <div className="mb-6 inline-flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
          <svg
            className="h-10 w-10 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        </div>

        <h1 className="mb-2 text-3xl font-bold">
          {intl.formatMessage({ id: 'maintenance.title' })}
        </h1>
        <p className="mb-6 text-muted-foreground">
          {intl.formatMessage({ id: 'maintenance.message' })}
        </p>

        <div className="mb-8">
          <h2 className="mb-3 text-lg font-medium">
            {intl.formatMessage({ id: 'maintenance.expected.back' })}
          </h2>
          <div className="grid grid-cols-3 gap-4">
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-3xl font-bold text-primary">
                {countdown.hours.toString().padStart(2, '0')}
              </div>
              <div className="text-xs uppercase text-gray-500">
                {intl.formatMessage({ id: 'maintenance.time.hours' })}
              </div>
            </div>
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-3xl font-bold text-primary">
                {countdown.minutes.toString().padStart(2, '0')}
              </div>
              <div className="text-xs uppercase text-gray-500">
                {intl.formatMessage({ id: 'maintenance.time.minutes' })}
              </div>
            </div>
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-3xl font-bold text-primary">
                {countdown.seconds.toString().padStart(2, '0')}
              </div>
              <div className="text-xs uppercase text-gray-500">
                {intl.formatMessage({ id: 'maintenance.time.seconds' })}
              </div>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="mb-3 text-xl font-semibold">
            {intl.formatMessage({ id: 'maintenance.why.title' })}
          </h2>
          <p className="text-gray-600">
            {intl.formatMessage({ id: 'maintenance.why.message' })}
          </p>
        </div>

        <div className="mb-8">
          <h2 className="mb-3 text-xl font-semibold">
            {intl.formatMessage({ id: 'maintenance.assistance.title' })}
          </h2>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'maintenance.assistance.message' })}{' '}
            <a
              href={`mailto:${supportEmail}`}
              className="text-primary hover:underline"
            >
              {supportEmail}
            </a>
          </p>
        </div>

        {/* Social Media Links */}
        <div className="mb-8">
          <h2 className="mb-3 text-xl font-semibold">
            {intl.formatMessage({ id: 'maintenance.updates.title' })}
          </h2>
          <p className="mb-4 text-muted-foreground">
            {intl.formatMessage({ id: 'maintenance.updates.message' })}
          </p>
          <div className="flex justify-center space-x-4">
            {social.twitter && (
              <a
                href={social.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary"
              >
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
            )}

            {social.facebook && (
              <a
                href={social.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-700 hover:text-primary"
              >
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </a>
            )}
          </div>
        </div>

        <div className="border-t border-gray-200 pt-6">
          <p className="text-sm text-gray-500">
            &copy; {new Date().getFullYear()} {companyName}.{' '}
            {intl.formatMessage({ id: 'maintenance.rights.reserved' })}
          </p>
        </div>
      </div>
    </div>
  );
}
