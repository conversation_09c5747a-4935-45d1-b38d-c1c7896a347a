import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { Link } from 'react-router-dom';
import { useEffect } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';

/**
 * 500 Server Error Page
 *
 * Displays when there's a server-side error.
 * Provides information about the error and support contact options.
 */
export default function Page() {
  const intl = useIntl();
  const companyName = SITE_INFO.organization.name;
  const supportEmail = SITE_INFO.contact.email.support;
  const metadata = PAGE_METADATA.serverError;

  // Update document metadata
  useEffect(() => {
    updatePageMetadata(metadata);
  }, [metadata]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background px-4">
      <div className="w-full max-w-md text-center">
        <h1 className="mb-4 text-9xl font-bold text-primary">500</h1>
        <h2 className="mb-2 text-3xl font-semibold text-foreground">
          {intl.formatMessage({ id: 'error.500.title' })}
        </h2>
        <p className="mb-8 text-muted-foreground">
          {intl.formatMessage({ id: 'error.500.technical.difficulties' })}
        </p>

        <div className="mb-8">
          <div className="rounded-lg bg-primary/10 p-4 text-sm text-primary-foreground/90">
            <p className="mb-2 font-medium">
              {intl.formatMessage({ id: 'error.500.technical.difficulties' })}
            </p>
            <p>
              {intl.formatMessage({ id: 'error.500.notification.message' })}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <Link
            to="/"
            className="block w-full rounded-md bg-primary px-4 py-3 font-medium text-primary-foreground hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            {intl.formatMessage({ id: 'error.500.return.home' })}
          </Link>

          <div className="text-muted-foreground">
            <p className="mb-2">
              {intl.formatMessage({ id: 'error.500.need.assistance' })}
            </p>
            <a
              href={`mailto:${supportEmail}`}
              className="text-primary hover:underline"
            >
              {intl.formatMessage({ id: 'error.500.contact.support' })}
            </a>
          </div>
        </div>

        <div className="mt-8 text-sm text-muted-foreground">
          <p>
            {companyName} - {new Date().getFullYear()}
          </p>
        </div>
      </div>
    </div>
  );
}
