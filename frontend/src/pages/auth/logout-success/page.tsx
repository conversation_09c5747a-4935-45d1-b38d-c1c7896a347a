import React, { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  CheckCircle, 
  AlertTriangle, 
  Globe, 
  Monitor, 
  Home, 
  LogIn,
  RefreshCw 
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * LogoutSuccessPage Component
 * 
 * Displays logout status information including:
 * - Success/partial success status
 * - Number of applications notified
 * - Failed notifications (if any)
 * - Options to return to login or home page
 */
const LogoutSuccessPage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [countdown, setCountdown] = useState(10);

  // Parse URL parameters
  const status = searchParams.get('status') || 'success';
  const appsNotified = parseInt(searchParams.get('apps_notified') || '0', 10);
  const failedApps = parseInt(searchParams.get('failed_apps') || '0', 10);

  const isSuccess = status === 'success';
  const isPartial = status === 'partial';

  // Auto-redirect countdown
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          navigate('/login', { replace: true });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate]);

  const handleGoToLogin = () => {
    navigate('/login', { replace: true });
  };

  const handleGoToHome = () => {
    navigate('/', { replace: true });
  };

  const getStatusIcon = () => {
    if (isSuccess) {
      return <CheckCircle className="h-12 w-12 text-green-600" />;
    }
    return <AlertTriangle className="h-12 w-12 text-amber-600" />;
  };

  const getStatusTitle = () => {
    if (isSuccess) {
      return intl.formatMessage(
        { id: 'auth.logout.success.title' },
        { defaultMessage: 'Successfully Logged Out' }
      );
    }
    return intl.formatMessage(
      { id: 'auth.logout.partial.title' },
      { defaultMessage: 'Logout Completed with Warnings' }
    );
  };

  const getStatusDescription = () => {
    if (isSuccess) {
      return intl.formatMessage(
        { id: 'auth.logout.success.description' },
        { 
          defaultMessage: 'You have been successfully logged out from all applications and devices.',
        }
      );
    }
    return intl.formatMessage(
      { id: 'auth.logout.partial.description' },
      { 
        defaultMessage: 'You have been logged out, but some applications may not have been notified.',
      }
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Main Status Card */}
        <Card>
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            <CardTitle className="text-xl">
              {getStatusTitle()}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-muted-foreground">
              {getStatusDescription()}
            </p>

            {/* Logout Statistics */}
            {(appsNotified > 0 || failedApps > 0) && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">
                    {intl.formatMessage(
                      { id: 'auth.logout.statistics.title' },
                      { defaultMessage: 'Logout Summary' }
                    )}
                  </h4>
                  
                  <div className="space-y-2">
                    {appsNotified > 0 && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-green-600" />
                          <span className="text-sm">
                            {intl.formatMessage(
                              { id: 'auth.logout.apps.notified' },
                              { defaultMessage: 'Applications notified' }
                            )}
                          </span>
                        </div>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {appsNotified}
                        </Badge>
                      </div>
                    )}

                    {failedApps > 0 && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-amber-600" />
                          <span className="text-sm">
                            {intl.formatMessage(
                              { id: 'auth.logout.apps.failed' },
                              { defaultMessage: 'Failed notifications' }
                            )}
                          </span>
                        </div>
                        <Badge variant="secondary" className="bg-amber-100 text-amber-800">
                          {failedApps}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Security Notice */}
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950">
              <div className="flex items-start gap-2">
                <Monitor className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200">
                    {intl.formatMessage(
                      { id: 'auth.logout.security.notice.title' },
                      { defaultMessage: 'Security Notice' }
                    )}
                  </p>
                  <p className="text-blue-700 dark:text-blue-300 mt-1">
                    {intl.formatMessage(
                      { id: 'auth.logout.security.notice.description' },
                      { 
                        defaultMessage: 'For security, close all browser windows and clear your browser cache if using a shared computer.',
                      }
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Auto-redirect notice */}
            <div className="text-center text-sm text-muted-foreground">
              {intl.formatMessage(
                { 
                  id: 'auth.logout.auto.redirect',
                  defaultMessage: 'Redirecting to login in {seconds} seconds...',
                },
                { seconds: countdown }
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <Button 
            onClick={handleGoToLogin}
            className="w-full"
          >
            <LogIn className="h-4 w-4 mr-2" />
            {intl.formatMessage(
              { id: 'auth.logout.go.to.login' },
              { defaultMessage: 'Go to Login' }
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleGoToHome}
            className="w-full"
          >
            <Home className="h-4 w-4 mr-2" />
            {intl.formatMessage(
              { id: 'auth.logout.go.to.home' },
              { defaultMessage: 'Go to Home' }
            )}
          </Button>
        </div>

        {/* Additional Help */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            {intl.formatMessage(
              { id: 'auth.logout.help.text' },
              { 
                defaultMessage: 'Having trouble? Contact support or try logging in again.',
              }
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default LogoutSuccessPage;
