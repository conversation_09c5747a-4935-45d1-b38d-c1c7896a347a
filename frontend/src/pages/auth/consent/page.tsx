import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { Checkbox } from '@/components/ui/shadcn/checkbox';
import { 
  Shield, 
  User, 
  Mail, 
  Globe, 
  Database,
  Eye,
  Settings,
  AlertTriangle,
  Check,
  X,
  ArrowLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScopeInfo {
  scope: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  required: boolean;
}

/**
 * ConsentScreen Component
 * 
 * GDPR-compliant consent screen that displays:
 * - Application information and permissions requested
 * - Clear scope descriptions with icons
 * - User consent options with granular control
 * - Privacy information and data usage details
 */
const ConsentScreen: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedScopes, setSelectedScopes] = useState<Set<string>>(new Set());

  // Extract OAuth2 parameters from URL
  const clientId = searchParams.get('client_id') || '';
  const redirectUri = searchParams.get('redirect_uri') || '';
  const responseType = searchParams.get('response_type') || '';
  const scope = searchParams.get('scope') || '';
  const state = searchParams.get('state') || '';
  const codeChallenge = searchParams.get('code_challenge') || '';
  const codeChallengeMethod = searchParams.get('code_challenge_method') || '';
  const applicationName = searchParams.get('application_name') || 'Unknown Application';
  const requestedScopes = searchParams.get('requested_scopes')?.split(',') || [];

  // Initialize selected scopes with all requested scopes
  useEffect(() => {
    setSelectedScopes(new Set(requestedScopes));
  }, [requestedScopes.join(',')]);

  // Scope information mapping
  const getScopeInfo = (scope: string): ScopeInfo => {
    const scopeMap: Record<string, ScopeInfo> = {
      'openid': {
        scope: 'openid',
        icon: <Shield className="h-4 w-4" />,
        title: intl.formatMessage({ id: 'user.consent.scope.openid.title', defaultMessage: 'Basic Authentication' }),
        description: intl.formatMessage({ id: 'user.consent.scope.openid.description', defaultMessage: 'Allow this application to verify your identity' }),
        required: true,
      },
      'profile': {
        scope: 'profile',
        icon: <User className="h-4 w-4" />,
        title: intl.formatMessage({ id: 'user.consent.scope.profile.title', defaultMessage: 'Profile Information' }),
        description: intl.formatMessage({ id: 'user.consent.scope.profile.description', defaultMessage: 'Access your basic profile information (name, username)' }),
        required: false,
      },
      'email': {
        scope: 'email',
        icon: <Mail className="h-4 w-4" />,
        title: intl.formatMessage({ id: 'user.consent.scope.email.title', defaultMessage: 'Email Address' }),
        description: intl.formatMessage({ id: 'user.consent.scope.email.description', defaultMessage: 'Access your email address' }),
        required: false,
      },
      'read': {
        scope: 'read',
        icon: <Eye className="h-4 w-4" />,
        title: intl.formatMessage({ id: 'user.consent.scope.read.title', defaultMessage: 'Read Access' }),
        description: intl.formatMessage({ id: 'user.consent.scope.read.description', defaultMessage: 'Read your data and settings' }),
        required: false,
      },
      'write': {
        scope: 'write',
        icon: <Settings className="h-4 w-4" />,
        title: intl.formatMessage({ id: 'user.consent.scope.write.title', defaultMessage: 'Write Access' }),
        description: intl.formatMessage({ id: 'user.consent.scope.write.description', defaultMessage: 'Modify your data and settings' }),
        required: false,
      },
    };

    return scopeMap[scope] || {
      scope,
      icon: <Globe className="h-4 w-4" />,
      title: scope.charAt(0).toUpperCase() + scope.slice(1),
      description: intl.formatMessage({ 
        id: 'user.consent.scope.custom.description', 
        defaultMessage: 'Custom application permission' 
      }),
      required: false,
    };
  };

  const handleScopeToggle = (scope: string) => {
    const scopeInfo = getScopeInfo(scope);
    if (scopeInfo.required) return; // Can't toggle required scopes

    const newSelectedScopes = new Set(selectedScopes);
    if (newSelectedScopes.has(scope)) {
      newSelectedScopes.delete(scope);
    } else {
      newSelectedScopes.add(scope);
    }
    setSelectedScopes(newSelectedScopes);
  };

  const handleGrantConsent = async () => {
    setIsLoading(true);
    try {
      // Grant consent via API
      const response = await fetch('/api/v1/consent/grant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          application_id: clientId, // Using client_id as application_id
          scopes: Array.from(selectedScopes),
          expires_days: 365,
        }),
      });

      if (response.ok) {
        // Consent granted successfully - redirect back to OAuth2 flow
        const oauthParams = new URLSearchParams({
          client_id: clientId,
          redirect_uri: redirectUri,
          response_type: responseType,
          scope: Array.from(selectedScopes).join(' '),
          state: state,
          ...(codeChallenge && { code_challenge: codeChallenge }),
          ...(codeChallengeMethod && { code_challenge_method: codeChallengeMethod }),
        });
        
        window.location.href = `/api/v1/oauth2/authorize?${oauthParams.toString()}`;
      } else {
        // Handle consent error
        console.error('Failed to grant consent');
        // Could show error message to user
      }
    } catch (error) {
      console.error('Consent error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDenyConsent = () => {
    // Redirect back to application with access_denied error
    const errorParams = new URLSearchParams({
      error: 'access_denied',
      error_description: 'User denied consent',
      ...(state && { state }),
    });
    
    window.location.href = `${redirectUri}?${errorParams.toString()}`;
  };

  const handleGoBack = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground">
            {intl.formatMessage({ id: 'user.consent.title', defaultMessage: 'Grant Permissions' })}
          </h1>
          <p className="text-muted-foreground mt-2">
            {intl.formatMessage(
              { 
                id: 'user.consent.subtitle',
                defaultMessage: '{applicationName} is requesting access to your account'
              },
              { applicationName }
            )}
          </p>
        </div>

        {/* Application Info Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Shield className="h-5 w-5 text-primary" />
              {applicationName}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Requested Permissions */}
            <div>
              <h3 className="font-medium text-sm text-foreground mb-3">
                {intl.formatMessage({ 
                  id: 'user.consent.permissions.title', 
                  defaultMessage: 'Requested Permissions' 
                })}
              </h3>
              <div className="space-y-2">
                {requestedScopes.map((scope) => {
                  const scopeInfo = getScopeInfo(scope);
                  const isSelected = selectedScopes.has(scope);
                  
                  return (
                    <div
                      key={scope}
                      className={cn(
                        "flex items-start gap-3 p-3 rounded-lg border transition-colors",
                        isSelected 
                          ? "border-primary/20 bg-primary/5" 
                          : "border-border bg-background",
                        !scopeInfo.required && "cursor-pointer hover:border-primary/30"
                      )}
                      onClick={() => handleScopeToggle(scope)}
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <div className="text-primary">
                          {scopeInfo.icon}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {scopeInfo.title}
                            </span>
                            {scopeInfo.required && (
                              <Badge variant="secondary" className="text-xs">
                                {intl.formatMessage({ 
                                  id: 'user.consent.required', 
                                  defaultMessage: 'Required' 
                                })}
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {scopeInfo.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {scopeInfo.required ? (
                          <Check className="h-4 w-4 text-primary" />
                        ) : (
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleScopeToggle(scope)}
                          />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <Separator />

            {/* Privacy Notice */}
            <div className="flex items-start gap-3 p-3 rounded-lg bg-amber-50 border border-amber-200 dark:bg-amber-950 dark:border-amber-800">
              <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-amber-800 dark:text-amber-200">
                  {intl.formatMessage({ 
                    id: 'user.consent.privacy.title', 
                    defaultMessage: 'Privacy Information' 
                  })}
                </p>
                <p className="text-amber-700 dark:text-amber-300 mt-1">
                  {intl.formatMessage({ 
                    id: 'user.consent.privacy.description', 
                    defaultMessage: 'You can revoke these permissions at any time from your account settings.' 
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <Button 
            onClick={handleGrantConsent}
            disabled={isLoading || selectedScopes.size === 0}
            className="w-full"
          >
            {isLoading ? (
              intl.formatMessage({ id: 'user.consent.granting', defaultMessage: 'Granting...' })
            ) : (
              intl.formatMessage({ id: 'user.consent.grant', defaultMessage: 'Grant Permissions' })
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleDenyConsent}
            disabled={isLoading}
            className="w-full"
          >
            <X className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.consent.deny', defaultMessage: 'Deny Access' })}
          </Button>

          <Button 
            variant="ghost" 
            onClick={handleGoBack}
            disabled={isLoading}
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.consent.back', defaultMessage: 'Back to Dashboard' })}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConsentScreen;
