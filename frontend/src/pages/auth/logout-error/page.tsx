import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { 
  XCircle, 
  RefreshCw, 
  Home, 
  LogIn, 
  AlertTriangle,
  HelpCircle,
  Loader2
} from 'lucide-react';

/**
 * LogoutErrorPage Component
 * 
 * Displays when logout encounters an error, providing:
 * - Error information
 * - Retry logout option
 * - Manual session cleanup instructions
 * - Navigation options
 */
const LogoutErrorPage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetryLogout = async () => {
    setIsRetrying(true);
    
    try {
      // Attempt logout again
      const response = await fetch('/api/v1/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        // Clear local storage and redirect to success page
        localStorage.removeItem('session_token');
        navigate('/auth/logout-success?status=success', { replace: true });
      } else {
        // Still failed, but clear local data anyway
        localStorage.removeItem('session_token');
        navigate('/login', { replace: true });
      }
    } catch (error) {
      console.error('Retry logout error:', error);
      // Clear local data and redirect to login
      localStorage.removeItem('session_token');
      navigate('/login', { replace: true });
    } finally {
      setIsRetrying(false);
    }
  };

  const handleForceLogout = () => {
    // Clear all local authentication data
    localStorage.removeItem('session_token');
    
    // Clear session cookies by setting them to expire
    document.cookie = 'geniengo_session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    
    // Redirect to login
    navigate('/login', { replace: true });
  };

  const handleGoToLogin = () => {
    navigate('/login', { replace: true });
  };

  const handleGoToHome = () => {
    navigate('/', { replace: true });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Main Error Card */}
        <Card>
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <XCircle className="h-12 w-12 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-600">
              {intl.formatMessage(
                { id: 'auth.logout.error.title' },
                { defaultMessage: 'Logout Error' }
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-muted-foreground">
              {intl.formatMessage(
                { id: 'auth.logout.error.description' },
                { 
                  defaultMessage: 'There was a problem logging you out. Your session may still be active on some applications.',
                }
              )}
            </p>

            {/* Error Details */}
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {intl.formatMessage(
                  { id: 'auth.logout.error.details' },
                  { 
                    defaultMessage: 'The logout process encountered an error. This may be due to network issues or server problems.',
                  }
                )}
              </AlertDescription>
            </Alert>

            {/* Security Recommendations */}
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950">
              <div className="flex items-start gap-2">
                <HelpCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-amber-800 dark:text-amber-200">
                    {intl.formatMessage(
                      { id: 'auth.logout.error.security.title' },
                      { defaultMessage: 'Security Recommendations' }
                    )}
                  </p>
                  <ul className="text-amber-700 dark:text-amber-300 mt-1 space-y-1 list-disc list-inside">
                    <li>
                      {intl.formatMessage(
                        { id: 'auth.logout.error.security.close.browser' },
                        { defaultMessage: 'Close all browser windows' }
                      )}
                    </li>
                    <li>
                      {intl.formatMessage(
                        { id: 'auth.logout.error.security.clear.cache' },
                        { defaultMessage: 'Clear browser cache and cookies' }
                      )}
                    </li>
                    <li>
                      {intl.formatMessage(
                        { id: 'auth.logout.error.security.manual.logout' },
                        { defaultMessage: 'Manually log out from connected applications' }
                      )}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <Button 
            onClick={handleRetryLogout}
            disabled={isRetrying}
            className="w-full"
          >
            {isRetrying ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {intl.formatMessage(
                  { id: 'auth.logout.error.retrying' },
                  { defaultMessage: 'Retrying...' }
                )}
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                {intl.formatMessage(
                  { id: 'auth.logout.error.retry' },
                  { defaultMessage: 'Retry Logout' }
                )}
              </>
            )}
          </Button>
          
          <Button 
            variant="destructive" 
            onClick={handleForceLogout}
            className="w-full"
          >
            <LogIn className="h-4 w-4 mr-2" />
            {intl.formatMessage(
              { id: 'auth.logout.error.force.logout' },
              { defaultMessage: 'Force Logout (Clear Local Session)' }
            )}
          </Button>

          <Button 
            variant="outline" 
            onClick={handleGoToHome}
            className="w-full"
          >
            <Home className="h-4 w-4 mr-2" />
            {intl.formatMessage(
              { id: 'auth.logout.error.go.to.home' },
              { defaultMessage: 'Go to Home' }
            )}
          </Button>
        </div>

        {/* Help Information */}
        <div className="text-center space-y-2">
          <p className="text-xs text-muted-foreground">
            {intl.formatMessage(
              { id: 'auth.logout.error.help.text' },
              { 
                defaultMessage: 'If you continue to experience issues, please contact support.',
              }
            )}
          </p>
          
          <div className="text-xs text-muted-foreground">
            {intl.formatMessage(
              { id: 'auth.logout.error.troubleshooting' },
              { 
                defaultMessage: 'Troubleshooting: Try clearing your browser cache or using an incognito window.',
              }
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoutErrorPage;
