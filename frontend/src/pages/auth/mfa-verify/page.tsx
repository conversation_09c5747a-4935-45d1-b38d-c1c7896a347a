import React from 'react';
import { useIntl } from 'react-intl';
import { useSearchParams } from 'react-router-dom';

// Import MFA verification component
import { MFAVerification } from '@/components/features/auth/mfa-verification';

/**
 * MFA Verification Page
 * 
 * Handles multi-factor authentication verification during login flow
 */
const MFAVerifyPage: React.FC = () => {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  
  // Get session token from URL params (passed from login)
  const sessionToken = searchParams.get('session') || '';
  const redirectUri = searchParams.get('redirect_uri') || '/dashboard';

  const handleVerificationSuccess = () => {
    // Redirect will be handled by the MFAVerification component
    console.log('MFA verification successful');
  };

  const handleVerificationError = (error: string) => {
    console.error('MFA verification failed:', error);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="w-full max-w-md">
        <MFAVerification
          sessionToken={sessionToken}
          onSuccess={handleVerificationSuccess}
          onCancel={() => window.location.href = '/login'}
        />
      </div>
    </div>
  );
};

export default MFAVerifyPage;
