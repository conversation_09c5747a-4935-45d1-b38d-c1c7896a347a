import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

// UI Components
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Skeleton } from '@/components/ui/shadcn/skeleton';

// Icons
import { Building2, Plus, Users, Globe, Calendar, Crown, Shield, User, AlertTriangle, ArrowLeft } from 'lucide-react';

// Services
import { organizationApi, Organization } from '@/services/organization-api';

export default function OrganizationsPage() {
  const intl = useIntl();
  const navigate = useNavigate();
  
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadOrganizations();
  }, []);

  const loadOrganizations = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await organizationApi.getUserOrganizations();
      setOrganizations(data);
    } catch (err: any) {
      console.error('Failed to load organizations:', err);
      setError(intl.formatMessage({ id: 'developer.organizations.common.error' }));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrganization = () => {
    navigate('/developer/organizations/new');
  };

  const handleViewOrganization = (organizationId: string) => {
    navigate(`/developer/organizations/${organizationId}`);
  };

  if (loading) {
    return <OrganizationsPageSkeleton />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {intl.formatMessage({ id: 'developer.organizations.title' })}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {intl.formatMessage({ id: 'developer.organizations.subtitle' })}
          </p>
        </div>
        
        <Button
          onClick={handleCreateOrganization}
          className="btn-animate shimmer-effect"
          size="lg"
        >
          <Plus className="w-4 h-4 mr-2" />
          {intl.formatMessage({ id: 'developer.organizations.empty.action' })}
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 dark:border-red-800">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <Button
                variant="outline"
                onClick={loadOrganizations}
                className="mt-4 btn-animate scale-effect"
                size="default"
              >
                {intl.formatMessage({ id: 'developer.organizations.common.retry' })}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Organizations List */}
      {!error && (
        <>
          {organizations.length === 0 ? (
            <Card className="shadow-sm border-gray-200 dark:border-gray-700">
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Building2 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {intl.formatMessage({ id: 'developer.organizations.empty.title' })}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    {intl.formatMessage({ id: 'developer.organizations.empty.description' })}
                  </p>
                  <Button
                    onClick={handleCreateOrganization}
                    className="btn-animate shimmer-effect"
                    size="lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {intl.formatMessage({ id: 'developer.organizations.empty.action' })}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {organizations.map((organization) => (
                <Card 
                  key={organization.id} 
                  className="shadow-sm border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleViewOrganization(organization.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                          <Building2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <CardTitle className="text-lg text-gray-900 dark:text-white">
                            {organization.name}
                          </CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            @{organization.slug}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex flex-col items-end space-y-2">
                        {/* Status Badge */}
                        <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                          organization.is_public
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-200'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                        }`}>
                          <Globe className="h-3 w-3" />
                          {intl.formatMessage({
                            id: organization.is_public ? 'developer.organizations.status.public' : 'developer.organizations.status.private'
                          })}
                        </span>

                        {/* Role Badge */}
                        <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                          organization.user_role === 'owner'
                            ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-200'
                            : organization.user_role === 'admin'
                            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-200'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                        }`}>
                          {organization.user_role === 'owner' ? (
                            <Crown className="h-3 w-3" />
                          ) : organization.user_role === 'admin' ? (
                            <Shield className="h-3 w-3" />
                          ) : (
                            <User className="h-3 w-3" />
                          )}
                          {intl.formatMessage({
                            id: `developer.organizations.role.${organization.user_role || 'member'}`
                          })}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {organization.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                        {organization.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>
                          {(() => {
                            const count = organization.member_count || 0;
                            if (count === 0) {
                              return intl.formatMessage({ id: 'developer.organizations.members.zero' });
                            } else if (count === 1) {
                              return intl.formatMessage({ id: 'developer.organizations.members.single' });
                            } else {
                              return intl.formatMessage({
                                id: 'developer.organizations.members.count'
                              }, { count });
                            }
                          })()}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(organization.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {organization.website && (
                      <div className="flex items-center space-x-1 text-sm">
                        <Globe className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <a
                          href={organization.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:underline truncate"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {organization.website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Loading skeleton component
const OrganizationsPageSkeleton: React.FC = () => {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Skeleton className="h-10 w-40" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <div>
                    <Skeleton className="h-5 w-32 mb-1" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
                <div className="space-y-1">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-12" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
