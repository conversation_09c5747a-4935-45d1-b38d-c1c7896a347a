/**
 * Developer API Documentation
 *
 * Interactive API documentation page with code examples, authentication guides,
 * and testing tools following developer portal standards.
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  BookOpen,
  Key,
  Play,
  ExternalLink,
  Shield,
  Globe,
  Zap,
  ChevronRight,
  ChevronDown,
  Settings
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Badge } from '@/components/ui/shadcn/badge';
import { CopyButton } from '@/components/ui/animate-ui/button/copy';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { useToast } from '@/hooks/use-toast';
import { getApiCategories, getApiEndpoints, generateCodeExample, type APIEndpoint } from '@/data/developer-api-endpoints';

const DeveloperAPIDocsPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('javascript');
  const [expandedEndpoint, setExpandedEndpoint] = useState<string | null>(null);

  // API Categories
  const categories = getApiCategories().map(cat => ({
    ...cat,
    name: intl.formatMessage({ id: cat.nameKey })
  }));

  // API endpoints from data file
  const apiEndpoints: APIEndpoint[] = getApiEndpoints();

  // Use code example generator from data file
  const getCodeExample = (endpoint: APIEndpoint, language: string): string => {
    return generateCodeExample(endpoint, language, intl);
  };



  const filteredEndpoints = apiEndpoints.filter(endpoint => {
    const matchesSearch = endpoint.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         endpoint.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         endpoint.path.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || endpoint.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'POST': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'PUT': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'DELETE': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'PATCH': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BookOpen className="h-8 w-8" />
            {intl.formatMessage({ id: 'developer.apiDocs.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'developer.apiDocs.description' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link to="/developer/settings">
              <Settings className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.apiDocs.apiSettings' })}
            </Link>
          </Button>
          <Button asChild>
            <a href="/api/v1/docs" target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.apiDocs.openApiSpec' })}
            </a>
          </Button>
        </div>
      </div>

      {/* Quick Start Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            {intl.formatMessage({ id: 'developer.apiDocs.quickStartGuide' })}
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'developer.apiDocs.quickStartDesc' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <Key className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'developer.apiDocs.getApiKeys' })}</h4>
              <p className="text-sm text-muted-foreground mb-3">
                {intl.formatMessage({ id: 'developer.apiDocs.getApiKeysDesc' })}
              </p>
              <Button size="sm" variant="outline" asChild>
                <Link to="/developer/register">{intl.formatMessage({ id: 'developer.apiDocs.registerApp' })}</Link>
              </Button>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <Shield className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'developer.apiDocs.authenticate' })}</h4>
              <p className="text-sm text-muted-foreground mb-3">
                {intl.formatMessage({ id: 'developer.apiDocs.authenticateDesc' })}
              </p>
              <Button size="sm" variant="outline">
                <a href="#auth-login">{intl.formatMessage({ id: 'developer.apiDocs.viewExamples' })}</a>
              </Button>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <Globe className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'developer.apiDocs.makeRequests' })}</h4>
              <p className="text-sm text-muted-foreground mb-3">
                {intl.formatMessage({ id: 'developer.apiDocs.makeRequestsDesc' })}
              </p>
              <Button size="sm" variant="outline">
                <a href="#user-profile">{intl.formatMessage({ id: 'developer.apiDocs.tryApi' })}</a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Explorer */}
      <div className="grid gap-6 lg:grid-cols-4">
        {/* Sidebar - Categories */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">API Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left p-2 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
                      : 'hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{category.name}</span>
                    <Badge variant="outline">{category.count}</Badge>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Main Content - API Endpoints */}
        <div className="lg:col-span-3 space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Input
                    placeholder={intl.formatMessage({ id: 'developer.apiDocs.searchPlaceholder' })}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="javascript">JavaScript</SelectItem>
                    <SelectItem value="python">Python</SelectItem>
                    <SelectItem value="curl">cURL</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* API Endpoints */}
          <div className="space-y-4">
            {filteredEndpoints.map((endpoint) => (
              <Card key={endpoint.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge className={getMethodColor(endpoint.method)}>
                        {endpoint.method}
                      </Badge>
                      <code className="text-sm font-mono bg-muted text-muted-foreground px-2 py-1 rounded">
                        {endpoint.path}
                      </code>
                      {endpoint.requiresAuth && (
                        <Badge variant="outline">
                          <Shield className="h-3 w-3 mr-1" />
                          {intl.formatMessage({ id: 'developer.apiDocs.authRequired' })}
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setExpandedEndpoint(
                        expandedEndpoint === endpoint.id ? null : endpoint.id
                      )}
                    >
                      {expandedEndpoint === endpoint.id ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div>
                    <CardTitle className="text-lg">{endpoint.title}</CardTitle>
                    <CardDescription>{endpoint.description}</CardDescription>
                  </div>
                </CardHeader>

                {expandedEndpoint === endpoint.id && (
                  <CardContent className="space-y-6">
                    {/* Parameters */}
                    {endpoint.parameters && (
                      <div>
                        <h4 className="font-medium mb-3">Parameters</h4>
                        <div className="space-y-2">
                          {endpoint.parameters.map((param) => (
                            <div key={param.name} className="border rounded-lg p-3">
                              <div className="flex items-center gap-2 mb-1">
                                <code className="text-sm font-mono">{param.name}</code>
                                <Badge variant={param.required ? 'destructive' : 'outline'}>
                                  {param.required ? 'Required' : 'Optional'}
                                </Badge>
                                <Badge variant="outline">{param.type}</Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{param.description}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Request Body */}
                    {endpoint.requestBody && (
                      <div>
                        <h4 className="font-medium mb-3">Request Body</h4>
                        <div className="bg-muted rounded-lg p-4">
                          <pre className="text-sm overflow-x-auto">
                            <code>{endpoint.requestBody.example}</code>
                          </pre>
                        </div>
                      </div>
                    )}

                    {/* Code Example */}
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{intl.formatMessage({ id: 'developer.apiDocs.codeExample' })} ({selectedLanguage})</h4>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex items-center gap-2"
                          onClick={() => {
                            navigator.clipboard.writeText(getCodeExample(endpoint, selectedLanguage));
                            toast({
                              title: intl.formatMessage({ id: 'developer.apiDocs.toast.copied.title' }),
                              description: intl.formatMessage({ id: 'developer.apiDocs.toast.copied.description' }),
                            });
                          }}
                        >
                          <CopyButton
                            content={getCodeExample(endpoint, selectedLanguage)}
                            variant="ghost"
                            size="sm"
                            className="p-0 h-4 w-4"
                            onCopy={() => {}}
                          />
                          {intl.formatMessage({ id: 'developer.apiDocs.copy' })}
                        </Button>
                      </div>
                      <div className="bg-gray-900 text-gray-100 rounded-lg p-4">
                        <pre className="text-sm overflow-x-auto">
                          <code>{getCodeExample(endpoint, selectedLanguage)}</code>
                        </pre>
                      </div>
                    </div>

                    {/* Responses */}
                    <div>
                      <h4 className="font-medium mb-3">{intl.formatMessage({ id: 'developer.apiDocs.responses' })}</h4>
                      <div className="space-y-3">
                        {endpoint.responses.map((response, index) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={response.status === 200 ? 'default' : 'destructive'}>
                                {response.status}
                              </Badge>
                              <span className="text-sm font-medium">{response.description}</span>
                            </div>
                            <div className="bg-muted rounded p-3">
                              <pre className="text-sm overflow-x-auto">
                                <code>{response.example}</code>
                              </pre>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Try It Button */}
                    <div className="pt-4 border-t">
                      <Button>
                        <Play className="h-4 w-4 mr-2" />
                        {intl.formatMessage({ id: 'developer.apiDocs.tryThisEndpoint' })}
                      </Button>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeveloperAPIDocsPage;
