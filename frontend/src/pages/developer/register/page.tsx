import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Plus,
  Trash2,
  Eye,
  EyeOff,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info,
  Download
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Checkbox } from '@/components/ui/animate-ui/radix/checkbox';
import { CopyButton } from '@/components/ui/animate-ui/button/copy';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/shadcn/dialog';
import { useToast } from '@/hooks/use-toast';
import { DeveloperApiService } from '@/services/developer-api';
import { termsOfServiceContent, privacyPolicyContent } from '@/data/public/policies';
import MarkdownRender from '@/components/common/markdown-renderer-lazy';
import { SITE_INFO } from '@/constants/site-config';
import useConfig from '@/hooks/use-config';

/**
 * Application Registration Portal
 * 
 * Self-service application registration for developers to integrate with GeNieGO SSO.
 * Includes form validation, client credential generation, and terms acceptance.
 */
const ApplicationRegister: React.FC = () => {
  const navigate = useNavigate();
  const intl = useIntl();
  const { toast } = useToast();
  const { locale } = useConfig();
  const [isLoading, setIsLoading] = useState(false);
  const [showClientSecret, setShowClientSecret] = useState(false);
  const [showCredentialsDialog, setShowCredentialsDialog] = useState(false);
  const [showTermsDialog, setShowTermsDialog] = useState(false);
  const [showPrivacyDialog, setShowPrivacyDialog] = useState(false);
  const [generatedCredentials, setGeneratedCredentials] = useState<{
    clientId: string;
    clientSecret: string;
  } | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    applicationName: '',
    description: '',
    redirectUris: [''],
    scopes: ['openid', 'profile'],
    termsAccepted: false,
    privacyAccepted: false
  });

  // Available scopes
  const availableScopes = [
    { id: 'openid', name: intl.formatMessage({ id: 'developer.register.scope.openid.name' }), description: intl.formatMessage({ id: 'developer.register.scope.openid.description' }), required: true },
    { id: 'profile', name: intl.formatMessage({ id: 'developer.register.scope.profile.name' }), description: intl.formatMessage({ id: 'developer.register.scope.profile.description' }), required: true },
    { id: 'email', name: intl.formatMessage({ id: 'developer.register.scope.email.name' }), description: intl.formatMessage({ id: 'developer.register.scope.email.description' }), required: false },
    { id: 'phone', name: intl.formatMessage({ id: 'developer.register.scope.phone.name' }), description: intl.formatMessage({ id: 'developer.register.scope.phone.description' }), required: false },
    { id: 'address', name: intl.formatMessage({ id: 'developer.register.scope.address.name' }), description: intl.formatMessage({ id: 'developer.register.scope.address.description' }), required: false }
  ];

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Application name validation
    if (!formData.applicationName.trim()) {
      newErrors.applicationName = intl.formatMessage({ id: 'developer.register.error.applicationNameRequired' });
    } else if (formData.applicationName.length < 3) {
      newErrors.applicationName = intl.formatMessage({ id: 'developer.register.error.applicationNameTooShort' });
    } else if (!/^[a-zA-Z0-9\s-_]+$/.test(formData.applicationName)) {
      newErrors.applicationName = intl.formatMessage({ id: 'developer.register.error.applicationNameInvalid' });
    }

    // Description validation
    if (!formData.description.trim()) {
      newErrors.description = intl.formatMessage({ id: 'developer.register.error.descriptionRequired' });
    } else if (formData.description.length < 10) {
      newErrors.description = intl.formatMessage({ id: 'developer.register.error.descriptionTooShort' });
    }

    // Redirect URIs validation
    const validUris = formData.redirectUris.filter(uri => uri.trim());
    if (validUris.length === 0) {
      newErrors.redirectUris = intl.formatMessage({ id: 'developer.register.error.redirectUriRequired' });
    } else {
      const invalidUris = validUris.filter(uri => {
        try {
          const url = new URL(uri);
          return !['http:', 'https:'].includes(url.protocol);
        } catch {
          return true;
        }
      });
      if (invalidUris.length > 0) {
        newErrors.redirectUris = intl.formatMessage({ id: 'developer.register.error.redirectUriInvalid' });
      }
    }

    // Terms validation
    if (!formData.termsAccepted) {
      newErrors.termsAccepted = intl.formatMessage({ id: 'developer.register.error.termsRequired' });
    }
    if (!formData.privacyAccepted) {
      newErrors.privacyAccepted = intl.formatMessage({ id: 'developer.register.error.privacyRequired' });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const generateCredentials = () => {
    // Generate client ID (format: application-name-random)
    const cleanName = formData.applicationName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const clientId = `${cleanName}-${randomSuffix}`;

    // Generate client secret (32 character random string)
    const clientSecret = Array.from(crypto.getRandomValues(new Uint8Array(24)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    setGeneratedCredentials({ clientId, clientSecret });
  };

  const downloadCredentialsCSV = () => {
    if (!generatedCredentials) return;

    const csvContent = [
      'Field,Value',
      `Application Name,${formData.applicationName}`,
      `Client ID,${generatedCredentials.clientId}`,
      `Client Secret,${generatedCredentials.clientSecret}`,
      `Created Date,${new Date().toISOString()}`,
      `Redirect URIs,"${formData.redirectUris.filter(uri => uri.trim()).join(', ')}"`,
      `Scopes,"${formData.scopes.join(', ')}"`,
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${formData.applicationName}-credentials.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleRegisterApplication = () => {
    setShowCredentialsDialog(false);
    registerApplication();
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleRedirectUriChange = (index: number, value: string) => {
    const newUris = [...formData.redirectUris];
    newUris[index] = value;
    setFormData(prev => ({ ...prev, redirectUris: newUris }));
    if (errors.redirectUris) {
      setErrors(prev => ({ ...prev, redirectUris: '' }));
    }
  };

  const addRedirectUri = () => {
    setFormData(prev => ({ 
      ...prev, 
      redirectUris: [...prev.redirectUris, ''] 
    }));
  };

  const removeRedirectUri = (index: number) => {
    if (formData.redirectUris.length > 1) {
      const newUris = formData.redirectUris.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, redirectUris: newUris }));
    }
  };

  const handleScopeChange = (scopeId: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({ 
        ...prev, 
        scopes: [...prev.scopes, scopeId] 
      }));
    } else {
      // Don't allow removing required scopes
      const scope = availableScopes.find(s => s.id === scopeId);
      if (!scope?.required) {
        setFormData(prev => ({ 
          ...prev, 
          scopes: prev.scopes.filter(s => s !== scopeId) 
        }));
      }
    }
  };

  const handleAgreeToTerms = () => {
    setFormData(prev => ({ ...prev, termsAccepted: true }));
    setShowTermsDialog(false);
    // Clear error if it exists
    if (errors.termsAccepted) {
      setErrors(prev => ({ ...prev, termsAccepted: '' }));
    }
  };

  const handleAgreeToPrivacy = () => {
    setFormData(prev => ({ ...prev, privacyAccepted: true }));
    setShowPrivacyDialog(false);
    // Clear error if it exists
    if (errors.privacyAccepted) {
      setErrors(prev => ({ ...prev, privacyAccepted: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Generate credentials and show dialog
    generateCredentials();
    setShowCredentialsDialog(true);
  };

  const registerApplication = async () => {
    setIsLoading(true);

    try {
      // Use proper API service to register application
      const registrationData = {
        application_name: formData.applicationName,
        description: formData.description,
        allowed_redirect_uris: formData.redirectUris.filter(uri => uri.trim()),
        allowed_scopes: formData.scopes,
      };

      const response = await DeveloperApiService.createApplication(registrationData);

      toast({
        title: intl.formatMessage({ id: 'developer.register.toast.success.title' }),
        description: intl.formatMessage({ id: 'developer.register.toast.success.description' }, { name: formData.applicationName }),
      });

      // Navigate to applications page
      navigate('/developer/applications', {
        state: {
          message: 'Application registered successfully!',
          applicationName: formData.applicationName
        }
      });

    } catch (error) {
      console.error('Registration failed:', error);
      toast({
        title: intl.formatMessage({ id: 'developer.register.toast.error.title' }),
        description: intl.formatMessage({ id: 'developer.register.toast.error.description' }),
        variant: "destructive",
      });
      setErrors({ submit: 'Failed to register application. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'developer.register.title' })}</h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'developer.register.subtitle' })}
        </p>
      </div>

      {/* Registration Form */}
      <div className="w-full">
        <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <h2 className="text-lg font-semibold mb-4">{intl.formatMessage({ id: 'developer.register.basicInfo' })}</h2>
          
          <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="applicationName" className="text-sm font-medium">
                {intl.formatMessage({ id: 'developer.register.applicationName' })} *
              </label>
              <input
                id="applicationName"
                type="text"
                value={formData.applicationName}
                onChange={(e) => handleInputChange('applicationName', e.target.value)}
                className={`w-full rounded-md border px-3 py-2 text-sm ${
                  errors.applicationName ? 'border-red-500' : 'border-input'
                } bg-background`}
                placeholder={intl.formatMessage({ id: 'developer.register.applicationNamePlaceholder' })}
              />
              {errors.applicationName && (
                <p className="text-sm text-red-500">{errors.applicationName}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                {intl.formatMessage({ id: 'developer.register.description' })} *
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className={`w-full rounded-md border px-3 py-2 text-sm ${
                  errors.description ? 'border-red-500' : 'border-input'
                } bg-background`}
                placeholder={intl.formatMessage({ id: 'developer.register.descriptionPlaceholder' })}
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>
          </div>
        </div>

        {/* Redirect URIs */}
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <h2 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'developer.register.redirectUris' })}</h2>
          <p className="text-sm text-muted-foreground mb-4">
            {intl.formatMessage({ id: 'developer.register.redirectUrisDescription' })}
          </p>
          
          <div className="space-y-3">
            {formData.redirectUris.map((uri, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="url"
                  value={uri}
                  onChange={(e) => handleRedirectUriChange(index, e.target.value)}
                  className={`flex-1 rounded-md border px-3 py-2 text-sm ${
                    errors.redirectUris ? 'border-red-500' : 'border-input'
                  } bg-background`}
                  placeholder={intl.formatMessage({ id: 'developer.register.redirectUriPlaceholder' })}
                />
                {formData.redirectUris.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeRedirectUri(index)}
                    className="rounded-md border border-red-200 p-2 text-red-600 hover:bg-red-50 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
            
            <button
              type="button"
              onClick={addRedirectUri}
              className="inline-flex items-center gap-2 text-sm text-primary hover:underline"
            >
              <Plus className="h-4 w-4" />
              {intl.formatMessage({ id: 'developer.register.addRedirectUri' })}
            </button>
            
            {errors.redirectUris && (
              <p className="text-sm text-red-500">{errors.redirectUris}</p>
            )}
          </div>
        </div>

        {/* Scopes Selection */}
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <h2 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'developer.register.permissions' })}</h2>
          <p className="text-sm text-muted-foreground mb-4">
            {intl.formatMessage({ id: 'developer.register.permissionsDescription' })}
          </p>

          <div className="grid gap-3 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {availableScopes.map((scope) => (
              <div key={scope.id} className="flex items-start gap-3 p-3 rounded-lg border">
                <Checkbox
                  id={scope.id}
                  checked={formData.scopes.includes(scope.id)}
                  onCheckedChange={(checked) => handleScopeChange(scope.id, !!checked)}
                  disabled={scope.required}
                  className="mt-1 shrink-0"
                />
                <div className="flex-1">
                  <label htmlFor={scope.id} className="text-sm font-medium cursor-pointer">
                    {scope.name}
                    {scope.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  <p className="text-xs text-muted-foreground">{scope.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>



        {/* Terms and Conditions */}
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <h2 className="text-lg font-semibold mb-4">{intl.formatMessage({ id: 'developer.register.termsAndConditions' })}</h2>

          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <Checkbox
                id="termsAccepted"
                checked={formData.termsAccepted}
                onCheckedChange={(checked) => handleInputChange('termsAccepted', !!checked)}
                className="mt-1 shrink-0"
              />
              <div>
                <label htmlFor="termsAccepted" className="text-sm cursor-pointer">
                  {intl.formatMessage({ id: 'developer.register.agreeTo' })}
                  <button
                    type="button"
                    onClick={() => setShowTermsDialog(true)}
                    className="text-primary underline hover:no-underline ml-1"
                  >
                    {intl.formatMessage({ id: 'developer.register.termsOfService' })}
                  </button>
                  *
                </label>
                {errors.termsAccepted && (
                  <p className="text-sm text-red-500 mt-1">{errors.termsAccepted}</p>
                )}
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Checkbox
                id="privacyAccepted"
                checked={formData.privacyAccepted}
                onCheckedChange={(checked) => handleInputChange('privacyAccepted', !!checked)}
                className="mt-1 shrink-0"
              />
              <div>
                <label htmlFor="privacyAccepted" className="text-sm cursor-pointer">
                  {intl.formatMessage({ id: 'developer.register.agreeTo' })}
                  <button
                    type="button"
                    onClick={() => setShowPrivacyDialog(true)}
                    className="text-primary underline hover:no-underline ml-1"
                  >
                    {intl.formatMessage({ id: 'developer.register.privacyPolicy' })}
                  </button>
                  *
                </label>
                {errors.privacyAccepted && (
                  <p className="text-sm text-red-500 mt-1">{errors.privacyAccepted}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Link
            to="/developer/dashboard"
            className="rounded-md border border-input px-6 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            {intl.formatMessage({ id: 'developer.register.cancel' })}
          </Link>

          <button
            type="submit"
            disabled={isLoading}
            className="inline-flex items-center gap-2 rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 disabled:opacity-50 transition-colors"
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                {intl.formatMessage({ id: 'developer.register.registering' })}
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                {intl.formatMessage({ id: 'developer.register.registerApplication' })}
              </>
            )}
          </button>
        </div>

        {errors.submit && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4 mt-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-sm text-red-700">{errors.submit}</p>
            </div>
          </div>
        )}
        </form>

        {/* Integration Guide */}
        <div className="rounded-lg border bg-blue-50 dark:bg-blue-950/20 p-6 mt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">{intl.formatMessage({ id: 'developer.register.nextSteps' })}</h3>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                {intl.formatMessage({ id: 'developer.register.nextStepsText' })}
                {' '}
                <Link to="/developer/docs" className="underline hover:no-underline">
                  {intl.formatMessage({ id: 'developer.register.integrationDocs' })}
                </Link>
                {' '}
              </p>
            </div>
          </div>
        </div>

        {/* Client Credentials Dialog */}
        <Dialog open={showCredentialsDialog} onOpenChange={setShowCredentialsDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{intl.formatMessage({ id: 'developer.register.clientCredentials' })}</DialogTitle>
              <DialogDescription>
                {intl.formatMessage({ id: 'developer.register.credentialsDialogDescription' })}
              </DialogDescription>
            </DialogHeader>

            {generatedCredentials && (
              <div className="space-y-4">
                {/* Security Notice */}
                <div className="rounded-lg bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800/30 p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-amber-800 dark:text-amber-100">{intl.formatMessage({ id: 'developer.register.securityNotice' })}</h3>
                      <p className="text-sm text-amber-700 dark:text-amber-200 mt-1">
                        {intl.formatMessage({ id: 'developer.register.securityNoticeText' })}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Client ID */}
                <div>
                  <label className="text-sm font-medium">{intl.formatMessage({ id: 'developer.register.clientId' })}</label>
                  <div className="flex gap-2 mt-1">
                    <input
                      type="text"
                      value={generatedCredentials.clientId}
                      readOnly
                      className="flex-1 rounded-md border border-input bg-muted px-3 py-2 text-sm"
                    />
                    <CopyButton
                      variant="outline"
                      size="sm"
                      content={generatedCredentials.clientId}
                    />
                  </div>
                </div>

                {/* Client Secret */}
                <div>
                  <label className="text-sm font-medium">{intl.formatMessage({ id: 'developer.register.clientSecret' })}</label>
                  <div className="flex gap-2 mt-1">
                    <input
                      type={showClientSecret ? "text" : "password"}
                      value={generatedCredentials.clientSecret}
                      readOnly
                      className="flex-1 rounded-md border border-input bg-muted px-3 py-2 text-sm"
                    />
                    <button
                      type="button"
                      onClick={() => setShowClientSecret(!showClientSecret)}
                      className="h-9 w-9 rounded-md border border-input flex items-center justify-center hover:bg-accent transition-colors"
                    >
                      {showClientSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    <CopyButton
                      variant="outline"
                      size="sm"
                      content={generatedCredentials.clientSecret}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <DialogFooter className="pt-4">
                  <Button
                    variant="outline"
                    onClick={downloadCredentialsCSV}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    {intl.formatMessage({ id: 'developer.register.downloadCSV' })}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCredentialsDialog(false)}
                  >
                    {intl.formatMessage({ id: 'developer.register.close' })}
                  </Button>
                  <Button onClick={handleRegisterApplication}>
                    {intl.formatMessage({ id: 'developer.register.registerApplication' })}
                  </Button>
                </DialogFooter>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Terms of Service Dialog */}
        <Dialog open={showTermsDialog} onOpenChange={setShowTermsDialog}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {termsOfServiceContent.titleTranslations?.[locale] || termsOfServiceContent.title}
              </DialogTitle>
              <DialogDescription>
                Last updated: {SITE_INFO.legal.termsLastUpdated}
              </DialogDescription>
            </DialogHeader>
            <div className="prose prose-sm dark:prose-invert max-w-none">
              <MarkdownRender
                content={termsOfServiceContent.content[locale] || termsOfServiceContent.content.en}
                variables={{
                  companyName: SITE_INFO.organization.name,
                  email: SITE_INFO.contact.email.general,
                }}
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowTermsDialog(false)}>
                {intl.formatMessage({ id: 'developer.register.close' })}
              </Button>
              <Button onClick={handleAgreeToTerms}>
                {intl.formatMessage({ id: 'developer.register.agree' })}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Privacy Policy Dialog */}
        <Dialog open={showPrivacyDialog} onOpenChange={setShowPrivacyDialog}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {privacyPolicyContent.titleTranslations?.[locale] || privacyPolicyContent.title}
              </DialogTitle>
              <DialogDescription>
                Last updated: {SITE_INFO.legal.privacyLastUpdated}
              </DialogDescription>
            </DialogHeader>
            <div className="prose prose-sm dark:prose-invert max-w-none">
              <MarkdownRender
                content={privacyPolicyContent.content[locale] || privacyPolicyContent.content.en}
                variables={{
                  companyName: SITE_INFO.organization.name,
                  email: SITE_INFO.contact.email.privacy,
                }}
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowPrivacyDialog(false)}>
                {intl.formatMessage({ id: 'developer.register.close' })}
              </Button>
              <Button onClick={handleAgreeToPrivacy}>
                {intl.formatMessage({ id: 'developer.register.agree' })}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ApplicationRegister;
