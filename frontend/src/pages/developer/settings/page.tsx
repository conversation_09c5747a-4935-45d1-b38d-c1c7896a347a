/**
 * Developer Settings
 *
 * Developer account settings with API key management, webhook configuration,
 * and development environment controls.
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Settings,
  Key,
  Webhook,
  Copy,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  RefreshCw,
  Save,
  Clock,
  Shield,
  Code,
  Bell,
  Monitor,
  Database,
  Zap
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import StyledTabs from '@/components/common/styled-tabs';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { useToast } from '@/hooks/use-toast';
import { PageLoader } from '@/components/base/page-loader';
import { useDeveloperSettings } from '@/hooks/use-developer-api';

interface APIKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  lastUsed: string | null;
  createdAt: string;
  expiresAt: string | null;
  isActive: boolean;
}

interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret: string;
  isActive: boolean;
  lastTriggered: string | null;
  createdAt: string;
}

interface DeveloperSettings {
  // Account Settings
  companyName: string;
  contactEmail: string;
  supportUrl: string;
  
  // Development Environment
  sandboxMode: boolean;
  debugMode: boolean;
  rateLimitBypass: boolean;
  
  // Notification Preferences
  emailNotifications: boolean;
  webhookFailureAlerts: boolean;
  usageAlerts: boolean;
  securityAlerts: boolean;
  
  // API Configuration
  defaultApiVersion: string;
  requestTimeout: number;
  maxRetries: number;
}

const DeveloperSettingsPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const { settings: apiSettings, isLoading, error, updateSettings } = useDeveloperSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [showApiKey, setShowApiKey] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('account');

  // Map API settings to local settings format
  const [settings, setSettings] = useState<DeveloperSettings>({
    companyName: '',
    contactEmail: '',
    supportUrl: '',
    sandboxMode: true,
    debugMode: false,
    rateLimitBypass: false,
    emailNotifications: true,
    webhookFailureAlerts: true,
    usageAlerts: true,
    securityAlerts: true,
    defaultApiVersion: 'v1',
    requestTimeout: 30,
    maxRetries: 3
  });

  // Update local settings when API settings are loaded
  useEffect(() => {
    if (apiSettings) {
      setSettings({
        companyName: apiSettings.account?.company || '',
        contactEmail: apiSettings.account?.email || '',
        supportUrl: apiSettings.api?.webhook_url || '',
        sandboxMode: true, // Default for now
        debugMode: false, // Default for now
        rateLimitBypass: false, // Default for now
        emailNotifications: apiSettings.notifications?.email_alerts || true,
        webhookFailureAlerts: true, // Default for now
        usageAlerts: true, // Default for now
        securityAlerts: apiSettings.notifications?.security_notifications || true,
        defaultApiVersion: apiSettings.api?.api_version || 'v1',
        requestTimeout: 30, // Default for now
        maxRetries: 3 // Default for now
      });
    }
  }, [apiSettings]);

  // Load API keys and webhooks from API
  useEffect(() => {
    const loadDeveloperData = async () => {
      try {
        // In a real implementation, these would be separate API calls
        // For now, we'll show empty states since these endpoints aren't implemented yet
        console.log('API Keys and Webhooks endpoints not yet implemented - showing empty states');
        // setApiKeys(await DeveloperApiService.getApiKeys());
        // setWebhooks(await DeveloperApiService.getWebhooks());
      } catch (error) {
        console.error('Error loading developer data:', error);
        toast({
          title: intl.formatMessage({ id: 'developer.settings.toast.loadFailed' }),
          description: intl.formatMessage({ id: 'developer.settings.toast.loadFailedDesc' }),
          variant: "destructive",
        });
      }
    };

    if (apiSettings) {
      loadDeveloperData();
    }
  }, [apiSettings, toast]);

  // API Keys state - will be loaded from API
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);

  // Webhooks state - will be loaded from API  
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);

  // Show error toast if API loading fails
  useEffect(() => {
    if (error) {
      toast({
        title: intl.formatMessage({ id: 'developer.settings.toast.settingsLoadFailed' }),
        description: intl.formatMessage({ id: 'developer.settings.toast.settingsLoadFailedDesc' }),
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const handleSettingChange = (key: keyof DeveloperSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Map local settings back to API format
      const apiSettingsUpdate = {
        account: {
          company: settings.companyName,
          email: settings.contactEmail,
        },
        api: {
          webhook_url: settings.supportUrl,
          api_version: settings.defaultApiVersion,
        },
        notifications: {
          email_alerts: settings.emailNotifications,
          security_notifications: settings.securityAlerts,
        }
      };

      await updateSettings(apiSettingsUpdate);

      toast({
        title: intl.formatMessage({ id: 'developer.settings.toast.saved' }),
        description: intl.formatMessage({ id: 'developer.settings.toast.savedDesc' }),
      });

      setHasChanges(false);
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'developer.settings.toast.saveFailed' }),
        description: intl.formatMessage({ id: 'developer.settings.toast.saveFailedDesc' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: intl.formatMessage({ id: 'developer.settings.toast.copied' }),
      description: intl.formatMessage({ id: 'developer.settings.toast.copiedDesc' }),
    });
  };

  const generateNewApiKey = () => {
    toast({
      title: intl.formatMessage({ id: 'developer.settings.toast.comingSoon' }),
      description: intl.formatMessage({ id: 'developer.settings.toast.comingSoonDesc' }),
      variant: "default",
    });
  };

  const revokeApiKey = (keyId: string) => {
    toast({
      title: intl.formatMessage({ id: 'developer.settings.toast.comingSoon' }),
      description: intl.formatMessage({ id: 'developer.settings.toast.comingSoonDesc' }),
      variant: "default",
    });
  };

  const addWebhook = () => {
    toast({
      title: intl.formatMessage({ id: 'developer.settings.toast.comingSoon' }),
      description: intl.formatMessage({ id: 'developer.settings.toast.comingSoonDesc' }),
      variant: "default",
    });
  };

  const deleteWebhook = (webhookId: string) => {
    toast({
      title: intl.formatMessage({ id: 'developer.settings.toast.comingSoon' }),
      description: intl.formatMessage({ id: 'developer.settings.toast.comingSoonDesc' }),
      variant: "default",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const maskApiKey = (key: string) => {
    return `${key.substring(0, 12)}...${key.substring(key.length - 4)}`;
  };

  // Tab configuration
  const tabs = [
    { id: 'account', label: intl.formatMessage({ id: 'developer.settings.tabs.general' }) },
    { id: 'api-keys', label: intl.formatMessage({ id: 'developer.settings.tabs.apiKeys' }) },
    { id: 'webhooks', label: intl.formatMessage({ id: 'developer.settings.tabs.webhooks' }) },
    { id: 'environment', label: intl.formatMessage({ id: 'developer.settings.tabs.environments' }) },
    { id: 'notifications', label: intl.formatMessage({ id: 'developer.settings.tabs.notifications' }) }
  ];

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            {intl.formatMessage({ id: 'developer.settings.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'developer.settings.description' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            onClick={handleSaveSettings} 
            disabled={!hasChanges || isSaving}
          >
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {intl.formatMessage({ id: 'developer.settings.saving' })}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'developer.settings.save' })}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          size="md"
          variant="default"
        />

        {activeTab === 'api-keys' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    {intl.formatMessage({ id: 'developer.settings.apiKeys.title' })}
                  </CardTitle>
                  <CardDescription>
                    {intl.formatMessage({ id: 'developer.settings.apiKeys.description' })}
                  </CardDescription>
                </div>
                <Button onClick={generateNewApiKey}>
                  <Plus className="h-4 w-4 mr-2" />
                  {intl.formatMessage({ id: 'developer.settings.apiKeys.createNew' })}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {apiKeys.length > 0 ? (
                <div className="space-y-4">
                  {apiKeys.map((apiKey) => (
                    <div key={apiKey.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{apiKey.name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                              {showApiKey === apiKey.id ? apiKey.key : maskApiKey(apiKey.key)}
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setShowApiKey(showApiKey === apiKey.id ? null : apiKey.id)}
                            >
                              {showApiKey === apiKey.id ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(apiKey.key, 'API Key')}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={apiKey.isActive ? 'default' : 'secondary'}>
                            {apiKey.isActive ? intl.formatMessage({ id: 'developer.settings.apiKeys.active' }) : intl.formatMessage({ id: 'developer.settings.apiKeys.inactive' })}
                          </Badge>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => revokeApiKey(apiKey.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.apiKeys.created' })}:</span>
                          <br />
                          {formatDate(apiKey.createdAt)}
                        </div>
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.apiKeys.lastUsed' })}:</span>
                          <br />
                          {apiKey.lastUsed ? formatDate(apiKey.lastUsed) : intl.formatMessage({ id: 'developer.settings.apiKeys.never' })}
                        </div>
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.apiKeys.expires' })}:</span>
                          <br />
                          {apiKey.expiresAt ? formatDate(apiKey.expiresAt) : intl.formatMessage({ id: 'developer.settings.apiKeys.never' })}
                        </div>
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.apiKeys.permissions' })}:</span>
                          <br />
                          {apiKey.permissions.length} {intl.formatMessage({ id: 'developer.settings.apiKeys.scopes' })}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {apiKey.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">{intl.formatMessage({ id: 'developer.settings.apiKeys.noKeys' })}</h3>
                  <p className="text-muted-foreground mb-4">
                    {intl.formatMessage({ id: 'developer.settings.apiKeys.noKeysLong' })}
                  </p>
                  <Button onClick={generateNewApiKey}>
                    <Plus className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'developer.settings.apiKeys.generateFirst' })}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'webhooks' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Webhook className="h-5 w-5" />
                    {intl.formatMessage({ id: 'developer.settings.webhooks.title' })}
                  </CardTitle>
                  <CardDescription>
                    {intl.formatMessage({ id: 'developer.settings.webhooks.description' })}
                  </CardDescription>
                </div>
                <Button onClick={addWebhook}>
                  <Plus className="h-4 w-4 mr-2" />
                  {intl.formatMessage({ id: 'developer.settings.webhooks.createNew' })}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {webhooks.length > 0 ? (
                <div className="space-y-4">
                  {webhooks.map((webhook) => (
                    <div key={webhook.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{webhook.name}</h4>
                          <code className="text-sm text-muted-foreground">{webhook.url}</code>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={webhook.isActive ? 'default' : 'secondary'}>
                            {webhook.isActive ? intl.formatMessage({ id: 'developer.settings.webhookActive' }) : intl.formatMessage({ id: 'developer.settings.webhookInactive' })}
                          </Badge>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => deleteWebhook(webhook.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.webhookCreated' })}</span>
                          <br />
                          {formatDate(webhook.createdAt)}
                        </div>
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.webhookLastTriggered' })}</span>
                          <br />
                          {webhook.lastTriggered ? formatDate(webhook.lastTriggered) : intl.formatMessage({ id: 'developer.settings.webhookNever' })}
                        </div>
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'developer.settings.webhookEvents' })}</span>
                          <br />
                          {webhook.events.length} {intl.formatMessage({ id: 'developer.settings.webhookEventsSubscribed' })}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {webhook.events.map((event) => (
                          <Badge key={event} variant="outline" className="text-xs">
                            {event}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center gap-2 pt-2 border-t">
                        <span className="text-sm font-medium">{intl.formatMessage({ id: 'developer.settings.webhookSecret' })}</span>
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {webhook.secret}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(webhook.secret, 'Webhook Secret')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Webhook className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">{intl.formatMessage({ id: 'developer.settings.noWebhooksConfigured' })}</h3>
                  <p className="text-muted-foreground mb-4">
                    {intl.formatMessage({ id: 'developer.settings.noWebhooksConfiguredDesc' })}
                  </p>
                  <Button onClick={addWebhook}>
                    <Plus className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'developer.settings.addFirstWebhook' })}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'account' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {intl.formatMessage({ id: 'developer.settings.accountInformation' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'developer.settings.accountInformationDesc' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="companyName">{intl.formatMessage({ id: 'developer.settings.companyName' })}</Label>
                  <Input
                    id="companyName"
                    value={settings.companyName}
                    onChange={(e) => handleSettingChange('companyName', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'developer.settings.companyNamePlaceholder' })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactEmail">{intl.formatMessage({ id: 'developer.settings.contactEmail' })}</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={settings.contactEmail}
                    onChange={(e) => handleSettingChange('contactEmail', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'developer.settings.contactEmailPlaceholder' })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supportUrl">{intl.formatMessage({ id: 'developer.settings.supportUrl' })}</Label>
                <Input
                  id="supportUrl"
                  type="url"
                  value={settings.supportUrl}
                  onChange={(e) => handleSettingChange('supportUrl', e.target.value)}
                  placeholder={intl.formatMessage({ id: 'developer.settings.supportUrlPlaceholder' })}
                />
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'environment' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                {intl.formatMessage({ id: 'developer.settings.developmentEnvironment' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'developer.settings.developmentEnvironmentDesc' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sandboxMode">{intl.formatMessage({ id: 'developer.settings.sandboxMode' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.sandboxModeDesc' })}</p>
                  </div>
                  <Switch
                    id="sandboxMode"
                    checked={settings.sandboxMode}
                    onCheckedChange={(checked) => handleSettingChange('sandboxMode', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="debugMode">{intl.formatMessage({ id: 'developer.settings.debugMode' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.debugModeDesc' })}</p>
                  </div>
                  <Switch
                    id="debugMode"
                    checked={settings.debugMode}
                    onCheckedChange={(checked) => handleSettingChange('debugMode', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="rateLimitBypass">{intl.formatMessage({ id: 'developer.settings.rateLimitBypass' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.rateLimitBypassDesc' })}</p>
                  </div>
                  <Switch
                    id="rateLimitBypass"
                    checked={settings.rateLimitBypass}
                    onCheckedChange={(checked) => handleSettingChange('rateLimitBypass', checked)}
                    disabled={!settings.sandboxMode}
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="defaultApiVersion">{intl.formatMessage({ id: 'developer.settings.defaultApiVersion' })}</Label>
                  <Select
                    value={settings.defaultApiVersion}
                    onValueChange={(value) => handleSettingChange('defaultApiVersion', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="v1">Version 1.0</SelectItem>
                      <SelectItem value="v2">Version 2.0 (Beta)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="requestTimeout">{intl.formatMessage({ id: 'developer.settings.requestTimeout' })}</Label>
                  <Input
                    id="requestTimeout"
                    type="number"
                    value={settings.requestTimeout}
                    onChange={(e) => handleSettingChange('requestTimeout', parseInt(e.target.value))}
                    min="5"
                    max="300"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {intl.formatMessage({ id: 'developer.settings.notificationPreferences' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'developer.settings.notificationPreferencesDesc' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotifications">{intl.formatMessage({ id: 'developer.settings.emailNotifications' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.emailNotificationsDesc' })}</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="webhookFailureAlerts">{intl.formatMessage({ id: 'developer.settings.webhookFailureAlerts' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.webhookFailureAlertsDesc' })}</p>
                  </div>
                  <Switch
                    id="webhookFailureAlerts"
                    checked={settings.webhookFailureAlerts}
                    onCheckedChange={(checked) => handleSettingChange('webhookFailureAlerts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="usageAlerts">{intl.formatMessage({ id: 'developer.settings.usageAlerts' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.usageAlertsDesc' })}</p>
                  </div>
                  <Switch
                    id="usageAlerts"
                    checked={settings.usageAlerts}
                    onCheckedChange={(checked) => handleSettingChange('usageAlerts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="securityAlerts">{intl.formatMessage({ id: 'developer.settings.securityAlerts' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'developer.settings.securityAlertsDesc' })}</p>
                  </div>
                  <Switch
                    id="securityAlerts"
                    checked={settings.securityAlerts}
                    onCheckedChange={(checked) => handleSettingChange('securityAlerts', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeveloperSettingsPage;
