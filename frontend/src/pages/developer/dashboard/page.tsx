import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { 
  Plus, 
  Settings, 
  BarChart3, 
  Key, 
  BookOpen, 
  Shield,
  Activity,
  Users,
  Globe,
  Building2,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import StyledTabs from '@/components/common/styled-tabs';
import FilterPanel from '@/components/common/filter-panel';
import { DeveloperAnalyticsDashboard } from '@/components/features/developer/developer-analytics-dashboard';
import { useDeveloperAnalytics, useDeveloperApplications } from '@/hooks/use-developer-api';
import {
  getApplicationFilterCategories,
  getAnalyticsFilterCategories,
  filterApplications,
  filterAnalytics
} from '@/data/developer-filters';

/**
 * Developer Dashboard Page
 * 
 * Central hub for developers to manage applications, view API documentation,
 * monitor usage, and generate client credentials for GeNieGO SSO integration.
 */
const DeveloperDashboard: React.FC = () => {
  const intl = useIntl();
  const { analytics, isLoading: analyticsLoading, error: analyticsError, refreshData: refreshAnalytics } = useDeveloperAnalytics();
  const { applications, isLoading: appsLoading, error: appsError, refreshData: refreshApps } = useDeveloperApplications();

  const refreshData = () => {
    refreshAnalytics();
    refreshApps();
  };

  // Tab and filter state
  const [activeTab, setActiveTab] = useState('overview');
  const [appSearchTerm, setAppSearchTerm] = useState('');
  const [appStatusFilter, setAppStatusFilter] = useState<string>('all');
  const [appTypeFilter, setAppTypeFilter] = useState<string>('all');
  const [appEnvironmentFilter, setAppEnvironmentFilter] = useState<string>('all');
  const [analyticsMetricFilter, setAnalyticsMetricFilter] = useState<string>('all');
  const [analyticsTimeframeFilter, setAnalyticsTimeframeFilter] = useState<string>('all');

  // Calculate stats from real API data
  const stats = {
    totalApplications: analytics?.total_applications || 0,
    activeApplications: applications?.filter(app => app.is_active).length || 0,
    totalUsers: analytics?.total_users || 0,
    monthlyRequests: analytics?.total_logins || 0
  };

  const isLoading = analyticsLoading || appsLoading;

  const quickActions = [
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.registerApp' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.registerAppDesc' }),
      icon: Plus,
      href: '/developer/register',
      featured: true
    },
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.manageApps' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.manageAppsDesc' }),
      icon: Settings,
      href: '/developer/applications'
    },
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.apiDocs' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.apiDocsDesc' }),
      icon: BookOpen,
      href: '/developer/docs'
    },
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.generateKeys' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.generateKeysDesc' }),
      icon: Key,
      href: '/developer/keys'
    },
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.viewAnalytics' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.viewAnalyticsDesc' }),
      icon: BarChart3,
      href: '/developer/analytics'
    },
    {
      title: intl.formatMessage({ id: 'developer.dashboard.quickActions.securitySettings' }),
      description: intl.formatMessage({ id: 'developer.dashboard.quickActions.securitySettingsDesc' }),
      icon: Shield,
      href: '/developer/security'
    }
  ];

  const systemStatus = [
    { service: intl.formatMessage({ id: 'developer.dashboard.systemStatus.authenticationApi' }), status: 'operational', uptime: '99.9%' },
    { service: intl.formatMessage({ id: 'developer.dashboard.systemStatus.userManagement' }), status: 'operational', uptime: '99.8%' },
    { service: intl.formatMessage({ id: 'developer.dashboard.systemStatus.tokenService' }), status: 'operational', uptime: '100%' },
    { service: intl.formatMessage({ id: 'developer.dashboard.systemStatus.analyticsEngine' }), status: 'maintenance', uptime: '98.5%' }
  ];

  // Filter categories
  const appFilterCategories = getApplicationFilterCategories(
    intl,
    appStatusFilter,
    appTypeFilter,
    appEnvironmentFilter,
    (value) => setAppStatusFilter(Array.isArray(value) ? value[0] : value),
    (value) => setAppTypeFilter(Array.isArray(value) ? value[0] : value),
    (value) => setAppEnvironmentFilter(Array.isArray(value) ? value[0] : value)
  );

  const analyticsFilterCategories = getAnalyticsFilterCategories(
    intl,
    analyticsMetricFilter,
    analyticsTimeframeFilter,
    (value) => setAnalyticsMetricFilter(Array.isArray(value) ? value[0] : value),
    (value) => setAnalyticsTimeframeFilter(Array.isArray(value) ? value[0] : value)
  );

  // Reset functions
  const resetAppFilters = () => {
    setAppSearchTerm('');
    setAppStatusFilter('all');
    setAppTypeFilter('all');
    setAppEnvironmentFilter('all');
  };

  const resetAnalyticsFilters = () => {
    setAnalyticsMetricFilter('all');
    setAnalyticsTimeframeFilter('all');
  };

  // Filtered data
  const filteredApplications = useMemo(() => {
    try {
      return filterApplications(
        applications || [],
        appSearchTerm,
        appStatusFilter,
        appTypeFilter,
        appEnvironmentFilter
      );
    } catch (error) {
      console.error('Error filtering applications:', error);
      return [];
    }
  }, [applications, appSearchTerm, appStatusFilter, appTypeFilter, appEnvironmentFilter]);

  const filteredAnalytics = useMemo(() => {
    try {
      return filterAnalytics(
        analytics,
        analyticsMetricFilter,
        analyticsTimeframeFilter
      );
    } catch (error) {
      console.error('Error filtering analytics:', error);
      return analytics;
    }
  }, [analytics, analyticsMetricFilter, analyticsTimeframeFilter]);

  // Tab configuration
  const tabs = [
    { id: 'overview', label: intl.formatMessage({ id: 'developer.dashboard.tabs.overview' }) },
    { id: 'applications', label: intl.formatMessage({ id: 'developer.dashboard.tabs.applications' }) },
    { id: 'analytics', label: intl.formatMessage({ id: 'developer.dashboard.tabs.analytics' }) },
    { id: 'documentation', label: intl.formatMessage({ id: 'developer.dashboard.tabs.documentation' }) }
  ];

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'developer.dashboard.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'developer.dashboard.description' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'developer.dashboard.refresh' })}
          </Button>
          <Button asChild>
            <Link to="/developer/register">
              <Plus className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'developer.dashboard.newApplication' })}
            </Link>
          </Button>
        </div>
      </div>

      {/* Enhanced Dashboard with Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          size="md"
          variant="default"
        />

        {activeTab === 'overview' && (
          <div className="space-y-6">
          {/* Quick Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'developer.dashboard.stats.myApplications' })}</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-2">
                    <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-16"></div>
                  </div>
                ) : (
                  <>
                    <div className="text-2xl font-bold">{stats.totalApplications}</div>
                    <p className="text-xs text-muted-foreground">
                      {stats.activeApplications} {intl.formatMessage({ id: 'developer.dashboard.stats.active' })}
                    </p>
                  </>
                )}
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'developer.dashboard.stats.totalUsers' })}</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.dashboard.stats.acrossAllApps' })}
                </p>
              </CardContent>
            </Card>
            
            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'developer.dashboard.stats.monthlyRequests' })}</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-2">
                    <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-24"></div>
                  </div>
                ) : (
                  <>
                    <div className="text-2xl font-bold">{stats.monthlyRequests.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      {intl.formatMessage({ id: 'developer.dashboard.stats.apiCalls' })} {intl.formatMessage({ id: 'developer.dashboard.stats.thisMonth' })}
                    </p>
                  </>
                )}
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'developer.dashboard.apiStatus' })}</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{intl.formatMessage({ id: 'developer.dashboard.online' })}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'developer.dashboard.allServicesOperational' })}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {quickActions.map((action, index) => (
              <Card key={index} className={`transition-all hover:shadow-sm ${action.featured ? 'ring-2 ring-primary' : ''}`}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <action.icon className="h-5 w-5" />
                    {action.title}
                  </CardTitle>
                  <CardDescription>{action.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button asChild className="w-full" variant={action.featured ? 'default' : 'outline'}>
                    <Link to={action.href}>
                      {action.featured ? intl.formatMessage({ id: 'developer.dashboard.getStarted' }) : intl.formatMessage({ id: 'developer.dashboard.access' })}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* System Status */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {intl.formatMessage({ id: 'developer.dashboard.systemStatus' })}
              </CardTitle>
              <CardDescription>{intl.formatMessage({ id: 'developer.dashboard.systemStatusDesc' })}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {systemStatus.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className={`w-2 h-2 rounded-full ${
                        service.status === 'operational' ? 'bg-green-500' :
                        service.status === 'maintenance' ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <span className="font-medium">{service.service}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium capitalize">{intl.formatMessage({ id: `developer.dashboard.systemStatus.${service.status}` })}</div>
                      <div className="text-xs text-muted-foreground">{service.uptime} {intl.formatMessage({ id: 'developer.dashboard.systemStatus.uptime' })}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Filter Panel for Analytics */}
            <FilterPanel
              title={intl.formatMessage({ id: 'developer.dashboard.analytics.filterTitle' })}
              filterCategories={analyticsFilterCategories}
              onResetAll={resetAnalyticsFilters}
              variant="default"
              showIcons={true}
              size="md"
            />
            <DeveloperAnalyticsDashboard />
          </div>
        )}

        {activeTab === 'applications' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
              title={intl.formatMessage({ id: 'developer.dashboard.applications.filterTitle' })}
              filterCategories={appFilterCategories}
              searchQuery={appSearchTerm}
              onSearchChange={setAppSearchTerm}
              onClearSearch={() => setAppSearchTerm('')}
              onResetAll={resetAppFilters}
              variant="default"
              showIcons={true}
              showSearchBar={true}
              searchPlaceholder={intl.formatMessage({ id: 'developer.dashboard.applications.searchPlaceholder' })}
              searchHotkey="cmd+f"
              size="md"
            />
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'developer.dashboard.applications.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'developer.dashboard.applications.description' }, { count: filteredApplications.length })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredApplications.length > 0 ? (
                  <div className="space-y-4">
                    {filteredApplications.slice(0, 5).map((app: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <Building2 className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium">{app.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {app.client_id} • {intl.formatMessage({ id: 'developer.dashboard.created' })} {new Date(app.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={app.is_active ? 'default' : 'secondary'}>
                            {app.is_active ? intl.formatMessage({ id: 'developer.dashboard.active' }) : intl.formatMessage({ id: 'developer.dashboard.inactive' })}
                          </Badge>
                          <Button variant="outline" size="sm">
                            {intl.formatMessage({ id: 'developer.dashboard.applications.manage' })}
                          </Button>
                        </div>
                      </div>
                    ))}
                    {filteredApplications.length > 5 && (
                      <div className="text-center py-4">
                        <Button asChild variant="outline">
                          <Link to="/developer/applications">
                            {intl.formatMessage({ id: 'developer.dashboard.applications.viewAll' }, { count: filteredApplications.length })}
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'developer.dashboard.applications.noAppsFound' })}</h3>
                    <p className="text-muted-foreground mb-4">
                      {appSearchTerm || appStatusFilter !== 'all' || appTypeFilter !== 'all' || appEnvironmentFilter !== 'all'
                        ? intl.formatMessage({ id: 'developer.dashboard.applications.noAppsFiltered' })
                        : intl.formatMessage({ id: 'developer.dashboard.applications.noAppsYet' })
                      }
                    </p>
                    <Button asChild>
                      <Link to="/developer/register">
                        {intl.formatMessage({ id: 'developer.dashboard.applications.registerFirst' })}
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'documentation' && (
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle>{intl.formatMessage({ id: 'developer.dashboard.documentation.apiDocs' })}</CardTitle>
                  <CardDescription>{intl.formatMessage({ id: 'developer.dashboard.documentation.apiDocsDesc' })}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button asChild className="w-full">
                    <Link to="/developer/docs">
                      <BookOpen className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'developer.dashboard.documentation.viewDocs' })}
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle>{intl.formatMessage({ id: 'developer.dashboard.documentation.apiKeys' })}</CardTitle>
                  <CardDescription>{intl.formatMessage({ id: 'developer.dashboard.documentation.apiKeysDesc' })}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button asChild className="w-full">
                    <Link to="/developer/keys">
                      <Key className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'developer.dashboard.documentation.manageKeys' })}
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle>{intl.formatMessage({ id: 'developer.dashboard.documentation.settings' })}</CardTitle>
                  <CardDescription>{intl.formatMessage({ id: 'developer.dashboard.documentation.settingsDesc' })}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button asChild className="w-full">
                    <Link to="/developer/settings">
                      <Settings className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'developer.dashboard.documentation.accountSettings' })}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeveloperDashboard;
