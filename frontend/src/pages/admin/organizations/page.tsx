import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Power,
  PowerOff,
  BarChart3,
  Users,
  Calendar,
  Activity,
  AlertTriangle,
  Shield,
  Settings,
  RefreshCw,
  Download,
  Trash2,
  Clock,
  Globe,
  Building2,
  UserCheck,
  Mail
} from 'lucide-react';

import { PageLoader } from '@/components/base/page-loader';
import FilterPanel from '@/components/common/filter-panel';
import { Badge } from '@/components/ui/shadcn/badge';
import { OrganizationDetailsDialog } from '@/components/features/admin/organization-details-dialog';
import { OrganizationMembersDialog } from '@/components/features/admin/organization-members-dialog';
import { OrganizationSettingsDialog } from '@/components/features/admin/organization-settings-dialog';

interface AdminOrganization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_public: boolean;
  is_active: boolean;
  member_count: number;
  max_members: number;
  created_at: string;
  updated_at: string;
  owner?: {
    id: string;
    username: string;
    email: string;
  };
  settings: Record<string, any>;
}

interface OrganizationFilters {
  status: 'all' | 'active' | 'inactive';
  visibility: 'all' | 'public' | 'private';
  dateRange: 'all' | 'today' | 'week' | 'month' | 'year';
  searchTerm: string;
}

/**
 * Admin Organization Management
 * 
 * System-level organization management interface for administrators to oversee
 * all organizations, manage settings, and monitor activity.
 */
const AdminOrganizationManagement: React.FC = () => {
  const intl = useIntl();
  const [organizations, setOrganizations] = useState<AdminOrganization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<AdminOrganization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrganization, setSelectedOrganization] = useState<AdminOrganization | null>(null);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [membersDialogOpen, setMembersDialogOpen] = useState(false);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string | null>(null);
  const [selectedOrganizationName, setSelectedOrganizationName] = useState<string>('');

  const [filters, setFilters] = useState<OrganizationFilters>({
    status: 'all',
    visibility: 'all',
    dateRange: 'all',
    searchTerm: ''
  });

  // Load organizations from real API
  useEffect(() => {
    const loadOrganizations = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/v1/admin/organizations', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to load organizations: ${response.status}`);
        }

        const data = await response.json();
        setOrganizations(data.organizations || []);
      } catch (error) {
        console.error('Failed to load organizations:', error);
        // Fallback to mock data when API fails (for development/testing)
        const mockData: AdminOrganization[] = [
          {
            id: '1bea99d2-83a8-421d-bbcf-5e51c1d0a277',
            name: 'GeNieGO Development Team',
            slug: 'geniego-dev',
            description: 'Main development organization for GeNieGO platform',
            is_public: false,
            is_active: true,
            member_count: 8,
            max_members: 50,
            created_at: '2024-01-15T10:30:00Z',
            updated_at: '2024-07-20T14:22:00Z',
            owner: {
              id: 'owner1',
              username: 'john_doe',
              email: '<EMAIL>'
            },
            settings: {}
          },
          {
            id: '7ef20a4d-32ce-4cd8-8f18-7cc76aa7ca9d',
            name: 'Genieland',
            slug: 'genieland',
            description: 'Main Genieland organization',
            is_public: true,
            is_active: true,
            member_count: 23,
            max_members: 50,
            created_at: '2024-02-10T09:15:00Z',
            updated_at: '2024-07-19T16:45:00Z',
            owner: {
              id: 'owner2',
              username: 'jane_smith',
              email: '<EMAIL>'
            },
            settings: {}
          },
          {
            id: 'f9beab07-5f65-405b-82b3-bd8839ab1e46',
            name: 'Test Organization 2',
            slug: 'test2',
            description: 'Organization for testing purposes',
            is_public: false,
            is_active: false,
            member_count: 5,
            max_members: 50,
            created_at: '2024-03-05T11:20:00Z',
            updated_at: '2024-06-15T13:30:00Z',
            owner: {
              id: 'owner3',
              username: 'mike_wilson',
              email: '<EMAIL>'
            },
            settings: {}
          }
        ];
        setOrganizations(mockData);
      } finally {
        setIsLoading(false);
      }
    };

    loadOrganizations();
  }, []);

  // Create filter categories for FilterPanel
  const organizationFilterCategories = useMemo(() => [
    {
      id: 'status',
      name: intl.formatMessage({ id: 'admin.organizations.filters.status' }),
      options: [
        { id: 'all', name: intl.formatMessage({ id: 'admin.organizations.filters.status.all' }) },
        { id: 'active', name: intl.formatMessage({ id: 'admin.organizations.filters.status.active' }) },
        { id: 'inactive', name: intl.formatMessage({ id: 'admin.organizations.filters.status.inactive' }) }
      ],
      selected: filters.status,
      onSelect: (value: string | string[]) => setFilters(prev => ({ ...prev, status: value as 'all' | 'active' | 'inactive' })),
      color: 'primary' as const
    },
    {
      id: 'visibility',
      name: intl.formatMessage({ id: 'admin.organizations.filters.visibility' }),
      options: [
        { id: 'all', name: intl.formatMessage({ id: 'admin.organizations.filters.visibility.all' }) },
        { id: 'public', name: intl.formatMessage({ id: 'admin.organizations.filters.visibility.public' }) },
        { id: 'private', name: intl.formatMessage({ id: 'admin.organizations.filters.visibility.private' }) }
      ],
      selected: filters.visibility,
      onSelect: (value: string | string[]) => setFilters(prev => ({ ...prev, visibility: value as 'all' | 'public' | 'private' })),
      color: 'secondary' as const
    }
  ], [filters.status, filters.visibility, intl]);

  // Reset all filters
  const resetOrganizationFilters = () => {
    setFilters({
      status: 'all',
      visibility: 'all',
      dateRange: 'all',
      searchTerm: ''
    });
    setSearchTerm('');
  };

  // Filter organizations based on current filters
  const enhancedFilteredOrganizations = useMemo(() => {
    let filtered = [...organizations];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(org =>
        org.name.toLowerCase().includes(searchLower) ||
        org.slug.toLowerCase().includes(searchLower) ||
        org.description?.toLowerCase().includes(searchLower) ||
        org.owner?.username.toLowerCase().includes(searchLower) ||
        org.owner?.email.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(org => 
        filters.status === 'active' ? org.is_active : !org.is_active
      );
    }

    // Apply visibility filter
    if (filters.visibility !== 'all') {
      filtered = filtered.filter(org => 
        filters.visibility === 'public' ? org.is_public : !org.is_public
      );
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      filtered = filtered.filter(org => 
        new Date(org.created_at) >= filterDate
      );
    }

    return filtered;
  }, [organizations, searchTerm, filters]);

  const getOrganizationName = (organizationId: string): string => {
    const organization = organizations.find(org => org.id === organizationId);
    return organization?.name || 'Unknown Organization';
  };

  const handleOrganizationAction = async (action: string, organizationId: string) => {
    setActionMenuOpen(null);

    try {
      switch (action) {
        case 'view_details':
          setSelectedOrganizationId(organizationId);
          setSelectedOrganizationName(getOrganizationName(organizationId));
          setDetailsDialogOpen(true);
          break;

        case 'toggle_status':
          await toggleOrganizationStatus(organizationId);
          break;

        case 'edit_settings':
          setSelectedOrganizationId(organizationId);
          setSelectedOrganizationName(getOrganizationName(organizationId));
          setSettingsDialogOpen(true);
          break;

        case 'view_members':
          setSelectedOrganizationId(organizationId);
          setSelectedOrganizationName(getOrganizationName(organizationId));
          setMembersDialogOpen(true);
          break;

        case 'delete':
          // TODO: Open delete confirmation modal
          console.log('Delete organization:', organizationId);
          break;
      }
    } catch (error) {
      console.error('Failed to perform action:', error);
    }
  };

  const toggleOrganizationStatus = async (organizationId: string) => {
    try {
      const organization = organizations.find(org => org.id === organizationId);
      if (!organization) return;

      const newStatus = !organization.is_active;

      const response = await fetch(`/api/v1/admin/organizations/${organizationId}/status?is_active=${newStatus}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to update organization status: ${response.status}`);
      }

      // Update local state
      setOrganizations(prev =>
        prev.map(org =>
          org.id === organizationId
            ? { ...org, is_active: newStatus }
            : org
        )
      );

      console.log(`Organization ${organizationId} ${newStatus ? 'activated' : 'deactivated'}`);
    } catch (error) {
      console.error('Failed to toggle organization status:', error);
    }
  };

  const exportAllOrganizations = () => {
    console.log('Exporting all organizations...');
    // TODO: Implement export functionality
  };

  const getStatusBadgeVariant = (isActive: boolean) => {
    return isActive ? 'default' : 'secondary';
  };

  const getVisibilityBadgeVariant = (isPublic: boolean) => {
    return isPublic ? 'outline' : 'secondary';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'admin.organizations.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.organizations.subtitle' })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={exportAllOrganizations}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <Download className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.organizations.exportAll' })}
          </button>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.organizations.refresh' })}
          </button>
        </div>
      </div>

      {/* Filter Panel */}
      <FilterPanel
        title={intl.formatMessage({ id: 'admin.organizations.filters.title' })}
        filterCategories={organizationFilterCategories}
        searchQuery={searchTerm}
        onSearchChange={setSearchTerm}
        onClearSearch={() => setSearchTerm('')}
        onResetAll={resetOrganizationFilters}
        variant="default"
        showIcons={true}
        showSearchBar={true}
        searchPlaceholder={intl.formatMessage({ id: 'admin.organizations.search.placeholder' })}
        searchHotkey="cmd+f"
        size="md"
      />

      {/* Organizations List */}
      <div className="space-y-4">
        {enhancedFilteredOrganizations.length === 0 ? (
          <div className="rounded-lg border bg-card p-12 text-center">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.organizations.empty.title' })}</h3>
            <p className="text-muted-foreground">
              {filters.searchTerm || filters.status !== 'all' || filters.visibility !== 'all'
                ? intl.formatMessage({ id: 'admin.organizations.empty.filtered' })
                : intl.formatMessage({ id: 'admin.organizations.empty.noOrganizations' })}
            </p>
          </div>
        ) : (
          enhancedFilteredOrganizations.map((organization) => (
            <div key={organization.id} className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Building2 className="h-6 w-6 text-primary" />
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold truncate">{organization.name}</h3>
                      <Badge variant={getStatusBadgeVariant(organization.is_active)}>
                        {organization.is_active
                          ? intl.formatMessage({ id: 'admin.organizations.status.active' })
                          : intl.formatMessage({ id: 'admin.organizations.status.inactive' })
                        }
                      </Badge>
                      <Badge variant={getVisibilityBadgeVariant(organization.is_public)}>
                        {organization.is_public
                          ? intl.formatMessage({ id: 'admin.organizations.visibility.public' })
                          : intl.formatMessage({ id: 'admin.organizations.visibility.private' })
                        }
                      </Badge>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {organization.description || intl.formatMessage({ id: 'admin.organizations.noDescription' })}
                    </p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{organization.member_count}/{organization.max_members} {intl.formatMessage({ id: 'admin.organizations.members' })}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <UserCheck className="h-4 w-4 text-muted-foreground" />
                        <span>{organization.owner?.username || intl.formatMessage({ id: 'admin.organizations.noOwner' })}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="truncate">{organization.owner?.email || 'N/A'}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(organization.created_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Menu */}
                <div className="relative">
                  <button
                    onClick={() => setActionMenuOpen(actionMenuOpen === organization.id ? null : organization.id)}
                    className="p-2 hover:bg-accent rounded-md transition-colors"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </button>

                  {actionMenuOpen === organization.id && (
                    <div className="absolute right-0 top-full mt-1 w-48 rounded-md border bg-popover p-1 shadow-sm z-10">
                      <button
                        onClick={() => handleOrganizationAction('view_details', organization.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Eye className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.organizations.actions.viewDetails' })}
                      </button>
                      <button
                        onClick={() => handleOrganizationAction('view_members', organization.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Users className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.organizations.actions.viewMembers' })}
                      </button>
                      <button
                        onClick={() => handleOrganizationAction('toggle_status', organization.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        {organization.is_active ? (
                          <>
                            <PowerOff className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.organizations.actions.deactivate' })}
                          </>
                        ) : (
                          <>
                            <Power className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.organizations.actions.activate' })}
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleOrganizationAction('edit_settings', organization.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Settings className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.organizations.actions.editSettings' })}
                      </button>
                      <div className="h-px bg-border my-1" />
                      <button
                        onClick={() => handleOrganizationAction('delete', organization.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.organizations.actions.delete' })}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Organization Details Dialog */}
      <OrganizationDetailsDialog
        isOpen={detailsDialogOpen}
        onClose={() => {
          setDetailsDialogOpen(false);
          setSelectedOrganizationId(null);
          setSelectedOrganizationName('');
        }}
        organizationId={selectedOrganizationId}
      />

      {/* Organization Members Dialog */}
      <OrganizationMembersDialog
        isOpen={membersDialogOpen}
        onClose={() => {
          setMembersDialogOpen(false);
          setSelectedOrganizationId(null);
          setSelectedOrganizationName('');
        }}
        organizationId={selectedOrganizationId}
        organizationName={selectedOrganizationName}
      />

      {/* Organization Settings Dialog */}
      <OrganizationSettingsDialog
        isOpen={settingsDialogOpen}
        onClose={() => {
          setSettingsDialogOpen(false);
          setSelectedOrganizationId(null);
          setSelectedOrganizationName('');
        }}
        organizationId={selectedOrganizationId}
        onSettingsUpdated={() => {
          // Reload organizations list
          window.location.reload(); // Simple reload for now
        }}
      />
    </div>
  );
};

export default AdminOrganizationManagement;
