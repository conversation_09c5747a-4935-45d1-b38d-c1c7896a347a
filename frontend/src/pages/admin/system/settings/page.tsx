/**
 * Admin System Settings
 *
 * Enterprise-grade system configuration page with security policies,
 * authentication settings, and compliance controls.
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Settings,
  Shield,
  Lock,
  Users,
  Clock,
  Database,
  Mail,
  Globe,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Save,
  Key,
  Server,
  FileText,
  Bell
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import StyledTabs from '@/components/common/styled-tabs';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { useToast } from '@/hooks/use-toast';
import { PageLoader } from '@/components/base/page-loader';

interface SystemSettings {
  // Authentication Settings
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireUppercase: boolean;
  passwordExpiryDays: number;
  twoFactorRequired: boolean;
  
  // Security Policies
  allowPasswordReset: boolean;
  requireEmailVerification: boolean;
  allowSelfRegistration: boolean;
  adminApprovalRequired: boolean;
  auditLogRetentionDays: number;
  
  // System Configuration
  systemName: string;
  systemDescription: string;
  supportEmail: string;
  maintenanceMode: boolean;
  debugMode: boolean;
  
  // Compliance Settings
  gdprCompliance: boolean;
  dataRetentionDays: number;
  cookieConsent: boolean;
  privacyPolicyUrl: string;
  termsOfServiceUrl: string;
  
  // Notification Settings
  emailNotificationsEnabled: boolean;
  securityAlertsEnabled: boolean;
  systemMaintenanceNotifications: boolean;
  
  // API Settings
  rateLimitEnabled: boolean;
  rateLimitRequests: number;
  rateLimitWindow: number;
  apiVersioning: string;
}

const AdminSystemSettingsPage: React.FC = () => {
  const { toast } = useToast();
  const intl = useIntl();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('authentication');
  
  const [settings, setSettings] = useState<SystemSettings>({
    // Authentication Settings
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    passwordRequireSpecialChars: true,
    passwordRequireNumbers: true,
    passwordRequireUppercase: true,
    passwordExpiryDays: 90,
    twoFactorRequired: false,
    
    // Security Policies
    allowPasswordReset: true,
    requireEmailVerification: true,
    allowSelfRegistration: true,
    adminApprovalRequired: false,
    auditLogRetentionDays: 365,
    
    // System Configuration
    systemName: 'GeNieGO SSO',
    systemDescription: 'Enterprise Single Sign-On Service',
    supportEmail: '<EMAIL>',
    maintenanceMode: false,
    debugMode: false,
    
    // Compliance Settings
    gdprCompliance: true,
    dataRetentionDays: 730,
    cookieConsent: true,
    privacyPolicyUrl: '/privacy-policy',
    termsOfServiceUrl: '/terms-of-service',
    
    // Notification Settings
    emailNotificationsEnabled: true,
    securityAlertsEnabled: true,
    systemMaintenanceNotifications: true,
    
    // API Settings
    rateLimitEnabled: true,
    rateLimitRequests: 1000,
    rateLimitWindow: 60,
    apiVersioning: 'v1'
  });

  useEffect(() => {
    // Simulate loading settings from API
    const loadSettings = async () => {
      setIsLoading(true);
      try {
        // In real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setIsLoading(false);
      } catch (error) {
        toast({
          title: intl.formatMessage({ id: 'admin.systemSettings.toast.loadFailed.title' }),
          description: intl.formatMessage({ id: 'admin.systemSettings.toast.loadFailed.description' }),
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [toast]);

  const handleSettingChange = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Simulate API call to save settings
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: intl.formatMessage({ id: 'admin.systemSettings.toast.saveSuccess.title' }),
        description: intl.formatMessage({ id: 'admin.systemSettings.toast.saveSuccess.description' }),
      });
      
      setHasChanges(false);
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'admin.systemSettings.toast.saveFailed.title' }),
        description: intl.formatMessage({ id: 'admin.systemSettings.toast.saveFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetToDefaults = () => {
    // Reset to default values
    setSettings({
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      passwordRequireSpecialChars: true,
      passwordRequireNumbers: true,
      passwordRequireUppercase: true,
      passwordExpiryDays: 90,
      twoFactorRequired: false,
      allowPasswordReset: true,
      requireEmailVerification: true,
      allowSelfRegistration: true,
      adminApprovalRequired: false,
      auditLogRetentionDays: 365,
      systemName: 'GeNieGO SSO',
      systemDescription: 'Enterprise Single Sign-On Service',
      supportEmail: '<EMAIL>',
      maintenanceMode: false,
      debugMode: false,
      gdprCompliance: true,
      dataRetentionDays: 730,
      cookieConsent: true,
      privacyPolicyUrl: '/privacy-policy',
      termsOfServiceUrl: '/terms-of-service',
      emailNotificationsEnabled: true,
      securityAlertsEnabled: true,
      systemMaintenanceNotifications: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60,
      apiVersioning: 'v1'
    });
    setHasChanges(true);
  };

  // Tab configuration
  const tabs = [
    { id: 'authentication', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.authentication' }) },
    { id: 'security', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.security' }) },
    { id: 'system', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.system' }) },
    { id: 'compliance', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.compliance' }) },
    { id: 'notifications', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.notifications' }) },
    { id: 'api', label: intl.formatMessage({ id: 'admin.systemSettings.tabs.api' }) }
  ];

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            {intl.formatMessage({ id: 'admin.systemSettings.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.systemSettings.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleResetToDefaults}>
              {intl.formatMessage({ id: 'admin.systemSettings.resetToDefaults' })}
            </Button>
          )}
          <Button
            onClick={handleSaveSettings}
            disabled={!hasChanges || isSaving}
          >
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {intl.formatMessage({ id: 'admin.systemSettings.saving' })}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'admin.systemSettings.saveChanges' })}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* System Status Alert */}
      {settings.maintenanceMode && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>{intl.formatMessage({ id: 'admin.systemSettings.alerts.maintenanceMode' })}</strong> {intl.formatMessage({ id: 'admin.systemSettings.alerts.maintenanceModeDescription' })}
          </AlertDescription>
        </Alert>
      )}

      {/* Settings Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          size="md"
          variant="default"
        />

        {activeTab === 'authentication' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.authentication.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.authentication.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">{intl.formatMessage({ id: 'admin.systemSettings.authentication.sessionTimeout' })}</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
                    min="5"
                    max="480"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.authentication.sessionTimeoutHelp' })}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">{intl.formatMessage({ id: 'admin.systemSettings.authentication.maxLoginAttempts' })}</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={settings.maxLoginAttempts}
                    onChange={(e) => handleSettingChange('maxLoginAttempts', parseInt(e.target.value))}
                    min="3"
                    max="10"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.authentication.maxLoginAttemptsHelp' })}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordPolicy' })}</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="passwordMinLength">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordMinLength' })}</Label>
                    <Input
                      id="passwordMinLength"
                      type="number"
                      value={settings.passwordMinLength}
                      onChange={(e) => handleSettingChange('passwordMinLength', parseInt(e.target.value))}
                      min="6"
                      max="32"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="passwordExpiryDays">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordExpiryDays' })}</Label>
                    <Input
                      id="passwordExpiryDays"
                      type="number"
                      value={settings.passwordExpiryDays}
                      onChange={(e) => handleSettingChange('passwordExpiryDays', parseInt(e.target.value))}
                      min="30"
                      max="365"
                    />
                    <p className="text-sm text-muted-foreground">
                      {intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordExpiryHelp' })}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireSpecialChars">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireSpecialChars' })}</Label>
                      <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireSpecialCharsHelp' })}</p>
                    </div>
                    <Switch
                      id="passwordRequireSpecialChars"
                      checked={settings.passwordRequireSpecialChars}
                      onCheckedChange={(checked) => handleSettingChange('passwordRequireSpecialChars', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireNumbers">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireNumbers' })}</Label>
                      <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireNumbersHelp' })}</p>
                    </div>
                    <Switch
                      id="passwordRequireNumbers"
                      checked={settings.passwordRequireNumbers}
                      onCheckedChange={(checked) => handleSettingChange('passwordRequireNumbers', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireUppercase">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireUppercase' })}</Label>
                      <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.authentication.passwordRequireUppercaseHelp' })}</p>
                    </div>
                    <Switch
                      id="passwordRequireUppercase"
                      checked={settings.passwordRequireUppercase}
                      onCheckedChange={(checked) => handleSettingChange('passwordRequireUppercase', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="twoFactorRequired">{intl.formatMessage({ id: 'admin.systemSettings.authentication.twoFactorRequired' })}</Label>
                      <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.authentication.twoFactorHelp' })}</p>
                    </div>
                    <Switch
                      id="twoFactorRequired"
                      checked={settings.twoFactorRequired}
                      onCheckedChange={(checked) => handleSettingChange('twoFactorRequired', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.security.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.security.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowPasswordReset">{intl.formatMessage({ id: 'admin.systemSettings.security.allowPasswordReset' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.security.allowPasswordResetHelp' })}</p>
                  </div>
                  <Switch
                    id="allowPasswordReset"
                    checked={settings.allowPasswordReset}
                    onCheckedChange={(checked) => handleSettingChange('allowPasswordReset', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requireEmailVerification">{intl.formatMessage({ id: 'admin.systemSettings.security.requireEmailVerification' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.security.requireEmailVerificationHelp' })}</p>
                  </div>
                  <Switch
                    id="requireEmailVerification"
                    checked={settings.requireEmailVerification}
                    onCheckedChange={(checked) => handleSettingChange('requireEmailVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowSelfRegistration">{intl.formatMessage({ id: 'admin.systemSettings.security.allowSelfRegistration' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.security.allowSelfRegistrationHelp' })}</p>
                  </div>
                  <Switch
                    id="allowSelfRegistration"
                    checked={settings.allowSelfRegistration}
                    onCheckedChange={(checked) => handleSettingChange('allowSelfRegistration', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="adminApprovalRequired">{intl.formatMessage({ id: 'admin.systemSettings.security.adminApprovalRequired' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.security.adminApprovalRequiredHelp' })}</p>
                  </div>
                  <Switch
                    id="adminApprovalRequired"
                    checked={settings.adminApprovalRequired}
                    onCheckedChange={(checked) => handleSettingChange('adminApprovalRequired', checked)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="auditLogRetentionDays">{intl.formatMessage({ id: 'admin.systemSettings.security.auditLogRetentionDays' })}</Label>
                <Input
                  id="auditLogRetentionDays"
                  type="number"
                  value={settings.auditLogRetentionDays}
                  onChange={(e) => handleSettingChange('auditLogRetentionDays', parseInt(e.target.value))}
                  min="30"
                  max="2555"
                />
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.systemSettings.security.auditLogRetentionDaysHelp' })}
                </p>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'system' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.system.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.system.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="systemName">{intl.formatMessage({ id: 'admin.systemSettings.system.systemName' })}</Label>
                  <Input
                    id="systemName"
                    value={settings.systemName}
                    onChange={(e) => handleSettingChange('systemName', e.target.value)}
                    placeholder="GeNieGO SSO"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.system.systemNameHelp' })}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supportEmail">{intl.formatMessage({ id: 'admin.systemSettings.system.supportEmail' })}</Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={settings.supportEmail}
                    onChange={(e) => handleSettingChange('supportEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.system.supportEmailHelp' })}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="systemDescription">{intl.formatMessage({ id: 'admin.systemSettings.system.systemDescription' })}</Label>
                <Input
                  id="systemDescription"
                  value={settings.systemDescription}
                  onChange={(e) => handleSettingChange('systemDescription', e.target.value)}
                  placeholder="Enterprise Single Sign-On Service"
                />
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.systemSettings.system.systemDescriptionHelp' })}
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">{intl.formatMessage({ id: 'admin.systemSettings.system.systemStatus' })}</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenanceMode">{intl.formatMessage({ id: 'admin.systemSettings.system.maintenanceMode' })}</Label>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({ id: 'admin.systemSettings.system.maintenanceModeHelp' })}
                        {settings.maintenanceMode && (
                          <Badge variant="destructive" className="ml-2">{intl.formatMessage({ id: 'admin.systemSettings.system.active' })}</Badge>
                        )}
                      </p>
                    </div>
                    <Switch
                      id="maintenanceMode"
                      checked={settings.maintenanceMode}
                      onCheckedChange={(checked) => handleSettingChange('maintenanceMode', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="debugMode">{intl.formatMessage({ id: 'admin.systemSettings.system.debugMode' })}</Label>
                      <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.system.debugModeHelp' })}</p>
                    </div>
                    <Switch
                      id="debugMode"
                      checked={settings.debugMode}
                      onCheckedChange={(checked) => handleSettingChange('debugMode', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'compliance' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.compliance.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.compliance.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="gdprCompliance">{intl.formatMessage({ id: 'admin.systemSettings.compliance.gdprComplianceMode' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.compliance.gdprComplianceModeHelp' })}</p>
                  </div>
                  <Switch
                    id="gdprCompliance"
                    checked={settings.gdprCompliance}
                    onCheckedChange={(checked) => handleSettingChange('gdprCompliance', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="cookieConsent">{intl.formatMessage({ id: 'admin.systemSettings.compliance.cookieConsentBanner' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.compliance.cookieConsentBannerHelp' })}</p>
                  </div>
                  <Switch
                    id="cookieConsent"
                    checked={settings.cookieConsent}
                    onCheckedChange={(checked) => handleSettingChange('cookieConsent', checked)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dataRetentionDays">{intl.formatMessage({ id: 'admin.systemSettings.compliance.dataRetentionDays' })}</Label>
                <Input
                  id="dataRetentionDays"
                  type="number"
                  value={settings.dataRetentionDays}
                  onChange={(e) => handleSettingChange('dataRetentionDays', parseInt(e.target.value))}
                  min="30"
                  max="2555"
                />
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.systemSettings.compliance.dataRetentionDaysHelp' })}
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="privacyPolicyUrl">{intl.formatMessage({ id: 'admin.systemSettings.compliance.privacyPolicyUrl' })}</Label>
                  <Input
                    id="privacyPolicyUrl"
                    value={settings.privacyPolicyUrl}
                    onChange={(e) => handleSettingChange('privacyPolicyUrl', e.target.value)}
                    placeholder="/privacy-policy"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.compliance.privacyPolicyUrlHelp' })}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="termsOfServiceUrl">{intl.formatMessage({ id: 'admin.systemSettings.compliance.termsOfServiceUrl' })}</Label>
                  <Input
                    id="termsOfServiceUrl"
                    value={settings.termsOfServiceUrl}
                    onChange={(e) => handleSettingChange('termsOfServiceUrl', e.target.value)}
                    placeholder="/terms-of-service"
                  />
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({ id: 'admin.systemSettings.compliance.termsOfServiceUrlHelp' })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.notifications.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.notifications.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotificationsEnabled">{intl.formatMessage({ id: 'admin.systemSettings.notifications.emailNotificationsEnabled' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.notifications.emailNotificationsEnabledHelp' })}</p>
                  </div>
                  <Switch
                    id="emailNotificationsEnabled"
                    checked={settings.emailNotificationsEnabled}
                    onCheckedChange={(checked) => handleSettingChange('emailNotificationsEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="securityAlertsEnabled">{intl.formatMessage({ id: 'admin.systemSettings.notifications.securityAlertsEnabled' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.notifications.securityAlertsEnabledHelp' })}</p>
                  </div>
                  <Switch
                    id="securityAlertsEnabled"
                    checked={settings.securityAlertsEnabled}
                    onCheckedChange={(checked) => handleSettingChange('securityAlertsEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="systemMaintenanceNotifications">{intl.formatMessage({ id: 'admin.systemSettings.notifications.systemMaintenanceNotifications' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.notifications.systemMaintenanceNotificationsHelp' })}</p>
                  </div>
                  <Switch
                    id="systemMaintenanceNotifications"
                    checked={settings.systemMaintenanceNotifications}
                    onCheckedChange={(checked) => handleSettingChange('systemMaintenanceNotifications', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'api' && (
          <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {intl.formatMessage({ id: 'admin.systemSettings.api.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.systemSettings.api.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="rateLimitEnabled">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitEnabled' })}</Label>
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitEnabledHelp' })}</p>
                  </div>
                  <Switch
                    id="rateLimitEnabled"
                    checked={settings.rateLimitEnabled}
                    onCheckedChange={(checked) => handleSettingChange('rateLimitEnabled', checked)}
                  />
                </div>
              </div>

              {settings.rateLimitEnabled && (
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="rateLimitRequests">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitRequests' })}</Label>
                    <Input
                      id="rateLimitRequests"
                      type="number"
                      value={settings.rateLimitRequests}
                      onChange={(e) => handleSettingChange('rateLimitRequests', parseInt(e.target.value))}
                      min="100"
                      max="10000"
                    />
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitRequestsHelp' })}</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rateLimitWindow">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitWindow' })}</Label>
                    <Input
                      id="rateLimitWindow"
                      type="number"
                      value={settings.rateLimitWindow}
                      onChange={(e) => handleSettingChange('rateLimitWindow', parseInt(e.target.value))}
                      min="60"
                      max="3600"
                    />
                    <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.systemSettings.api.rateLimitWindowHelp' })}</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="apiVersioning">{intl.formatMessage({ id: 'admin.systemSettings.api.apiVersioning' })}</Label>
                <Select
                  value={settings.apiVersioning}
                  onValueChange={(value) => handleSettingChange('apiVersioning', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="v1">{intl.formatMessage({ id: 'admin.systemSettings.api.version1' })}</SelectItem>
                    <SelectItem value="v2">{intl.formatMessage({ id: 'admin.systemSettings.api.version2' })}</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.systemSettings.api.apiVersioningHelp' })}
                </p>
              </div>
            </CardContent>
          </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSystemSettingsPage;
