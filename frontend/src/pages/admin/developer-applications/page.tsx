import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Filter,
  Search,
  Calendar,
  User,
  Mail,
  FileText,
} from 'lucide-react';

import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/shadcn/dialog';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { useToast } from '@/hooks/use-toast';

// Import API service
import { roleTransitionApi, DeveloperApplicationWithUser } from '@/services/role-transition-api';

// Use the API type directly
type RoleApplication = DeveloperApplicationWithUser;

/**
 * Admin Role Applications Review Page
 * 
 * P3-F008: Allows admins to review and approve/reject developer role applications
 */
const AdminRoleApplicationsPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  
  const [applications, setApplications] = useState<RoleApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedApplication, setSelectedApplication] = useState<RoleApplication | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isReviewing, setIsReviewing] = useState(false);

  // Load applications
  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const data = await roleTransitionApi.getAllApplications();
      setApplications(data);
    } catch (error) {
      console.error('Failed to load applications:', error);
      toast({
        title: intl.formatMessage({ id: 'admin.developer-applications.error.load' }),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter applications
  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Handle application review
  const handleReview = async (applicationId: string, decision: 'approved' | 'rejected') => {
    try {
      setIsReviewing(true);
      await roleTransitionApi.reviewApplication(applicationId, {
        decision: decision,
        admin_notes: reviewNotes,
      });
      
      toast({
        title: intl.formatMessage({ 
          id: decision === 'approved' ? 'admin.developer-applications.success.approved' : 'admin.developer-applications.success.rejected' 
        }),
        variant: 'default',
      });
      
      setSelectedApplication(null);
      setReviewNotes('');
      await loadApplications();
    } catch (error) {
      console.error('Failed to review application:', error);
      toast({
        title: intl.formatMessage({ id: 'admin.developer-applications.error.review' }),
        variant: 'destructive',
      });
    } finally {
      setIsReviewing(false);
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'pending': return 'secondary';
      default: return 'outline';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  // Statistics
  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    approved: applications.filter(app => app.status === 'approved').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'common.loading' })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8" />
            {intl.formatMessage({ id: 'admin.developer-applications.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.developer-applications.subtitle' })}
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'admin.developer-applications.stats.total' })}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'admin.developer-applications.stats.pending' })}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'admin.developer-applications.stats.approved' })}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'admin.developer-applications.stats.rejected' })}
            </CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {intl.formatMessage({ id: 'admin.developer-applications.filters.title' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder={intl.formatMessage({ id: 'admin.developer-applications.filters.search' })}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {intl.formatMessage({ id: 'admin.developer-applications.filters.all' })}
                </SelectItem>
                <SelectItem value="pending">
                  {intl.formatMessage({ id: 'admin.developer-applications.filters.pending' })}
                </SelectItem>
                <SelectItem value="approved">
                  {intl.formatMessage({ id: 'admin.developer-applications.filters.approved' })}
                </SelectItem>
                <SelectItem value="rejected">
                  {intl.formatMessage({ id: 'admin.developer-applications.filters.rejected' })}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Applications List */}
      <Card>
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'admin.developer-applications.list.title' })} ({filteredApplications.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredApplications.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {intl.formatMessage({ id: 'admin.developer-applications.list.empty' })}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredApplications.map((application) => (
                <div key={application.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(application.status)}
                        <Badge variant={getStatusBadgeVariant(application.status)}>
                          {intl.formatMessage({ id: `admin.developer-applications.status.${application.status}` })}
                        </Badge>
                      </div>
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {application.user.username}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {application.user.email}
                        </div>
                      </div>
                      <div className="text-sm">
                        <div className="font-medium">
                          {application.user.role} → developer
                        </div>
                        <div className="text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(application.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedApplication(application)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {intl.formatMessage({ id: 'admin.developer-applications.action.review' })}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto overflow-x-hidden w-full">
                        <DialogHeader>
                          <DialogTitle className="text-lg sm:text-xl">
                            {intl.formatMessage({ id: 'admin.developer-applications.review.title' })}
                          </DialogTitle>
                          <DialogDescription className="text-sm text-muted-foreground">
                            {intl.formatMessage({ id: 'admin.developer-applications.review.description' })}
                          </DialogDescription>
                        </DialogHeader>

                        {selectedApplication && (
                          <div className="space-y-4 sm:space-y-6 overflow-hidden w-full">
                            {/* Application Details */}
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.user' })}
                                  </label>
                                  <div className="text-sm text-muted-foreground">
                                    <div className="font-medium">{selectedApplication.user.username}</div>
                                    <div className="text-xs">{selectedApplication.user.email}</div>
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.transition' })}
                                  </label>
                                  <div className="text-sm text-muted-foreground">
                                    {selectedApplication.user.role} → developer
                                  </div>
                                </div>
                              </div>
                              
                              <div>
                                <label className="text-sm font-medium">
                                  {intl.formatMessage({ id: 'admin.developer-applications.review.reason' })}
                                </label>
                                <div className="text-sm text-muted-foreground mt-1 p-3 bg-muted rounded-md break-words min-h-[60px] max-h-[200px] overflow-y-auto w-full max-w-full overflow-x-hidden">
                                  {selectedApplication.reason || selectedApplication.application_reason}
                                </div>
                              </div>

                              {/* Technical Background */}
                              {selectedApplication.technical_background && (
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.technical-background' })}
                                  </label>
                                  <div className="text-sm text-muted-foreground mt-1 p-3 bg-muted rounded-md break-words min-h-[60px] max-h-[200px] overflow-y-auto w-full max-w-full overflow-x-hidden">
                                    {selectedApplication.technical_background}
                                  </div>
                                </div>
                              )}

                              {/* Intended Use */}
                              {selectedApplication.intended_use && (
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.intended-use' })}
                                  </label>
                                  <div className="text-sm text-muted-foreground mt-1 p-3 bg-muted rounded-md break-words min-h-[60px] max-h-[200px] overflow-y-auto w-full max-w-full overflow-x-hidden">
                                    {selectedApplication.intended_use}
                                  </div>
                                </div>
                              )}
                              
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.submitted' })}
                                  </label>
                                  <div className="text-sm text-muted-foreground">
                                    {new Date(selectedApplication.created_at).toLocaleString()}
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.status' })}
                                  </label>
                                  <div className="flex items-center gap-2 mt-1">
                                    {getStatusIcon(selectedApplication.status)}
                                    <Badge variant={getStatusBadgeVariant(selectedApplication.status)}>
                                      {intl.formatMessage({ id: `admin.developer-applications.status.${selectedApplication.status}` })}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Review Section */}
                            {selectedApplication.status === 'pending' && (
                              <div className="space-y-4 border-t pt-4">
                                <div>
                                  <label className="text-sm font-medium">
                                    {intl.formatMessage({ id: 'admin.developer-applications.review.notes' })}
                                  </label>
                                  <Textarea
                                    placeholder={intl.formatMessage({ id: 'admin.developer-applications.review.notes.placeholder' })}
                                    value={reviewNotes}
                                    onChange={(e) => setReviewNotes(e.target.value)}
                                    className="mt-1 resize-none"
                                    rows={3}
                                  />
                                </div>

                                <div className="flex gap-2 justify-end">
                                  <Button
                                    variant="destructive"
                                    onClick={() => handleReview(selectedApplication.id, 'rejected')}
                                    disabled={isReviewing}
                                  >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    {intl.formatMessage({ id: 'admin.developer-applications.action.reject' })}
                                  </Button>
                                  <Button
                                    onClick={() => handleReview(selectedApplication.id, 'approved')}
                                    disabled={isReviewing}
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    {intl.formatMessage({ id: 'admin.developer-applications.action.approve' })}
                                  </Button>
                                </div>
                              </div>
                            )}

                            {/* Previous Review */}
                            {selectedApplication.status !== 'pending' && selectedApplication.admin_notes && (
                              <div className="space-y-2 border-t pt-4">
                                <label className="text-sm font-medium">
                                  {intl.formatMessage({ id: 'admin.developer-applications.review.previous' })}
                                </label>
                                <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md break-words min-h-[60px] max-h-[200px] overflow-y-auto w-full max-w-full overflow-x-hidden">
                                  {selectedApplication.admin_notes}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminRoleApplicationsPage;
