/**
 * Admin Security Dashboard
 *
 * Comprehensive security monitoring dashboard with threat detection,
 * audit logs, and security metrics for enterprise SSO service.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  AlertTriangle,
  Activity,
  Users,
  Lock,
  Eye,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  ArrowLeft,
  Download,
  Filter,
  Search,
  Calendar,
  MapPin,
  Smartphone,
  Monitor,
  Globe,
  Ban,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Badge } from '@/components/ui/shadcn/badge';
import StyledTabs from '@/components/common/styled-tabs';
import FilterPanel from '@/components/common/filter-panel';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { useToast } from '@/hooks/use-toast';
import { PageLoader } from '@/components/base/page-loader';
import {
  getSecurityEventFilterCategories,
  getThreatFilterCategories,
  filterSecurityEvents,
  filterThreats
} from '@/data/security-filters';

interface SecurityMetrics {
  totalThreats: number;
  activeThreats: number;
  blockedAttempts: number;
  suspiciousLogins: number;
  failedLogins: number;
  successfulLogins: number;
  uniqueUsers: number;
  newDevices: number;
  riskScore: number;
  lastUpdated: string;
}

interface SecurityEvent {
  id: string;
  type: 'login_failure' | 'suspicious_login' | 'blocked_ip' | 'new_device' | 'password_reset' | 'account_locked';
  severity: 'low' | 'medium' | 'high' | 'critical';
  user: string;
  description: string;
  ipAddress: string;
  location: string;
  device: string;
  timestamp: string;
  status: 'active' | 'resolved' | 'investigating';
}

interface ThreatAlert {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'brute_force' | 'suspicious_location' | 'multiple_devices' | 'credential_stuffing' | 'account_takeover';
  affectedUsers: number;
  firstSeen: string;
  lastSeen: string;
  status: 'active' | 'mitigated' | 'resolved';
}

const AdminSecurityDashboardPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);

  // Tab and filter state
  const [activeTab, setActiveTab] = useState('overview');
  const [eventSearchTerm, setEventSearchTerm] = useState('');
  const [eventSeverityFilter, setEventSeverityFilter] = useState<string>('all');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [eventTimeframeFilter, setEventTimeframeFilter] = useState<string>('all');
  const [threatSearchTerm, setThreatSearchTerm] = useState('');
  const [threatStatusFilter, setThreatStatusFilter] = useState<string>('all');
  const [threatRiskFilter, setThreatRiskFilter] = useState<string>('all');

  // Legacy filters (keeping for compatibility)
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<string>('24h');

  // Real security events from API
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);

  // Real threat alerts from API
  const [threatAlerts, setThreatAlerts] = useState<ThreatAlert[]>([]);

  useEffect(() => {
    // Load real security data from API
    const loadSecurityData = async () => {
      setIsLoading(true);
      try {
        // Try to fetch real security data from admin API
        const [overviewResponse, alertsResponse] = await Promise.all([
          fetch('/api/v1/admin/security/overview', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
          }),
          fetch('/api/v1/admin/security/alerts', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
          })
        ]);

        if (overviewResponse.ok) {
          const overviewData = await overviewResponse.json();
          // Map backend data to frontend format
          const realMetrics: SecurityMetrics = {
            totalThreats: 0, // Calculate from alerts
            activeThreats: 0, // Calculate from alerts
            blockedAttempts: 0, // Not available in current API
            suspiciousLogins: overviewData.suspicious_ips_count || 0,
            failedLogins: overviewData.failed_logins_24h || 0,
            successfulLogins: overviewData.successful_logins_24h || 0,
            uniqueUsers: overviewData.active_users_7d || 0,
            newDevices: 0, // Not available in current API
            riskScore: (100 - (overviewData.security_score || 100)) / 10, // Convert to 0-10 scale
            lastUpdated: new Date().toISOString()
          };
          setMetrics(realMetrics);
        } else {
          console.log('Security overview API not available:', overviewResponse.status);
          // Show empty state instead of mock data
          setMetrics({
            totalThreats: 0,
            activeThreats: 0,
            blockedAttempts: 0,
            suspiciousLogins: 0,
            failedLogins: 0,
            successfulLogins: 0,
            uniqueUsers: 0,
            newDevices: 0,
            riskScore: 0,
            lastUpdated: new Date().toISOString()
          });
        }

        if (alertsResponse.ok) {
          const alertsData = await alertsResponse.json();
          // Convert backend alerts to frontend format
          const realEvents: SecurityEvent[] = alertsData.map((alert: any) => ({
            id: alert.id,
            type: alert.type || 'login_failure',
            severity: alert.severity || 'medium',
            user: alert.details?.user_id || 'Unknown',
            description: alert.message || 'Security event',
            ipAddress: alert.ip_address || 'Unknown',
            location: 'Unknown', // Not available in current API
            device: alert.user_agent || 'Unknown',
            timestamp: alert.timestamp,
            status: 'active'
          }));
          setSecurityEvents(realEvents);
        } else {
          console.log('Security alerts API not available:', alertsResponse.status);
          setSecurityEvents([]);
        }

        // No real threat alerts API yet, keep empty
        setThreatAlerts([]);

      } catch (error) {
        console.error('Error loading security data:', error);
        // Show empty state instead of mock data
        setMetrics({
          totalThreats: 0,
          activeThreats: 0,
          blockedAttempts: 0,
          suspiciousLogins: 0,
          failedLogins: 0,
          successfulLogins: 0,
          uniqueUsers: 0,
          newDevices: 0,
          riskScore: 0,
          lastUpdated: new Date().toISOString()
        });
        setSecurityEvents([]);
        setThreatAlerts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadSecurityData();
  }, [toast]);

  const refreshData = () => {
    setMetrics(null);
    setIsLoading(true);
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'high': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'medium': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'low': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'login_failure': return <Lock className="h-4 w-4" />;
      case 'suspicious_login': return <AlertTriangle className="h-4 w-4" />;
      case 'blocked_ip': return <Ban className="h-4 w-4" />;
      case 'new_device': return <Smartphone className="h-4 w-4" />;
      case 'password_reset': return <RefreshCw className="h-4 w-4" />;
      case 'account_locked': return <Lock className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Filter categories
  const eventFilterCategories = getSecurityEventFilterCategories(
    intl,
    eventSeverityFilter,
    eventTypeFilter,
    eventTimeframeFilter,
    (value: string | string[]) => setEventSeverityFilter(Array.isArray(value) ? value[0] : value),
    (value: string | string[]) => setEventTypeFilter(Array.isArray(value) ? value[0] : value),
    (value: string | string[]) => setEventTimeframeFilter(Array.isArray(value) ? value[0] : value)
  );

  const threatFilterCategories = getThreatFilterCategories(
    intl,
    threatStatusFilter,
    threatRiskFilter,
    (value: string | string[]) => setThreatStatusFilter(Array.isArray(value) ? value[0] : value),
    (value: string | string[]) => setThreatRiskFilter(Array.isArray(value) ? value[0] : value)
  );

  // Reset functions
  const resetEventFilters = () => {
    setEventSearchTerm('');
    setEventSeverityFilter('all');
    setEventTypeFilter('all');
    setEventTimeframeFilter('all');
  };

  const resetThreatFilters = () => {
    setThreatSearchTerm('');
    setThreatStatusFilter('all');
    setThreatRiskFilter('all');
  };

  // Filtered data
  const filteredEvents = useMemo(() => {
    try {
      return filterSecurityEvents(
        securityEvents,
        eventSearchTerm,
        eventSeverityFilter,
        eventTypeFilter,
        eventTimeframeFilter
      );
    } catch (error) {
      console.error('Error filtering security events:', error);
      return [];
    }
  }, [securityEvents, eventSearchTerm, eventSeverityFilter, eventTypeFilter, eventTimeframeFilter]);

  const filteredThreats = useMemo(() => {
    try {
      return filterThreats(
        threatAlerts,
        threatSearchTerm,
        threatStatusFilter,
        threatRiskFilter
      );
    } catch (error) {
      console.error('Error filtering threats:', error);
      return [];
    }
  }, [threatAlerts, threatSearchTerm, threatStatusFilter, threatRiskFilter]);

  // Legacy filtered events (keeping for compatibility)
  const legacyFilteredEvents = securityEvents.filter(event => {
    const matchesSearch = event.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.ipAddress.includes(searchTerm);
    const matchesSeverity = severityFilter === 'all' || event.severity === severityFilter;
    return matchesSearch && matchesSeverity;
  });

  // Tab configuration
  const tabs = [
    { id: 'overview', label: intl.formatMessage({ id: 'admin.securityDashboard.tabs.overview' }) },
    { id: 'threats', label: intl.formatMessage({ id: 'admin.securityDashboard.tabs.threats' }) },
    { id: 'events', label: intl.formatMessage({ id: 'admin.securityDashboard.tabs.events' }) },
    { id: 'analytics', label: intl.formatMessage({ id: 'admin.securityDashboard.tabs.analytics' }) }
  ];

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            {intl.formatMessage({ id: 'admin.securityDashboard.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.securityDashboard.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">{intl.formatMessage({ id: 'admin.securityDashboard.timeRange.lastHour' })}</SelectItem>
              <SelectItem value="24h">{intl.formatMessage({ id: 'admin.securityDashboard.timeRange.last24h' })}</SelectItem>
              <SelectItem value="7d">{intl.formatMessage({ id: 'admin.securityDashboard.timeRange.last7d' })}</SelectItem>
              <SelectItem value="30d">{intl.formatMessage({ id: 'admin.securityDashboard.timeRange.last30d' })}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'admin.securityDashboard.refresh' })}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'admin.securityDashboard.exportReport' })}
          </Button>
        </div>
      </div>

      {/* Security Risk Alert */}
      {metrics && metrics.riskScore > 7 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>{intl.formatMessage({ id: 'admin.securityDashboard.alerts.elevatedRisk' })}</strong> {intl.formatMessage({ id: 'admin.securityDashboard.alerts.riskScoreMessage' }, { score: metrics.riskScore, threats: metrics.activeThreats })}
          </AlertDescription>
        </Alert>
      )}

      {/* Security Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.securityDashboard.metrics.activeThreats' })}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{metrics?.activeThreats}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.totalThreats} {intl.formatMessage({ id: 'admin.securityDashboard.metrics.totalDetected' })}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.securityDashboard.metrics.blockedAttempts' })}</CardTitle>
            <Ban className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.blockedAttempts}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'admin.securityDashboard.metrics.last24h' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.securityDashboard.metrics.failedLogins' })}</CardTitle>
            <Lock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.failedLogins}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.suspiciousLogins} {intl.formatMessage({ id: 'admin.securityDashboard.metrics.suspicious' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.securityDashboard.metrics.riskScore' })}</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.riskScore}/10</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'admin.securityDashboard.metrics.securityRiskLevel' })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Security Dashboard Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          size="md"
          variant="default"
        />

        {activeTab === 'overview' && (
          <div className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.overview.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'admin.securityDashboard.overview.description' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.overview.title' })}</h3>
                  <p className="text-muted-foreground mb-4">
                    {intl.formatMessage({ id: 'admin.securityDashboard.overview.monitorHealth' })}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'threats' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
              title={intl.formatMessage({ id: 'admin.securityDashboard.threats.filterTitle' })}
              filterCategories={threatFilterCategories}
              searchQuery={threatSearchTerm}
              onSearchChange={setThreatSearchTerm}
              onClearSearch={() => setThreatSearchTerm('')}
              onResetAll={resetThreatFilters}
              variant="default"
              showIcons={true}
              showSearchBar={true}
              searchPlaceholder={intl.formatMessage({ id: 'admin.securityDashboard.threats.searchPlaceholder' })}
              searchHotkey="cmd+f"
              size="md"
            />
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.threats.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'admin.securityDashboard.threats.description' }, { count: filteredThreats.length })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredThreats.length > 0 ? (
                  <div className="space-y-4">
                    {filteredThreats.map((alert) => (
                  <div key={alert.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getSeverityIcon(alert.severity)}
                        <div>
                          <h4 className="font-medium">{alert.title}</h4>
                          <p className="text-sm text-muted-foreground">{alert.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getSeverityColor(alert.severity) as any}>
                          {alert.severity.toUpperCase()}
                        </Badge>
                        <Badge variant="outline">
                          {alert.affectedUsers} users
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>First seen: {formatTimestamp(alert.firstSeen)}</span>
                      <span>Last seen: {formatTimestamp(alert.lastSeen)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button size="sm">Investigate</Button>
                      <Button size="sm" variant="outline">Mitigate</Button>
                      <Button size="sm" variant="outline">Mark Resolved</Button>
                    </div>
                  </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.threats.noThreats' })}</h3>
                    <p className="text-muted-foreground mb-4">
                      {threatSearchTerm || threatStatusFilter !== 'all' || threatRiskFilter !== 'all'
                        ? intl.formatMessage({ id: 'admin.securityDashboard.threats.noThreatsFiltered' })
                        : intl.formatMessage({ id: 'admin.securityDashboard.threats.noActiveThreats' })
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'events' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
              title={intl.formatMessage({ id: 'admin.securityDashboard.events.filterTitle' })}
              filterCategories={eventFilterCategories}
              searchQuery={eventSearchTerm}
              onSearchChange={setEventSearchTerm}
              onClearSearch={() => setEventSearchTerm('')}
              onResetAll={resetEventFilters}
              variant="default"
              showIcons={true}
              showSearchBar={true}
              searchPlaceholder={intl.formatMessage({ id: 'admin.securityDashboard.events.searchPlaceholder' })}
              searchHotkey="cmd+f"
              size="md"
            />

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.events.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'admin.securityDashboard.events.description' }, { count: filteredEvents.length })}
                </CardDescription>
              </CardHeader>
              <CardContent>

                {filteredEvents.length > 0 ? (
                  <div className="space-y-3">
                    {filteredEvents.map((event) => (
                      <div key={event.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            {getEventTypeIcon(event.type)}
                            <div>
                              <h4 className="font-medium">{event.description}</h4>
                              <p className="text-sm text-muted-foreground">User: {event.user}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getSeverityIcon(event.severity)}
                            <Badge variant={getSeverityColor(event.severity) as any}>
                              {event.severity}
                            </Badge>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Globe className="h-3 w-3" />
                            {event.ipAddress}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {event.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <Monitor className="h-3 w-3" />
                            {event.device}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatTimestamp(event.timestamp)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.events.noEvents' })}</h3>
                    <p className="text-muted-foreground mb-4">
                      {eventSearchTerm || eventSeverityFilter !== 'all' || eventTypeFilter !== 'all' || eventTimeframeFilter !== 'all'
                        ? intl.formatMessage({ id: 'admin.securityDashboard.events.noEventsFiltered' })
                        : intl.formatMessage({ id: 'admin.securityDashboard.events.noEventsRecorded' })
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.analytics.loginSuccessRate' })}</CardTitle>
                  <CardDescription>{intl.formatMessage({ id: 'admin.securityDashboard.analytics.authenticationSuccessMetrics' })}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="text-4xl font-bold text-green-600 mb-2">
                      {metrics && ((metrics.successfulLogins / (metrics.successfulLogins + metrics.failedLogins)) * 100).toFixed(1)}%
                    </div>
                    <p className="text-muted-foreground">
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.successfulFailed' }, { successful: metrics?.successfulLogins, failed: metrics?.failedLogins })}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.analytics.deviceSecurity' })}</CardTitle>
                  <CardDescription>{intl.formatMessage({ id: 'admin.securityDashboard.analytics.newDeviceRegistrations' })}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="text-4xl font-bold text-blue-600 mb-2">
                      {metrics?.newDevices}
                    </div>
                    <p className="text-muted-foreground">
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.newDevicesToday' })}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.securityDashboard.analytics.securityReports' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'admin.securityDashboard.analytics.generateAndDownload' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div className="border rounded-lg p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.analytics.dailySecurityReport' })}</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.dailySecurityReportDesc' })}
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.download' })}
                    </Button>
                  </div>

                  <div className="border rounded-lg p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.analytics.threatAnalysis' })}</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.threatAnalysisDesc' })}
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.download' })}
                    </Button>
                  </div>

                  <div className="border rounded-lg p-4 text-center">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'admin.securityDashboard.analytics.complianceReport' })}</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.complianceReportDesc' })}
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'admin.securityDashboard.analytics.download' })}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSecurityDashboardPage;
