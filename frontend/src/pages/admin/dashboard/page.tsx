/**
 * Admin Dashboard
 *
 * Main admin dashboard with system overview, analytics, and management tools.
 * Integrates enhanced analytics while maintaining original structure.
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Users,
  Settings,
  BarChart3,
  Shield,
  Activity,
  Clock,
  RefreshCw,
  Building2
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';

import { Button } from '@/components/ui/shadcn/button';
import StyledTabs from '@/components/common/styled-tabs';
import { AdminAnalyticsDashboard } from '@/components/features/admin/admin-analytics-dashboard';
import { PageLoader } from '@/components/base/page-loader';
import { useAdminSystemAnalytics, useAdminUsers, useAdminApplications } from '@/hooks/use-admin-api';
import useAuth from '@/hooks/use-auth';
import { Badge } from '@/components/ui/shadcn/badge';
import FilterPanel from '@/components/common/filter-panel';
import { 
  getUserFilterCategories, 
  getApplicationFilterCategories,
  filterUsers,
  filterApplications
} from '@/data/admin-filters';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalApplications: number;
  activeApplications: number;
  pendingApprovals: number;
  totalLogins: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastUpdated: string;
}

const AdminDashboard: React.FC = () => {
  const intl = useIntl();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [userStatusFilter, setUserStatusFilter] = useState<string>('all');
  const [userRoleFilters, setUserRoleFilters] = useState<string[]>([]);
  const [appSearchTerm, setAppSearchTerm] = useState('');
  const [appStatusFilter, setAppStatusFilter] = useState<string>('all');
  const [appApprovalFilter, setAppApprovalFilter] = useState<string>('all');
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Get filter categories from data file
  const userFilterCategories = getUserFilterCategories(
    intl,
    userStatusFilter,
    userRoleFilters,
    (selected: string | string[]) => typeof selected === 'string' ? setUserStatusFilter(selected) : setUserStatusFilter(selected[0] || 'all'),
    (selected: string | string[]) => Array.isArray(selected) ? setUserRoleFilters(selected) : setUserRoleFilters([selected])
  );

  const appFilterCategories = getApplicationFilterCategories(
    intl,
    appStatusFilter,
    appApprovalFilter,
    (selected: string | string[]) => typeof selected === 'string' ? setAppStatusFilter(selected) : setAppStatusFilter(selected[0] || 'all'),
    (selected: string | string[]) => typeof selected === 'string' ? setAppApprovalFilter(selected) : setAppApprovalFilter(selected[0] || 'all')
  );

  // Only load analytics after authentication is confirmed
  const { analytics } = useAdminSystemAnalytics(isAuthenticated && !authLoading);

  // Create stable parameter objects to prevent infinite loops
  const usersParams = React.useMemo(() => ({}), []);
  const appsParams = React.useMemo(() => ({}), []);

  // Load users and applications data
  const { users, isLoading: usersLoading } = useAdminUsers(usersParams, isAuthenticated && !authLoading);
  const { applications, isLoading: appsLoading } = useAdminApplications(appsParams, isAuthenticated && !authLoading);

  useEffect(() => {
    // Load real stats from analytics API
    const loadStats = async () => {
      setIsLoading(true);

      // Only use real analytics data, no mock fallbacks
      if (analytics) {
        const realStats: DashboardStats = {
          totalUsers: analytics.total_users || 0,
          activeUsers: 0, // Not available in current analytics API
          totalApplications: analytics.total_applications || 0,
          activeApplications: analytics.active_applications || 0,
          pendingApprovals: analytics.pending_approvals || 0,
          totalLogins: analytics.total_logins_today || 0,
          systemHealth: 'healthy', // Default value since not in analytics
          lastUpdated: new Date().toISOString(),
        };
        setStats(realStats);
      } else {
        // No analytics data available - show zeros
        const emptyStats: DashboardStats = {
          totalUsers: 0,
          activeUsers: 0,
          totalApplications: 0,
          activeApplications: 0,
          pendingApprovals: 0,
          totalLogins: 0,
          systemHealth: 'healthy',
          lastUpdated: new Date().toISOString(),
        };
        setStats(emptyStats);
      }

      setIsLoading(false);
    };

    loadStats();
  }, [analytics]);

  // Filter users based on search and status with comprehensive error handling
  const filteredUsers = React.useMemo(() => {
    try {
      return filterUsers(users, userSearchTerm, userStatusFilter, userRoleFilters);
    } catch (error) {
      console.error('Error filtering users:', error);
      return [];
    }
  }, [users, userSearchTerm, userStatusFilter, userRoleFilters]);

  // Filter applications based on search and status with comprehensive error handling
  const filteredApplications = React.useMemo(() => {
    try {
      return filterApplications(applications, appSearchTerm, appStatusFilter, appApprovalFilter);
    } catch (error) {
      console.error('Error filtering applications:', error);
      return [];
    }
  }, [applications, appSearchTerm, appStatusFilter, appApprovalFilter]);

  // Reset functions for filters
  const resetUserFilters = () => {
    setUserSearchTerm('');
    setUserStatusFilter('all');
    setUserRoleFilters([]);
  };

  const resetAppFilters = () => {
    setAppSearchTerm('');
    setAppStatusFilter('all');
    setAppApprovalFilter('all');
  };

  const refreshData = () => {
    setStats(null);
    setIsLoading(true);
    // Trigger refresh
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'admin.dashboard.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.dashboard.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'admin.dashboard.refresh' })}
          </Button>
          <Button asChild>
            <Link to="/admin/system/settings">
              <Settings className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'admin.dashboard.systemSettings' })}
            </Link>
          </Button>
        </div>
      </div>

      {/* Enhanced Dashboard with Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={[
            { id: 'overview', label: intl.formatMessage({ id: 'admin.dashboard.tabs.overview' }) },
            { id: 'analytics', label: intl.formatMessage({ id: 'admin.dashboard.tabs.analytics' }) },
            { id: 'users', label: intl.formatMessage({ id: 'admin.dashboard.tabs.users' }) },
            { id: 'applications', label: intl.formatMessage({ id: 'admin.dashboard.tabs.applications' }) }
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="default"
          size="md"
        />

        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.dashboard.stats.totalUsers' })}</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalUsers.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.activeUsers} {intl.formatMessage({ id: 'admin.dashboard.stats.activeUsers' })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.dashboard.stats.applications' })}</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalApplications}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.activeApplications} {intl.formatMessage({ id: 'admin.dashboard.stats.applicationsActive' })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.dashboard.stats.todaysLogins' })}</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalLogins}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.dashboard.stats.authRequests' })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'admin.dashboard.stats.pendingApprovals' })}</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.pendingApprovals}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'admin.dashboard.stats.awaitingReview' })}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.dashboard.quickActions.userManagement.title' })}</CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'admin.dashboard.quickActions.userManagement.description' })}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link to="/admin/users">
                    <Users className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'admin.dashboard.quickActions.userManagement.button' })}
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.dashboard.quickActions.applicationManagement.title' })}</CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'admin.dashboard.quickActions.applicationManagement.description' })}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link to="/admin/applications">
                    <Shield className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'admin.dashboard.quickActions.applicationManagement.button' })}
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'admin.dashboard.quickActions.systemAnalytics.title' })}</CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'admin.dashboard.quickActions.systemAnalytics.description' })}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full" variant="outline">
                  <Link to="/admin/analytics">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'admin.dashboard.quickActions.systemAnalytics.button' })}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <AdminAnalyticsDashboard />
          </div>
        )}

        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
            title="Filter Users"
            filterCategories={userFilterCategories}
            searchQuery={userSearchTerm}
            onSearchChange={setUserSearchTerm}
            onClearSearch={() => setUserSearchTerm('')}
            onResetAll={resetUserFilters}
            variant="default"
            showIcons={true}
            showSearchBar={true}
            searchPlaceholder={intl.formatMessage({ id: 'admin.dashboard.users.searchPlaceholder' })}
            searchHotkey="cmd+f"
            size="md"
          />

          {/* Users List Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{intl.formatMessage({ id: 'admin.dashboard.users.title' })}</span>
                <Button asChild variant="outline" size="sm">
                  <Link to="/admin/users">
                    {intl.formatMessage({ id: 'admin.dashboard.users.viewAllUsers' })}
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.dashboard.users.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Users List */}
              {usersLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 bg-card rounded-lg shadow-sm border">
                      <div className="h-10 w-10 bg-muted rounded-full animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded animate-pulse w-1/3"></div>
                        <div className="h-3 bg-muted rounded animate-pulse w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.dashboard.users.noUsersFound' })}</h3>
                  <p className="text-muted-foreground">
                    {userSearchTerm || userStatusFilter !== 'all' || userRoleFilters.length > 0
                      ? intl.formatMessage({ id: 'admin.dashboard.users.noUsersFiltered' })
                      : intl.formatMessage({ id: 'admin.dashboard.users.noUsersRegistered' })}
                  </p>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {(filteredUsers || []).slice(0, 10).map((user) => (
                    <div key={user?.id || Math.random()} className="flex items-center justify-between p-4 bg-card rounded-lg shadow-sm border hover:bg-accent transition-colors duration-200">
                      <div className="flex items-center space-x-4">
                        <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <p className="font-medium">{user?.full_name || user?.email || intl.formatMessage({ id: 'admin.dashboard.users.unknownUser' })}</p>
                          <p className="text-sm text-muted-foreground">{user?.email || intl.formatMessage({ id: 'admin.dashboard.users.noEmail' })}</p>
                          <p className="text-xs text-muted-foreground">
                            {intl.formatMessage({ id: 'admin.dashboard.users.role' })}: {user?.role || intl.formatMessage({ id: 'admin.dashboard.users.unknown' })} • {intl.formatMessage({ id: 'admin.dashboard.users.joined' })}: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : intl.formatMessage({ id: 'admin.dashboard.users.unknown' })}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={user?.is_active ? "default" : "secondary"}>
                          {user?.is_active ? intl.formatMessage({ id: 'admin.dashboard.users.active' }) : intl.formatMessage({ id: 'admin.dashboard.users.inactive' })}
                        </Badge>
                        <Badge variant={user?.is_verified ? "default" : "outline"}>
                          {user?.is_verified ? intl.formatMessage({ id: 'admin.dashboard.users.verified' }) : intl.formatMessage({ id: 'admin.dashboard.users.unverified' })}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {filteredUsers.length > 10 && (
                    <div className="text-center py-4">
                      <Button asChild variant="outline">
                        <Link to="/admin/users">
                          {intl.formatMessage({ id: 'admin.dashboard.users.viewAllCount' }, { count: filteredUsers.length })}
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        )}

        {activeTab === 'applications' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
            title={intl.formatMessage({ id: 'admin.dashboard.applications.filterTitle' })}
            filterCategories={appFilterCategories}
            searchQuery={appSearchTerm}
            onSearchChange={setAppSearchTerm}
            onClearSearch={() => setAppSearchTerm('')}
            onResetAll={resetAppFilters}
            variant="default"
            showIcons={true}
            showSearchBar={true}
            searchPlaceholder={intl.formatMessage({ id: 'admin.dashboard.applications.searchPlaceholder' })}
            searchHotkey="cmd+f"
            size="md"
          />

          {/* Applications List Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{intl.formatMessage({ id: 'admin.dashboard.applications.title' })}</span>
                <Button asChild variant="outline" size="sm">
                  <Link to="/admin/applications">
                    {intl.formatMessage({ id: 'admin.dashboard.applications.viewAllApplications' })}
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'admin.dashboard.quickActions.applicationManagement.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Applications List */}
              {appsLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 bg-card rounded-lg shadow-sm border">
                      <div className="h-10 w-10 bg-muted rounded-full animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded animate-pulse w-1/3"></div>
                        <div className="h-3 bg-muted rounded animate-pulse w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredApplications.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Applications Found</h3>
                  <p className="text-muted-foreground">
                    {appSearchTerm || appStatusFilter !== 'all' || appApprovalFilter !== 'all'
                      ? 'Try adjusting your search criteria or filters'
                      : 'No applications have been registered yet'}
                  </p>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {(filteredApplications || []).slice(0, 10).map((app) => (
                    <div key={app?.id || Math.random()} className="flex items-center justify-between p-4 bg-card rounded-lg shadow-sm border hover:bg-accent transition-colors duration-200">
                      <div className="flex items-center space-x-4">
                        <div className="h-10 w-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                          <Building2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                          <p className="font-medium">{app?.application_name || 'Unknown Application'}</p>
                          <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'admin.dashboard.applications.owner' })}: {app?.owner_email || 'Unknown'}</p>
                          <p className="text-xs text-muted-foreground">
                            {intl.formatMessage({ id: 'admin.dashboard.applications.clientId' })}: {app?.client_id || 'Unknown'} • {intl.formatMessage({ id: 'admin.dashboard.applications.created' })}: {app?.created_at ? new Date(app.created_at).toLocaleDateString() : 'Unknown'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={app?.is_approved ? "default" : "secondary"}>
                          {app?.is_approved ? intl.formatMessage({ id: 'admin.dashboard.applications.approved' }) : intl.formatMessage({ id: 'admin.dashboard.applications.pending' })}
                        </Badge>
                        <Badge variant={app?.is_active ? "default" : "outline"}>
                          {app?.is_active ? intl.formatMessage({ id: 'admin.dashboard.applications.active' }) : intl.formatMessage({ id: 'admin.dashboard.applications.inactive' })}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {filteredApplications.length > 10 && (
                    <div className="text-center py-4">
                      <Button asChild variant="outline">
                        <Link to="/admin/applications">
                          View All {filteredApplications.length} Applications
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
