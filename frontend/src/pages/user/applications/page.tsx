import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Globe,
  Shield,
  Eye,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  MoreVertical
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Input } from '@/components/ui/shadcn/input';
import { AdvancedDataTable } from '@/components/common/data-table/advanced-data-table';
import { PageLoader } from '@/components/base/page-loader';
import { useUserApi } from '@/hooks/use-user-api';
import type { ConnectedApplication } from '@/types/api/user';

/**
 * Connected Applications Management Page
 * 
 * Allows end users to view and manage applications that have access to their account,
 * including viewing permissions, revoking access, and monitoring application activity.
 */
const ConnectedApplicationsPage: React.FC = () => {
  const intl = useIntl();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');

  const { applications, isLoading, revokeAccess } = useUserApi();

  const handleRevokeAccess = async (applicationId: string, applicationName: string) => {
    if (window.confirm(intl.formatMessage({ id: 'user.applications.revokeConfirm' }, { name: applicationName }))) {
      try {
        await revokeAccess(applicationId);
        // Refresh data or show success message
        window.location.reload();
      } catch (error) {
        console.error('Failed to revoke access:', error);
        alert(intl.formatMessage({ id: 'user.applications.revokeError' }));
      }
    }
  };

  const filteredApplications = applications?.applications?.filter((app: ConnectedApplication) => {
    const matchesSearch = (app.application_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (app.developer_email?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'active' && app.is_active) ||
                         (filterStatus === 'inactive' && !app.is_active);
    return matchesSearch && matchesFilter;
  }) || [];

  const columns = [
    {
      key: 'application_name' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.application' }),
      sortable: true,
      render: (value: any, app: ConnectedApplication) => (
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${app.is_active ? 'bg-green-500' : 'bg-gray-400'}`} />
          <div>
            <div className="font-medium">{app.application_name}</div>
            <div className="text-sm text-muted-foreground">{app.description || intl.formatMessage({ id: 'user.applications.table.noDescription' })}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'developer_email' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.developer' }),
      sortable: true,
      render: (value: any, app: ConnectedApplication) => (
        <div className="text-sm">{app.developer_email}</div>
      ),
    },
    {
      key: 'connected_at' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.connected' }),
      sortable: true,
      render: (value: any, app: ConnectedApplication) => (
        <div className="text-sm">
          {new Date(app.connected_at).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'last_access' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.lastAccess' }),
      sortable: true,
      render: (value: any, app: ConnectedApplication) => (
        <div className="text-sm">
          {app.last_access ? new Date(app.last_access).toLocaleDateString() : intl.formatMessage({ id: 'user.applications.table.never' })}
        </div>
      ),
    },
    {
      key: 'granted_scopes' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.permissions' }),
      render: (value: any, app: ConnectedApplication) => (
        <div className="flex flex-wrap gap-1">
          {(app.granted_scopes || []).slice(0, 2).map((scope: string, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {scope}
            </Badge>
          ))}
          {(app.granted_scopes?.length || 0) > 2 && (
            <Badge variant="outline" className="text-xs">
              {intl.formatMessage({ id: 'user.applications.table.morePermissions' }, { count: (app.granted_scopes?.length || 0) - 2 })}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'is_active' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.status' }),
      sortable: true,
      render: (value: any, app: ConnectedApplication) => (
        <Badge variant={app.is_active ? "default" : "secondary"}>
          {app.is_active ? intl.formatMessage({ id: 'user.applications.table.active' }) : intl.formatMessage({ id: 'user.applications.table.inactive' })}
        </Badge>
      ),
    },
    {
      key: 'application_id' as keyof ConnectedApplication,
      title: intl.formatMessage({ id: 'user.applications.table.actions' }),
      render: (value: any, app: ConnectedApplication) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            asChild
          >
            <Link to={`/user/applications/${app.application_id}`}>
              <Eye className="h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleRevokeAccess(app.application_id, app.application_name)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'user.applications.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.applications.subtitle' })}
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.applications.stats.total' })}</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{applications?.applications?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.applications.stats.total.description' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.applications.stats.active' })}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications?.applications?.filter((app: ConnectedApplication) => app.is_active).length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.applications.stats.active.description' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.applications.stats.permissions' })}</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications?.applications?.reduce((total: number, app: ConnectedApplication) => total + (app.granted_scopes?.length || 0), 0) || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.applications.stats.permissions.description' })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.applications.management.title' })}</CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'user.applications.management.description' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder={intl.formatMessage({ id: 'user.applications.management.searchPlaceholder' })}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('all')}
              >
                {intl.formatMessage({ id: 'user.applications.management.filterAll' })}
              </Button>
              <Button
                variant={filterStatus === 'active' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('active')}
              >
                {intl.formatMessage({ id: 'user.applications.management.filterActive' })}
              </Button>
              <Button
                variant={filterStatus === 'inactive' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('inactive')}
              >
                {intl.formatMessage({ id: 'user.applications.management.filterInactive' })}
              </Button>
            </div>
          </div>

          {/* Applications Table */}
          <AdvancedDataTable
            data={filteredApplications}
            columns={columns}
            searchable={false} // We handle search above
            bulkActions={[
              {
                label: intl.formatMessage({ id: 'user.applications.management.bulkRevoke' }),
                action: (selectedItems) => {
                  if (window.confirm(intl.formatMessage({ id: 'user.applications.bulkRevokeConfirm' }, { count: selectedItems.length }))) {
                    selectedItems.forEach(item => {
                      revokeAccess(item.application_id);
                    });
                    window.location.reload();
                  }
                },
                variant: 'destructive'
              }
            ]}
            emptyMessage={intl.formatMessage({ id: 'user.applications.management.emptyMessage' })}
          />
        </CardContent>
      </Card>

      {/* Security Notice */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">{intl.formatMessage({ id: 'user.applications.security.title' })}</h3>
              <p className="text-sm text-yellow-700 mt-1">
                {intl.formatMessage({ id: 'user.applications.security.description' })}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConnectedApplicationsPage;
