import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Key,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  AlertTriangle,
  Info,
  Lock,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Progress } from '@/components/ui/animate-ui/radix/progress';
import { useToast } from '@/hooks/use-toast';
import { useUserSecurityOverview } from '@/hooks/use-user-api';

/**
 * User Password Management Page
 * 
 * Allows users to change their password with strength validation
 * and security recommendations.
 */
const UserPasswordManagementPage: React.FC = () => {
  const intl = useIntl();
  const { securityOverview, isLoading } = useUserSecurityOverview();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Password strength calculation
  const calculatePasswordStrength = (password: string) => {
    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      noCommon: !['password', '123456', 'qwerty', 'admin'].includes(password.toLowerCase())
    };
    
    Object.values(checks).forEach(check => {
      if (check) score += 1;
    });
    
    return {
      score: Math.min(score * 20, 100),
      checks,
      strength: score < 3 ? intl.formatMessage({ id: 'user.password.strength.weak' }) :
                score < 5 ? intl.formatMessage({ id: 'user.password.strength.fair' }) :
                score < 6 ? intl.formatMessage({ id: 'user.password.strength.good' }) :
                intl.formatMessage({ id: 'user.password.strength.strong' })
    };
  };

  const passwordStrength = calculatePasswordStrength(formData.newPassword);

  const getStrengthColor = (strength: string) => {
    const weakText = intl.formatMessage({ id: 'user.password.strength.weak' });
    const fairText = intl.formatMessage({ id: 'user.password.strength.fair' });
    const goodText = intl.formatMessage({ id: 'user.password.strength.good' });
    const strongText = intl.formatMessage({ id: 'user.password.strength.strong' });

    switch (strength) {
      case weakText: return 'text-red-600 dark:text-red-400';
      case fairText: return 'text-yellow-600 dark:text-yellow-400';
      case goodText: return 'text-blue-600 dark:text-blue-400';
      case strongText: return 'text-green-600 dark:text-green-400';
      default: return 'text-muted-foreground';
    }
  };

  const getProgressColor = (strength: string) => {
    const weakText = intl.formatMessage({ id: 'user.password.strength.weak' });
    const fairText = intl.formatMessage({ id: 'user.password.strength.fair' });
    const goodText = intl.formatMessage({ id: 'user.password.strength.good' });
    const strongText = intl.formatMessage({ id: 'user.password.strength.strong' });

    switch (strength) {
      case weakText: return 'bg-red-500 dark:bg-red-600';
      case fairText: return 'bg-yellow-500 dark:bg-yellow-600';
      case goodText: return 'bg-blue-500 dark:bg-blue-600';
      case strongText: return 'bg-green-500 dark:bg-green-600';
      default: return 'bg-muted';
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.currentPassword) {
      newErrors.currentPassword = intl.formatMessage({ id: 'user.password.error.currentRequired' });
    }

    if (!formData.newPassword) {
      newErrors.newPassword = intl.formatMessage({ id: 'user.password.error.newRequired' });
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = intl.formatMessage({ id: 'user.password.error.minLength' });
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = intl.formatMessage({ id: 'user.password.error.confirmRequired' });
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = intl.formatMessage({ id: 'user.password.error.passwordMismatch' });
    }

    if (formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = intl.formatMessage({ id: 'user.password.error.samePassword' });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: intl.formatMessage({ id: 'user.password.success.title' }),
        description: intl.formatMessage({ id: 'user.password.success.description' }),
      });

      // Reset form
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'user.password.error.title' }),
        description: intl.formatMessage({ id: 'user.password.error.description' }),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const getPasswordLastChanged = () => {
    if (!securityOverview?.password_last_changed) return intl.formatMessage({ id: 'user.password.security.never' });

    const lastChanged = new Date(securityOverview.password_last_changed);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - lastChanged.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return intl.formatMessage({ id: 'user.password.security.dayAgo' });
    if (diffDays < 30) return intl.formatMessage({ id: 'user.password.security.daysAgo' }, { days: diffDays });
    if (diffDays < 365) return intl.formatMessage({ id: 'user.password.security.monthsAgo' }, { months: Math.floor(diffDays / 30) });
    return intl.formatMessage({ id: 'user.password.security.yearsAgo' }, { years: Math.floor(diffDays / 365) });
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Key className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.password.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.password.subtitle' })}
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Password Change Form */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.password.changePassword' })}</CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.password.changePassword.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Current Password */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword">{intl.formatMessage({ id: 'user.password.currentPassword' })}</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showPasswords.current ? 'text' : 'password'}
                    value={formData.currentPassword}
                    onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.password.currentPassword.placeholder' })}
                    className={errors.currentPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => togglePasswordVisibility('current')}
                  >
                    {showPasswords.current ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.currentPassword && (
                  <p className="text-sm text-red-600">{errors.currentPassword}</p>
                )}
              </div>

              {/* New Password */}
              <div className="space-y-2">
                <Label htmlFor="newPassword">{intl.formatMessage({ id: 'user.password.newPassword' })}</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showPasswords.new ? 'text' : 'password'}
                    value={formData.newPassword}
                    onChange={(e) => handleInputChange('newPassword', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.password.newPassword.placeholder' })}
                    className={errors.newPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => togglePasswordVisibility('new')}
                  >
                    {showPasswords.new ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.newPassword && (
                  <p className="text-sm text-red-600">{errors.newPassword}</p>
                )}
                
                {/* Password Strength Indicator */}
                {formData.newPassword && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{intl.formatMessage({ id: 'user.password.strength.label' })}</span>
                      <span className={`text-sm font-medium ${getStrengthColor(passwordStrength.strength)}`}>
                        {passwordStrength.strength}
                      </span>
                    </div>
                    <Progress
                      value={passwordStrength.score}
                      className="h-2"
                    />
                  </div>
                )}
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{intl.formatMessage({ id: 'user.password.confirmPassword' })}</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showPasswords.confirm ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.password.confirmPassword.placeholder' })}
                    className={errors.confirmPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => togglePasswordVisibility('confirm')}
                  >
                    {showPasswords.confirm ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting || passwordStrength.strength === intl.formatMessage({ id: 'user.password.strength.weak' })}
              >
                {isSubmitting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {intl.formatMessage({ id: 'user.password.updating' })}
                  </>
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'user.password.updatePassword' })}
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Password Requirements & Info */}
        <div className="space-y-6">
          {/* Password Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.password.requirements.title' })}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {formData.newPassword && (
                <div className="space-y-2">
                  {Object.entries(passwordStrength.checks).map(([key, passed]) => {
                    const labels = {
                      length: intl.formatMessage({ id: 'user.password.requirements.length' }),
                      lowercase: intl.formatMessage({ id: 'user.password.requirements.lowercase' }),
                      uppercase: intl.formatMessage({ id: 'user.password.requirements.uppercase' }),
                      numbers: intl.formatMessage({ id: 'user.password.requirements.numbers' }),
                      symbols: intl.formatMessage({ id: 'user.password.requirements.symbols' }),
                      noCommon: intl.formatMessage({ id: 'user.password.requirements.noCommon' })
                    };

                    return (
                      <div key={key} className="flex items-center gap-2">
                        {passed ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm ${passed ? 'text-green-600' : 'text-red-600'}`}>
                          {labels[key as keyof typeof labels]}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}
              
              {!formData.newPassword && (
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    <span>{intl.formatMessage({ id: 'user.password.requirements.info.length' })}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    <span>{intl.formatMessage({ id: 'user.password.requirements.info.case' })}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    <span>{intl.formatMessage({ id: 'user.password.requirements.info.numbers' })}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    <span>{intl.formatMessage({ id: 'user.password.requirements.info.common' })}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.password.security.title' })}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.password.security.lastChanged' })}</p>
                <p className="text-sm text-muted-foreground">{getPasswordLastChanged()}</p>
              </div>

              <div>
                <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.password.security.twoFactor' })}</p>
                <div className="flex items-center gap-2 mt-1">
                  {securityOverview?.two_factor_enabled ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-600">{intl.formatMessage({ id: 'user.password.security.enabled' })}</span>
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm text-yellow-600">{intl.formatMessage({ id: 'user.password.security.notEnabled' })}</span>
                    </>
                  )}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.password.security.activeSessions' })}</p>
                <p className="text-sm text-muted-foreground">
                  {securityOverview?.active_sessions_count || 0} {intl.formatMessage({ id: 'user.password.security.devicesSignedIn' })}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Security Tips */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>{intl.formatMessage({ id: 'user.password.tips.title' })}</strong>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• {intl.formatMessage({ id: 'user.password.tips.unique' })}</li>
                <li>• {intl.formatMessage({ id: 'user.password.tips.manager' })}</li>
                <li>• {intl.formatMessage({ id: 'user.password.tips.twoFactor' })}</li>
                <li>• {intl.formatMessage({ id: 'user.password.tips.regular' })}</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    </div>
  );
};

export default UserPasswordManagementPage;
