import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  Key,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  EyeOff,
  RefreshCw,
  Settings,
  Lock,
  Unlock,
  Activity,
  Calendar,
  MapPin
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Progress } from '@/components/ui/animate-ui/radix/progress';
import { PageLoader } from '@/components/base/page-loader';
import { useUserSecurityOverview, useUserProfile } from '@/hooks/use-user-api';

/**
 * User Security Overview Page
 * 
 * Displays comprehensive security status, recommendations, and quick actions
 * for users to manage their account security.
 */
const UserSecurityOverviewPage: React.FC = () => {
  const intl = useIntl();
  const { securityOverview, isLoading: securityLoading, error: securityError } = useUserSecurityOverview();
  const { profile, isLoading: profileLoading } = useUserProfile();
  const [showDetails, setShowDetails] = useState(false);

  const isLoading = securityLoading || profileLoading;
  const hasError = securityError;

  // Calculate security score based on various factors
  const calculateSecurityScore = () => {
    if (!securityOverview) return 0;
    
    let score = 0;
    const maxScore = 100;
    
    // 2FA enabled (40 points)
    if (securityOverview.two_factor_enabled) score += 40;
    
    // Recent password change (20 points)
    if (securityOverview.password_last_changed) {
      const passwordAge = new Date().getTime() - new Date(securityOverview.password_last_changed).getTime();
      const daysOld = Math.floor(passwordAge / (1000 * 60 * 60 * 24));
      if (daysOld <= 90) score += 20;
      else if (daysOld <= 180) score += 10;
    }
    
    // No recent security events (20 points)
    if (securityOverview.recent_security_events === 0) score += 20;
    
    // Reasonable number of active sessions (10 points)
    if (securityOverview.active_sessions_count <= 3) score += 10;
    
    // Has trusted devices (10 points)
    if (securityOverview.trusted_devices_count > 0) score += 10;
    
    return Math.min(score, maxScore);
  };

  const securityScore = calculateSecurityScore();

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return intl.formatMessage({ id: 'user.security.score.excellent' });
    if (score >= 60) return intl.formatMessage({ id: 'user.security.score.good' });
    if (score >= 40) return intl.formatMessage({ id: 'user.security.score.fair' });
    return intl.formatMessage({ id: 'user.security.score.poor' });
  };

  // Security recommendations based on current status
  const getRecommendations = () => {
    if (!securityOverview) return [];
    
    const recommendations = [];
    
    if (!securityOverview.two_factor_enabled) {
      recommendations.push({
        type: 'critical' as const,
        title: intl.formatMessage({ id: 'user.security.recommendations.enable2fa' }),
        description: intl.formatMessage({ id: 'user.security.recommendations.enable2fa.description' }),
        action: { text: intl.formatMessage({ id: 'user.security.actions.enable2fa' }), href: '/user/security/2fa' },
        icon: Shield
      });
    }
    
    if (securityOverview.password_last_changed) {
      const passwordAge = new Date().getTime() - new Date(securityOverview.password_last_changed).getTime();
      const daysOld = Math.floor(passwordAge / (1000 * 60 * 60 * 24));
      if (daysOld > 90) {
        recommendations.push({
          type: 'warning' as const,
          title: intl.formatMessage({ id: 'user.security.recommendations.updatePassword' }),
          description: intl.formatMessage({ id: 'user.security.recommendations.updatePassword.description' }, { days: daysOld }),
          action: { text: intl.formatMessage({ id: 'user.security.actions.changePassword' }), href: '/user/security/password' },
          icon: Key
        });
      }
    }
    
    if (securityOverview.active_sessions_count > 5) {
      recommendations.push({
        type: 'warning' as const,
        title: intl.formatMessage({ id: 'user.security.recommendations.reviewSessions' }),
        description: intl.formatMessage({ id: 'user.security.recommendations.reviewSessions.description' }, { count: securityOverview.active_sessions_count }),
        action: { text: intl.formatMessage({ id: 'user.security.actions.manageDevices' }), href: '/user/security/devices' },
        icon: Smartphone
      });
    }
    
    if (securityOverview.trusted_devices_count === 0) {
      recommendations.push({
        type: 'info' as const,
        title: intl.formatMessage({ id: 'user.security.recommendations.addTrustedDevices' }),
        description: intl.formatMessage({ id: 'user.security.recommendations.addTrustedDevices.description' }),
        action: { text: intl.formatMessage({ id: 'user.security.actions.manageDevices' }), href: '/user/security/devices' },
        icon: Smartphone
      });
    }
    
    return recommendations;
  };

  const recommendations = getRecommendations();

  if (isLoading) {
    return <PageLoader />;
  }

  if (hasError || !securityOverview) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {intl.formatMessage({ id: 'user.security.error.loadFailed' })}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.security.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.security.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.security.refresh' })}
          </Button>
          <Button asChild>
            <Link to="/user/settings">
              <Settings className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'user.security.settings' })}
            </Link>
          </Button>
        </div>
      </div>

      {/* Security Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {intl.formatMessage({ id: 'user.security.score.title' })}
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'user.security.score.description' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className={`text-4xl font-bold ${getScoreColor(securityScore)}`}>
                {securityScore}/100
              </div>
              <div className={`text-lg font-medium ${getScoreColor(securityScore)}`}>
                {getScoreLabel(securityScore)}
              </div>
            </div>
            <div className="text-right">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                {showDetails ? intl.formatMessage({ id: 'user.security.score.hideDetails' }) : intl.formatMessage({ id: 'user.security.score.showDetails' })}
              </Button>
            </div>
          </div>
          <Progress value={securityScore} className="mb-4" />
          {showDetails && (
            <div className="grid gap-2 text-sm">
              <div className="flex justify-between">
                <span>{intl.formatMessage({ id: 'user.security.score.details.twoFactor' })}</span>
                <span className={securityOverview.two_factor_enabled ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                  {securityOverview.two_factor_enabled ? intl.formatMessage({ id: 'user.security.score.points.plus40' }) : intl.formatMessage({ id: 'user.security.score.points.zero' })}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{intl.formatMessage({ id: 'user.security.score.details.passwordAge' })}</span>
                <span className="text-muted-foreground">
                  {securityOverview.password_last_changed ? intl.formatMessage({ id: 'user.security.score.points.plus10to20' }) : intl.formatMessage({ id: 'user.security.score.points.zero' })}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{intl.formatMessage({ id: 'user.security.score.details.securityEvents' })}</span>
                <span className={securityOverview.recent_security_events === 0 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}>
                  {securityOverview.recent_security_events === 0 ? intl.formatMessage({ id: 'user.security.score.points.plus20' }) : intl.formatMessage({ id: 'user.security.score.points.zero' })}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{intl.formatMessage({ id: 'user.security.score.details.sessionManagement' })}</span>
                <span className={securityOverview.active_sessions_count <= 3 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}>
                  {securityOverview.active_sessions_count <= 3 ? intl.formatMessage({ id: 'user.security.score.points.plus10' }) : intl.formatMessage({ id: 'user.security.score.points.zero' })}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{intl.formatMessage({ id: 'user.security.score.details.trustedDevices' })}</span>
                <span className={securityOverview.trusted_devices_count > 0 ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}>
                  {securityOverview.trusted_devices_count > 0 ? intl.formatMessage({ id: 'user.security.score.points.plus10' }) : intl.formatMessage({ id: 'user.security.score.points.zero' })}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Recommendations */}
      {recommendations.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">{intl.formatMessage({ id: 'user.security.recommendations.title' })}</h2>
          {recommendations.map((rec, index) => (
            <Alert
              key={index}
              className={
                rec.type === 'critical' ? 'border-red-200 dark:border-red-800/30 bg-red-50 dark:bg-red-950/20' :
                rec.type === 'warning' ? 'border-yellow-200 dark:border-yellow-800/30 bg-yellow-50 dark:bg-yellow-950/20' :
                'border-blue-200 dark:border-blue-800/30 bg-blue-50 dark:bg-blue-950/20'
              }
            >
              <rec.icon className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <div>
                  <strong>{rec.title}</strong>
                  <p className="text-sm text-muted-foreground mt-1">{rec.description}</p>
                </div>
                <Button asChild size="sm" variant="outline">
                  <Link to={rec.action.href}>{rec.action.text}</Link>
                </Button>
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Security Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.status.twoFactor' })}</CardTitle>
            {securityOverview.two_factor_enabled ? (
              <Lock className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Unlock className="h-4 w-4 text-red-600 dark:text-red-400" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {securityOverview.two_factor_enabled ? intl.formatMessage({ id: 'user.security.status.enabled' }) : intl.formatMessage({ id: 'user.security.status.disabled' })}
            </div>
            <p className="text-xs text-muted-foreground">
              {securityOverview.two_factor_enabled ? intl.formatMessage({ id: 'user.security.status.twoFactor.protected' }) : intl.formatMessage({ id: 'user.security.status.twoFactor.enable' })}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.status.activeSessions' })}</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityOverview.active_sessions_count}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.security.status.activeSessions.description' })}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.status.trustedDevices' })}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityOverview.trusted_devices_count}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.security.status.trustedDevices.description' })}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.status.securityEvents' })}</CardTitle>
            {securityOverview.recent_security_events === 0 ? (
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityOverview.recent_security_events}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.security.status.securityEvents.description' })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.security.actions.title' })}</CardTitle>
          <CardDescription>{intl.formatMessage({ id: 'user.security.actions.description' })}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-start">
              <Link to="/user/security/password">
                <Key className="h-5 w-5 mb-2" />
                <div className="text-left">
                  <div className="font-medium">{intl.formatMessage({ id: 'user.security.actions.changePassword' })}</div>
                  <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.security.actions.changePassword.description' })}</div>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-start">
              <Link to="/user/security/2fa">
                <Shield className="h-5 w-5 mb-2" />
                <div className="text-left">
                  <div className="font-medium">{intl.formatMessage({ id: 'user.security.actions.twoFactor' })}</div>
                  <div className="text-sm text-muted-foreground">
                    {securityOverview.two_factor_enabled ? intl.formatMessage({ id: 'user.security.actions.twoFactor.manage' }) : intl.formatMessage({ id: 'user.security.actions.twoFactor.enable' })}
                  </div>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-start">
              <Link to="/user/security/devices">
                <Smartphone className="h-5 w-5 mb-2" />
                <div className="text-left">
                  <div className="font-medium">{intl.formatMessage({ id: 'user.security.actions.deviceManagement' })}</div>
                  <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.security.actions.deviceManagement.description' })}</div>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.security.account.title' })}</CardTitle>
          <CardDescription>{intl.formatMessage({ id: 'user.security.account.description' })}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.account.created' })}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(securityOverview.account_created).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.account.lastLogin' })}</p>
                  <p className="text-sm text-muted-foreground">
                    {securityOverview.last_login
                      ? new Date(securityOverview.last_login).toLocaleString()
                      : intl.formatMessage({ id: 'user.security.account.never' })
                    }
                  </p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Key className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.account.passwordChanged' })}</p>
                  <p className="text-sm text-muted-foreground">
                    {securityOverview.password_last_changed
                      ? new Date(securityOverview.password_last_changed).toLocaleDateString()
                      : intl.formatMessage({ id: 'user.security.account.never' })
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{intl.formatMessage({ id: 'user.security.account.status' })}</p>
                  <div className="flex items-center gap-2">
                    <Badge variant={profile?.is_active ? 'default' : 'destructive'}>
                      {profile?.is_active ? intl.formatMessage({ id: 'user.security.account.active' }) : intl.formatMessage({ id: 'user.security.account.inactive' })}
                    </Badge>
                    {profile?.is_verified && (
                      <Badge variant="outline">{intl.formatMessage({ id: 'user.security.account.verified' })}</Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSecurityOverviewPage;
