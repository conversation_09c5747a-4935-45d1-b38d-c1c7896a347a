import React, { useState, useEffect } from 'react';
import { Smartphone, Monitor, Tablet, MapPin, Calendar, Trash2, RefreshCw, Shield } from 'lucide-react';
import { useIntl } from 'react-intl';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import axiosServices from '@/utils/api/axios';

interface TrustedDevice {
  id: string;
  device_name: string;
  device_type: string;
  browser: string;
  os: string;
  location: string;
  is_trusted: boolean;
  first_seen_at: string;
  last_seen_at: string;
  access_count: number;
}

const DeviceIcon = ({ deviceType }: { deviceType: string }) => {
  switch (deviceType?.toLowerCase()) {
    case 'mobile':
      return <Smartphone className="h-5 w-5" />;
    case 'tablet':
      return <Tablet className="h-5 w-5" />;
    default:
      return <Monitor className="h-5 w-5" />;
  }
};

const UserDeviceManagementPage: React.FC = () => {
  const intl = useIntl();
  const [devices, setDevices] = useState<TrustedDevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [removingDeviceId, setRemovingDeviceId] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadTrustedDevices();
  }, []);

  const loadTrustedDevices = async () => {
    try {
      setIsLoading(true);
      const response = await axiosServices.get('/api/v1/user/security/devices');
      setDevices(response.data);
    } catch (error) {
      console.error('Error loading trusted devices:', error);
      toast({
        title: intl.formatMessage({ id: 'user.devices.toast.loadFailed.title' }),
        description: intl.formatMessage({ id: 'user.devices.toast.loadFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const removeDevice = async (deviceId: string) => {
    try {
      setRemovingDeviceId(deviceId);
      await axiosServices.delete(`/api/v1/user/security/devices/${deviceId}`);
      
      // Remove device from local state
      setDevices(devices.filter(device => device.id !== deviceId));
      
      toast({
        title: intl.formatMessage({ id: 'user.devices.toast.removed.title' }),
        description: intl.formatMessage({ id: 'user.devices.toast.removed.description' }),
      });
    } catch (error) {
      console.error('Error removing device:', error);
      toast({
        title: intl.formatMessage({ id: 'user.devices.toast.removeFailed.title' }),
        description: intl.formatMessage({ id: 'user.devices.toast.removeFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setRemovingDeviceId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isCurrentDevice = (device: TrustedDevice) => {
    // Simple heuristic to identify current device
    const now = new Date();
    const lastSeen = new Date(device.last_seen_at);
    const timeDiff = now.getTime() - lastSeen.getTime();
    return timeDiff < 5 * 60 * 1000; // Less than 5 minutes ago
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.devices.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.devices.subtitle' })}
          </p>
        </div>
        <Button variant="outline" onClick={loadTrustedDevices} disabled={isLoading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          {intl.formatMessage({ id: 'user.devices.refresh' })}
        </Button>
      </div>

      {/* Info Alert */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>{intl.formatMessage({ id: 'user.devices.alert.title' })}</strong> {intl.formatMessage({ id: 'user.devices.alert.description' })}
        </AlertDescription>
      </Alert>

      {/* Devices List */}
      <div className="grid gap-4">
        {devices.length === 0 ? (
          <Card className="shadow-sm">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Smartphone className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.devices.empty.title' })}</h3>
              <p className="text-muted-foreground text-center">
                {intl.formatMessage({ id: 'user.devices.empty.description' })}
              </p>
            </CardContent>
          </Card>
        ) : (
          devices.map((device) => (
            <Card key={device.id} className="shadow-sm">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <DeviceIcon deviceType={device.device_type} />
                    <div>
                      <CardTitle className="text-lg flex items-center gap-2">
                        {device.device_name}
                        {isCurrentDevice(device) && (
                          <Badge variant="secondary" className="text-xs">
                            {intl.formatMessage({ id: 'user.devices.badge.current' })}
                          </Badge>
                        )}
                        {device.is_trusted && (
                          <Badge variant="outline" className="text-xs">
                            {intl.formatMessage({ id: 'user.devices.badge.trusted' })}
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>
                        {device.browser} on {device.os}
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeDevice(device.id)}
                    disabled={removingDeviceId === device.id || isCurrentDevice(device)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{intl.formatMessage({ id: 'user.devices.info.location' })}:</span>
                    <span>{device.location || intl.formatMessage({ id: 'user.devices.info.unknown' })}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{intl.formatMessage({ id: 'user.devices.info.lastSeen' })}:</span>
                    <span>{formatDate(device.last_seen_at)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{intl.formatMessage({ id: 'user.devices.info.accessCount' })}:</span>
                    <span>{device.access_count}</span>
                  </div>
                </div>
                <div className="mt-3 text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.devices.info.firstSeen' })}: {formatDate(device.first_seen_at)}
                  {isCurrentDevice(device) && (
                    <span className="ml-2 text-blue-600 dark:text-blue-400">• {intl.formatMessage({ id: 'user.devices.warning.cannotRemoveCurrent' })}</span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Security Tips */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.devices.security.title' })}</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• {intl.formatMessage({ id: 'user.devices.security.tip1' })}</li>
            <li>• {intl.formatMessage({ id: 'user.devices.security.tip2' })}</li>
            <li>• {intl.formatMessage({ id: 'user.devices.security.tip3' })}</li>
            <li>• {intl.formatMessage({ id: 'user.devices.security.tip4' })}</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserDeviceManagementPage;
