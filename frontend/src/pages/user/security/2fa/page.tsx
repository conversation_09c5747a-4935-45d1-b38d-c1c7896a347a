import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import {
  Shield,
  Plus,
} from 'lucide-react';

import { Button } from '@/components/ui/shadcn/button';
import { useToast } from '@/hooks/use-toast';

// Import new MFA components
import { MFASetup } from '@/components/features/user/mfa-setup';
import { MFAManagement } from '@/components/features/user/mfa-management';

/**
 * User 2-Factor Authentication Page
 * 
 * Comprehensive 2FA management with setup, backup codes, 
 * and device management using new MFA components.
 */
const User2FAPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const [showSetup, setShowSetup] = useState(false);

  // Check if user wants to set up MFA or manage existing MFA
  const handleSetupComplete = () => {
    setShowSetup(false);
    toast({
      title: intl.formatMessage({ id: 'user.twofa.message.setup.success' }),
      variant: 'default',
    });
  };

  const handleSetupCancel = () => {
    setShowSetup(false);
  };

  // Show setup component if user wants to set up MFA
  if (showSetup) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <MFASetup
          onComplete={handleSetupComplete}
          onCancel={handleSetupCancel}
        />
      </div>
    );
  }

  // Main MFA management interface
  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.twofa.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.twofa.subtitle' })}
          </p>
        </div>
        <Button onClick={() => setShowSetup(true)} size="sm">
          <Plus className="mr-2 h-4 w-4" />
          {intl.formatMessage({ id: 'user.twofa.setup.enable.title' })}
        </Button>
      </div>

      {/* MFA Management Component */}
      <MFAManagement />
    </div>
  );
};

export default User2FAPage;
