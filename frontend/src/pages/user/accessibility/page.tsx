import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Accessibility, Eye, Type, Palette, Volume2, Keyboard, Save, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { Label } from '@/components/ui/shadcn/label';
import { Separator } from '@/components/ui/shadcn/separator';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import axiosServices from '@/utils/api/axios';

interface AccessibilitySettings {
  theme: string;
  font_size: string;
  high_contrast: boolean;
  screen_reader: boolean;
  keyboard_navigation: boolean;
  reduced_motion: boolean;
  color_blind_friendly: boolean;
}

const UserAccessibilityPage: React.FC = () => {
  const intl = useIntl();
  const [settings, setSettings] = useState<AccessibilitySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadAccessibilitySettings();
  }, []);

  const loadAccessibilitySettings = async () => {
    try {
      setIsLoading(true);
      const response = await axiosServices.get('/api/v1/user/accessibility');
      setSettings(response.data);
    } catch (error) {
      console.error('Error loading accessibility settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.accessibility.error.loadFailed' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      await axiosServices.put('/api/v1/user/accessibility', settings);
      toast({
        title: intl.formatMessage({ id: 'common.success' }),
        description: intl.formatMessage({ id: 'user.accessibility.success.saved' }),
      });
    } catch (error) {
      console.error('Error saving accessibility settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.accessibility.error.saveFailed' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (key: keyof AccessibilitySettings, value: any) => {
    if (!settings) return;
    setSettings({
      ...settings,
      [key]: value
    });
  };

  if (isLoading) {
    return <PageLoader />;
  }

  if (!settings) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center py-12">
          <h1 className="text-3xl font-bold tracking-tight mb-4">Error Loading Settings</h1>
          <p className="text-muted-foreground mb-6">Unable to load your accessibility settings.</p>
          <Button onClick={loadAccessibilitySettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Accessibility className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.accessibility.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.accessibility.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadAccessibilitySettings} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.accessibility.refresh' })}
          </Button>
          <Button onClick={saveSettings} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? intl.formatMessage({ id: 'user.accessibility.saving' }) : intl.formatMessage({ id: 'user.accessibility.save' })}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Visual Settings */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.accessibility.visual.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.accessibility.visual.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="theme">Theme</Label>
                <Select 
                  value={settings.theme} 
                  onValueChange={(value) => updateSetting('theme', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="font-size">Font Size</Label>
                <Select 
                  value={settings.font_size} 
                  onValueChange={(value) => updateSetting('font_size', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select font size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                    <SelectItem value="extra-large">Extra Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="high-contrast">{intl.formatMessage({ id: 'user.accessibility.visual.highContrast' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.accessibility.visual.highContrast.description' })}
                </p>
              </div>
              <Switch
                id="high-contrast"
                checked={settings.high_contrast}
                onCheckedChange={(checked) => updateSetting('high_contrast', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="color-blind-friendly">Color Blind Friendly</Label>
                <p className="text-sm text-muted-foreground">
                  Use patterns and symbols in addition to colors
                </p>
              </div>
              <Switch
                id="color-blind-friendly"
                checked={settings.color_blind_friendly}
                onCheckedChange={(checked) => updateSetting('color_blind_friendly', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Motion & Animation */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Motion & Animation
            </CardTitle>
            <CardDescription>
              Control animations and motion effects
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="reduced-motion">Reduce Motion</Label>
                <p className="text-sm text-muted-foreground">
                  Minimize animations and transitions
                </p>
              </div>
              <Switch
                id="reduced-motion"
                checked={settings.reduced_motion}
                onCheckedChange={(checked) => updateSetting('reduced_motion', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Assistive Technology */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              Assistive Technology
            </CardTitle>
            <CardDescription>
              Settings for screen readers and other assistive tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="screen-reader">Screen Reader Support</Label>
                <p className="text-sm text-muted-foreground">
                  Optimize interface for screen readers
                </p>
              </div>
              <Switch
                id="screen-reader"
                checked={settings.screen_reader}
                onCheckedChange={(checked) => updateSetting('screen_reader', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Keyboard className="h-5 w-5" />
              Navigation
            </CardTitle>
            <CardDescription>
              Keyboard and navigation preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="keyboard-navigation">Enhanced Keyboard Navigation</Label>
                <p className="text-sm text-muted-foreground">
                  Improve keyboard navigation with visual indicators
                </p>
              </div>
              <Switch
                id="keyboard-navigation"
                checked={settings.keyboard_navigation}
                onCheckedChange={(checked) => updateSetting('keyboard_navigation', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Accessibility Info */}
        <Alert>
          <Accessibility className="h-4 w-4" />
          <AlertDescription>
            <strong>Accessibility Commitment:</strong> We're committed to making our platform accessible to everyone. 
            If you encounter any accessibility barriers or need additional accommodations, 
            please contact our support team for assistance.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

export default UserAccessibilityPage;
