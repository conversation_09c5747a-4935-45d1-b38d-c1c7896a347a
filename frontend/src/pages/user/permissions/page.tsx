/**
 * User Permissions Page
 * 
 * Displays user's permissions across all connected applications
 */

import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  Globe,
  Users,
  Settings,
  Eye,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Calendar
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { useUserPermissions } from '@/hooks/use-user-api';
import useAuth from '@/hooks/use-auth';
import type { UserPermissions } from '@/types/api/user';

/**
 * User Permissions Page
 * 
 * Central page for users to view and understand their granted permissions
 * across all connected applications and services.
 */
const UserPermissionsPage: React.FC = () => {
  const intl = useIntl();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Load permissions data
  const { permissions, isLoading, error, refreshData } = useUserPermissions(
    isAuthenticated && !authLoading
  );

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const getScopeIcon = (scope: string) => {
    switch (scope.toLowerCase()) {
      case 'profile':
      case 'openid':
        return Users;
      case 'email':
        return Settings;
      case 'read':
        return Eye;
      default:
        return Shield;
    }
  };

  const getScopeColor = (scope: string) => {
    switch (scope.toLowerCase()) {
      case 'profile':
      case 'openid':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'email':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'read':
        return 'bg-secondary text-secondary-foreground';
      case 'write':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
    }
  };

  const getRiskLevel = (riskCount: number) => {
    if (riskCount === 0) return {
      level: intl.formatMessage({ id: 'user.permissions.stats.securityRisk.low' }),
      color: 'text-green-600',
      icon: CheckCircle
    };
    if (riskCount <= 2) return {
      level: intl.formatMessage({ id: 'user.permissions.stats.securityRisk.medium' }),
      color: 'text-yellow-600',
      icon: AlertTriangle
    };
    return {
      level: intl.formatMessage({ id: 'user.permissions.stats.securityRisk.high' }),
      color: 'text-red-600',
      icon: AlertTriangle
    };
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid gap-4 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const riskInfo = permissions?.security_summary ? 
    getRiskLevel(permissions.security_summary.high_risk_permissions) : 
    getRiskLevel(0);

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'user.permissions.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.permissions.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refreshData()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.permissions.refresh' })}
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.permissions.error.title' })}</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => refreshData()}>{intl.formatMessage({ id: 'user.permissions.error.tryAgain' })}</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {!error && permissions && (
        <>
          {/* Overview Stats */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.permissions.stats.totalApps' })}</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{permissions.total_applications}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.permissions.stats.totalApps.description' })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.permissions.stats.totalPermissions' })}</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{permissions.total_permissions}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.permissions.stats.totalPermissions.description' })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.permissions.stats.securityRisk' })}</CardTitle>
                <riskInfo.icon className={`h-4 w-4 ${riskInfo.color}`} />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${riskInfo.color}`}>{riskInfo.level}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.permissions.stats.highRiskPermissions' }, { count: permissions.security_summary?.high_risk_permissions || 0 })}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Permissions by Scope */}
          <Card>
            <CardHeader>
              <CardTitle>{intl.formatMessage({ id: 'user.permissions.byScope.title' })}</CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'user.permissions.byScope.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {permissions.permissions_by_scope && permissions.permissions_by_scope.length > 0 ? (
                <div className="space-y-4">
                  {permissions.permissions_by_scope.map((scopeData, index) => {
                    const IconComponent = getScopeIcon(scopeData.scope);
                    return (
                      <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                        <div className="flex items-center gap-4">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getScopeColor(scopeData.scope)}`}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div>
                            <div className="font-medium capitalize">{scopeData.scope}</div>
                            <div className="text-sm text-muted-foreground">
                              {scopeData.description}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {intl.formatMessage({ id: 'user.permissions.byScope.granted' }, { date: formatDate(scopeData.granted_at) })}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary">
                            {scopeData.applications.length === 1
                              ? intl.formatMessage({ id: 'user.permissions.byScope.apps' }, { count: scopeData.applications.length })
                              : intl.formatMessage({ id: 'user.permissions.byScope.appsPlural' }, { count: scopeData.applications.length })
                            }
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {scopeData.applications.slice(0, 2).join(', ')}
                            {scopeData.applications.length > 2 && intl.formatMessage({ id: 'user.permissions.byScope.moreApps' }, { count: scopeData.applications.length - 2 })}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.permissions.byScope.noPermissions.title' })}</h3>
                  <p className="text-muted-foreground">
                    {intl.formatMessage({ id: 'user.permissions.byScope.noPermissions.description' })}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Grants */}
          {permissions.recent_grants && permissions.recent_grants.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'user.permissions.recentGrants.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.permissions.recentGrants.description' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {permissions.recent_grants.slice(0, 5).map((grant, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        <div>
                          <span className="font-medium">{grant.application_name}</span>
                          <div className="text-sm text-muted-foreground">
                            {intl.formatMessage({ id: 'user.permissions.recentGrants.granted' }, { scope: grant.scope })}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(grant.granted_at)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.permissions.quickActions.applications.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.permissions.quickActions.applications.description' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link to="/user/applications">
                    {intl.formatMessage({ id: 'user.permissions.quickActions.applications.button' })}
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.permissions.quickActions.privacy.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.permissions.quickActions.privacy.description' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full" variant="outline">
                  <Link to="/user/privacy">
                    {intl.formatMessage({ id: 'user.permissions.quickActions.privacy.button' })}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {!error && !permissions && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.permissions.noData.title' })}</h3>
              <p className="text-muted-foreground mb-4">
                {intl.formatMessage({ id: 'user.permissions.noData.description' })}
              </p>
              <Button onClick={() => refreshData()}>{intl.formatMessage({ id: 'user.permissions.error.tryAgain' })}</Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserPermissionsPage;
