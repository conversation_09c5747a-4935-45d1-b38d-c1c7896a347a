import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  Eye,
  EyeOff,
  Lock,
  Globe,
  Users,
  Database,
  Download,
  Trash2,
  Settings,
  Info,
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Label } from '@/components/ui/shadcn/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { useToast } from '@/hooks/use-toast';

/**
 * User Privacy Controls Page
 * 
 * Comprehensive privacy settings for data sharing controls, 
 * visibility settings, and GDPR compliance features.
 */
const UserPrivacyControlsPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Privacy settings state
  const [privacySettings, setPrivacySettings] = useState({
    // Profile Visibility
    profileVisibility: 'private', // public, friends, private
    showEmail: false,
    showPhone: false,
    showLocation: false,
    showLastSeen: true,
    
    // Data Sharing
    allowAnalytics: true,
    allowMarketing: false,
    allowThirdParty: false,
    allowPersonalization: true,
    
    // Communication Preferences
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: false,
    
    // Account Privacy
    searchableByEmail: false,
    searchableByPhone: false,
    allowFriendRequests: true,
    requireApprovalForApps: true,
    
    // Data Retention
    autoDeleteInactiveData: false,
    dataRetentionPeriod: '2years', // 1year, 2years, 5years, never
  });

  const handleSettingChange = (key: string, value: boolean | string) => {
    setPrivacySettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: intl.formatMessage({ id: 'user.privacy.toast.updated.title' }),
        description: intl.formatMessage({ id: 'user.privacy.toast.updated.description' }),
      });
      
      setHasChanges(false);
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'user.privacy.toast.updateFailed.title' }),
        description: intl.formatMessage({ id: 'user.privacy.toast.updateFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetToDefaults = () => {
    setPrivacySettings({
      profileVisibility: 'private',
      showEmail: false,
      showPhone: false,
      showLocation: false,
      showLastSeen: true,
      allowAnalytics: true,
      allowMarketing: false,
      allowThirdParty: false,
      allowPersonalization: true,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      marketingEmails: false,
      searchableByEmail: false,
      searchableByPhone: false,
      allowFriendRequests: true,
      requireApprovalForApps: true,
      autoDeleteInactiveData: false,
      dataRetentionPeriod: '2years',
    });
    setHasChanges(true);
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public': return <Globe className="h-4 w-4" />;
      case 'friends': return <Users className="h-4 w-4" />;
      case 'private': return <Lock className="h-4 w-4" />;
      default: return <Lock className="h-4 w-4" />;
    }
  };

  const getVisibilityColor = (visibility: string) => {
    switch (visibility) {
      case 'public': return 'text-red-600';
      case 'friends': return 'text-yellow-600';
      case 'private': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.privacy.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.privacy.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleResetToDefaults}>
              {intl.formatMessage({ id: 'common.resetToDefaults' })}
            </Button>
          )}
          <Button 
            onClick={handleSaveSettings} 
            disabled={!hasChanges || isLoading}
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {intl.formatMessage({ id: 'user.privacy.saving' })}
              </>
            ) : (
              <>
                <Settings className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'user.privacy.saveChanges' })}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Privacy Overview */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>{intl.formatMessage({ id: 'user.privacy.alert.title' })}</strong> {intl.formatMessage({ id: 'user.privacy.alert.description' })} {' '}
          <Link to="/privacy-policy" className="underline">{intl.formatMessage({ id: 'user.privacy.alert.privacyPolicy' })}</Link>.
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Profile Visibility */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getVisibilityIcon(privacySettings.profileVisibility)}
              <span className={getVisibilityColor(privacySettings.profileVisibility)}>
                {intl.formatMessage({ id: 'user.privacy.profileVisibility.title' })}
              </span>
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.privacy.profileVisibility.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="profileVisibility">{intl.formatMessage({ id: 'user.privacy.profileVisibility.label' })}</Label>
              <Select
                value={privacySettings.profileVisibility}
                onValueChange={(value) => handleSettingChange('profileVisibility', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      {intl.formatMessage({ id: 'user.privacy.profileVisibility.public' })}
                    </div>
                  </SelectItem>
                  <SelectItem value="friends">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      {intl.formatMessage({ id: 'user.privacy.profileVisibility.friends' })}
                    </div>
                  </SelectItem>
                  <SelectItem value="private">
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      {intl.formatMessage({ id: 'user.privacy.profileVisibility.private' })}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="showEmail">{intl.formatMessage({ id: 'user.privacy.profileVisibility.showEmail' })}</Label>
                <Switch
                  id="showEmail"
                  checked={privacySettings.showEmail}
                  onCheckedChange={(checked) => handleSettingChange('showEmail', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="showPhone">{intl.formatMessage({ id: 'user.privacy.profileVisibility.showPhone' })}</Label>
                <Switch
                  id="showPhone"
                  checked={privacySettings.showPhone}
                  onCheckedChange={(checked) => handleSettingChange('showPhone', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="showLocation">{intl.formatMessage({ id: 'user.privacy.profileVisibility.showLocation' })}</Label>
                <Switch
                  id="showLocation"
                  checked={privacySettings.showLocation}
                  onCheckedChange={(checked) => handleSettingChange('showLocation', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="showLastSeen">{intl.formatMessage({ id: 'user.privacy.profileVisibility.showLastSeen' })}</Label>
                <Switch
                  id="showLastSeen"
                  checked={privacySettings.showLastSeen}
                  onCheckedChange={(checked) => handleSettingChange('showLastSeen', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Sharing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              {intl.formatMessage({ id: 'user.privacy.dataSharing.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.privacy.dataSharing.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowAnalytics">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowAnalytics' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowAnalytics.description' })}</p>
              </div>
              <Switch
                id="allowAnalytics"
                checked={privacySettings.allowAnalytics}
                onCheckedChange={(checked) => handleSettingChange('allowAnalytics', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowPersonalization">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowPersonalization' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowPersonalization.description' })}</p>
              </div>
              <Switch
                id="allowPersonalization"
                checked={privacySettings.allowPersonalization}
                onCheckedChange={(checked) => handleSettingChange('allowPersonalization', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowMarketing">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowMarketing' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowMarketing.description' })}</p>
              </div>
              <Switch
                id="allowMarketing"
                checked={privacySettings.allowMarketing}
                onCheckedChange={(checked) => handleSettingChange('allowMarketing', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowThirdParty">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowThirdParty' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.dataSharing.allowThirdParty.description' })}</p>
              </div>
              <Switch
                id="allowThirdParty"
                checked={privacySettings.allowThirdParty}
                onCheckedChange={(checked) => handleSettingChange('allowThirdParty', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Communication Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {intl.formatMessage({ id: 'user.privacy.communication.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.privacy.communication.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifications">{intl.formatMessage({ id: 'user.privacy.communication.emailNotifications' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.communication.emailNotifications.description' })}</p>
              </div>
              <Switch
                id="emailNotifications"
                checked={privacySettings.emailNotifications}
                onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="pushNotifications">{intl.formatMessage({ id: 'user.privacy.communication.pushNotifications' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.communication.pushNotifications.description' })}</p>
              </div>
              <Switch
                id="pushNotifications"
                checked={privacySettings.pushNotifications}
                onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsNotifications">{intl.formatMessage({ id: 'user.privacy.communication.smsNotifications' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.communication.smsNotifications.description' })}</p>
              </div>
              <Switch
                id="smsNotifications"
                checked={privacySettings.smsNotifications}
                onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="marketingEmails">{intl.formatMessage({ id: 'user.privacy.communication.marketingEmails' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.communication.marketingEmails.description' })}</p>
              </div>
              <Switch
                id="marketingEmails"
                checked={privacySettings.marketingEmails}
                onCheckedChange={(checked) => handleSettingChange('marketingEmails', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Account Privacy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              {intl.formatMessage({ id: 'user.privacy.accountPrivacy.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.privacy.accountPrivacy.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="searchableByEmail">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.searchableByEmail' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.searchableByEmail.description' })}</p>
              </div>
              <Switch
                id="searchableByEmail"
                checked={privacySettings.searchableByEmail}
                onCheckedChange={(checked) => handleSettingChange('searchableByEmail', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="searchableByPhone">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.searchableByPhone' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.searchableByPhone.description' })}</p>
              </div>
              <Switch
                id="searchableByPhone"
                checked={privacySettings.searchableByPhone}
                onCheckedChange={(checked) => handleSettingChange('searchableByPhone', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowFriendRequests">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.allowFriendRequests' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.allowFriendRequests.description' })}</p>
              </div>
              <Switch
                id="allowFriendRequests"
                checked={privacySettings.allowFriendRequests}
                onCheckedChange={(checked) => handleSettingChange('allowFriendRequests', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="requireApprovalForApps">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.requireApprovalForApps' })}</Label>
                <p className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.accountPrivacy.requireApprovalForApps.description' })}</p>
              </div>
              <Switch
                id="requireApprovalForApps"
                checked={privacySettings.requireApprovalForApps}
                onCheckedChange={(checked) => handleSettingChange('requireApprovalForApps', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Retention */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            {intl.formatMessage({ id: 'user.privacy.dataRetention.title' })}
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'user.privacy.dataRetention.description' })}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="dataRetentionPeriod">{intl.formatMessage({ id: 'user.privacy.dataRetention.period' })}</Label>
              <Select
                value={privacySettings.dataRetentionPeriod}
                onValueChange={(value) => handleSettingChange('dataRetentionPeriod', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1year">{intl.formatMessage({ id: 'user.privacy.dataRetention.1year' })}</SelectItem>
                  <SelectItem value="2years">{intl.formatMessage({ id: 'user.privacy.dataRetention.2years' })}</SelectItem>
                  <SelectItem value="5years">{intl.formatMessage({ id: 'user.privacy.dataRetention.5years' })}</SelectItem>
                  <SelectItem value="never">{intl.formatMessage({ id: 'user.privacy.dataRetention.never' })}</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'user.privacy.dataRetention.period.description' })}
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="autoDeleteInactiveData">{intl.formatMessage({ id: 'user.privacy.dataRetention.autoDelete' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.privacy.dataRetention.autoDelete.description' })}
                </p>
              </div>
              <Switch
                id="autoDeleteInactiveData"
                checked={privacySettings.autoDeleteInactiveData}
                onCheckedChange={(checked) => handleSettingChange('autoDeleteInactiveData', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.privacy.actions.title' })}</CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'user.privacy.actions.description' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-start">
              <Link to="/user/data-export">
                <Download className="h-5 w-5 mb-2" />
                <div className="text-left">
                  <div className="font-medium">{intl.formatMessage({ id: 'user.privacy.actions.exportData' })}</div>
                  <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.actions.exportData.description' })}</div>
                </div>
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-start">
              <Link to="/user/applications">
                <Globe className="h-5 w-5 mb-2" />
                <div className="text-left">
                  <div className="font-medium">{intl.formatMessage({ id: 'user.privacy.actions.connectedApps' })}</div>
                  <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.actions.connectedApps.description' })}</div>
                </div>
              </Link>
            </Button>

            <Button variant="outline" className="h-auto p-4 flex flex-col items-start" disabled>
              <Trash2 className="h-5 w-5 mb-2" />
              <div className="text-left">
                <div className="font-medium">{intl.formatMessage({ id: 'user.privacy.actions.deleteAccount' })}</div>
                <div className="text-sm text-muted-foreground">{intl.formatMessage({ id: 'user.privacy.actions.deleteAccount.description' })}</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserPrivacyControlsPage;
