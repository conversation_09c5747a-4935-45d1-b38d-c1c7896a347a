/**
 * User Organizations Page
 * 
 * Shows organizations that the user is a member of
 * Allows users to view organization details and leave organizations
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Building2, Users, Calendar, ExternalLink, LogOut } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { useToast } from '@/hooks/use-toast';
import { organizationApi } from '@/services/organization-api';
import useAuth from '@/hooks/use-auth';
import { useInvitationContext } from '@/contexts/invitation-context';

interface UserOrganization {
  id: string;
  name: string;
  description?: string;
  tier?: string;
  member_count?: number;
  application_count?: number;
  user_role?: string;
  joined_at?: string;
  created_at: string;
}

/**
 * User Organizations Page
 * 
 * Displays organizations that the current user is a member of.
 * Allows users to view organization details and leave organizations.
 */
const UserOrganizationsPage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();
  const { refreshTrigger } = useInvitationContext();

  const [organizations, setOrganizations] = useState<UserOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserOrganizations();
    }
  }, [isAuthenticated, user, refreshTrigger]); // Add refreshTrigger to refresh when invitations are accepted

  const loadUserOrganizations = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await organizationApi.getUserOrganizations();
      setOrganizations(data);
    } catch (err: any) {
      console.error('Failed to load user organizations:', err);
      setError('Failed to load organizations');
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveOrganization = async (organizationId: string, organizationName: string) => {
    if (!confirm(`Are you sure you want to leave "${organizationName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await organizationApi.leaveOrganization(organizationId);
      
      toast({
        title: intl.formatMessage({ id: 'user.organizations.leaveSuccess.title' }),
        description: intl.formatMessage(
          { id: 'user.organizations.leaveSuccess.description' },
          { organizationName }
        ),
      });

      // Refresh the list
      await loadUserOrganizations();
    } catch (err: any) {
      console.error('Failed to leave organization:', err);
      toast({
        title: intl.formatMessage({ id: 'user.organizations.error.title' }),
        description: err.response?.data?.detail || intl.formatMessage({ id: 'user.organizations.leaveError' }),
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const getRoleBadgeVariant = (role?: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      case 'member':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getTierBadgeVariant = (tier?: string) => {
    switch (tier) {
      case 'enterprise':
        return 'default';
      case 'premium':
        return 'secondary';
      case 'basic':
        return 'outline';
      case 'free':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {intl.formatMessage({ id: 'user.organizations.title' })}
            </h1>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.organizations.subtitle' })}
            </p>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.organizations.loading' })}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {intl.formatMessage({ id: 'user.organizations.title' })}
            </h1>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.organizations.subtitle' })}
            </p>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="text-red-600 mb-4">{error}</div>
          <Button onClick={loadUserOrganizations}>
            {intl.formatMessage({ id: 'user.organizations.retry' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {intl.formatMessage({ id: 'user.organizations.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.organizations.subtitle' })}
          </p>
        </div>
      </div>

      {/* Organizations Grid */}
      {organizations.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {intl.formatMessage({ id: 'user.organizations.empty.title' })}
          </h3>
          <p className="text-muted-foreground mb-4">
            {intl.formatMessage({ id: 'user.organizations.empty.description' })}
          </p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {organizations.map((org) => (
            <Card key={org.id} className="shadow-sm hover:shadow-md transition-shadow flex flex-col">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-8 w-8 text-primary" />
                    <div>
                      <CardTitle className="text-lg">{org.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={getRoleBadgeVariant(org.user_role)}>
                          {org.user_role || 'member'}
                        </Badge>
                        {org.tier && (
                          <Badge variant={getTierBadgeVariant(org.tier)}>
                            {org.tier}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <CardDescription className="mt-2">
                  {org.description || intl.formatMessage({ id: 'user.organizations.noDescription' })}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                <div className="space-y-3 flex-1">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{intl.formatMessage({ id: 'user.organizations.members' })}</span>
                    </div>
                    <span className="font-medium">{org.member_count || 0}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                      <span>{intl.formatMessage({ id: 'user.organizations.applications' })}</span>
                    </div>
                    <span className="font-medium">{org.application_count || 0}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{intl.formatMessage({ id: 'user.organizations.joined' })}</span>
                    </div>
                    <span className="font-medium">{formatDate(org.joined_at || org.created_at)}</span>
                  </div>
                </div>

                <div className="flex space-x-2 mt-4">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 h-10 text-xs"
                    onClick={() => navigate(`/user/organizations/${org.id}`)}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'user.organizations.viewDetails' })}
                  </Button>

                  {org.user_role !== 'owner' && org.user_role && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-10 px-3 text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleLeaveOrganization(org.id, org.name)}
                    >
                      <LogOut className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserOrganizationsPage;
