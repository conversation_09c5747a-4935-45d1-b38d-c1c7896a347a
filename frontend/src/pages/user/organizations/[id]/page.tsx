/**
 * User Organization Detail Page
 * 
 * Shows detailed view of a specific organization that the user is a member of
 */

import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useParams, useNavigate } from 'react-router-dom';
import { Building2, Users, Calendar, ExternalLink, LogOut, ArrowLeft, Settings } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { useToast } from '@/hooks/use-toast';
import { organizationApi } from '@/services/organization-api';
import useAuth from '@/hooks/use-auth';

interface OrganizationDetail {
  id: string;
  name: string;
  description?: string;
  tier?: string;
  member_count?: number;
  application_count?: number;
  user_role?: string;
  joined_at?: string;
  created_at: string;
  members?: Array<{
    id: string;
    username: string;
    email: string;
    role: string;
    joined_at: string;
  }>;
  applications?: Array<{
    id: string;
    name: string;
    description?: string;
    created_at: string;
  }>;
}

/**
 * User Organization Detail Page
 * 
 * Displays detailed information about a specific organization the user belongs to.
 */
const UserOrganizationDetailPage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();
  const { id } = useParams<{ id: string }>();

  const [organization, setOrganization] = useState<OrganizationDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && user && id) {
      loadOrganizationDetail();
    }
  }, [isAuthenticated, user, id]);

  const loadOrganizationDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await organizationApi.getOrganizationById(id);
      setOrganization(data);
    } catch (err: any) {
      console.error('Failed to load organization detail:', err);
      if (err.response?.status === 404) {
        setError('Organization not found or you do not have access');
      } else {
        setError('Failed to load organization details');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveOrganization = async () => {
    if (!organization) return;
    
    if (!confirm(`Are you sure you want to leave "${organization.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await organizationApi.leaveOrganization(organization.id);
      
      toast({
        title: intl.formatMessage({ id: 'user.organizations.leaveSuccess.title' }),
        description: intl.formatMessage(
          { id: 'user.organizations.leaveSuccess.description' },
          { organizationName: organization.name }
        ),
      });

      // Navigate back to organizations list
      navigate('/user/organizations');
    } catch (err: any) {
      console.error('Failed to leave organization:', err);
      toast({
        title: intl.formatMessage({ id: 'user.organizations.error.title' }),
        description: err.response?.data?.detail || intl.formatMessage({ id: 'user.organizations.leaveError' }),
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const getRoleBadgeVariant = (role?: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      case 'member':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getTierBadgeVariant = (tier?: string) => {
    switch (tier) {
      case 'enterprise':
        return 'default';
      case 'premium':
        return 'secondary';
      case 'basic':
        return 'outline';
      case 'free':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center py-8">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.organizations.loading' })}
          </div>
        </div>
      </div>
    );
  }

  if (error || !organization) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">

        <div className="text-center py-8">
          <div className="text-red-600 mb-4">{error}</div>
          <Button onClick={loadOrganizationDetail}>
            {intl.formatMessage({ id: 'user.organizations.retry' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <Building2 className="h-8 w-8 text-primary" />
              {organization.name}
            </h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant={getRoleBadgeVariant(organization.user_role)}>
                {organization.user_role || 'member'}
              </Badge>
              {organization.tier && (
                <Badge variant={getTierBadgeVariant(organization.tier)}>
                  {organization.tier}
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex gap-2">
          {organization.user_role !== 'owner' && (
            <Button
              variant="outline"
              onClick={handleLeaveOrganization}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'user.organizations.detail.leave' })}
            </Button>
          )}
        </div>
      </div>

      {/* Organization Info */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'user.organizations.members' })}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{organization.member_count || 0}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.organizations.detail.totalMembers' })}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'user.organizations.applications' })}
            </CardTitle>
            <ExternalLink className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{organization.application_count || 0}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.organizations.detail.totalApplications' })}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {intl.formatMessage({ id: 'user.organizations.joined' })}
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDate(organization.joined_at || organization.created_at)}
            </div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.organizations.detail.memberSince' })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {organization.description && (
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.organizations.detail.description' })}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{organization.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Additional Info */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.organizations.detail.membershipInfo' })}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>{intl.formatMessage({ id: 'user.organizations.detail.yourRole' })}</span>
              <Badge variant={getRoleBadgeVariant(organization.user_role)}>
                {organization.user_role || 'member'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>{intl.formatMessage({ id: 'user.organizations.detail.organizationTier' })}</span>
              <span>{organization.tier || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span>{intl.formatMessage({ id: 'user.organizations.detail.createdAt' })}</span>
              <span>{formatDate(organization.created_at)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserOrganizationDetailPage;
