import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Settings, Globe, Clock, Palette, Save, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { Label } from '@/components/ui/shadcn/label';
import { Separator } from '@/components/ui/shadcn/separator';
import { PageLoader } from '@/components/base/page-loader';
import axiosServices from '@/utils/api/axios';

interface UserSettings {
  profile_visibility: string;
  email_visibility: string;
  activity_visibility: string;
  theme: string;
  font_size: string;
  high_contrast: boolean;
  screen_reader: boolean;
  data_retention_days: number;
  auto_delete_inactive: boolean;
}

const timezones = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'London (GMT)' },
  { value: 'Europe/Paris', label: 'Paris (CET)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
  { value: 'Australia/Sydney', label: 'Sydney (AEST)' },
];

const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'zh', label: '中文' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
];

const UserSettingsPage: React.FC = () => {
  const intl = useIntl();
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadUserSettings();
  }, []);

  const loadUserSettings = async () => {
    try {
      setIsLoading(true);
      const response = await axiosServices.get('/api/v1/user/settings');
      setSettings(response.data);
    } catch (error) {
      console.error('Error loading user settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.settings.error.loadFailed' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      await axiosServices.put('/api/v1/user/settings', settings);
      toast({
        title: intl.formatMessage({ id: 'common.success' }),
        description: intl.formatMessage({ id: 'user.settings.success.saved' }),
      });
    } catch (error) {
      console.error('Error saving user settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.settings.error.saveFailed' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (key: keyof UserSettings, value: any) => {
    if (!settings) return;
    setSettings({
      ...settings,
      [key]: value
    });
  };

  if (isLoading) {
    return <PageLoader />;
  }

  if (!settings) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center py-12">
          <h1 className="text-3xl font-bold tracking-tight mb-4">{intl.formatMessage({ id: 'user.settings.error.loadingTitle' })}</h1>
          <p className="text-muted-foreground mb-6">{intl.formatMessage({ id: 'user.settings.error.loadingDescription' })}</p>
          <Button onClick={loadUserSettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.settings.tryAgain' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.settings.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.settings.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadUserSettings} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.settings.refresh' })}
          </Button>
          <Button onClick={saveSettings} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? intl.formatMessage({ id: 'user.settings.saving' }) : intl.formatMessage({ id: 'user.settings.save' })}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Localization Settings */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.settings.localization.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.settings.localization.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="language">{intl.formatMessage({ id: 'user.settings.localization.language' })}</Label>
                <Select value="en" onValueChange={(value) => console.log('Language:', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.localization.language.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">{intl.formatMessage({ id: 'user.settings.localization.timezone' })}</Label>
                <Select value="UTC" onValueChange={(value) => console.log('Timezone:', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.localization.timezone.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    {timezones.map((tz) => (
                      <SelectItem key={tz.value} value={tz.value}>
                        {tz.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Appearance Settings */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.settings.display.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.settings.display.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="theme">{intl.formatMessage({ id: 'user.settings.display.theme' })}</Label>
                <Select 
                  value={settings.theme} 
                  onValueChange={(value) => updateSetting('theme', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.display.theme.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="system">{intl.formatMessage({ id: 'user.settings.display.theme.system' })}</SelectItem>
                    <SelectItem value="light">{intl.formatMessage({ id: 'user.settings.display.theme.light' })}</SelectItem>
                    <SelectItem value="dark">{intl.formatMessage({ id: 'user.settings.display.theme.dark' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="font-size">{intl.formatMessage({ id: 'user.settings.display.fontSize' })}</Label>
                <Select
                  value={settings.font_size}
                  onValueChange={(value) => updateSetting('font_size', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.display.fontSize.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">{intl.formatMessage({ id: 'user.settings.display.fontSize.small' })}</SelectItem>
                    <SelectItem value="medium">{intl.formatMessage({ id: 'user.settings.display.fontSize.medium' })}</SelectItem>
                    <SelectItem value="large">{intl.formatMessage({ id: 'user.settings.display.fontSize.large' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.settings.privacy.title' })}</CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.settings.privacy.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="profile-visibility">{intl.formatMessage({ id: 'user.settings.privacy.profileVisibility' })}</Label>
                <Select
                  value={settings.profile_visibility}
                  onValueChange={(value) => updateSetting('profile_visibility', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.privacy.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">{intl.formatMessage({ id: 'user.settings.privacy.profileVisibility.public' })}</SelectItem>
                    <SelectItem value="private">{intl.formatMessage({ id: 'user.settings.privacy.profileVisibility.private' })}</SelectItem>
                    <SelectItem value="friends">{intl.formatMessage({ id: 'user.settings.privacy.profileVisibility.friends' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-visibility">{intl.formatMessage({ id: 'user.settings.privacy.emailVisibility' })}</Label>
                <Select
                  value={settings.email_visibility}
                  onValueChange={(value) => updateSetting('email_visibility', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.privacy.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">{intl.formatMessage({ id: 'user.settings.privacy.emailVisibility.public' })}</SelectItem>
                    <SelectItem value="private">{intl.formatMessage({ id: 'user.settings.privacy.emailVisibility.private' })}</SelectItem>
                    <SelectItem value="friends">{intl.formatMessage({ id: 'user.settings.privacy.emailVisibility.friends' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="activity-visibility">{intl.formatMessage({ id: 'user.settings.privacy.activityVisibility' })}</Label>
                <Select
                  value={settings.activity_visibility}
                  onValueChange={(value) => updateSetting('activity_visibility', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.privacy.placeholder' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">{intl.formatMessage({ id: 'user.settings.privacy.activityVisibility.public' })}</SelectItem>
                    <SelectItem value="private">{intl.formatMessage({ id: 'user.settings.privacy.activityVisibility.private' })}</SelectItem>
                    <SelectItem value="friends">{intl.formatMessage({ id: 'user.settings.privacy.activityVisibility.friends' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.settings.dataManagement.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.settings.dataManagement.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="data-retention">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays' })}</Label>
              <Select
                value={settings.data_retention_days.toString()}
                onValueChange={(value) => updateSetting('data_retention_days', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.placeholder' })} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.30' })}</SelectItem>
                  <SelectItem value="90">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.90' })}</SelectItem>
                  <SelectItem value="180">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.180' })}</SelectItem>
                  <SelectItem value="365">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.365' })}</SelectItem>
                  <SelectItem value="730">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.730' })}</SelectItem>
                  <SelectItem value="-1">{intl.formatMessage({ id: 'user.settings.dataManagement.retentionDays.never' })}</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'user.settings.dataManagement.retentionDescription' })}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserSettingsPage;
