import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Bell, Mail, Smartphone, Monitor, Settings, Save, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Label } from '@/components/ui/shadcn/label';
import { Separator } from '@/components/ui/shadcn/separator';
import { PageLoader } from '@/components/base/page-loader';
import axiosServices from '@/utils/api/axios';

interface NotificationSettings {
  email: {
    security_alerts: boolean;
    product_updates: boolean;
    newsletters: boolean;
    usage_reports: boolean;
  };
  browser: boolean;
  mobile: boolean;
  in_app: {
    mentions: boolean;
    comments: boolean;
    task_assignments: boolean;
    status_changes: boolean;
  };
}

const UserNotificationsPage: React.FC = () => {
  const intl = useIntl();
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  const loadNotificationSettings = async () => {
    try {
      setIsLoading(true);
      const response = await axiosServices.get('/api/v1/user/notifications/settings');
      setSettings(response.data);
    } catch (error) {
      console.error('Error loading notification settings:', error);
      toast({
        title: intl.formatMessage({ id: 'user.notifications.toast.loadFailed.title' }),
        description: intl.formatMessage({ id: 'user.notifications.toast.loadFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      await axiosServices.put('/api/v1/user/notifications/settings', settings);
      toast({
        title: intl.formatMessage({ id: 'user.notifications.toast.saved.title' }),
        description: intl.formatMessage({ id: 'user.notifications.toast.saved.description' }),
      });
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast({
        title: intl.formatMessage({ id: 'user.notifications.toast.saveFailed.title' }),
        description: intl.formatMessage({ id: 'user.notifications.toast.saveFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateEmailSetting = (key: keyof NotificationSettings['email'], value: boolean) => {
    if (!settings) return;
    setSettings({
      ...settings,
      email: {
        ...settings.email,
        [key]: value
      }
    });
  };

  const updateInAppSetting = (key: keyof NotificationSettings['in_app'], value: boolean) => {
    if (!settings) return;
    setSettings({
      ...settings,
      in_app: {
        ...settings.in_app,
        [key]: value
      }
    });
  };

  if (isLoading) {
    return <PageLoader />;
  }

  if (!settings) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center py-12">
          <h1 className="text-3xl font-bold tracking-tight mb-4">{intl.formatMessage({ id: 'user.notifications.error.loadingTitle' })}</h1>
          <p className="text-muted-foreground mb-6">{intl.formatMessage({ id: 'user.notifications.error.loadingDescription' })}</p>
          <Button onClick={loadNotificationSettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.notifications.tryAgain' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Bell className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.notifications.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.notifications.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadNotificationSettings} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.notifications.refresh' })}
          </Button>
          <Button onClick={saveSettings} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? intl.formatMessage({ id: 'user.notifications.saving' }) : intl.formatMessage({ id: 'user.notifications.saveChanges' })}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Email Notifications */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.notifications.email.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.notifications.email.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="security-alerts">{intl.formatMessage({ id: 'user.notifications.email.securityAlerts' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.email.securityAlerts.description' })}
                </p>
              </div>
              <Switch
                id="security-alerts"
                checked={settings.email.security_alerts}
                onCheckedChange={(checked) => updateEmailSetting('security_alerts', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="product-updates">{intl.formatMessage({ id: 'user.notifications.email.productUpdates' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.email.productUpdates.description' })}
                </p>
              </div>
              <Switch
                id="product-updates"
                checked={settings.email.product_updates}
                onCheckedChange={(checked) => updateEmailSetting('product_updates', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="newsletters">{intl.formatMessage({ id: 'user.notifications.email.newsletters' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.email.newsletters.description' })}
                </p>
              </div>
              <Switch
                id="newsletters"
                checked={settings.email.newsletters}
                onCheckedChange={(checked) => updateEmailSetting('newsletters', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="usage-reports">{intl.formatMessage({ id: 'user.notifications.email.usageReports' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.email.usageReports.description' })}
                </p>
              </div>
              <Switch
                id="usage-reports"
                checked={settings.email.usage_reports}
                onCheckedChange={(checked) => updateEmailSetting('usage_reports', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* In-App Notifications */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.notifications.inApp.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.notifications.inApp.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="mentions">{intl.formatMessage({ id: 'user.notifications.inApp.mentions' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.inApp.mentions.description' })}
                </p>
              </div>
              <Switch
                id="mentions"
                checked={settings.in_app.mentions}
                onCheckedChange={(checked) => updateInAppSetting('mentions', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="comments">{intl.formatMessage({ id: 'user.notifications.inApp.comments' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.inApp.comments.description' })}
                </p>
              </div>
              <Switch
                id="comments"
                checked={settings.in_app.comments}
                onCheckedChange={(checked) => updateInAppSetting('comments', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="task-assignments">{intl.formatMessage({ id: 'user.notifications.inApp.taskAssignments' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.inApp.taskAssignments.description' })}
                </p>
              </div>
              <Switch
                id="task-assignments"
                checked={settings.in_app.task_assignments}
                onCheckedChange={(checked) => updateInAppSetting('task_assignments', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="status-changes">{intl.formatMessage({ id: 'user.notifications.inApp.statusChanges' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.inApp.statusChanges.description' })}
                </p>
              </div>
              <Switch
                id="status-changes"
                checked={settings.in_app.status_changes}
                onCheckedChange={(checked) => updateInAppSetting('status_changes', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Device Notifications */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.notifications.device.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.notifications.device.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="browser-notifications">{intl.formatMessage({ id: 'user.notifications.device.browser' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.device.browser.description' })}
                </p>
              </div>
              <Switch
                id="browser-notifications"
                checked={settings.browser}
                onCheckedChange={(checked) => setSettings({ ...settings, browser: checked })}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="mobile-notifications">{intl.formatMessage({ id: 'user.notifications.device.mobile' })}</Label>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.notifications.device.mobile.description' })}
                </p>
              </div>
              <Switch
                id="mobile-notifications"
                checked={settings.mobile}
                onCheckedChange={(checked) => setSettings({ ...settings, mobile: checked })}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserNotificationsPage;
