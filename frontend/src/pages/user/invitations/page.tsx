/**
 * User Invitations Page
 * 
 * Displays user's organization invitations with accept/decline functionality
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Mail,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Calendar,
  User,
  Building
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import { useToast } from '@/hooks/use-toast';
import { getUserInvitations, acceptInvitation, declineInvitation } from '@/services/user-api';
import useAuth from '@/hooks/use-auth';
import { useInvitationContext } from '@/contexts/invitation-context';

interface OrganizationInvitation {
  id: string;
  organization_id: string;
  organization_name: string;
  organization_slug: string;
  email: string;
  role: string;
  status: string;
  message?: string;
  token: string;
  invited_at: string;
  expires_at: string;
  inviter_name?: string;
  inviter_email?: string;
}

/**
 * User Invitations Page
 * 
 * Central page for users to view and manage their organization invitations.
 */
const UserInvitationsPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();
  const { refreshInvitations } = useInvitationContext();

  const [invitations, setInvitations] = useState<OrganizationInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingInvitation, setProcessingInvitation] = useState<string | null>(null);

  const loadInvitations = async () => {
    if (!isAuthenticated || authLoading) return;

    try {
      setIsLoading(true);
      setError(null);
      console.log('Loading invitations for user:', user?.email);
      const response = await getUserInvitations({ status_filter: 'pending' }); // Only get pending invitations
      console.log('Invitations response:', response);
      setInvitations(response.invitations);
    } catch (err: any) {
      console.error('Failed to load invitations:', err);
      setError(intl.formatMessage({ id: 'user.invitations.loadError' }));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, [isAuthenticated, authLoading]);

  const handleAcceptInvitation = async (invitation: OrganizationInvitation) => {
    try {
      setProcessingInvitation(invitation.id);
      await acceptInvitation(invitation.token);
      
      toast({
        title: intl.formatMessage({ id: 'user.invitations.acceptSuccess.title' }),
        description: intl.formatMessage(
          { id: 'user.invitations.acceptSuccess' },
          { organizationName: invitation.organization_name, role: invitation.role }
        ),
      });

      // Refresh invitations list
      await loadInvitations();
      // Trigger global invitation refresh for badges and widgets
      refreshInvitations();
    } catch (err: any) {
      console.error('Failed to accept invitation:', err);
      toast({
        title: intl.formatMessage({ id: 'user.invitations.error.title' }),
        description: err.response?.data?.detail || intl.formatMessage({ id: 'user.invitations.acceptError' }),
        variant: "destructive",
      });
    } finally {
      setProcessingInvitation(null);
    }
  };

  const handleDeclineInvitation = async (invitation: OrganizationInvitation) => {
    try {
      setProcessingInvitation(invitation.id);
      await declineInvitation(invitation.token);
      
      toast({
        title: intl.formatMessage({ id: 'user.invitations.declineSuccess.title' }),
        description: intl.formatMessage(
          { id: 'user.invitations.declineSuccess' },
          { organizationName: invitation.organization_name }
        ),
      });

      // Refresh invitations list
      await loadInvitations();
      // Trigger global invitation refresh for badges and widgets
      refreshInvitations();
    } catch (err: any) {
      console.error('Failed to decline invitation:', err);
      toast({
        title: intl.formatMessage({ id: 'user.invitations.error.title' }),
        description: err.response?.data?.detail || intl.formatMessage({ id: 'user.invitations.declineError' }),
        variant: "destructive",
      });
    } finally {
      setProcessingInvitation(null);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const isExpired = (expiresAt: string) => {
    try {
      return new Date(expiresAt) < new Date();
    } catch {
      return false;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'default';
      case 'admin':
        return 'secondary';
      case 'member':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (authLoading) {
    return <PageLoader />;
  }

  if (!isAuthenticated) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please log in to view your invitations.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Only system admins should not see this page - they have access to all organizations
  if (user && user.role === 'admin') {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            System administrators have access to all organizations and don't need to manage invitations here.
            You can oversee all organization invitations from the Admin Portal.
          </AlertDescription>
        </Alert>
        <div className="flex gap-4">
          <Button asChild>
            <Link to="/admin/users">Go to Admin Portal</Link>
          </Button>
          <Button asChild>
            <Link to="/developer/organizations">Go to Developer Portal</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {intl.formatMessage({ id: 'user.invitations.title' })}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            {intl.formatMessage({ id: 'user.invitations.subtitle' })}
          </p>
        </div>
        <Button onClick={loadInvitations} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          {intl.formatMessage({ id: 'user.invitations.refresh' })}
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <PageLoader />
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && invitations.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Mail className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {intl.formatMessage({ id: 'user.invitations.noPending.title' })}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-center">
              {intl.formatMessage({ id: 'user.invitations.noPending.description' })}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Invitations List */}
      {!isLoading && !error && invitations.length > 0 && (
        <div className="space-y-4">
          {invitations.map((invitation) => (
            <Card key={invitation.id} className="shadow-sm">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Building className="h-8 w-8 text-blue-600" />
                    <div>
                      <CardTitle className="text-lg">
                        {invitation.organization_name}
                      </CardTitle>
                      <CardDescription>
                        {intl.formatMessage(
                          { id: 'user.invitations.invitedBy' },
                          { name: invitation.inviter_name || invitation.inviter_email }
                        )}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge variant={getRoleBadgeVariant(invitation.role)}>
                    {invitation.role}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Invitation Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {intl.formatMessage(
                          { id: 'user.invitations.invited' },
                          { date: formatDate(invitation.invited_at) }
                        )}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className={`${isExpired(invitation.expires_at) ? 'text-red-600' : 'text-gray-600 dark:text-gray-400'}`}>
                        {intl.formatMessage(
                          { id: 'user.invitations.expires' },
                          { date: formatDate(invitation.expires_at) }
                        )}
                        {isExpired(invitation.expires_at) && ` ${intl.formatMessage({ id: 'user.invitations.expired' })}`}
                      </span>
                    </div>
                  </div>

                  {/* Message */}
                  {invitation.message && (
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        "{invitation.message}"
                      </p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-3 pt-2">
                    <Button
                      onClick={() => handleAcceptInvitation(invitation)}
                      disabled={processingInvitation === invitation.id || isExpired(invitation.expires_at)}
                      className="flex-1"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'user.invitations.accept' })}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleDeclineInvitation(invitation)}
                      disabled={processingInvitation === invitation.id || isExpired(invitation.expires_at)}
                      className="flex-1"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      {intl.formatMessage({ id: 'user.invitations.decline' })}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserInvitationsPage;
