import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Building,
  Globe,
  Clock,
  Camera,
  Save,
  RefreshCw,
  Edit3
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/shadcn/avatar';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import { useUserProfile } from '@/hooks/use-user-api';
import { useToast } from '@/hooks/use-toast';
import type { UserProfileResponse } from '@/types/api/user';

/**
 * User Personal Information Page
 * 
 * Allows users to view and edit their personal profile information
 * including name, contact details, bio, and professional information.
 */
const UserProfilePage: React.FC = () => {
  const intl = useIntl();
  const { profile, isLoading, error, updateProfile } = useUserProfile();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfileResponse>>({});

  // Initialize form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        email: profile.email || '',
        phone: profile.phone || '',
        bio: profile.bio || '',
        location: profile.location || '',
        job_title: profile.job_title || '',
        company: profile.company || '',
        timezone: profile.timezone || '',
        language: profile.language || 'en',
      });
    }
  }, [profile]);

  const handleInputChange = (field: keyof UserProfileResponse, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: intl.formatMessage({ id: 'user.personal.toast.updateSuccess.title' }),
        description: intl.formatMessage({ id: 'user.personal.toast.updateSuccess.description' }),
      });
    } catch (error) {
      toast({
        title: intl.formatMessage({ id: 'user.personal.toast.updateFailed.title' }),
        description: intl.formatMessage({ id: 'user.personal.toast.updateFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        email: profile.email || '',
        phone: profile.phone || '',
        bio: profile.bio || '',
        location: profile.location || '',
        job_title: profile.job_title || '',
        company: profile.company || '',
        timezone: profile.timezone || '',
        language: profile.language || 'en',
      });
    }
    setIsEditing(false);
  };

  if (isLoading) {
    return <PageLoader />;
  }

  if (error || !profile) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <Alert variant="destructive">
          <AlertDescription>
            {intl.formatMessage({ id: 'user.personal.error.loadFailed' })}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <User className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.personal.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.personal.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit3 className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'user.personal.editProfile' })}
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={handleCancel}>
                {intl.formatMessage({ id: 'user.personal.cancel' })}
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {intl.formatMessage({ id: 'user.personal.saveChanges' })}
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Picture and Basic Info */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.personal.profilePicture.title' })}</CardTitle>
            <CardDescription>{intl.formatMessage({ id: 'user.personal.profilePicture.description' })}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={profile.avatar_url} alt={profile.username} />
                <AvatarFallback className="text-lg">
                  {profile.first_name?.[0]}{profile.last_name?.[0]}
                </AvatarFallback>
              </Avatar>
              <Button variant="outline" size="sm" disabled>
                <Camera className="h-4 w-4 mr-2" />
                {intl.formatMessage({ id: 'user.personal.profilePicture.changePhoto' })}
              </Button>
              <div className="text-center">
                <p className="font-medium">{profile.first_name} {profile.last_name}</p>
                <p className="text-sm text-muted-foreground">@{profile.username}</p>
                <Badge
                  variant={profile.is_verified ? 'default' : 'secondary'}
                  className={`mt-2 ${profile.is_verified ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' : ''}`}
                >
                  {profile.is_verified ? intl.formatMessage({ id: 'user.personal.profilePicture.verified' }) : intl.formatMessage({ id: 'user.personal.profilePicture.unverified' })}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Details */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.personal.details.title' })}</CardTitle>
            <CardDescription>{intl.formatMessage({ id: 'user.personal.details.description' })}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Name Fields */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="first_name">{intl.formatMessage({ id: 'user.personal.fields.firstName' })}</Label>
                {isEditing ? (
                  <Input
                    id="first_name"
                    value={formData.first_name || ''}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.firstName.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.first_name || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">{intl.formatMessage({ id: 'user.personal.fields.lastName' })}</Label>
                {isEditing ? (
                  <Input
                    id="last_name"
                    value={formData.last_name || ''}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.lastName.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.last_name || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="email">{intl.formatMessage({ id: 'user.personal.fields.email' })}</Label>
                <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{profile.email}</span>
                  <Badge variant="outline" className="ml-auto">{intl.formatMessage({ id: 'user.personal.fields.email.primary' })}</Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.personal.fields.email.note' })}
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">{intl.formatMessage({ id: 'user.personal.fields.phone' })}</Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.phone.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.phone || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Bio */}
            <div className="space-y-2">
              <Label htmlFor="bio">{intl.formatMessage({ id: 'user.personal.fields.bio' })}</Label>
              {isEditing ? (
                <Textarea
                  id="bio"
                  value={formData.bio || ''}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  placeholder={intl.formatMessage({ id: 'user.personal.fields.bio.placeholder' })}
                  rows={3}
                />
              ) : (
                <div className="p-2 border rounded-md bg-muted/50 min-h-[80px]">
                  <span>{profile.bio || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                </div>
              )}
            </div>

            {/* Location and Professional Info */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="location">{intl.formatMessage({ id: 'user.personal.fields.location' })}</Label>
                {isEditing ? (
                  <Input
                    id="location"
                    value={formData.location || ''}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.location.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.location || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="job_title">{intl.formatMessage({ id: 'user.personal.fields.jobTitle' })}</Label>
                {isEditing ? (
                  <Input
                    id="job_title"
                    value={formData.job_title || ''}
                    onChange={(e) => handleInputChange('job_title', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.jobTitle.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.job_title || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="company">{intl.formatMessage({ id: 'user.personal.fields.company' })}</Label>
                {isEditing ? (
                  <Input
                    id="company"
                    value={formData.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.company.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.company || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">{intl.formatMessage({ id: 'user.personal.fields.timezone' })}</Label>
                {isEditing ? (
                  <Input
                    id="timezone"
                    value={formData.timezone || ''}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                    placeholder={intl.formatMessage({ id: 'user.personal.fields.timezone.placeholder' })}
                  />
                ) : (
                  <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.timezone || intl.formatMessage({ id: 'user.personal.fields.notProvided' })}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.personal.account.title' })}</CardTitle>
          <CardDescription>{intl.formatMessage({ id: 'user.personal.account.description' })}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <Label className="text-sm font-medium">{intl.formatMessage({ id: 'user.personal.fields.username' })}</Label>
              <p className="text-sm text-muted-foreground mt-1">@{profile.username}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">{intl.formatMessage({ id: 'user.personal.account.memberSince' })}</Label>
              <p className="text-sm text-muted-foreground mt-1">
                {new Date(profile.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">{intl.formatMessage({ id: 'user.personal.account.lastLogin' })}</Label>
              <p className="text-sm text-muted-foreground mt-1">
                {new Date(profile.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserProfilePage;
