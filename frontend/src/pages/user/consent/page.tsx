import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Shield, 
  User, 
  Mail, 
  Globe, 
  Eye,
  Settings,
  Clock,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  History,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConsentAuditEntry {
  id: string;
  user_id: string;
  application_id: string;
  application_name: string;
  action: 'granted' | 'revoked' | 'expired' | 'updated';
  scopes: string[];
  previous_scopes?: string[];
  reason?: string;
  timestamp: string;
}

interface ConsentInfo {
  id: string;
  user_id: string;
  application_id: string;
  application_name: string;
  scopes: string[];
  granted_at: string;
  expires_at?: string;
  is_active: boolean;
  is_expired: boolean;
  is_valid: boolean;
}

/**
 * ConsentHistoryPage Component
 * 
 * Displays comprehensive consent history and management including:
 * - Current active consents with scope details
 * - Complete audit trail of consent changes
 * - Consent revocation controls
 * - Scope change history
 * - GDPR-compliant consent management
 */
const ConsentHistoryPage: React.FC = () => {
  const intl = useIntl();
  const [consents, setConsents] = useState<ConsentInfo[]>([]);
  const [auditLog, setAuditLog] = useState<ConsentAuditEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRevoking, setIsRevoking] = useState<string | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  // Fetch user consents
  const fetchConsents = async () => {
    try {
      const response = await fetch(`/api/v1/consent/my-consents?active_only=${showActiveOnly}`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setConsents(data);
      }
    } catch (error) {
      console.error('Failed to fetch consents:', error);
    }
  };

  // Fetch consent audit log
  const fetchAuditLog = async () => {
    try {
      const response = await fetch('/api/v1/consent/audit-log?limit=50', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setAuditLog(data);
      }
    } catch (error) {
      console.error('Failed to fetch audit log:', error);
    }
  };

  // Load data
  const loadData = async () => {
    setIsLoading(true);
    await Promise.all([fetchConsents(), fetchAuditLog()]);
    setIsLoading(false);
  };

  // Revoke consent
  const revokeConsent = async (applicationId: string, applicationName: string) => {
    try {
      setIsRevoking(applicationId);
      const response = await fetch('/api/v1/consent/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          application_id: applicationId,
          reason: `User revoked consent for ${applicationName}`,
        }),
      });
      
      if (response.ok) {
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to revoke consent:', error);
    } finally {
      setIsRevoking(null);
    }
  };

  // Get scope icon
  const getScopeIcon = (scope: string) => {
    switch (scope) {
      case 'openid':
        return <Shield className="h-3 w-3" />;
      case 'profile':
        return <User className="h-3 w-3" />;
      case 'email':
        return <Mail className="h-3 w-3" />;
      case 'read':
        return <Eye className="h-3 w-3" />;
      case 'write':
        return <Settings className="h-3 w-3" />;
      default:
        return <Globe className="h-3 w-3" />;
    }
  };

  // Get scope display name
  const getScopeName = (scope: string) => {
    const scopeNames: Record<string, string> = {
      'openid': intl.formatMessage({ id: 'user.consent.scope.openid', defaultMessage: 'Basic Authentication' }),
      'profile': intl.formatMessage({ id: 'user.consent.scope.profile', defaultMessage: 'Profile Information' }),
      'email': intl.formatMessage({ id: 'user.consent.scope.email', defaultMessage: 'Email Address' }),
      'read': intl.formatMessage({ id: 'user.consent.scope.read', defaultMessage: 'Read Access' }),
      'write': intl.formatMessage({ id: 'user.consent.scope.write', defaultMessage: 'Write Access' }),
    };
    return scopeNames[scope] || scope.charAt(0).toUpperCase() + scope.slice(1);
  };

  // Get action icon and color
  const getActionDisplay = (action: string) => {
    switch (action) {
      case 'granted':
        return {
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          color: 'text-green-700',
          bg: 'bg-green-50 border-green-200',
          label: intl.formatMessage({ id: 'user.consent.action.granted', defaultMessage: 'Granted' }),
        };
      case 'revoked':
        return {
          icon: <XCircle className="h-4 w-4 text-red-500" />,
          color: 'text-red-700',
          bg: 'bg-red-50 border-red-200',
          label: intl.formatMessage({ id: 'user.consent.action.revoked', defaultMessage: 'Revoked' }),
        };
      case 'expired':
        return {
          icon: <Clock className="h-4 w-4 text-amber-500" />,
          color: 'text-amber-700',
          bg: 'bg-amber-50 border-amber-200',
          label: intl.formatMessage({ id: 'user.consent.action.expired', defaultMessage: 'Expired' }),
        };
      case 'updated':
        return {
          icon: <RefreshCw className="h-4 w-4 text-blue-500" />,
          color: 'text-blue-700',
          bg: 'bg-blue-50 border-blue-200',
          label: intl.formatMessage({ id: 'user.consent.action.updated', defaultMessage: 'Updated' }),
        };
      default:
        return {
          icon: <History className="h-4 w-4 text-gray-500" />,
          color: 'text-gray-700',
          bg: 'bg-gray-50 border-gray-200',
          label: action,
        };
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  useEffect(() => {
    loadData();
  }, [showActiveOnly]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            {intl.formatMessage({ id: 'user.consent.history.title', defaultMessage: 'Consent History' })}
          </h1>
          <p className="text-muted-foreground mt-1">
            {intl.formatMessage({
              id: 'user.consent.history.subtitle',
              defaultMessage: 'Manage your application permissions and view consent history'
            })}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={showActiveOnly ? "default" : "outline"}
            onClick={() => setShowActiveOnly(!showActiveOnly)}
          >
            <Filter className="h-4 w-4 mr-2" />
            {showActiveOnly
              ? intl.formatMessage({ id: 'user.consent.filter.active', defaultMessage: 'Active Only' })
              : intl.formatMessage({ id: 'user.consent.filter.all', defaultMessage: 'Show All' })
            }
          </Button>
          <Button
            variant="outline"
            onClick={loadData}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
            {intl.formatMessage({ id: 'user.consent.refresh', defaultMessage: 'Refresh' })}
          </Button>
        </div>
      </div>

      {/* Current Consents */}
      <div>
        <h2 className="text-lg font-semibold mb-4">
          {intl.formatMessage({ 
            id: 'user.consent.current.title', 
            defaultMessage: 'Current Permissions' 
          })}
        </h2>
        <div className="grid gap-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : consents.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {intl.formatMessage({ 
                      id: 'user.consent.empty', 
                      defaultMessage: 'No consents found' 
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            consents.map((consent) => (
              <Card key={consent.id} className={cn(
                "transition-colors",
                !consent.is_valid && "opacity-60"
              )}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-primary" />
                      {consent.application_name}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={consent.is_valid ? "default" : "secondary"}>
                        {consent.is_valid
                          ? intl.formatMessage({ id: 'user.consent.status.active', defaultMessage: 'Active' })
                          : consent.is_expired
                            ? intl.formatMessage({ id: 'user.consent.status.expired', defaultMessage: 'Expired' })
                            : intl.formatMessage({ id: 'user.consent.status.revoked', defaultMessage: 'Revoked' })
                        }
                      </Badge>
                      {consent.is_valid && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => revokeConsent(consent.application_id, consent.application_name)}
                          disabled={isRevoking === consent.application_id}
                        >
                          {isRevoking === consent.application_id ? (
                            <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-2" />
                          )}
                          {intl.formatMessage({ id: 'user.consent.revoke', defaultMessage: 'Revoke' })}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Granted Scopes */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">
                      {intl.formatMessage({ 
                        id: 'user.consent.scopes.granted', 
                        defaultMessage: 'Granted Permissions' 
                      })}
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {consent.scopes.map((scope) => (
                        <Badge key={scope} variant="outline" className="text-xs">
                          {getScopeIcon(scope)}
                          <span className="ml-1">{getScopeName(scope)}</span>
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Consent Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {intl.formatMessage({ id: 'user.consent.granted', defaultMessage: 'Granted:' })}
                      </span>
                      <span>{formatDate(consent.granted_at)}</span>
                    </div>
                    {consent.expires_at && (
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          {intl.formatMessage({ id: 'user.consent.expires', defaultMessage: 'Expires:' })}
                        </span>
                        <span>{formatDate(consent.expires_at)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Audit Log */}
      <div>
        <h2 className="text-lg font-semibold mb-4">
          {intl.formatMessage({ 
            id: 'user.consent.audit.title', 
            defaultMessage: 'Consent History' 
          })}
        </h2>
        <div className="space-y-3">
          {auditLog.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {intl.formatMessage({ 
                      id: 'user.consent.audit.empty', 
                      defaultMessage: 'No consent history found' 
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            auditLog.map((entry) => {
              const actionDisplay = getActionDisplay(entry.action);
              return (
                <Card key={entry.id} className={cn("border-l-4", actionDisplay.bg)}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {actionDisplay.icon}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{entry.application_name}</span>
                            <Badge variant="outline" className={actionDisplay.color}>
                              {actionDisplay.label}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground mb-2">
                            {formatDate(entry.timestamp)}
                          </div>
                          {entry.scopes && entry.scopes.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-2">
                              {entry.scopes.map((scope) => (
                                <Badge key={scope} variant="secondary" className="text-xs">
                                  {getScopeIcon(scope)}
                                  <span className="ml-1">{getScopeName(scope)}</span>
                                </Badge>
                              ))}
                            </div>
                          )}
                          {entry.reason && (
                            <p className="text-xs text-muted-foreground italic">
                              {entry.reason}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default ConsentHistoryPage;
