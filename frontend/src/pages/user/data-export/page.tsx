import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Download,
  FileText,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Info,
  Shield,
  Database,
  Archive,
  Mail,
  User,
  Settings
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { Checkbox } from '@/components/ui/animate-ui/radix/checkbox';
import { useToast } from '@/hooks/use-toast';
import { getExportCategories, getExportRequests, requestDataExport, downloadExport } from '@/services/user-api';

interface ExportRequest {
  id: string;
  type: 'full' | 'profile' | 'activity' | 'applications';
  format: 'json' | 'csv' | 'pdf';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedAt: string;
  completedAt?: string;
  downloadUrl?: string;
  expiresAt?: string;
  fileSize?: string;
}

interface DataCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  included: boolean;
  estimatedSize: string;
}

/**
 * User Data Export Page
 * 
 * GDPR compliance data export functionality with download 
 * options and export history.
 */
const UserDataExportPage: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [exportFormat, setExportFormat] = useState<'json' | 'csv' | 'pdf'>('json');
  const [exportHistory, setExportHistory] = useState<ExportRequest[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [dataCategories, setDataCategories] = useState<DataCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Icon mapping for data categories
  const getIconForCategory = (categoryId: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      profile: <User className="h-4 w-4" />,
      activity: <Clock className="h-4 w-4" />,
      applications: <Settings className="h-4 w-4" />,
      security: <Shield className="h-4 w-4" />,
      communications: <Mail className="h-4 w-4" />
    };
    return iconMap[categoryId] || <Database className="h-4 w-4" />;
  };

  // Get translated name for data category
  const getTranslatedCategoryName = (categoryId: string) => {
    const keyMap: Record<string, string> = {
      profile: 'user.dataExport.categories.profile',
      activity: 'user.dataExport.categories.activity',
      applications: 'user.dataExport.categories.applications',
      security: 'user.dataExport.categories.security',
      communications: 'user.dataExport.categories.communications'
    };
    return intl.formatMessage({ id: keyMap[categoryId] || 'user.dataExport.categories.unknown' });
  };

  // Get translated description for data category
  const getTranslatedCategoryDescription = (categoryId: string) => {
    const keyMap: Record<string, string> = {
      profile: 'user.dataExport.categories.profile.description',
      activity: 'user.dataExport.categories.activity.description',
      applications: 'user.dataExport.categories.applications.description',
      security: 'user.dataExport.categories.security.description',
      communications: 'user.dataExport.categories.communications.description'
    };
    return intl.formatMessage({ id: keyMap[categoryId] || 'user.dataExport.categories.unknown.description' });
  };

  // Load data categories from API
  const loadDataCategories = async () => {
    try {
      setIsLoadingCategories(true);
      const categories = await getExportCategories();

      // Transform API response to match frontend interface with translations
      const transformedCategories: DataCategory[] = categories.map((cat: any) => ({
        id: cat.id,
        name: getTranslatedCategoryName(cat.id),
        description: getTranslatedCategoryDescription(cat.id),
        icon: getIconForCategory(cat.id),
        included: cat.included,
        estimatedSize: cat.estimated_size
      }));

      setDataCategories(transformedCategories);
    } catch (error) {
      console.error('Error loading data categories:', error);
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.loadCategoriesFailed.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.loadCategoriesFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoadingCategories(false);
    }
  };

  // Load export history from API
  const loadExportHistory = async () => {
    try {
      setIsLoadingHistory(true);
      const exports = await getExportRequests();

      // Transform API response to match frontend interface
      const transformedExports: ExportRequest[] = exports.map((exp: any) => ({
        id: exp.id,
        type: exp.export_type as 'full' | 'profile' | 'activity' | 'applications',
        format: 'json', // Default format since API doesn't specify
        status: exp.status as 'pending' | 'processing' | 'completed' | 'failed',
        requestedAt: exp.requested_at,
        completedAt: exp.completed_at,
        downloadUrl: exp.status === 'completed' ? `/api/v1/user/export/${exp.id}/download` : undefined,
        expiresAt: exp.expires_at,
        fileSize: exp.file_size
      }));

      setExportHistory(transformedExports);
    } catch (error) {
      console.error('Error loading export history:', error);
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.loadHistoryFailed.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.loadHistoryFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoadingHistory(false);
    }
  };

  useEffect(() => {
    loadDataCategories();
    loadExportHistory();
  }, []);

  const handleCategoryToggle = (categoryId: string) => {
    setDataCategories(prev => 
      prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, included: !cat.included }
          : cat
      )
    );
  };

  const handleRequestExport = async () => {
    const selectedCategories = dataCategories.filter(cat => cat.included);

    if (selectedCategories.length === 0) {
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.noDataSelected.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.noDataSelected.description' }),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const exportType = selectedCategories.length === dataCategories.length ? 'full' : 'profile';
      await requestDataExport(exportType);

      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.exportRequested.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.exportRequested.description' }),
      });

      // Reload export history to show the new request
      await loadExportHistory();
    } catch (error) {
      console.error('Error requesting export:', error);
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.exportFailed.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.exportFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (exportId: string, filename: string) => {
    try {
      await downloadExport(exportId);
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.downloadStarted.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.downloadStarted.description' }, { filename }),
      });
    } catch (error) {
      console.error('Error downloading export:', error);
      toast({
        title: intl.formatMessage({ id: 'user.dataExport.toast.downloadFailed.title' }),
        description: intl.formatMessage({ id: 'user.dataExport.toast.downloadFailed.description' }),
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing': return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'processing': return 'secondary';
      case 'pending': return 'outline';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  const getTotalEstimatedSize = () => {
    const selectedCategories = dataCategories.filter(cat => cat.included);
    const totalKB = selectedCategories.reduce((total, cat) => {
      const sizeMatch = cat.estimatedSize.match(/(\d+)/);
      return total + (sizeMatch ? parseInt(sizeMatch[1]) : 0);
    }, 0);
    return `${totalKB} KB`;
  };

  const isExportDisabled = () => {
    return dataCategories.filter(cat => cat.included).length === 0 || isLoading;
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Download className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.dataExport.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.dataExport.subtitle' })}
          </p>
        </div>
      </div>

      {/* GDPR Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>{intl.formatMessage({ id: 'user.dataExport.gdpr.title' })}:</strong> {intl.formatMessage({ id: 'user.dataExport.gdpr.description' })}
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Export Request Form */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                {intl.formatMessage({ id: 'user.dataExport.form.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'user.dataExport.form.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Data Categories */}
              <div className="space-y-4">
                <Label className="text-base font-medium">{intl.formatMessage({ id: 'user.dataExport.form.dataCategories' })}</Label>
                <div className="space-y-3">
                  {dataCategories.map((category) => (
                    <div key={category.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        id={category.id}
                        checked={category.included}
                        onCheckedChange={() => handleCategoryToggle(category.id)}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          {category.icon}
                          <Label htmlFor={category.id} className="font-medium cursor-pointer">
                            {category.name}
                          </Label>
                          <Badge variant="outline" className="text-xs">
                            {category.estimatedSize}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {category.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Export Format */}
              <div className="space-y-2">
                <Label>{intl.formatMessage({ id: 'user.dataExport.form.exportFormat' })}</Label>
                <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="json">{intl.formatMessage({ id: 'user.dataExport.format.json' })}</SelectItem>
                    <SelectItem value="csv">{intl.formatMessage({ id: 'user.dataExport.format.csv' })}</SelectItem>
                    <SelectItem value="pdf">{intl.formatMessage({ id: 'user.dataExport.format.pdf' })}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Export Summary */}
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{intl.formatMessage({ id: 'user.dataExport.summary.selectedCategories' })}:</span>
                  <span className="font-medium">
                    {dataCategories.filter(cat => cat.included).length} of {dataCategories.length}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{intl.formatMessage({ id: 'user.dataExport.summary.estimatedSize' })}:</span>
                  <span className="font-medium">{getTotalEstimatedSize()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{intl.formatMessage({ id: 'user.dataExport.summary.format' })}:</span>
                  <span className="font-medium uppercase">{exportFormat}</span>
                </div>
              </div>

              {/* Request Button */}
              <Button 
                onClick={handleRequestExport}
                disabled={isExportDisabled()}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {intl.formatMessage({ id: 'user.dataExport.requestingExport' })}
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    {intl.formatMessage({ id: 'user.dataExport.requestExport' })}
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Export Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {intl.formatMessage({ id: 'user.dataExport.info.title' })}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-sm">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>{intl.formatMessage({ id: 'user.dataExport.info.processingTime' })}</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>{intl.formatMessage({ id: 'user.dataExport.info.downloadValidity' })}</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>{intl.formatMessage({ id: 'user.dataExport.info.emailNotification' })}</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>{intl.formatMessage({ id: 'user.dataExport.info.encryption' })}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Export History */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5" />
                {intl.formatMessage({ id: 'user.dataExport.history.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'user.dataExport.history.description' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingHistory ? (
                <div className="text-center py-8 text-muted-foreground">
                  <RefreshCw className="h-8 w-8 mx-auto mb-2 opacity-50 animate-spin" />
                  <p>{intl.formatMessage({ id: 'user.dataExport.history.loading' })}</p>
                </div>
              ) : exportHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Archive className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>{intl.formatMessage({ id: 'user.dataExport.history.empty' })}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {exportHistory.map((exportReq) => (
                    <div key={exportReq.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="font-medium capitalize">
                            {exportReq.type} {intl.formatMessage({ id: 'user.dataExport.history.export' })}
                          </span>
                          <Badge variant={getStatusColor(exportReq.status) as any}>
                            {getStatusIcon(exportReq.status)}
                            <span className="ml-1 capitalize">{exportReq.status}</span>
                          </Badge>
                        </div>
                        <span className="text-sm text-muted-foreground uppercase">
                          {exportReq.format}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">{intl.formatMessage({ id: 'user.dataExport.history.requested' })}:</span>
                          <br />
                          {formatDate(exportReq.requestedAt)}
                        </div>
                        {exportReq.completedAt && (
                          <div>
                            <span className="font-medium">{intl.formatMessage({ id: 'user.dataExport.history.completed' })}:</span>
                            <br />
                            {formatDate(exportReq.completedAt)}
                          </div>
                        )}
                      </div>

                      {exportReq.fileSize && (
                        <div className="text-sm text-muted-foreground">
                          <span className="font-medium">{intl.formatMessage({ id: 'user.dataExport.history.fileSize' })}:</span> {exportReq.fileSize}
                        </div>
                      )}

                      {exportReq.status === 'completed' && exportReq.downloadUrl && (
                        <div className="flex items-center justify-between pt-2 border-t">
                          <div className="text-sm text-muted-foreground">
                            {intl.formatMessage({ id: 'user.dataExport.history.expires' })}: {exportReq.expiresAt && formatDate(exportReq.expiresAt)}
                          </div>
                          <Button 
                            size="sm"
                            onClick={() => handleDownload(exportReq.id, `export-${exportReq.id}.${exportReq.format}`)}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            {intl.formatMessage({ id: 'user.dataExport.history.download' })}
                          </Button>
                        </div>
                      )}
                      
                      {exportReq.status === 'processing' && (
                        <div className="text-sm text-blue-600 flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          {intl.formatMessage({ id: 'user.dataExport.history.processing' })}
                        </div>
                      )}
                      
                      {exportReq.status === 'failed' && (
                        <div className="text-sm text-red-600 flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4" />
                          {intl.formatMessage({ id: 'user.dataExport.history.failed' })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Data Retention Policy */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {intl.formatMessage({ id: 'user.dataExport.privacy.title' })}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-sm">
                <p>
                  <strong>{intl.formatMessage({ id: 'user.dataExport.privacy.exportFiles' })}:</strong> {intl.formatMessage({ id: 'user.dataExport.privacy.exportFiles.description' })}
                </p>
                <p>
                  <strong>{intl.formatMessage({ id: 'user.dataExport.privacy.accountData' })}:</strong> {intl.formatMessage({ id: 'user.dataExport.privacy.accountData.description' })}
                </p>
                <p>
                  <strong>{intl.formatMessage({ id: 'user.dataExport.privacy.activityLogs' })}:</strong> {intl.formatMessage({ id: 'user.dataExport.privacy.activityLogs.description' })}
                </p>
                <p>
                  <strong>{intl.formatMessage({ id: 'user.dataExport.privacy.securityLogs' })}:</strong> {intl.formatMessage({ id: 'user.dataExport.privacy.securityLogs.description' })}
                </p>
              </div>
              
              <div className="pt-3 border-t">
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.dataExport.privacy.contactSupport' })}
                  <Link to="/support" className="text-blue-600 hover:underline ml-1">
                    {intl.formatMessage({ id: 'user.dataExport.privacy.supportTeam' })}
                  </Link>.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UserDataExportPage;
