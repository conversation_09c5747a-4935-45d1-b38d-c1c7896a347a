import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import {
  User,
  ArrowRight,
  Code,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  FileText,
  Send,
  Plus,
  Eye,
  ArrowUp,
} from 'lucide-react';

import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Label } from '@/components/ui/shadcn/label';
import { PageLoader } from '@/components/base/page-loader';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';

// Import API service
import { roleTransitionApi, type DeveloperApplication } from '@/services/role-transition-api';

/**
 * User Role Transitions Page - P3-F007
 *
 * Allows users to apply for developer role upgrade
 * Located at /user/role-transitions with proper styling following project patterns
 */
const UserRoleTransitionsPage: React.FC = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();

  const [applications, setApplications] = useState<DeveloperApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    application_reason: '',
    technical_background: '',
    intended_use: '',
  });

  // Load existing applications
  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const data = await roleTransitionApi.getMyApplications();
      setApplications(data);
    } catch (error) {
      console.error('Failed to load applications:', error);
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.error.network' }),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.application_reason.trim()) {
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.error.reason.required' }),
        variant: 'destructive',
      });
      return;
    }

    if (formData.application_reason.trim().length < 50) {
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.error.reason.min.length' }),
        variant: 'destructive',
      });
      return;
    }

    try {
      setSubmitting(true);
      await roleTransitionApi.submitApplication({
        application_reason: formData.application_reason,
        technical_background: formData.technical_background || undefined,
        intended_use: formData.intended_use || undefined,
      });

      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.submitted' }),
        variant: 'default',
      });

      // Reset form and reload applications
      setFormData({ application_reason: '', technical_background: '', intended_use: '' });
      setShowForm(false);
      await loadApplications();
    } catch (error) {
      console.error('Failed to submit application:', error);
      toast({
        title: intl.formatMessage({ id: 'user.role.transition.message.submit.error' }),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'pending': return 'secondary';
      default: return 'outline';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  // Check if user can apply (no pending applications)
  const canApply = !applications.some(app => app.status === 'pending');
  const hasApproved = applications.some(app => app.status === 'approved');

  // Redirect if user is already developer or admin
  if (user && (user.role === 'developer' || user.role === 'admin')) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full max-w-4xl mx-auto">
        <Card className="shadow-sm">
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <CheckCircle className="h-16 w-16 mx-auto text-green-500 mb-4" />
              <h2 className="text-2xl font-bold mb-2">
                {intl.formatMessage({ id: 'user.role.transition.message.already.developer' })}
              </h2>
              <p className="text-muted-foreground mb-6">
                You already have {user.role} access and can create applications.
              </p>
              <Button onClick={() => navigate('/user/home')}>
                {intl.formatMessage({ id: 'common.back' })}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6 w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex flex-wrap items-center gap-2">
            <User className="h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0" />
            <ArrowRight className="h-4 w-4 sm:h-6 sm:w-6 text-muted-foreground flex-shrink-0" />
            <Code className="h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0" />
            <span className="break-words">
              {intl.formatMessage({ id: 'user.role.transition.title' })}
            </span>
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            {intl.formatMessage({ id: 'user.role.transition.subtitle' })}
          </p>
        </div>
      </div>

      {/* Current Status */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'user.role.transition.applicant.info' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="flex flex-wrap items-center gap-2">
              <User className="h-5 w-5 flex-shrink-0" />
              <span className="font-medium text-sm sm:text-base">
                {intl.formatMessage({ id: 'user.role.transition.current.role' })}:
              </span>
              <Badge variant="outline" className="text-xs sm:text-sm">
                {user?.role || 'user'}
              </Badge>
            </div>
            {hasApproved && (
              <Alert className="flex-1">
                <CheckCircle className="h-4 w-4 flex-shrink-0" />
                <AlertDescription className="text-sm">
                  {intl.formatMessage({ id: 'user.role.transition.message.already.developer' })}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Developer Benefits */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'user.role.transition.description' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-start gap-3">
              <Code className="h-5 w-5 mt-0.5 text-primary" />
              <div>
                <h4 className="font-medium">
                  {intl.formatMessage({ id: 'user.role.transition.benefits.api.title' })}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.benefits.api.description' })}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <FileText className="h-5 w-5 mt-0.5 text-primary" />
              <div>
                <h4 className="font-medium">
                  {intl.formatMessage({ id: 'user.role.transition.benefits.management.title' })}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'user.role.transition.benefits.management.description' })}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Application Form */}
      {!hasApproved && (
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <span className="text-lg sm:text-xl">
                {intl.formatMessage({ id: 'user.role.transition.application.form.title' })}
              </span>
              {canApply && !showForm && (
                <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="text-sm sm:text-base">
                    {intl.formatMessage({ id: 'user.role.transition.apply' })}
                  </span>
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!canApply && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {intl.formatMessage({ id: 'user.role.transition.message.pending.exists' })}
                </AlertDescription>
              </Alert>
            )}

            {showForm && canApply && (
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="application_reason" className="text-sm sm:text-base font-medium">
                    {intl.formatMessage({ id: 'user.role.transition.form.reason.label' })} *
                  </Label>
                  <Textarea
                    id="application_reason"
                    placeholder={intl.formatMessage({ id: 'user.role.transition.form.reason.placeholder' })}
                    value={formData.application_reason}
                    onChange={(e) => setFormData({ ...formData, application_reason: e.target.value })}
                    className="mt-1 w-full resize-none text-sm sm:text-base"
                    rows={6}
                    required
                    maxLength={1000}
                  />
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 text-xs sm:text-sm text-muted-foreground">
                    <span className="break-words">
                      {formData.application_reason.trim().length < 50
                        ? intl.formatMessage(
                            { id: 'user.role.transition.form.reason.min.help' },
                            { needed: 50 - formData.application_reason.trim().length }
                          )
                        : intl.formatMessage({ id: 'user.role.transition.form.reason.min.met' })
                      }
                    </span>
                    <span className="flex-shrink-0">{formData.application_reason.length}/1000</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="technical_background" className="text-sm sm:text-base font-medium">
                    {intl.formatMessage({ id: 'user.role.transition.form.technical.label' })}
                  </Label>
                  <Textarea
                    id="technical_background"
                    placeholder={intl.formatMessage({ id: 'user.role.transition.form.technical.placeholder' })}
                    value={formData.technical_background}
                    onChange={(e) => setFormData({ ...formData, technical_background: e.target.value })}
                    className="mt-1 w-full resize-none text-sm sm:text-base"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="intended_use" className="text-sm sm:text-base font-medium">
                    {intl.formatMessage({ id: 'user.role.transition.form.intended.label' })}
                  </Label>
                  <Textarea
                    id="intended_use"
                    placeholder={intl.formatMessage({ id: 'user.role.transition.form.intended.placeholder' })}
                    value={formData.intended_use}
                    onChange={(e) => setFormData({ ...formData, intended_use: e.target.value })}
                    className="mt-1 w-full resize-none text-sm sm:text-base"
                    rows={3}
                  />
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowForm(false)}
                    disabled={submitting}
                    className="w-full sm:w-auto order-2 sm:order-1"
                  >
                    <span className="text-sm sm:text-base">
                      {intl.formatMessage({ id: 'common.cancel' })}
                    </span>
                  </Button>
                  <Button
                    type="submit"
                    disabled={submitting || formData.application_reason.trim().length < 50}
                    className="w-full sm:w-auto order-1 sm:order-2"
                  >
                    <Send className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="text-sm sm:text-base">
                      {submitting ? (
                        intl.formatMessage({ id: 'user.role.transition.form.submitting' })
                      ) : (
                        intl.formatMessage({ id: 'user.role.transition.form.submit' })
                      )}
                    </span>
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      )}

      {/* Application History */}
      {applications.length > 0 && (
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>
              {intl.formatMessage({ id: 'user.role.transition.my.applications.title' })}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {applications.map((application) => (
                <div key={application.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(application.status)}
                      <Badge variant={getStatusBadgeVariant(application.status)}>
                        {intl.formatMessage({ id: `user.role.transition.status.${application.status}` })}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(application.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium mb-1">
                      {intl.formatMessage({ id: 'user.role.transition.details.reason' })}:
                    </p>
                    <p className="text-muted-foreground">{application.application_reason}</p>
                  </div>
                  {application.admin_notes && (
                    <div className="mt-3 p-3 bg-muted rounded-md">
                      <p className="text-sm font-medium mb-1">
                        {intl.formatMessage({ id: 'user.role.transition.details.admin.notes' })}:
                      </p>
                      <p className="text-sm text-muted-foreground">{application.admin_notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserRoleTransitionsPage;
