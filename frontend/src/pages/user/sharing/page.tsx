import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Share2, Users, Globe, Lock, Eye, EyeOff, Save, RefreshCw, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Switch } from '@/components/ui/animate-ui/radix/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { Label } from '@/components/ui/shadcn/label';
import { Separator } from '@/components/ui/shadcn/separator';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import axiosServices from '@/utils/api/axios';

interface SharingSettings {
  profile_sharing: string;
  activity_sharing: string;
  email_sharing: string;
  allow_friend_requests: boolean;
  show_online_status: boolean;
  allow_collaboration: boolean;
}

interface SharingRule {
  id: string;
  name: string;
  type: string;
  permissions: string[];
  created_at: string;
}

const UserSharingPage: React.FC = () => {
  const intl = useIntl();
  const [settings, setSettings] = useState<SharingSettings | null>(null);
  const [sharingRules, setSharingRules] = useState<SharingRule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadSharingSettings();
  }, []);

  const loadSharingSettings = async () => {
    try {
      setIsLoading(true);
      const response = await axiosServices.get('/api/v1/user/sharing');
      setSettings(response.data);
      
      // Note: Sharing rules functionality is not yet implemented in the backend
      // Only basic sharing settings are available
      setSharingRules([]);
    } catch (error) {
      console.error('Error loading sharing settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.loadFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      await axiosServices.put('/api/v1/user/sharing', settings);
      toast({
        title: intl.formatMessage({ id: 'user.sharing.toast.saveSuccess.title' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.saveSuccess.description' }),
      });
    } catch (error) {
      console.error('Error saving sharing settings:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.saveFailed.description' }),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (key: keyof SharingSettings, value: any) => {
    if (!settings) return;
    setSettings({
      ...settings,
      [key]: value
    });
  };

  const createSharingRule = async () => {
    try {
      const newRule = {
        name: 'New Sharing Rule',
        type: 'custom',
        permissions: ['read']
      };
      
      await axiosServices.post('/api/v1/user/sharing', newRule);
      
      // Note: Backend doesn't store sharing rules yet, so we don't update local state
      // In a full implementation, we would reload the rules from the server
      
      toast({
        title: intl.formatMessage({ id: 'user.sharing.toast.ruleCreated.title' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.ruleCreated.description' }),
      });
    } catch (error) {
      console.error('Error creating sharing rule:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.createRuleFailed.description' }),
        variant: "destructive",
      });
    }
  };

  const deleteSharingRule = async (ruleId: string) => {
    try {
      await axiosServices.delete(`/api/v1/user/sharing/${ruleId}`);
      setSharingRules(sharingRules.filter(rule => rule.id !== ruleId));
      
      toast({
        title: intl.formatMessage({ id: 'user.sharing.toast.ruleDeleted.title' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.ruleDeleted.description' }),
      });
    } catch (error) {
      console.error('Error deleting sharing rule:', error);
      toast({
        title: intl.formatMessage({ id: 'common.error' }),
        description: intl.formatMessage({ id: 'user.sharing.toast.deleteRuleFailed.description' }),
        variant: "destructive",
      });
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <Globe className="h-4 w-4" />;
      case 'private':
        return <Lock className="h-4 w-4" />;
      case 'friends':
        return <Users className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return <PageLoader />;
  }

  if (!settings) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="text-center py-12">
          <h1 className="text-3xl font-bold tracking-tight mb-4">Error Loading Settings</h1>
          <p className="text-muted-foreground mb-6">Unable to load your sharing settings.</p>
          <Button onClick={loadSharingSettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Share2 className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.sharing.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.sharing.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSharingSettings} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.sharing.refresh' })}
          </Button>
          <Button onClick={saveSettings} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? intl.formatMessage({ id: 'user.sharing.saving' }) : intl.formatMessage({ id: 'user.sharing.save' })}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Privacy Controls */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Privacy Controls
            </CardTitle>
            <CardDescription>
              Control who can see your information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="profile-sharing" className="flex items-center gap-2">
                  {getVisibilityIcon(settings.profile_sharing)}
                  Profile Visibility
                </Label>
                <Select 
                  value={settings.profile_sharing} 
                  onValueChange={(value) => updateSetting('profile_sharing', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="friends">Friends Only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="activity-sharing" className="flex items-center gap-2">
                  {getVisibilityIcon(settings.activity_sharing)}
                  Activity Visibility
                </Label>
                <Select 
                  value={settings.activity_sharing} 
                  onValueChange={(value) => updateSetting('activity_sharing', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="friends">Friends Only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-sharing" className="flex items-center gap-2">
                  {getVisibilityIcon(settings.email_sharing)}
                  Email Visibility
                </Label>
                <Select 
                  value={settings.email_sharing} 
                  onValueChange={(value) => updateSetting('email_sharing', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="friends">Friends Only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Social Features */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Social Features
            </CardTitle>
            <CardDescription>
              Control social interactions and collaboration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="friend-requests">Allow Friend Requests</Label>
                <p className="text-sm text-muted-foreground">
                  Let other users send you friend requests
                </p>
              </div>
              <Switch
                id="friend-requests"
                checked={settings.allow_friend_requests}
                onCheckedChange={(checked) => updateSetting('allow_friend_requests', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="online-status">Show Online Status</Label>
                <p className="text-sm text-muted-foreground">
                  Display when you're online to other users
                </p>
              </div>
              <Switch
                id="online-status"
                checked={settings.show_online_status}
                onCheckedChange={(checked) => updateSetting('show_online_status', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="collaboration">Allow Collaboration</Label>
                <p className="text-sm text-muted-foreground">
                  Enable collaborative features and shared workspaces
                </p>
              </div>
              <Switch
                id="collaboration"
                checked={settings.allow_collaboration}
                onCheckedChange={(checked) => updateSetting('allow_collaboration', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Sharing Rules */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Share2 className="h-5 w-5" />
                Sharing Rules
              </span>
              <Button size="sm" onClick={createSharingRule}>
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Button>
            </CardTitle>
            <CardDescription>
              Manage custom sharing rules for specific data or features
            </CardDescription>
          </CardHeader>
          <CardContent>
            {sharingRules.length === 0 ? (
              <div className="text-center py-8">
                <Share2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Sharing Rules</h3>
                <p className="text-muted-foreground mb-4">
                  Create custom sharing rules to control access to specific data or features.
                </p>
                <Button onClick={createSharingRule}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Rule
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {sharingRules.map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div>
                        <h4 className="font-medium">{rule.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {rule.type}
                          </Badge>
                          {rule.permissions.map((permission) => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteSharingRule(rule.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Privacy Notice */}
        <Alert>
          <Lock className="h-4 w-4" />
          <AlertDescription>
            <strong>Privacy Notice:</strong> Your data privacy is important to us. 
            These settings control how your information is shared within our platform. 
            We never share your personal data with third parties without your explicit consent.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

export default UserSharingPage;
