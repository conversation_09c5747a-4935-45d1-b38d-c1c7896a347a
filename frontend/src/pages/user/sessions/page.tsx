import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SessionInfo {
  id: string;
  device_fingerprint: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  last_activity: string;
  expires_at: string;
  is_active: boolean;
  is_current: boolean;
  location?: string;
  device_type: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  browser: string;
  os: string;
  connected_apps: Array<{
    id: string;
    name: string;
    last_used: string;
  }>;
}

/**
 * SessionManagementPage Component
 * 
 * Comprehensive session management dashboard that displays:
 * - All active user sessions with device information
 * - Session details including location, browser, OS
 * - Connected applications for each session
 * - Session termination controls
 * - Current session highlighting
 */
const SessionManagementPage: React.FC = () => {
  const intl = useIntl();
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTerminating, setIsTerminating] = useState<string | null>(null);

  // Fetch user sessions
  const fetchSessions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/v1/sessions/active', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions || []);
      }
    } catch (error) {
      console.error('Failed to fetch sessions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Terminate a specific session
  const terminateSession = async (sessionId: string) => {
    try {
      setIsTerminating(sessionId);
      const response = await fetch(`/api/v1/sessions/${sessionId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      
      if (response.ok) {
        // Refresh sessions list
        await fetchSessions();
      }
    } catch (error) {
      console.error('Failed to terminate session:', error);
    } finally {
      setIsTerminating(null);
    }
  };

  // Logout from all sessions
  const logoutAllSessions = async () => {
    try {
      const response = await fetch('/api/v1/sessions/logout-all', {
        method: 'POST',
        credentials: 'include',
      });
      
      if (response.ok) {
        // Redirect to login page
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Failed to logout from all sessions:', error);
    }
  };

  // Get device icon based on device type
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'tablet':
        return <Tablet className="h-4 w-4" />;
      case 'desktop':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Calculate time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return intl.formatMessage({ id: 'user.sessions.time.now', defaultMessage: 'Just now' });
    if (diffMins < 60) return intl.formatMessage({ id: 'user.sessions.time.minutes', defaultMessage: '{minutes} minutes ago' }, { minutes: diffMins });
    if (diffHours < 24) return intl.formatMessage({ id: 'user.sessions.time.hours', defaultMessage: '{hours} hours ago' }, { hours: diffHours });
    return intl.formatMessage({ id: 'user.sessions.time.days', defaultMessage: '{days} days ago' }, { days: diffDays });
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            {intl.formatMessage({ id: 'user.sessions.title', defaultMessage: 'Active Sessions' })}
          </h1>
          <p className="text-muted-foreground mt-1">
            {intl.formatMessage({
              id: 'user.sessions.subtitle',
              defaultMessage: 'Manage your active sessions and connected devices'
            })}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchSessions}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
            {intl.formatMessage({ id: 'user.sessions.refresh', defaultMessage: 'Refresh' })}
          </Button>
          <Button
            variant="destructive"
            onClick={logoutAllSessions}
          >
            <LogOut className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.sessions.logoutAll', defaultMessage: 'Logout All' })}
          </Button>
        </div>
      </div>

      {/* Sessions List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : sessions.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {intl.formatMessage({ 
                    id: 'user.sessions.empty', 
                    defaultMessage: 'No active sessions found' 
                  })}
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          sessions.map((session) => (
            <Card key={session.id} className={cn(
              "transition-colors",
              session.is_current && "border-primary/50 bg-primary/5"
            )}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {getDeviceIcon(session.device_type)}
                    <span>{session.browser} on {session.os}</span>
                    {session.is_current && (
                      <Badge variant="default" className="ml-2">
                        {intl.formatMessage({ id: 'user.sessions.current', defaultMessage: 'Current' })}
                      </Badge>
                    )}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant={session.is_active ? "default" : "secondary"}>
                      {session.is_active ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {session.is_active
                        ? intl.formatMessage({ id: 'user.sessions.active', defaultMessage: 'Active' })
                        : intl.formatMessage({ id: 'user.sessions.inactive', defaultMessage: 'Inactive' })
                      }
                    </Badge>
                    {!session.is_current && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => terminateSession(session.id)}
                        disabled={isTerminating === session.id}
                      >
                        {isTerminating === session.id ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <LogOut className="h-3 w-3" />
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Session Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {intl.formatMessage({ id: 'user.sessions.location', defaultMessage: 'Location:' })}
                    </span>
                    <span>{session.location || session.ip_address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {intl.formatMessage({ id: 'user.sessions.lastActivity', defaultMessage: 'Last Activity:' })}
                    </span>
                    <span>{getTimeAgo(session.last_activity)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {intl.formatMessage({ id: 'user.sessions.created', defaultMessage: 'Created:' })}
                    </span>
                    <span>{formatDate(session.created_at)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {intl.formatMessage({ id: 'user.sessions.expires', defaultMessage: 'Expires:' })}
                    </span>
                    <span>{formatDate(session.expires_at)}</span>
                  </div>
                </div>

                {/* Connected Applications */}
                {session.connected_apps.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium text-sm mb-2">
                        {intl.formatMessage({ 
                          id: 'user.sessions.connectedApps', 
                          defaultMessage: 'Connected Applications' 
                        })}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {session.connected_apps.map((app) => (
                          <Badge key={app.id} variant="outline" className="text-xs">
                            {app.name}
                            <span className="ml-1 text-muted-foreground">
                              ({getTimeAgo(app.last_used)})
                            </span>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default SessionManagementPage;
