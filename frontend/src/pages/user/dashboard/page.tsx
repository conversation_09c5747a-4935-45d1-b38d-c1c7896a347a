import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  Activity,
  Settings,
  Smartphone,
  Globe,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import StyledTabs from '@/components/common/styled-tabs';
import FilterPanel from '@/components/common/filter-panel';
import InvitationDashboardWidget from '@/components/features/user/invitation-dashboard-widget';
import { useUserConnectedApplications, useUserActivity, useUserPermissions, useUserDashboardStats } from '@/hooks/use-user-api';
import useAuth from '@/hooks/use-auth';
import type { ConnectedApplication, UserActivity } from '@/types/api/user';
import {
  getApplicationFilterCategories,
  getActivityFilterCategories,
  filterApplications,
  filterActivities
} from '@/data/user-filters';

/**
 * User Dashboard Page
 * 
 * Central hub for end users to manage their connected applications,
 * view permissions, monitor activity, and control privacy settings.
 */
const UserDashboard: React.FC = () => {
  const intl = useIntl();
  const [stats, setStats] = useState({
    connectedApps: 0,
    activeSessions: 0,
    recentActivity: 0,
    securityAlerts: 0
  });

  // Tab and filter state
  const [activeTab, setActiveTab] = useState('overview');
  const [appSearchTerm, setAppSearchTerm] = useState('');
  const [appStatusFilter, setAppStatusFilter] = useState<string>('all');
  const [appTypeFilter, setAppTypeFilter] = useState<string>('all');
  const [appPermissionFilter, setAppPermissionFilter] = useState<string>('all');
  const [activitySearchTerm, setActivitySearchTerm] = useState('');
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>('all');
  const [activityTimeframeFilter, setActivityTimeframeFilter] = useState<string>('all');

  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Only load data after authentication is confirmed
  const applications = useUserConnectedApplications({}, isAuthenticated && !authLoading);
  const activity = useUserActivity({ limit: 10 }, isAuthenticated && !authLoading);
  const permissions = useUserPermissions(isAuthenticated && !authLoading);
  const dashboardStats = useUserDashboardStats(isAuthenticated && !authLoading);

  // Load real statistics from backend data
  useEffect(() => {
    if (applications && activity && dashboardStats.stats) {
      setStats({
        connectedApps: applications?.applications?.length || 0,
        activeSessions: dashboardStats.stats.activeSessions,
        recentActivity: activity?.activity?.length || 0,
        securityAlerts: dashboardStats.stats.securityAlerts
      });
    }
  }, [applications, activity, dashboardStats.stats]);

  const refreshData = () => {
    window.location.reload();
  };

  const quickActions = [
    {
      title: intl.formatMessage({ id: 'user.dashboard.quickActions.connectedApps.title' }),
      description: intl.formatMessage({ id: 'user.dashboard.quickActions.connectedApps.description' }),
      icon: Globe,
      href: '/user/applications',
      color: 'bg-primary text-primary-foreground',
      featured: true
    },
    {
      title: intl.formatMessage({ id: 'user.dashboard.quickActions.privacy.title' }),
      description: intl.formatMessage({ id: 'user.dashboard.quickActions.privacy.description' }),
      icon: Shield,
      href: '/user/permissions',
      color: 'bg-secondary text-secondary-foreground'
    },
    {
      title: intl.formatMessage({ id: 'user.dashboard.quickActions.activity.title' }),
      description: intl.formatMessage({ id: 'user.dashboard.quickActions.activity.description' }),
      icon: Activity,
      href: '/user/activity',
      color: 'bg-accent text-accent-foreground'
    },
    {
      title: intl.formatMessage({ id: 'user.dashboard.quickActions.security.title' }),
      description: intl.formatMessage({ id: 'user.dashboard.quickActions.security.description' }),
      icon: Settings,
      href: '/user/security',
      color: 'bg-muted text-muted-foreground'
    }
  ];

  // Filter categories
  const appFilterCategories = getApplicationFilterCategories(
    intl,
    appStatusFilter,
    appTypeFilter,
    appPermissionFilter,
    (value) => setAppStatusFilter(Array.isArray(value) ? value[0] : value),
    (value) => setAppTypeFilter(Array.isArray(value) ? value[0] : value),
    (value) => setAppPermissionFilter(Array.isArray(value) ? value[0] : value)
  );

  const activityFilterCategories = getActivityFilterCategories(
    intl,
    activityTypeFilter,
    activityTimeframeFilter,
    (value) => setActivityTypeFilter(Array.isArray(value) ? value[0] : value),
    (value) => setActivityTimeframeFilter(Array.isArray(value) ? value[0] : value)
  );

  // Reset functions
  const resetAppFilters = () => {
    setAppSearchTerm('');
    setAppStatusFilter('all');
    setAppTypeFilter('all');
    setAppPermissionFilter('all');
  };

  const resetActivityFilters = () => {
    setActivitySearchTerm('');
    setActivityTypeFilter('all');
    setActivityTimeframeFilter('all');
  };

  // Filtered data with enhanced error handling
  const filteredApplications = useMemo(() => {
    try {
      // Ensure we have a valid array to work with
      const appsArray = applications?.applications;
      if (!appsArray || !Array.isArray(appsArray)) {
        return [];
      }

      return filterApplications(
        appsArray,
        appSearchTerm,
        appStatusFilter,
        appTypeFilter,
        appPermissionFilter
      );
    } catch (error) {
      console.error('Error filtering applications:', error);
      return [];
    }
  }, [applications?.applications, appSearchTerm, appStatusFilter, appTypeFilter, appPermissionFilter]);

  const filteredActivities = useMemo(() => {
    try {
      // Ensure we have a valid array to work with
      const activityArray = activity?.activity;
      if (!activityArray || !Array.isArray(activityArray)) {
        return [];
      }

      return filterActivities(
        activityArray,
        activitySearchTerm,
        activityTypeFilter,
        activityTimeframeFilter
      );
    } catch (error) {
      console.error('Error filtering activities:', error);
      return [];
    }
  }, [activity?.activity, activitySearchTerm, activityTypeFilter, activityTimeframeFilter]);

  const recentApplications = (filteredApplications || []).slice(0, 3);
  const recentActivityItems = (filteredActivities || []).slice(0, 5);

  // Tab configuration
  const tabs = [
    { id: 'overview', label: intl.formatMessage({ id: 'user.dashboard.tabs.overview' }) },
    { id: 'applications', label: intl.formatMessage({ id: 'user.dashboard.tabs.applications' }) },
    { id: 'activity', label: intl.formatMessage({ id: 'user.dashboard.tabs.activity' }) },
    { id: 'permissions', label: intl.formatMessage({ id: 'user.dashboard.tabs.permissions' }) }
  ];

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'user.dashboard.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.dashboard.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.dashboard.refresh' })}
          </Button>
          <Button asChild>
            <Link to="/user/settings">
              <Settings className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'user.dashboard.settings' })}
            </Link>
          </Button>
        </div>
      </div>

      {/* Dashboard with Tabs */}
      <div className="space-y-6">
        <StyledTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          size="md"
          variant="default"
        />

        {activeTab === 'overview' && (
          <div className="space-y-6">
          {/* Quick Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.dashboard.stats.connectedApps' })}</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.connectedApps}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.dashboard.stats.connectedApps.description' })}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.dashboard.stats.activeSessions' })}</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeSessions}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.dashboard.stats.activeSessions.description' })}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.dashboard.stats.recentActivity' })}</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.recentActivity}</div>
                <p className="text-xs text-muted-foreground">
                  {intl.formatMessage({ id: 'user.dashboard.stats.recentActivity.description' })}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.dashboard.stats.securityStatus' })}</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{intl.formatMessage({ id: 'user.dashboard.stats.securityStatus.secure' })}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.securityAlerts} {intl.formatMessage({ id: 'user.dashboard.stats.securityStatus.alerts' })}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions Grid */}
          <div className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
            {quickActions.map((action, index) => (
              <Card key={index} className={`transition-all hover:shadow-sm flex flex-col h-full ${action.featured ? 'ring-2 ring-primary' : ''}`}>
                <CardHeader className="pb-3 flex-shrink-0">
                  <CardTitle className="flex items-center gap-2 text-sm font-medium">
                    <action.icon className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{action.title}</span>
                  </CardTitle>
                  <CardDescription className="text-xs line-clamp-2 min-h-[2.5rem]">
                    {action.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0 mt-auto">
                  <Button asChild className="w-full" size="sm" variant={action.featured ? 'default' : 'outline'}>
                    <Link to={action.href}>
                      {action.featured ? intl.formatMessage({ id: 'user.dashboard.quickActions.connectedApps.button' }) : intl.formatMessage({ id: 'user.dashboard.quickActions.privacy.button' })}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}

          </div>

          {/* Invitation Widget - Separate row for better layout */}
          <div className="grid gap-4">
            <InvitationDashboardWidget />
          </div>

          {/* Recent Activity & Connected Apps */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Recent Applications */}
            <Card className="flex flex-col h-full">
              <CardHeader className="flex-shrink-0">
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {intl.formatMessage({ id: 'user.dashboard.recentApps.title' })}
                </CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'user.dashboard.recentApps.description' })}</CardDescription>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                <div className="space-y-3 flex-1">
                  {recentApplications.length > 0 ? (
                    recentApplications.map((app: ConnectedApplication, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${app.is_active ? 'bg-green-500' : 'bg-gray-400'}`} />
                          <div>
                            <span className="font-medium">{app.application_name}</span>
                            <div className="text-xs text-muted-foreground">
                              {intl.formatMessage({ id: 'user.dashboard.recentApps.connected' })} {new Date(app.connected_at).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <Badge variant={app.is_active ? "default" : "secondary"}>
                          {app.is_active ? intl.formatMessage({ id: 'user.dashboard.recentApps.active' }) : intl.formatMessage({ id: 'user.dashboard.recentApps.inactive' })}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {intl.formatMessage({ id: 'user.dashboard.recentApps.empty' })}
                    </div>
                  )}
                </div>
                <div className="mt-4">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/user/applications">{intl.formatMessage({ id: 'user.dashboard.recentApps.viewAll' })}</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="flex flex-col h-full">
              <CardHeader className="flex-shrink-0">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  {intl.formatMessage({ id: 'user.dashboard.recentActivity.title' })}
                </CardTitle>
                <CardDescription>{intl.formatMessage({ id: 'user.dashboard.recentActivity.description' })}</CardDescription>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                <div className="space-y-3 flex-1">
                  {recentActivityItems.length > 0 ? (
                    recentActivityItems.map((item: UserActivity, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${item.success ? 'bg-green-500' : 'bg-red-500'}`} />
                          <div>
                            <span className="font-medium">{item.action}</span>
                            <div className="text-xs text-muted-foreground">
                              {item.application_name} • {new Date(item.timestamp).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <Badge variant={item.success ? "default" : "destructive"}>
                          {item.success ? intl.formatMessage({ id: 'user.dashboard.recentActivity.success' }) : intl.formatMessage({ id: 'user.dashboard.recentActivity.failed' })}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {intl.formatMessage({ id: 'user.dashboard.recentActivity.empty' })}
                    </div>
                  )}
                </div>
                <div className="mt-4">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/user/activity">{intl.formatMessage({ id: 'user.dashboard.recentActivity.viewFull' })}</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          </div>
        )}

        {activeTab === 'applications' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
              title={intl.formatMessage({ id: 'user.dashboard.filters.applications.title' })}
              filterCategories={appFilterCategories}
              searchQuery={appSearchTerm}
              onSearchChange={setAppSearchTerm}
              onClearSearch={() => setAppSearchTerm('')}
              onResetAll={resetAppFilters}
              variant="default"
              showIcons={true}
              showSearchBar={true}
              searchPlaceholder={intl.formatMessage({ id: 'user.dashboard.filters.applications.search' })}
              searchHotkey="cmd+f"
              size="md"
            />

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'user.dashboard.applications.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.dashboard.applications.totalCount' }, { count: (filteredApplications || []).length })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {(filteredApplications || []).length > 0 ? (
                  <div className="space-y-4">
                    {(filteredApplications || []).map((app: ConnectedApplication, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <Globe className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium">{app.application_name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {intl.formatMessage({ id: 'user.dashboard.applications.connected' })} {new Date(app.connected_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={app.is_active ? 'default' : 'secondary'}>
                            {app.is_active ? intl.formatMessage({ id: 'user.dashboard.applications.active' }) : intl.formatMessage({ id: 'user.dashboard.applications.inactive' })}
                          </Badge>
                          <Button variant="outline" size="sm">
                            {intl.formatMessage({ id: 'user.dashboard.applications.manage' })}
                          </Button>
                        </div>
                      </div>
                    ))}
                    {(filteredApplications || []).length > 5 && (
                      <div className="text-center py-4">
                        <Button asChild variant="outline">
                          <Link to="/user/applications">
                            {intl.formatMessage({ id: 'user.dashboard.applications.viewAll' }, { count: (filteredApplications || []).length })}
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Globe className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.dashboard.applications.noAppsFound' })}</h3>
                    <p className="text-muted-foreground mb-4">
                      {appSearchTerm || appStatusFilter !== 'all' || appTypeFilter !== 'all' || appPermissionFilter !== 'all'
                        ? intl.formatMessage({ id: 'user.dashboard.applications.noAppsFiltered' })
                        : intl.formatMessage({ id: 'user.dashboard.applications.noAppsConnected' })
                      }
                    </p>
                    <Button asChild>
                      <Link to="/user/applications">
                        {intl.formatMessage({ id: 'user.dashboard.applications.manageApplications' })}
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="space-y-6">
            {/* Filter Panel with integrated search */}
            <FilterPanel
              title={intl.formatMessage({ id: 'user.dashboard.filters.activity.title' })}
              filterCategories={activityFilterCategories}
              searchQuery={activitySearchTerm}
              onSearchChange={setActivitySearchTerm}
              onClearSearch={() => setActivitySearchTerm('')}
              onResetAll={resetActivityFilters}
              variant="default"
              showIcons={true}
              showSearchBar={true}
              searchPlaceholder={intl.formatMessage({ id: 'user.dashboard.filters.activity.search' })}
              searchHotkey="cmd+f"
              size="md"
            />

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'user.dashboard.activity.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.dashboard.activity.totalCount' }, { count: filteredActivities.length })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredActivities.length > 0 ? (
                  <div className="space-y-3">
                    {filteredActivities.slice(0, 10).map((item: UserActivity, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${item.success ? 'bg-green-500' : 'bg-red-500'}`} />
                          <div>
                            <span className="font-medium">{item.action}</span>
                            <div className="text-xs text-muted-foreground">
                              {item.application_name} • {new Date(item.timestamp).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <Badge variant={item.success ? "default" : "destructive"}>
                          {item.success ? intl.formatMessage({ id: 'user.dashboard.activity.success' }) : intl.formatMessage({ id: 'user.dashboard.activity.failed' })}
                        </Badge>
                      </div>
                    ))}
                    {filteredActivities.length > 10 && (
                      <div className="text-center py-4">
                        <Button asChild variant="outline">
                          <Link to="/user/activity">
                            {intl.formatMessage({ id: 'user.dashboard.activity.viewAll' }, { count: filteredActivities.length })}
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.dashboard.activity.noActivityFound' })}</h3>
                    <p className="text-muted-foreground mb-4">
                      {activitySearchTerm || activityTypeFilter !== 'all' || activityTimeframeFilter !== 'all'
                        ? intl.formatMessage({ id: 'user.dashboard.activity.noActivityFiltered' })
                        : intl.formatMessage({ id: 'user.dashboard.activity.noRecentActivity' })
                      }
                    </p>
                    <Button asChild>
                      <Link to="/user/activity">
                        {intl.formatMessage({ id: 'user.dashboard.activity.viewHistory' })}
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'permissions' && (
          <div className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>{intl.formatMessage({ id: 'user.dashboard.permissions.title' })}</CardTitle>
                <CardDescription>
                  {intl.formatMessage({ id: 'user.dashboard.permissions.description' })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.dashboard.permissions.heading' })}</h3>
                  <p className="text-muted-foreground mb-4">
                    {intl.formatMessage({ id: 'user.dashboard.permissions.subtext' })}
                  </p>
                  <Button asChild>
                    <Link to="/user/security">
                      {intl.formatMessage({ id: 'user.dashboard.permissions.button' })}
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserDashboard;
