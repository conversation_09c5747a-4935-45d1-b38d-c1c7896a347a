import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Shield,
  Activity,
  Settings,
  User,
  AlertTriangle,
  CheckCircle,
  Clock,
  Smartphone,
  Globe,
  Key,
  RefreshCw,
  Bell,
  Lock,
  Eye,
  Home
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { PageLoader } from '@/components/base/page-loader';
import { useUserProfile, useUserSecurityOverview, useUserApi } from '@/hooks/use-user-api';

/**
 * User Account Home Page
 * 
 * Central hub page with security alerts, quick actions, and account overview.
 * This is the main landing page for users when they access /user/home.
 */
const UserAccountHomePage: React.FC = () => {
  const intl = useIntl();
  const [stats, setStats] = useState({
    connectedApps: 0,
    activeSessions: 0,
    recentActivity: 0,
    securityAlerts: 0
  });

  const { profile, isLoading: profileLoading, error: profileError } = useUserProfile();
  const { securityOverview, isLoading: securityLoading, error: securityError } = useUserSecurityOverview();
  const { applications, activity } = useUserApi();

  const isLoading = profileLoading || securityLoading;
  const hasError = profileError || securityError;

  // Update stats when data loads with proper null checks
  useEffect(() => {
    if (securityOverview && applications?.applications && Array.isArray(applications.applications)) {
      setStats({
        connectedApps: applications.applications.length,
        activeSessions: securityOverview.active_sessions_count || 0,
        recentActivity: (activity?.activity && Array.isArray(activity.activity)) ? activity.activity.length : 0,
        securityAlerts: securityOverview.recent_security_events || 0
      });
    }
  }, [securityOverview, applications, activity]);

  const refreshData = () => {
    window.location.reload();
  };

  // Security alerts based on security overview
  const securityAlerts = [];
  if (securityOverview) {
    if (!securityOverview.two_factor_enabled) {
      securityAlerts.push({
        type: 'warning' as const,
        title: '2-Factor Authentication Disabled',
        description: 'Enable 2FA to secure your account',
        action: { text: 'Enable 2FA', href: '/user/security/2fa' }
      });
    }
    
    if (securityOverview.password_last_changed) {
      const passwordAge = new Date().getTime() - new Date(securityOverview.password_last_changed).getTime();
      const daysOld = Math.floor(passwordAge / (1000 * 60 * 60 * 24));
      if (daysOld > 90) {
        securityAlerts.push({
          type: 'warning' as const,
          title: 'Password Needs Update',
          description: `Your password is ${daysOld} days old. Consider updating it.`,
          action: { text: 'Change Password', href: '/user/security/password' }
        });
      }
    }
  }

  const quickActions = [
    {
      title: intl.formatMessage({ id: 'user.home.quickActions.personalInfo.title' }),
      description: intl.formatMessage({ id: 'user.home.quickActions.personalInfo.description' }),
      icon: User,
      href: '/user/profile',
      color: 'bg-primary text-primary-foreground',
      featured: true
    },
    {
      title: intl.formatMessage({ id: 'user.home.quickActions.security.title' }),
      description: intl.formatMessage({ id: 'user.home.quickActions.security.description' }),
      icon: Shield,
      href: '/user/security',
      color: 'bg-secondary text-secondary-foreground'
    },
    {
      title: intl.formatMessage({ id: 'user.home.quickActions.applications.title' }),
      description: intl.formatMessage({ id: 'user.home.quickActions.applications.description' }),
      icon: Globe,
      href: '/user/applications',
      color: 'bg-accent text-accent-foreground'
    },
    {
      title: intl.formatMessage({ id: 'user.home.quickActions.privacy.title' }),
      description: intl.formatMessage({ id: 'user.home.quickActions.privacy.description' }),
      icon: Lock,
      href: '/user/privacy',
      color: 'bg-muted text-muted-foreground'
    }
  ];

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Home className="h-8 w-8" />
            {intl.formatMessage({ id: 'user.home.title' })}
          </h1>
          <p className="text-muted-foreground">
            {profile?.first_name
              ? intl.formatMessage({ id: 'user.home.subtitle' }, { name: `, ${profile.first_name}` })
              : intl.formatMessage({ id: 'user.home.subtitleNoName' })
            }
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.home.refresh' })}
          </Button>
          <Button asChild>
            <Link to="/user/settings">
              <Settings className="h-4 w-4 mr-2" />
              {intl.formatMessage({ id: 'user.home.settings' })}
            </Link>
          </Button>
        </div>
      </div>

      {/* Security Alerts */}
      {securityAlerts.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">{intl.formatMessage({ id: 'user.home.security.recommendations' })}</h2>
          {securityAlerts.map((alert, index) => (
            <Alert key={index} className={alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50' : ''}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <div>
                  <strong>{alert.title}</strong>
                  <p className="text-sm text-muted-foreground mt-1">{alert.description}</p>
                </div>
                <Button asChild size="sm" variant="outline">
                  <Link to={alert.action.href}>{alert.action.text}</Link>
                </Button>
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Account Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.home.stats.connectedApps' })}</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.connectedApps}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.home.stats.connectedApps.description' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.home.stats.activeSessions' })}</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSessions}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.home.stats.activeSessions.description' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.home.stats.recentActivity' })}</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentActivity}</div>
            <p className="text-xs text-muted-foreground">
              {intl.formatMessage({ id: 'user.home.stats.recentActivity.description' })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{intl.formatMessage({ id: 'user.home.stats.securityStatus' })}</CardTitle>
            {stats.securityAlerts === 0 ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.securityAlerts === 0 ? intl.formatMessage({ id: 'user.home.stats.securityStatus.good' }) : stats.securityAlerts}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.securityAlerts === 0 ? intl.formatMessage({ id: 'user.home.stats.securityStatus.noIssues' }) : intl.formatMessage({ id: 'user.home.stats.securityStatus.issuesReview' })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold mb-4">{intl.formatMessage({ id: 'user.home.quickActions.title' })}</h2>
        <div className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
          {quickActions.map((action, index) => (
            <Card key={index} className="hover:shadow-sm transition-shadow flex flex-col h-full">
              <CardHeader className="pb-3 flex-shrink-0">
                <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-3`}>
                  <action.icon className="h-5 w-5" />
                </div>
                <CardTitle className="text-base font-medium leading-tight line-clamp-2">
                  {action.title}
                </CardTitle>
                <CardDescription className="text-sm line-clamp-2 min-h-[2.5rem]">
                  {action.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0 mt-auto">
                <Button asChild className="w-full" size="sm">
                  <Link to={action.href} className="truncate">
                    {action.title.length > 15
                      ? intl.formatMessage({ id: 'user.home.quickActions.view' })
                      : intl.formatMessage({ id: 'user.home.quickActions.goTo' }, { title: action.title })
                    }
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Account Summary */}
      {profile && (
        <Card>
          <CardHeader>
            <CardTitle>{intl.formatMessage({ id: 'user.home.accountSummary.title' })}</CardTitle>
            <CardDescription>{intl.formatMessage({ id: 'user.home.accountSummary.description' })}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'user.home.accountSummary.profileInfo' })}</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.name' })}</strong> {profile.first_name} {profile.last_name}</p>
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.email' })}</strong> {profile.email}</p>
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.username' })}</strong> {profile.username}</p>
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.memberSince' })}</strong> {new Date(profile.created_at).toLocaleDateString()}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">{intl.formatMessage({ id: 'user.home.accountSummary.securityInfo' })}</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.lastLogin' })}</strong> {profile.last_login ? new Date(profile.last_login).toLocaleString() : intl.formatMessage({ id: 'user.home.accountSummary.lastLoginNever' })}</p>
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.twoFactorStatus' })}</strong>
                    <Badge variant={securityOverview?.two_factor_enabled ? 'default' : 'secondary'} className="ml-2">
                      {securityOverview?.two_factor_enabled ? intl.formatMessage({ id: 'user.home.accountSummary.enabled' }) : intl.formatMessage({ id: 'user.home.accountSummary.disabled' })}
                    </Badge>
                  </p>
                  <p><strong>{intl.formatMessage({ id: 'user.home.accountSummary.accountStatus' })}</strong>
                    <Badge variant={profile.is_active ? 'default' : 'destructive'} className="ml-2">
                      {profile.is_active ? intl.formatMessage({ id: 'user.home.accountSummary.active' }) : intl.formatMessage({ id: 'user.home.accountSummary.inactive' })}
                    </Badge>
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserAccountHomePage;
