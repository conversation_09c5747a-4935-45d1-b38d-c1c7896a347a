/**
 * User Activity Page
 * 
 * Displays user's activity history and login logs
 */

import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { 
  Activity, 
  Calendar, 
  MapPin, 
  Clock, 
  CheckCircle, 
  XCircle,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Input } from '@/components/ui/shadcn/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import FilterPanel from '@/components/common/filter-panel';
import { useUserActivity } from '@/hooks/use-user-api';
import useAuth from '@/hooks/use-auth';
import type { UserActivity } from '@/types/api/user';
import {
  getActivityFilterCategories,
  filterActivities
} from '@/data/user-filters';

/**
 * User Activity Page
 * 
 * Central page for users to view their complete activity history,
 * login logs, and account access patterns.
 */
const UserActivityPage: React.FC = () => {
  const intl = useIntl();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [timeframeFilter, setTimeframeFilter] = useState('all');

  // Load activity data
  const { activity, total, isLoading, error, refreshData } = useUserActivity(
    { limit: 100 }, 
    isAuthenticated && !authLoading
  );

  // Filter categories
  const filterCategories = getActivityFilterCategories(
    intl,
    typeFilter,
    timeframeFilter,
    (value: string | string[]) => setTypeFilter(Array.isArray(value) ? value[0] : value),
    (value: string | string[]) => setTimeframeFilter(Array.isArray(value) ? value[0] : value)
  );

  // Reset filters
  const resetFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setTimeframeFilter('all');
  };

  // Filtered activities
  const filteredActivities = useMemo(() => {
    try {
      const activityArray = activity || [];
      return filterActivities(
        activityArray,
        searchTerm,
        typeFilter,
        timeframeFilter
      );
    } catch (error) {
      console.error('Error filtering activities:', error);
      return [];
    }
  }, [activity, searchTerm, typeFilter, timeframeFilter]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return intl.formatMessage({ id: 'user.activity.date.invalid' });
    }
  };

  const getActivityIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login':
      case 'oauth2_authorization':
        return CheckCircle;
      case 'logout':
        return XCircle;
      default:
        return Activity;
    }
  };

  const getActivityName = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login':
        return intl.formatMessage({ id: 'user.activity.actions.login' });
      case 'logout':
        return intl.formatMessage({ id: 'user.activity.actions.logout' });
      case 'oauth2_authorization':
        return intl.formatMessage({ id: 'user.activity.actions.oauth2Authorization' });
      case 'password_change':
        return intl.formatMessage({ id: 'user.activity.actions.passwordChange' });
      case 'profile_update':
        return intl.formatMessage({ id: 'user.activity.actions.profileUpdate' });
      case 'app_authorization':
        return intl.formatMessage({ id: 'user.activity.actions.appAuthorization' });
      case 'permission_grant':
        return intl.formatMessage({ id: 'user.activity.actions.permissionGrant' });
      case 'security_event':
        return intl.formatMessage({ id: 'user.activity.actions.securityEvent' });
      default:
        return action; // fallback to original action if no translation found
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'user.activity.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.activity.subtitle' })}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refreshData()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {intl.formatMessage({ id: 'user.activity.refresh' })}
          </Button>
        </div>
      </div>

      {/* Filter Panel */}
      <FilterPanel
        title={intl.formatMessage({ id: 'user.activity.filters.title' })}
        filterCategories={filterCategories}
        searchQuery={searchTerm}
        onSearchChange={setSearchTerm}
        onClearSearch={() => setSearchTerm('')}
        onResetAll={resetFilters}
        variant="default"
        showIcons={true}
        showSearchBar={true}
        searchPlaceholder={intl.formatMessage({ id: 'user.activity.filters.search' })}
        searchHotkey="cmd+f"
        size="md"
      />

      {/* Activity List */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>{intl.formatMessage({ id: 'user.activity.log.title' })}</CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'user.activity.log.description' }, {
              filtered: filteredActivities.length,
              total: total
            })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="text-center py-8">
              <XCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.activity.error.title' })}</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => refreshData()}>{intl.formatMessage({ id: 'user.activity.error.tryAgain' })}</Button>
            </div>
          )}

          {!error && filteredActivities.length > 0 ? (
            <div className="space-y-3">
              {filteredActivities.map((item: UserActivity, index: number) => {
                const IconComponent = getActivityIcon(item.action);
                return (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        item.success ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                      }`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{getActivityName(item.action)}</span>
                          <Badge variant={item.success ? "default" : "destructive"}>
                            {item.success ? intl.formatMessage({ id: 'user.activity.status.success' }) : intl.formatMessage({ id: 'user.activity.status.failed' })}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {item.application_name && (
                            <span className="mr-4">{intl.formatMessage({ id: 'user.activity.info.app' })}: {item.application_name}</span>
                          )}
                          {item.ip_address && (
                            <span className="mr-4">IP: {item.ip_address}</span>
                          )}
                          {item.location && (
                            <span>{intl.formatMessage({ id: 'user.activity.info.location' })}: {item.location}</span>
                          )}
                        </div>
                        {item.details && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {item.details}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatDate(item.timestamp)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : !error && (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'user.activity.empty.title' })}</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || typeFilter !== 'all' || timeframeFilter !== 'all'
                  ? intl.formatMessage({ id: 'user.activity.noData.description' })
                  : intl.formatMessage({ id: 'user.activity.empty.description' })
                }
              </p>
              {(searchTerm || typeFilter !== 'all' || timeframeFilter !== 'all') && (
                <Button variant="outline" onClick={resetFilters}>
                  {intl.formatMessage({ id: 'user.activity.filters.reset' })}
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.activity.quickActions.security.title' })}</CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.activity.quickActions.security.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link to="/user/security">
                {intl.formatMessage({ id: 'user.activity.quickActions.security.button' })}
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{intl.formatMessage({ id: 'user.activity.quickActions.applications.title' })}</CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.activity.quickActions.applications.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full" variant="outline">
              <Link to="/user/applications">
                {intl.formatMessage({ id: 'user.activity.quickActions.applications.button' })}
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserActivityPage;
