import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom';

// project imports
import { ConfigProvider, useConfig } from './contexts/config-context';
import { AuthProvider } from './contexts/auth-context';
import { TooltipProvider } from './components/ui/shadcn/tooltip';
import Locales from './components/base/locales';
import NavigationScroll from './layout/navigation-scroll';

// routes
import ThemeRoutes from './routes';

function AppContent() {
  const { fontSize, theme } = useConfig();

  // Apply font size and theme to document root
  useEffect(() => {
    const root = window.document.documentElement;

    // Remove existing font size classes
    root.classList.remove('font-size-small', 'font-size-default', 'font-size-large');

    // Apply new font size class
    root.classList.add(`font-size-${fontSize}`);
  }, [fontSize]);

  // Apply theme to document root
  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(theme);
  }, [theme]);

  return (
    <Router>
      <NavigationScroll>
        <TooltipProvider>
          <div className="App">
            <ThemeRoutes />
          </div>
        </TooltipProvider>
      </NavigationScroll>
    </Router>
  );
}

function App() {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Locales>
          <AppContent />
        </Locales>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;