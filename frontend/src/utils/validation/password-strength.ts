/**
 * Password Strength Utilities
 *
 * This module provides password validation and strength calculation utilities
 */
import { NumbColorFunc, StringBoolFunc, StringNumFunc } from '@/types';

// Color values for strength indicators
const strengthColors = {
  errorMain: '#f44336',
  errorDark: '#d32f2f',
  warningMain: '#ff9800',
  warningDark: '#f57c00',
  orangeMain: '#ff5722',
  successMain: '#4caf50',
  successDark: '#388e3c',
};

/**
 * Check if password contains numbers
 *
 * @param password - Password to check
 * @returns True if password contains numbers
 */
export const hasNumber: StringBoolFunc = (password: string): boolean => {
  return /[0-9]/.test(password);
};

/**
 * Check if password has mixed case letters
 *
 * @param password - Password to check
 * @returns True if password has both lowercase and uppercase letters
 */
export const hasMixed: StringBoolFunc = (password: string): boolean => {
  return /[a-z]/.test(password) && /[A-Z]/.test(password);
};

/**
 * Check if password contains special characters
 *
 * @param password - Password to check
 * @returns True if password contains special characters
 */
export const hasSpecial: StringBoolFunc = (password: string): boolean => {
  return /[!#@$%^&*)(+=._-]/.test(password);
};

// set color based on password strength
export const strengthColor: NumbColorFunc = count => {
  if (count < 2) return { label: 'Poor', color: strengthColors.errorMain };
  if (count < 3) return { label: 'Weak', color: strengthColors.warningDark };
  if (count < 4) return { label: 'Normal', color: strengthColors.orangeMain };
  if (count < 5) return { label: 'Good', color: strengthColors.successMain };
  if (count < 6) return { label: 'Strong', color: strengthColors.successDark };
  return { label: 'Poor', color: strengthColors.errorMain };
};

// password strength indicator
export const strengthIndicator: StringNumFunc = password => {
  let strengths = 0;

  // Length checks
  if (password.length > 5) strengths += 1;
  if (password.length > 7) strengths += 1;

  // Character type checks
  if (hasNumber(password)) strengths += 1;
  if (hasSpecial(password)) strengths += 1;
  if (hasMixed(password)) strengths += 1;

  return strengths;
};

/**
 * Get password strength level and description
 *
 * @param password - Password to evaluate
 * @returns Object with strength level and description
 */
export const getPasswordStrength = (
  password: string
): {
  level: number;
  label: string;
  color: string;
  description: string;
} => {
  const level = strengthIndicator(password);
  const strengthResult = strengthColor(level);
  const label = strengthResult?.label || 'Poor';
  const color = strengthResult?.color || strengthColors.errorMain;

  const descriptions = {
    Poor: 'Password is too weak. Add more characters and complexity.',
    Weak: 'Password is weak. Consider adding numbers and special characters.',
    Normal: 'Password is acceptable but could be stronger.',
    Good: 'Password is good. Well done!',
    Strong: 'Password is very strong. Excellent!',
  };

  return {
    level,
    label,
    color,
    description:
      descriptions[label as keyof typeof descriptions] || descriptions.Poor,
  };
};

/**
 * Validate password meets minimum requirements (original implementation)
 *
 * @param password - Password to validate
 * @param minLength - Minimum length requirement (default: 8)
 * @returns Object with validation result and errors
 */
export const validatePasswordDetailed = (
  password: string,
  minLength: number = 8
): {
  isValid: boolean;
  errors: string[];
  requirements: { [key: string]: boolean };
} => {
  const errors: string[] = [];
  const requirements = {
    length: password.length >= minLength,
    number: hasNumber(password),
    mixed: hasMixed(password),
    special: hasSpecial(password),
  };

  if (!requirements.length) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  if (!requirements.number) {
    errors.push('Password must contain at least one number');
  }
  if (!requirements.mixed) {
    errors.push('Password must contain both uppercase and lowercase letters');
  }
  if (!requirements.special) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
    requirements,
  };
};

/**
 * Advanced password validation with detailed feedback (main validation function)
 *
 * @param password - Password to validate
 * @returns Validation result with detailed feedback
 */
export const validatePassword = (
  password: string
): {
  isValid: boolean;
  strength: number;
  feedback: string[];
  score: { label: string; color: string };
} => {
  const feedback: string[] = [];
  let isValid = true;

  // Length requirements
  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long');
    isValid = false;
  }

  if (password.length > 128) {
    feedback.push('Password must be less than 128 characters long');
    isValid = false;
  }

  // Character requirements
  if (!hasNumber(password)) {
    feedback.push('Password must contain at least one number');
  }

  if (!hasMixed(password)) {
    feedback.push('Password must contain both uppercase and lowercase letters');
  }

  if (!hasSpecial(password)) {
    feedback.push('Password must contain at least one special character');
  }

  // Common pattern checks
  if (/(.)\1{2,}/.test(password)) {
    feedback.push('Password should not contain repeated characters');
  }

  if (/123|abc|qwerty|password/i.test(password)) {
    feedback.push('Password should not contain common patterns');
  }

  const strength = strengthIndicator(password);
  const strengthResult = strengthColor(strength);
  const score = {
    label: strengthResult?.label || 'Poor',
    color: strengthResult?.color || strengthColors.errorMain,
  };

  // Additional validation for minimum requirements
  if (strength < 3) {
    isValid = false;
  }

  return {
    isValid,
    strength,
    feedback,
    score,
  };
};

/**
 * Check if password meets minimum security requirements
 *
 * @param password - Password to check
 * @returns True if password meets minimum requirements
 */
export const meetsMinimumRequirements = (password: string): boolean => {
  return (
    password.length >= 8 &&
    hasNumber(password) &&
    hasMixed(password) &&
    hasSpecial(password)
  );
};

/**
 * Generate password suggestions
 *
 * @returns Array of password improvement suggestions
 */
export const getPasswordSuggestions = (): string[] => {
  return [
    'Use a mix of uppercase and lowercase letters',
    'Include numbers in your password',
    'Add special characters (!@#$%^&*)',
    'Make your password at least 8 characters long',
    'Avoid common words and patterns',
    'Consider using a passphrase with multiple words',
    "Don't use personal information like names or dates",
  ];
};
