/**
 * General Validation Utilities
 *
 * This module provides common validation functions for forms and user input
 */

/**
 * Validate email address format
 *
 * @param email - Email address to validate
 * @returns True if email format is valid
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (basic international format)
 *
 * @param phone - Phone number to validate
 * @returns True if phone format is valid
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s-()]{10,}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate URL format
 *
 * @param url - URL to validate
 * @returns True if URL format is valid
 */
export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate if string is not empty or just whitespace
 *
 * @param value - String to validate
 * @returns True if string has content
 */
export const isNotEmpty = (value: string): boolean => {
  return value.trim().length > 0;
};

/**
 * Validate if value is a number
 *
 * @param value - Value to validate
 * @returns True if value is a valid number
 */
export const isNumber = (value: unknown): boolean => {
  return !isNaN(Number(value)) && !isNaN(parseFloat(String(value)));
};

/**
 * Validate if number is within range
 *
 * @param value - Number to validate
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (inclusive)
 * @returns True if number is within range
 */
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

/**
 * Validate if string meets length requirements
 *
 * @param value - String to validate
 * @param minLength - Minimum length (default: 0)
 * @param maxLength - Maximum length (optional)
 * @returns True if string meets length requirements
 */
export const isValidLength = (
  value: string,
  minLength: number = 0,
  maxLength?: number
): boolean => {
  const length = value.length;
  if (length < minLength) return false;
  if (maxLength !== undefined && length > maxLength) return false;
  return true;
};

/**
 * Validate if string contains only alphanumeric characters
 *
 * @param value - String to validate
 * @returns True if string is alphanumeric
 */
export const isAlphanumeric = (value: string): boolean => {
  const alphanumericRegex = /^[a-zA-Z0-9]+$/;
  return alphanumericRegex.test(value);
};

/**
 * Validate if string contains only letters
 *
 * @param value - String to validate
 * @returns True if string contains only letters
 */
export const isAlpha = (value: string): boolean => {
  const alphaRegex = /^[a-zA-Z]+$/;
  return alphaRegex.test(value);
};

/**
 * Validate if string contains only numbers
 *
 * @param value - String to validate
 * @returns True if string contains only numbers
 */
export const isNumeric = (value: string): boolean => {
  const numericRegex = /^[0-9]+$/;
  return numericRegex.test(value);
};

/**
 * Validate credit card number using Luhn algorithm
 *
 * @param cardNumber - Credit card number to validate
 * @returns True if credit card number is valid
 */
export const isValidCreditCard = (cardNumber: string): boolean => {
  // Remove spaces and dashes
  const cleanNumber = cardNumber.replace(/[\s-]/g, '');

  // Check if it's all digits and appropriate length
  if (!/^\d{13,19}$/.test(cleanNumber)) return false;

  // Luhn algorithm
  let sum = 0;
  let isEvenPosition = false;

  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i]);

    if (isEvenPosition) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEvenPosition = !isEvenPosition;
  }

  return sum % 10 === 0;
};

/**
 * Validate postal/zip code (basic validation)
 *
 * @param postalCode - Postal code to validate
 * @param country - Country code (US, CA, UK, etc.)
 * @returns True if postal code format is valid
 */
export const isValidPostalCode = (
  postalCode: string,
  country: string = 'US'
): boolean => {
  const patterns = {
    US: /^\d{5}(-\d{4})?$/, // 12345 or 12345-6789
    CA: /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/, // A1A 1A1
    UK: /^[A-Za-z]{1,2}\d[A-Za-z\d]? \d[A-Za-z]{2}$/, // SW1A 1AA
    DE: /^\d{5}$/, // 12345
    FR: /^\d{5}$/, // 12345
  };

  const pattern = patterns[country as keyof typeof patterns];
  return pattern ? pattern.test(postalCode) : true; // Return true for unknown countries
};

/**
 * Validate date string format
 *
 * @param dateString - Date string to validate
 * @returns True if date format is valid
 */
export const isValidDateString = (dateString: string): boolean => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

/**
 * Validate if date is in the future
 *
 * @param date - Date to validate
 * @returns True if date is in the future
 */
export const isFutureDate = (date: Date | string): boolean => {
  const inputDate = new Date(date);
  const now = new Date();
  return inputDate > now;
};

/**
 * Validate if date is in the past
 *
 * @param date - Date to validate
 * @returns True if date is in the past
 */
export const isPastDate = (date: Date | string): boolean => {
  const inputDate = new Date(date);
  const now = new Date();
  return inputDate < now;
};
