# Authentication System Documentation

## Overview

The authentication system has been redesigned based on the template patterns from `base-portal-dashboard` to provide better separation of concerns, improved type safety, and more maintainable code.

## Architecture

### Components

#### 1. AuthGuard
- **Purpose**: Protects routes that require authentication
- **Behavior**: Redirects unauthenticated users to login page
- **Usage**: Wrap components that need authentication

```tsx
import { AuthGuard } from '@/utils/auth';

<AuthGuard>
  <DashboardContent />
</AuthGuard>
```

#### 2. GuestGuard
- **Purpose**: Protects routes that should only be accessible to unauthenticated users
- **Behavior**: Redirects authenticated users to dashboard
- **Usage**: Wrap login/register pages

```tsx
import { GuestGuard } from '@/utils/auth';

<GuestGuard>
  <LoginPage />
</GuestGuard>
```

#### 3. RoleGuard
- **Purpose**: Protects routes based on user roles with hierarchical permissions
- **Behavior**: Redirects users without required role to appropriate dashboard
- **Role Hierarchy**: guest < user < developer < admin < super_admin

```tsx
import { RoleGuard } from '@/utils/auth';

<RoleGuard requiredRole="admin" fallbackPath="/unauthorized">
  <AdminPanel />
</RoleGuard>
```

#### 4. ProtectedRoute (Improved)
- **Purpose**: Convenient wrapper combining AuthGuard and RoleGuard
- **Behavior**: Handles both authentication and role-based access
- **Usage**: Primary component for protecting routes

```tsx
import ProtectedRoute from '@/components/auth/protected-route';

// Auth only
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>

// Auth + Role
<ProtectedRoute requiredRole="admin">
  <AdminPanel />
</ProtectedRoute>

// Auth + Role + Custom fallback
<ProtectedRoute requiredRole="developer" fallbackPath="/unauthorized">
  <DeveloperTools />
</ProtectedRoute>
```

## Key Improvements

### 1. Separation of Concerns
- **Before**: Single `ProtectedRoute` component handling all logic
- **After**: Separate guards for different concerns (auth, guest, role)

### 2. Better Role Hierarchy
- **Before**: Manual role checking with hardcoded logic
- **After**: Hierarchical role system where higher roles inherit lower permissions

### 3. Improved Loading States
- Consistent loading indicators across all guards
- Prevents flash of unauthorized content

### 4. Type Safety
- Proper TypeScript interfaces for all guard props
- Type-safe role definitions and checking

### 5. Cleaner Imports
- Centralized exports from `@/utils/auth`
- Easy access to all auth utilities

## Migration Guide

### Old Pattern
```tsx
// Before
import ProtectedRoute from '@/components/auth/protected-route';

<ProtectedRoute requiredRole="admin">
  <AdminComponent />
</ProtectedRoute>
```

### New Pattern
```tsx
// After - Same API, better implementation
import ProtectedRoute from '@/components/auth/protected-route';

<ProtectedRoute requiredRole="admin">
  <AdminComponent />
</ProtectedRoute>

// Or use guards directly for more control
import { RoleGuard } from '@/utils/auth';

<RoleGuard requiredRole="admin" fallbackPath="/custom-unauthorized">
  <AdminComponent />
</RoleGuard>
```

## Available Utilities

### Token Management
```tsx
import { 
  getAccessToken, 
  setAccessToken, 
  clearAuthTokens, 
  isTokenValid 
} from '@/utils/auth';
```

### Role Checking
```tsx
import { hasRole, isAdmin, isSuperAdmin } from '@/utils/auth';

const canAccess = hasRole(userRole, 'admin');
const isUserAdmin = isAdmin(user);
```

### Session Management
```tsx
import { 
  updateLastActivity, 
  isSessionExpired, 
  clearSessionData 
} from '@/utils/auth';
```

## Best Practices

1. **Use ProtectedRoute** for most cases - it provides the cleanest API
2. **Use specific guards** when you need custom behavior
3. **Always handle loading states** - guards automatically show loading indicators
4. **Use role hierarchy** - admin users can access developer areas automatically
5. **Provide fallback paths** for better UX when access is denied

## Configuration

Configure paths in `/src/config.ts`:

```typescript
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  DASHBOARD: '/dashboard',
  ADMIN: '/admin',
  DEVELOPER: '/developer',
};
```

## Error Handling

All guards handle errors gracefully:
- Network errors during auth checks
- Invalid tokens
- Missing user data
- Session expiration

Users are automatically redirected to appropriate pages with proper error states.
