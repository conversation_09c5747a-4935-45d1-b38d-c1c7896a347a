import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

// project imports
import useAuth from '@/hooks/use-auth';
import { DASHBOARD_PATH } from '@/config';
import { GuardProps } from '@/types';

// ==============================|| GUEST GUARD ||============================== //

/**
 * Guest guard for routes having no auth required
 * Redirects authenticated users to dashboard
 * @param {GuardProps} props - Component props containing children
 */
const GuestGuard = ({ children }: GuardProps) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      navigate(DASHBOARD_PATH, { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render children if not authenticated
  if (!isAuthenticated) {
    return <>{children}</>;
  }

  // Return null while redirecting
  return null;
};

export default GuestGuard;
