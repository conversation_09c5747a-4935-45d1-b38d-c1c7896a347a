import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

// project imports
import useAuth from '@/hooks/use-auth';
import { ROUTES } from '@/config';
import { RoleGuardProps } from '@/types';
import { hasRole } from '@/utils/auth/auth-utils';

// ==============================|| ROLE GUARD ||============================== //

/**
 * Role-based access guard for routes requiring specific roles
 * Redirects users without required role to appropriate dashboard
 * @param {RoleGuardProps} props - Component props containing children and role requirements
 */
const RoleGuard = ({ children, requiredRole, fallbackPath }: RoleGuardProps) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isLoading) return;

    // If not authenticated, redirect to login
    if (!isAuthenticated || !user) {
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    // If no role requirement, allow access
    if (!requiredRole) {
      return;
    }

    // Check if user has required role
    const userRole = user.role || 'user';
    const hasRequiredRole = hasRole(userRole as any, requiredRole as any);

    if (!hasRequiredRole) {
      // Redirect to fallback path or user's appropriate dashboard
      const redirectPath = fallbackPath || getUserDashboardPath(userRole);
      navigate(redirectPath, { replace: true });
    }
  }, [isAuthenticated, user, isLoading, requiredRole, fallbackPath, navigate]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check authentication
  if (!isAuthenticated || !user) {
    return null; // Will redirect via useEffect
  }

  // Check role requirement
  if (requiredRole) {
    const userRole = user.role || 'user';
    const hasRequiredRole = hasRole(userRole as any, requiredRole as any);
    
    if (!hasRequiredRole) {
      return null; // Will redirect via useEffect
    }
  }

  return <>{children}</>;
};

/**
 * Get appropriate dashboard path for user role
 * All users go to the unified dashboard which redirects to user portal
 * @param {string} role - User role
 * @returns {string} Dashboard path
 */
const getUserDashboardPath = (role: string): string => {
  // All users go to the unified dashboard (/dashboard -> /user/home)
  return ROUTES.DASHBOARD;
};

export default RoleGuard;
