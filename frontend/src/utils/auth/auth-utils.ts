/**
 * Authentication and authorization utilities
 * Provides helper functions for token management, permissions, and auth state
 */

// Define types directly here for now (consider moving to main types later)
export type UserRole = 'guest' | 'user' | 'developer' | 'admin' | 'super_admin';
export type Permission = string;

// Export guard components
export { default as GuestGuard } from './guest-guard';
export { default as AuthGuard } from './auth-guard';
export { default as RoleGuard } from './role-guard';

// ==============================|| TOKEN MANAGEMENT ||============================== //

/**
 * Storage keys for authentication tokens
 */
export const AUTH_TOKENS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  TOKEN_EXPIRY: 'tokenExpiry',
} as const;

/**
 * Get access token from localStorage
 * @returns {string | null} The access token or null if not found
 */
export const getAccessToken = (): string | null => {
  return localStorage.getItem(AUTH_TOKENS.ACCESS_TOKEN);
};

/**
 * Set access token in localStorage
 * @param {string} token - The access token to store
 */
export const setAccessToken = (token: string): void => {
  localStorage.setItem(AUTH_TOKENS.ACCESS_TOKEN, token);
};

/**
 * Get refresh token from localStorage
 * @returns {string | null} The refresh token or null if not found
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem(AUTH_TOKENS.REFRESH_TOKEN);
};

/**
 * Set refresh token in localStorage
 * @param {string} token - The refresh token to store
 */
export const setRefreshToken = (token: string): void => {
  localStorage.setItem(AUTH_TOKENS.REFRESH_TOKEN, token);
};

/**
 * Get token expiry timestamp from localStorage
 * @returns {number | null} The expiry timestamp or null if not found
 */
export const getTokenExpiry = (): number | null => {
  const expiry = localStorage.getItem(AUTH_TOKENS.TOKEN_EXPIRY);
  return expiry ? parseInt(expiry, 10) : null;
};

/**
 * Set token expiry timestamp in localStorage
 * @param {number} expiry - The expiry timestamp
 */
export const setTokenExpiry = (expiry: number): void => {
  localStorage.setItem(AUTH_TOKENS.TOKEN_EXPIRY, expiry.toString());
};

/**
 * Clear all authentication tokens from localStorage
 */
export const clearAuthTokens = (): void => {
  localStorage.removeItem(AUTH_TOKENS.ACCESS_TOKEN);
  localStorage.removeItem(AUTH_TOKENS.REFRESH_TOKEN);
  localStorage.removeItem(AUTH_TOKENS.TOKEN_EXPIRY);
};

/**
 * Check if access token exists and is not expired
 * @returns {boolean} True if token is valid, false otherwise
 */
export const isTokenValid = (): boolean => {
  const token = getAccessToken();
  const expiry = getTokenExpiry();

  if (!token || !expiry) {
    return false;
  }

  return Date.now() < expiry;
};

/**
 * Check if token is expired but refresh token exists
 * @returns {boolean} True if token can be refreshed, false otherwise
 */
export const canRefreshToken = (): boolean => {
  const refreshToken = getRefreshToken();
  return !!refreshToken;
};

// ==============================|| JWT UTILITIES ||============================== //

/**
 * Decode JWT token payload (client-side only, not for security validation)
 * @param {string} token - The JWT token to decode
 * @returns {any} The decoded payload or null if invalid
 */
export const decodeJWTPayload = (token: string): any => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
};

/**
 * Get user information from access token
 * @returns {any} User information from token or null
 */
export const getUserFromToken = (): any => {
  const token = getAccessToken();
  if (!token) {
    return null;
  }

  return decodeJWTPayload(token);
};

/**
 * Check if JWT token is expired
 * @param {string} token - The JWT token to check
 * @returns {boolean} True if token is expired, false otherwise
 */
export const isJWTExpired = (token: string): boolean => {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) {
    return true;
  }

  return Date.now() >= payload.exp * 1000;
};

// ==============================|| ROLE & PERMISSION UTILITIES ||============================== //

/**
 * Check if user has required role
 * @param {UserRole} userRole - Current user's role
 * @param {UserRole} requiredRole - Required role
 * @returns {boolean} True if user has required role or higher
 */
export const hasRole = (
  userRole: UserRole,
  requiredRole: UserRole
): boolean => {
  const roleHierarchy: Record<UserRole, number> = {
    guest: 0,
    user: 1,
    developer: 2,
    admin: 3,
    super_admin: 4,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

/**
 * Check if user has specific permission
 * @param {Permission[]} userPermissions - User's permissions array
 * @param {Permission} requiredPermission - Required permission
 * @returns {boolean} True if user has the permission
 */
export const hasPermission = (
  userPermissions: Permission[],
  requiredPermission: Permission
): boolean => {
  return userPermissions.includes(requiredPermission);
};

/**
 * Check if user has any of the specified permissions
 * @param {Permission[]} userPermissions - User's permissions array
 * @param {Permission[]} requiredPermissions - Array of required permissions
 * @returns {boolean} True if user has at least one permission
 */
export const hasAnyPermission = (
  userPermissions: Permission[],
  requiredPermissions: Permission[]
): boolean => {
  return requiredPermissions.some(permission =>
    userPermissions.includes(permission)
  );
};

/**
 * Check if user has all of the specified permissions
 * @param {Permission[]} userPermissions - User's permissions array
 * @param {Permission[]} requiredPermissions - Array of required permissions
 * @returns {boolean} True if user has all permissions
 */
export const hasAllPermissions = (
  userPermissions: Permission[],
  requiredPermissions: Permission[]
): boolean => {
  return requiredPermissions.every(permission =>
    userPermissions.includes(permission)
  );
};

// ==============================|| SESSION MANAGEMENT ||============================== //

/**
 * Session storage keys
 */
export const SESSION_KEYS = {
  USER_PREFERENCES: 'userPreferences',
  LAST_ACTIVITY: 'lastActivity',
  SESSION_ID: 'sessionId',
} as const;

/**
 * Update last activity timestamp
 */
export const updateLastActivity = (): void => {
  sessionStorage.setItem(SESSION_KEYS.LAST_ACTIVITY, Date.now().toString());
};

/**
 * Get last activity timestamp
 * @returns {number | null} Last activity timestamp or null
 */
export const getLastActivity = (): number | null => {
  const lastActivity = sessionStorage.getItem(SESSION_KEYS.LAST_ACTIVITY);
  return lastActivity ? parseInt(lastActivity, 10) : null;
};

/**
 * Check if session is expired based on inactivity
 * @param {number} timeoutMinutes - Session timeout in minutes
 * @returns {boolean} True if session is expired
 */
export const isSessionExpired = (timeoutMinutes: number = 30): boolean => {
  const lastActivity = getLastActivity();
  if (!lastActivity) {
    return true;
  }

  const timeoutMs = timeoutMinutes * 60 * 1000;
  return Date.now() - lastActivity > timeoutMs;
};

/**
 * Clear session data
 */
export const clearSessionData = (): void => {
  sessionStorage.removeItem(SESSION_KEYS.USER_PREFERENCES);
  sessionStorage.removeItem(SESSION_KEYS.LAST_ACTIVITY);
  sessionStorage.removeItem(SESSION_KEYS.SESSION_ID);
};

/**
 * Generate a session ID
 * @returns {string} Generated session ID
 */
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// ==============================|| AUTH STATE UTILITIES ||============================== //

/**
 * Get current authentication state
 * @returns {object} Authentication state object
 */
export const getAuthState = () => {
  const hasValidToken = isTokenValid();
  const canRefresh = canRefreshToken();
  const user = getUserFromToken();

  return {
    isAuthenticated: hasValidToken,
    canRefreshToken: canRefresh,
    user,
    needsRefresh: !hasValidToken && canRefresh,
  };
};

/**
 * Initialize auth state on app startup
 * @returns {Promise<boolean>} Promise resolving to authentication status
 */
export const initializeAuth = async (): Promise<boolean> => {
  const authState = getAuthState();

  if (authState.isAuthenticated) {
    updateLastActivity();
    return true;
  }

  if (authState.needsRefresh) {
    // Token refresh logic would go here
    // This would typically call an API endpoint to refresh the token
    console.log('Token needs refresh');
    return false;
  }

  // Clear invalid tokens
  clearAuthTokens();
  clearSessionData();
  return false;
};

/**
 * Logout user and clear all auth data
 */
export const logout = (): void => {
  clearAuthTokens();
  clearSessionData();

  // Optionally call logout API endpoint
  // await logoutAPI();
};

// ==============================|| TYPE GUARDS ||============================== //

/**
 * Type guard to check if user has admin role
 * @param {any} user - User object to check
 * @returns {boolean} True if user is admin
 */
export const isAdmin = (user: any): boolean => {
  return user?.role === 'admin' || user?.role === 'super_admin';
};

/**
 * Type guard to check if user is super admin
 * @param {any} user - User object to check
 * @returns {boolean} True if user is super admin
 */
export const isSuperAdmin = (user: any): boolean => {
  return user?.role === 'super_admin';
};

/**
 * Type guard to check if user is guest
 * @param {any} user - User object to check
 * @returns {boolean} True if user is guest
 */
export const isGuest = (user: any): boolean => {
  return !user || user?.role === 'guest';
};
