/**
 * Authentication utilities index
 * Centralized exports for all auth-related components and utilities
 */

// Guard components
export { default as AuthGuard } from './auth-guard';
export { default as GuestGuard } from './guest-guard';
export { default as RoleGuard } from './role-guard';

// Essential utility functions
export {
  // Token management
  getAccessToken,
  setAccessToken,
  getRefreshToken,
  setRefreshToken,
  clearAuthTokens,
  isTokenValid,
  
  // Role checking
  hasRole,
  isAdmin,
  isSuperAdmin,
  
  // Auth state
  getAuthState,
  logout,
} from './auth-utils';
