import { useNavigate } from 'react-router-dom';
import { PropsWithChildren, useEffect } from 'react';

// project imports
import useAuth from '@/hooks/use-auth';
import { ROUTES } from '@/config';

// ==============================|| AUTH GUARD ||============================== //

/**
 * Authentication guard for protected routes
 * Redirects unauthenticated users to login page
 * @param {PropsWithChildren} props - Component props containing children
 */
const AuthGuard = ({ children }: PropsWithChildren) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate(ROUTES.LOGIN, { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render children if authenticated
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Return null while redirecting
  return null;
};

export default AuthGuard;
