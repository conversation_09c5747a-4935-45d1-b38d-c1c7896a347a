/**
 * Canvas Utilities
 *
 * This module provides utility functions for HTML5 Canvas operations,
 * including scaling, coordinate calculations, drawing helpers, and canvas manipulation
 */

/**
 * Scale canvas for high-DPI displays
 *
 * @param canvas HTML Canvas element to scale
 */
export function scaleCanvas(canvas: HTMLCanvasElement): void {
  const context = canvas.getContext('2d');
  if (context != null) {
    const devicePixelRatio = window.devicePixelRatio || 1;
    canvas.width = canvas.clientWidth * devicePixelRatio;
    canvas.height = canvas.clientHeight * devicePixelRatio;
    context.scale(devicePixelRatio, devicePixelRatio);
  }
}

/**
 * Scale canvas for high DPI displays (alternative name)
 *
 * @param canvas - HTML Canvas element to scale
 */
export function scale(canvas: HTMLCanvasElement): void {
  return scaleCanvas(canvas);
}

/**
 * Get mouse coordinates relative to canvas
 *
 * @param canvas HTML Canvas element
 * @param event Mouse event
 * @returns Array with [x, y] coordinates
 */
export function getCanvasCoordinate(
  canvas: HTMLCanvasElement,
  event: MouseEvent
): [number, number] {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  return [x, y];
}

/**
 * Get mouse coordinates relative to canvas (alternative name)
 *
 * @param canvas - HTML Canvas element
 * @param event - Mouse event
 * @returns Array containing [x, y] coordinates
 */
export function getCoordinate(
  canvas: HTMLCanvasElement,
  event: MouseEvent
): [number, number] {
  return getCanvasCoordinate(canvas, event);
}

/**
 * Clear entire canvas
 *
 * @param canvas HTML Canvas element to clear
 */
export function clearCanvas(canvas: HTMLCanvasElement): void {
  const context = canvas.getContext('2d');
  if (context != null) {
    context.clearRect(0, 0, canvas.width, canvas.height);
  }
}

/**
 * Clear entire canvas (alternative name)
 *
 * @param canvas - HTML Canvas element to clear
 */
export function clear(canvas: HTMLCanvasElement): void {
  return clearCanvas(canvas);
}

/**
 * Set canvas size while maintaining aspect ratio
 *
 * @param canvas HTML Canvas element
 * @param width Target width
 * @param height Target height
 * @param maintainAspectRatio Whether to maintain aspect ratio
 */
export function setCanvasSize(
  canvas: HTMLCanvasElement,
  width: number,
  height: number,
  maintainAspectRatio: boolean = true
): void {
  if (maintainAspectRatio) {
    const aspectRatio = canvas.width / canvas.height;
    if (width / height > aspectRatio) {
      width = height * aspectRatio;
    } else {
      height = width / aspectRatio;
    }
  }

  canvas.style.width = `${width}px`;
  canvas.style.height = `${height}px`;
}

/**
 * Convert canvas to data URL
 *
 * @param canvas HTML Canvas element
 * @param type Image MIME type (default: 'image/png')
 * @param quality Image quality for lossy formats (0-1)
 * @returns Data URL string
 */
export function canvasToDataURL(
  canvas: HTMLCanvasElement,
  type: string = 'image/png',
  quality?: number
): string {
  return canvas.toDataURL(type, quality);
}

/**
 * Get canvas as data URL (alternative name)
 *
 * @param canvas - HTML Canvas element
 * @param type - Image format (default: 'image/png')
 * @param quality - Image quality for lossy formats (0-1)
 * @returns Data URL string
 */
export function getDataURL(
  canvas: HTMLCanvasElement,
  type: string = 'image/png',
  quality?: number
): string {
  return canvasToDataURL(canvas, type, quality);
}

/**
 * Draw image on canvas with optional scaling
 *
 * @param canvas HTML Canvas element
 * @param image Image to draw
 * @param x X position (default: 0)
 * @param y Y position (default: 0)
 * @param width Width to scale to (optional)
 * @param height Height to scale to (optional)
 */
export function drawImageOnCanvas(
  canvas: HTMLCanvasElement,
  image: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement,
  x: number = 0,
  y: number = 0,
  width?: number,
  height?: number
): void {
  const context = canvas.getContext('2d');
  if (!context) return;

  if (width !== undefined && height !== undefined) {
    context.drawImage(image, x, y, width, height);
  } else {
    context.drawImage(image, x, y);
  }
}

/**
 * Draw a circle on canvas
 *
 * @param canvas - HTML Canvas element
 * @param x - X coordinate of circle center
 * @param y - Y coordinate of circle center
 * @param radius - Circle radius
 * @param fillStyle - Fill color (optional)
 * @param strokeStyle - Stroke color (optional)
 */
export function drawCircle(
  canvas: HTMLCanvasElement,
  x: number,
  y: number,
  radius: number,
  fillStyle?: string,
  strokeStyle?: string
): void {
  const context = canvas.getContext('2d');
  if (context != null) {
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI);

    if (fillStyle) {
      context.fillStyle = fillStyle;
      context.fill();
    }

    if (strokeStyle) {
      context.strokeStyle = strokeStyle;
      context.stroke();
    }
  }
}

/**
 * Draw a rectangle on canvas
 *
 * @param canvas - HTML Canvas element
 * @param x - X coordinate of rectangle
 * @param y - Y coordinate of rectangle
 * @param width - Rectangle width
 * @param height - Rectangle height
 * @param fillStyle - Fill color (optional)
 * @param strokeStyle - Stroke color (optional)
 */
export function drawRectangle(
  canvas: HTMLCanvasElement,
  x: number,
  y: number,
  width: number,
  height: number,
  fillStyle?: string,
  strokeStyle?: string
): void {
  const context = canvas.getContext('2d');
  if (context != null) {
    if (fillStyle) {
      context.fillStyle = fillStyle;
      context.fillRect(x, y, width, height);
    }

    if (strokeStyle) {
      context.strokeStyle = strokeStyle;
      context.strokeRect(x, y, width, height);
    }
  }
}
