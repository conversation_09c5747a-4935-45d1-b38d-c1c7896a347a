{"// Developer Pages": "", "developer.register.title": "註冊新子繫統", "developer.register.subtitle": "將您的應用程式註冊到 GeNieGO SSO，為您的用戶啟用單點登錄", "developer.register.backToDashboard": "返回儀表板", "developer.register.basicInfo": "基本信息", "developer.register.applicationName": "子繫統名稱", "developer.register.applicationNamePlaceholder": "例如：GenieMove、MyApp", "developer.register.description": "描述", "developer.register.descriptionPlaceholder": "您應用程式的簡要描述", "developer.register.redirectUris": "重定向 URI", "developer.register.redirectUrisDescription": "添加用戶認證後將被重定向到的 URL", "developer.register.redirectUriPlaceholder": "https://yourapp.com/auth/callback", "developer.register.addRedirectUri": "添加另一個重定向 URI", "developer.register.permissions": "權限（範圍）", "developer.register.permissionsDescription": "選擇您的應用程式需要訪問的用戶信息", "developer.register.clientCredentials": "客戶端憑據", "developer.register.credentialsDialogDescription": "您的應用程式憑據已生成。請安全地儲存它們，因為客戶端密鑰不會再次顯示。", "developer.register.securityNotice": "重要安全提醒", "developer.register.securityNoticeText": "請安全地存儲這些憑據。客戶端密鑰僅顯示一次，無法恢復。", "developer.register.clientId": "客戶端 ID", "developer.register.clientSecret": "客戶端密鑰", "developer.register.regenerateCredentials": "重新生成憑據", "developer.register.downloadCSV": "下載 CSV", "developer.register.close": "關閉", "developer.register.agree": "同意", "developer.register.termsAndConditions": "條款和條件", "developer.register.agreeTo": "我同意", "developer.register.termsOfService": "服務條款", "developer.register.privacyPolicy": "隱私政策", "developer.register.cancel": "取消", "developer.register.generateCredentials": "生成憑據", "developer.register.registerApplication": "註冊子繫統", "developer.register.generating": "生成中...", "developer.register.registering": "註冊中...", "developer.register.nextSteps": "下一步", "developer.register.nextStepsText": "註冊後，您將能夠將 GeNieGO SSO 整合到您的應用程式中。查看我們的整合文檔以獲取實施指南和代碼示例。", "developer.register.integrationDocs": "整合文檔", "developer.register.error.applicationNameRequired": "子繫統名稱是必填項", "developer.register.error.applicationNameTooShort": "子繫統名稱必須至少3個字符", "developer.register.error.applicationNameInvalid": "子繫統名稱只能包含字母、數字、空格、連字符和下劃線", "developer.register.error.descriptionRequired": "描述是必填項", "developer.register.error.descriptionTooShort": "描述必須至少10個字符", "developer.register.error.redirectUriRequired": "至少需要一個重定向 URI", "developer.register.error.redirectUriInvalid": "所有重定向 URI 必須是有效的 HTTP/HTTPS URL", "developer.register.error.termsRequired": "您必須接受服務條款", "developer.register.error.privacyRequired": "您必須接受隱私政策", "developer.register.error.registrationFailed": "註冊失敗，請重試。", "// Toast Messages": "", "developer.register.toast.success.title": "應用程式已註冊", "developer.register.toast.success.description": "{name} 已成功註冊。", "developer.register.toast.error.title": "註冊失敗", "developer.register.toast.error.description": "註冊應用程式失敗。請重試。", "// Scope Options": "", "developer.register.scope.openid.name": "OpenID Connect", "developer.register.scope.openid.description": "基本身份驗證", "developer.register.scope.profile.name": "個人資料", "developer.register.scope.profile.description": "使用者個人資料資訊", "developer.register.scope.email.name": "電子郵件", "developer.register.scope.email.description": "使用者電子郵件地址", "developer.register.scope.phone.name": "電話", "developer.register.scope.phone.description": "使用者電話號碼", "developer.register.scope.address.name": "地址", "developer.register.scope.address.description": "使用者地址資訊"}