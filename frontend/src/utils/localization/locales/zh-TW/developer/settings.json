{"// Developer Settings": "", "developer.settings.title": "開發者設定", "developer.settings.description": "配置您的開發者帳戶、API 金鑰和整合設定", "developer.settings.refresh": "重新整理", "developer.settings.save": "儲存變更", "developer.settings.saving": "儲存中...", "// Tabs": "", "developer.settings.tabs.general": "一般", "developer.settings.tabs.apiKeys": "API 金鑰", "developer.settings.tabs.webhooks": "Webhook", "developer.settings.tabs.security": "安全性", "developer.settings.tabs.notifications": "通知", "developer.settings.tabs.environments": "環境", "// General Settings": "", "developer.settings.general.title": "一般設定", "developer.settings.general.description": "配置您的開發者帳戶設定", "developer.settings.general.developerName": "開發者姓名", "developer.settings.general.developerNameHelp": "您在開發者入口網站中顯示的姓名", "developer.settings.general.email": "電子郵件地址", "developer.settings.general.emailHelp": "用於開發者通知和警示的電子郵件", "developer.settings.general.organization": "組織", "developer.settings.general.organizationHelp": "您的公司或組織名稱", "developer.settings.general.defaultRedirectUri": "預設重定向 URI", "developer.settings.general.defaultRedirectUriHelp": "OAuth 重定向的預設 URI", "developer.settings.general.defaultScopes": "預設範圍", "developer.settings.general.defaultScopesHelp": "新應用程式的預設 OAuth 範圍", "// API Keys": "", "developer.settings.apiKeys.title": "API 金鑰", "developer.settings.apiKeys.description": "管理您用於存取 GeNieGO API 的 API 金鑰", "developer.settings.apiKeys.createNew": "產生新金鑰", "developer.settings.apiKeys.name": "金鑰名稱", "developer.settings.apiKeys.permissions": "權限", "developer.settings.apiKeys.created": "建立", "developer.settings.apiKeys.expires": "到期", "developer.settings.apiKeys.lastUsed": "最後使用", "developer.settings.apiKeys.status": "狀態", "developer.settings.apiKeys.active": "活躍", "developer.settings.apiKeys.inactive": "非活躍", "developer.settings.apiKeys.copy": "複製", "developer.settings.apiKeys.delete": "刪除", "developer.settings.apiKeys.regenerate": "重新產生", "developer.settings.apiKeys.show": "顯示", "developer.settings.apiKeys.hide": "隱藏", "developer.settings.apiKeys.never": "從未", "developer.settings.apiKeys.noExpiration": "無到期日", "developer.settings.apiKeys.scopes": "範圍", "developer.settings.apiKeys.noKeys": "無 API 金鑰", "developer.settings.apiKeys.noKeysDesc": "您尚未建立任何 API 金鑰", "developer.settings.apiKeys.noKeysLong": "您尚未產生任何 API 金鑰。建立您的第一個 API 金鑰以開始與 GeNieGO SSO 整合。", "developer.settings.apiKeys.generateFirst": "產生您的第一個 API 金鑰", "// Webhooks": "", "developer.settings.webhooks.title": "Webhook", "developer.settings.webhooks.description": "配置用於即時事件通知的 Webhook", "developer.settings.webhooks.createNew": "新增 Webhook", "developer.settings.webhooks.url": "Webhook URL", "developer.settings.webhooks.events": "事件", "developer.settings.webhooks.secret": "密鑰", "developer.settings.webhooks.status": "狀態", "developer.settings.webhooks.active": "活躍", "developer.settings.webhooks.inactive": "非活躍", "developer.settings.webhooks.lastTriggered": "最後觸發", "developer.settings.webhooks.noWebhooks": "無 Webhook", "developer.settings.webhooks.noWebhooksDesc": "您尚未建立任何 Webhook", "// Security": "", "developer.settings.security.title": "安全設定", "developer.settings.security.description": "配置您開發者帳戶的安全設定", "developer.settings.security.twoFactor": "雙重驗證", "developer.settings.security.twoFactorHelp": "開發者帳戶存取需要 2FA", "developer.settings.security.ipRestrictions": "IP 限制", "developer.settings.security.ipRestrictionsHelp": "限制 API 存取特定 IP 地址", "developer.settings.security.sessionTimeout": "工作階段逾時", "developer.settings.security.sessionTimeoutHelp": "開發者工作階段到期前的時間（分鐘）", "// Notifications": "", "developer.settings.notifications.title": "通知設定", "developer.settings.notifications.description": "配置您如何接收開發者通知", "developer.settings.notifications.email": "電子郵件通知", "developer.settings.notifications.emailHelp": "透過電子郵件接收通知", "developer.settings.notifications.apiUsage": "API 使用警示", "developer.settings.notifications.apiUsageHelp": "獲得 API 使用閾值通知", "developer.settings.notifications.securityAlerts": "安全警示", "developer.settings.notifications.securityAlertsHelp": "接收安全相關通知", "// Environments": "", "developer.settings.environments.title": "環境設定", "developer.settings.environments.description": "配置開發、測試和生產環境", "developer.settings.environments.defaultEnvironment": "預設環境", "developer.settings.environments.defaultEnvironmentHelp": "新應用程式的預設環境", "developer.settings.environments.development": "開發", "developer.settings.environments.staging": "測試", "developer.settings.environments.production": "生產", "// Toast Messages": "", "developer.settings.toast.copied": "已複製到剪貼簿", "developer.settings.toast.copiedDesc": "文字已複製到您的剪貼簿", "developer.settings.toast.saved": "設定已儲存", "developer.settings.toast.savedDesc": "開發者設定已成功更新", "developer.settings.toast.saveFailed": "儲存失敗", "developer.settings.toast.saveFailedDesc": "儲存設定失敗。請重試", "developer.settings.toast.loadFailed": "載入資料失敗", "developer.settings.toast.loadFailedDesc": "無法載入 API 金鑰和 Webhook。請重試", "developer.settings.toast.settingsLoadFailed": "載入設定失敗", "developer.settings.toast.settingsLoadFailedDesc": "無法載入開發者設定。請重試", "developer.settings.toast.comingSoon": "功能即將推出", "developer.settings.toast.comingSoonDesc": "此功能將在未來更新中提供", "// Account Information": "", "developer.settings.accountInformation": "帳戶資訊", "developer.settings.accountInformationDesc": "更新您的開發者帳戶詳細資料", "developer.settings.companyName": "公司名稱", "developer.settings.companyNamePlaceholder": "您的公司名稱", "developer.settings.contactEmail": "聯絡電子郵件", "developer.settings.contactEmailPlaceholder": "<EMAIL>", "developer.settings.supportUrl": "支援 URL", "developer.settings.supportUrlPlaceholder": "https://yourcompany.com/support", "// Development Environment": "", "developer.settings.developmentEnvironment": "開發環境", "developer.settings.developmentEnvironmentDesc": "配置您的開發和測試環境", "developer.settings.sandboxMode": "沙盒模式", "developer.settings.sandboxModeDesc": "使用測試環境進行開發", "developer.settings.debugMode": "除錯模式", "developer.settings.debugModeDesc": "啟用詳細的 API 回應記錄", "developer.settings.rateLimitBypass": "速率限制繞過", "developer.settings.rateLimitBypassDesc": "繞過測試的速率限制（僅限沙盒）", "// Notification Preferences": "", "developer.settings.notificationPreferences": "通知偏好設定", "developer.settings.notificationPreferencesDesc": "配置您接收更新和警報的方式", "developer.settings.emailNotifications": "電子郵件通知", "developer.settings.emailNotificationsDesc": "透過電子郵件接收一般更新", "developer.settings.webhookFailureAlerts": "Webhook 失敗警報", "developer.settings.webhookFailureAlertsDesc": "當 webhook 失敗時收到通知", "developer.settings.usageAlerts": "使用量警報", "developer.settings.usageAlertsDesc": "API 使用量限制警報", "developer.settings.securityAlerts": "安全警報", "developer.settings.securityAlertsDesc": "重要的安全通知", "// Webhook Management": "", "developer.settings.noWebhooksConfigured": "尚未配置 Webhook", "developer.settings.noWebhooksConfiguredDesc": "設定 webhook 端點以接收有關應用程式事件的即時通知。", "developer.settings.addFirstWebhook": "新增您的第一個 Webhook", "developer.settings.webhookActive": "啟用", "developer.settings.webhookInactive": "停用", "developer.settings.webhookCreated": "建立時間：", "developer.settings.webhookLastTriggered": "最後觸發：", "developer.settings.webhookNever": "從未", "developer.settings.webhookEvents": "事件：", "developer.settings.webhookEventsSubscribed": "已訂閱", "developer.settings.webhookSecret": "密鑰：", "// API Configuration": "", "developer.settings.defaultApiVersion": "預設 API 版本", "developer.settings.requestTimeout": "請求逾時（秒）"}