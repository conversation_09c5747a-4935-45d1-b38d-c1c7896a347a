{"// Developer Organizations Management": "", "developer.organizations.title": "组织管理", "developer.organizations.subtitle": "管理您的团队组织並协作開發應用程序", "// Common Actions": "", "developer.organizations.common.error": "載入组织失敗", "developer.organizations.common.retry": "重試", "developer.organizations.common.back": "返回组织列表", "// Empty State": "", "developer.organizations.empty.title": "暂無组织", "developer.organizations.empty.description": "創建您的第一個组织以開始与团队协作", "developer.organizations.empty.action": "創建组织", "// Create Organization": "", "developer.organizations.create.title": "創建组织", "developer.organizations.create.subtitle": "為您的团队設置新组织", "// Create Form": "", "developer.organizations.create.form.basicInfo": "基本信息", "developer.organizations.create.form.additionalDetails": "附加詳情", "developer.organizations.create.form.settings": "组织設置", "developer.organizations.create.form.name.label": "组织名称", "developer.organizations.create.form.name.placeholder": "輸入组织名称", "developer.organizations.create.form.name.help": "為您的组织选择一個描述性名称", "developer.organizations.create.form.slug.label": "组织標识", "developer.organizations.create.form.slug.placeholder": "organization-slug", "developer.organizations.create.form.slug.help": "URL友好標识符（仅限小写字母、數字和連字符）", "developer.organizations.create.form.description.label": "描述", "developer.organizations.create.form.description.placeholder": "描述您组织的目的", "developer.organizations.create.form.description.help": "可选的组织用途描述", "developer.organizations.create.form.website.label": "網站", "developer.organizations.create.form.website.placeholder": "https://example.com", "developer.organizations.create.form.website.help": "可选的组织網站URL", "developer.organizations.create.form.isPublic.label": "公開组织", "developer.organizations.create.form.isPublic.help": "允许其他人發現並請求加入此组织", "developer.organizations.create.form.maxMembers.label": "最大成員數", "developer.organizations.create.form.maxMembers.help": "設置此组织允许的最大成員數量", "// Create Actions": "", "developer.organizations.create.actions.create": "創建组织", "developer.organizations.create.actions.cancel": "取消", "developer.organizations.create.actions.creating": "創建中...", "// Create Messages": "", "developer.organizations.create.success": "组织創建成功！", "// Create Errors": "", "developer.organizations.create.errors.nameRequired": "组织名称為必填項", "developer.organizations.create.errors.slugRequired": "组织標识為必填項", "developer.organizations.create.errors.slugInvalid": "標识只能包含小写字母、數字和連字符", "developer.organizations.create.errors.slugTaken": "此標识已被使用", "developer.organizations.create.errors.websiteInvalid": "請輸入有效的網站URL", "developer.organizations.create.errors.maxMembersInvalid": "最大成員數必須在1到1000之間", "developer.organizations.create.errors.createFailed": "創建组织失敗", "// Dashboard Navigation": "", "developer.organizations.navigation.overview": "概覽", "developer.organizations.navigation.members": "成員", "developer.organizations.navigation.applications": "應用程序", "developer.organizations.navigation.invitations": "邀請", "developer.organizations.navigation.settings": "設置", "// Dashboard Overview": "", "developer.organizations.dashboard.overview.members.title": "成員", "developer.organizations.dashboard.overview.members.count": "{count} 名成員", "developer.organizations.dashboard.overview.members.viewAll": "查看所有成員", "developer.organizations.dashboard.overview.applications.title": "應用程序", "developer.organizations.dashboard.overview.applications.count": "{count} 個應用", "developer.organizations.dashboard.overview.applications.viewAll": "查看所有應用", "developer.organizations.dashboard.overview.invitations.title": "待處理邀請", "developer.organizations.dashboard.overview.invitations.count": "{count} 個待處理", "developer.organizations.dashboard.overview.invitations.viewAll": "查看所有邀請", "// Dashboard Settings": "", "developer.organizations.dashboard.settings.general.title": "常规設置", "developer.organizations.dashboard.settings.general.name": "组织名称", "developer.organizations.dashboard.settings.general.description": "描述", "developer.organizations.dashboard.settings.general.website": "網站", "developer.organizations.dashboard.settings.general.isPublic": "公開组织", "developer.organizations.dashboard.settings.general.maxMembers": "最大成員數", "// Dashboard Organization Info": "", "developer.organizations.dashboard.info.id": "组织ID", "developer.organizations.dashboard.info.created": "創建時間", "developer.organizations.dashboard.info.role": "您的角色", "developer.organizations.dashboard.info.tier": "等级", "developer.organizations.dashboard.info.status": "狀態", "// Organization Status & Roles": "", "developer.organizations.status.public": "公開", "developer.organizations.status.private": "私有", "developer.organizations.role.owner": "所有者", "developer.organizations.role.admin": "管理員", "developer.organizations.role.member": "成員", "developer.organizations.members.count": "{count} 名成員", "developer.organizations.members.single": "1 名成員", "developer.organizations.members.zero": "0 名成員", "developer.organizations.dashboard.navigation.backToOrganizations": "返回组织列表", "// Dashboard Overview Cards": "", "developer.organizations.dashboard.overview.applications.single": "1 個應用", "developer.organizations.dashboard.overview.applications.zero": "0 個應用", "developer.organizations.dashboard.overview.invitations.single": "1 個待處理", "developer.organizations.dashboard.overview.invitations.zero": "0 個待處理", "// Organization Info": "", "developer.organizations.dashboard.info.generalInformation": "基本信息", "developer.organizations.dashboard.info.organizationName": "组织名称", "developer.organizations.dashboard.info.maximumMembers": "最大成員數", "developer.organizations.dashboard.info.yourRole": "您的角色", "// Organization Tiers": "", "developer.organizations.tier.free": "免费版", "developer.organizations.tier.basic": "基础版", "developer.organizations.tier.premium": "高级版", "developer.organizations.tier.enterprise": "企业版", "// Dashboard Actions": "", "developer.organizations.dashboard.actions.inviteMembers": "邀請成員", "developer.organizations.dashboard.actions.manageApplications": "管理應用", "// Dashboard Tabs": "", "developer.organizations.dashboard.members.title": "成員", "developer.organizations.dashboard.members.subtitle": "管理组织成員及其角色", "developer.organizations.dashboard.members.invite": "邀請成員", "developer.organizations.dashboard.members.changeRole": "角色已更改", "developer.organizations.dashboard.members.remove": "成員已移除", "developer.organizations.dashboard.applications.title": "應用", "developer.organizations.dashboard.applications.subtitle": "管理与此组织關聯的應用", "developer.organizations.dashboard.applications.add": "添加應用", "developer.organizations.dashboard.applications.empty.title": "暂無應用", "developer.organizations.dashboard.applications.empty.description": "添加應用以將其归类到此组织下", "developer.organizations.dashboard.applications.status.active": "活躍", "developer.organizations.dashboard.applications.edit": "應用已编辑", "developer.organizations.dashboard.applications.remove": "應用已移除", "developer.organizations.dashboard.applications.addDescription": "功能即將推出 - 應用分组", "developer.organizations.dashboard.applications.status.inactive": "非活躍", "developer.organizations.dashboard.invitations.title": "邀請", "developer.organizations.dashboard.invitations.subtitle": "管理待處理的成員邀請", "developer.organizations.dashboard.invitations.send": "發送邀請", "developer.organizations.dashboard.invitations.empty.title": "暂無待處理邀請", "developer.organizations.dashboard.invitations.empty.description": "發送邀請以添加新成員到您的组织", "developer.organizations.dashboard.invitations.status.pending": "待處理", "developer.organizations.dashboard.invitations.status.accepted": "已接受", "developer.organizations.dashboard.invitations.status.declined": "已拒絕", "developer.organizations.dashboard.invitations.status.rejected": "已拒绝", "developer.organizations.dashboard.invitations.status.expired": "已過期", "developer.organizations.dashboard.invitations.sendDescription": "功能即將推出 - 邀請繫统", "developer.organizations.dashboard.members.actions.makeAdmin": "設為管理員", "developer.organizations.dashboard.members.actions.makeMember": "設為成員", "developer.organizations.dashboard.members.actions.removeMember": "移除成員", "developer.organizations.dashboard.applications.actions.edit": "编辑應用", "developer.organizations.dashboard.applications.actions.removeFromOrganization": "從组织中移除", "// Toast Messages": "", "developer.organizations.toast.invitationSent.title": "邀請已發送", "developer.organizations.toast.invitationSent.description": "已成功向 {email} 發送邀請", "developer.organizations.toast.invitationCancelled.title": "邀請已取消", "developer.organizations.toast.invitationCancelled.description": "邀請已成功取消", "developer.organizations.toast.memberRemoved.title": "成員已移除", "developer.organizations.toast.memberRemoved.description": "成員已從組織中移除", "developer.organizations.toast.roleChanged.title": "角色已更新", "developer.organizations.toast.roleChanged.description": "成員角色已成功更新", "developer.organizations.toast.error.title": "錯誤", "developer.organizations.toast.searchFailed.description": "搜尋失敗：{query}。請重試。", "developer.organizations.dashboard.members.search": "搜索成員...", "developer.organizations.dashboard.members.sortBy": "排序方式", "developer.organizations.dashboard.members.sortByName": "按姓名排序", "developer.organizations.dashboard.members.sortByRole": "按角色排序", "developer.organizations.dashboard.members.sortByJoined": "按加入日期排序", "developer.organizations.dashboard.applications.search": "搜索應用...", "developer.organizations.dashboard.applications.sortBy": "排序方式", "developer.organizations.dashboard.applications.sortByName": "按名称排序", "developer.organizations.dashboard.applications.sortByStatus": "按狀態排序", "developer.organizations.dashboard.applications.sortByAdded": "按添加日期排序", "developer.organizations.dashboard.members.roleChanged": "已將 {user} 的角色更改為 {role}", "developer.organizations.dashboard.settings.edit": "编辑設置", "developer.organizations.dashboard.settings.save": "儲存更改", "developer.organizations.dashboard.settings.cancel": "取消", "developer.organizations.dashboard.members.addUserDialog.title": "添加用戶到组织", "developer.organizations.dashboard.members.addUserDialog.searchUser": "搜索用戶", "developer.organizations.dashboard.members.addUserDialog.searchPlaceholder": "按姓名或郵箱搜索...", "developer.organizations.dashboard.members.addUserDialog.role": "角色", "developer.organizations.dashboard.members.addUserDialog.addUser": "添加用戶", "developer.organizations.dashboard.members.addUserDialog.sendInvitation": "發送邀請", "developer.organizations.dashboard.members.addUserDialog.searching": "搜索用戶中...", "developer.organizations.dashboard.members.addUserDialog.cancel": "取消", "developer.organizations.dashboard.members.addUserDialog.noResults": "沒有找到搜索結果：{query}", "developer.organizations.dashboard.members.addUserDialog.searchError": "搜索失敗：{query}。請重試。", "developer.organizations.dashboard.members.addUserDialog.allMembersAlready": "所有匹配'{query}'的用戶都已经是该组织的成員。", "developer.organizations.dashboard.members.addUserDialog.alreadyInvited": "匹配'{query}'的用戶已经被邀請加入该组织。", "developer.organizations.dashboard.invitations.cancel": "取消邀請", "developer.organizations.dashboard.members.addUserDialog.message": "消息（可选）", "developer.organizations.dashboard.members.addUserDialog.messagePlaceholder": "輸入邀請消息", "developer.organizations.dashboard.members.addUserDialog.userCannotBeAdmin": "繫統使用者只能被邀請為成員，不能成為管理員", "developer.organizations.dashboard.settings.general.maxMembers.10": "10 名成員", "developer.organizations.dashboard.settings.general.maxMembers.25": "25 名成員", "developer.organizations.dashboard.settings.general.maxMembers.50": "50 名成員", "developer.organizations.dashboard.settings.general.maxMembers.100": "100 名成員", "developer.organizations.dashboard.settings.general.maxMembers.250": "250 名成員", "developer.organizations.dashboard.settings.general.maxMembers.500": "500 名成員", "developer.organizations.dashboard.settings.general.maxMembers.1000": "1000 名成員", "developer.organizations.dashboard.applications.addApplicationDialog.title": "添加應用到组织", "developer.organizations.dashboard.applications.addApplicationDialog.selectApplication": "选择應用", "developer.organizations.dashboard.applications.addApplicationDialog.loadingApplications": "加載應用中...", "developer.organizations.dashboard.applications.addApplicationDialog.noApplications": "沒有可添加的應用", "developer.organizations.dashboard.applications.addApplicationDialog.selectPlaceholder": "选择一個應用", "developer.organizations.dashboard.applications.addApplicationDialog.description": "描述（可选）", "developer.organizations.dashboard.applications.addApplicationDialog.descriptionPlaceholder": "輸入應用描述", "developer.organizations.dashboard.applications.addApplicationDialog.cancel": "取消", "developer.organizations.dashboard.applications.addApplicationDialog.addApplication": "添加應用", "developer.organizations.dashboard.settings.title": "設置", "developer.organizations.dashboard.settings.subtitle": "配置组织設置和偏好", "developer.organizations.dashboard.settings.general.isPublicHelp": "允许其他人發現並請求加入此组织", "developer.organizations.dashboard.settings.danger.title": "危险区域", "developer.organizations.dashboard.settings.danger.description": "這些操作無法撤销，請谨慎操作。", "developer.organizations.dashboard.settings.danger.delete": "删除组织"}