{"// Admin Dashboard": "", "admin.dashboard.title": "GenieAdmin 儀表板", "admin.dashboard.subtitle": "GeNieGO SSO 繫統管理和監控", "admin.dashboard.refresh": "重新整理", "admin.dashboard.systemSettings": "繫統設定", "// Tabs": "", "admin.dashboard.tabs.overview": "概覽", "admin.dashboard.tabs.analytics": "分析", "admin.dashboard.tabs.users": "使用者", "admin.dashboard.tabs.applications": "應用程式", "// Quick Stats": "", "admin.dashboard.stats.totalUsers": "使用者總數", "admin.dashboard.stats.activeUsers": "位活躍使用者", "admin.dashboard.stats.applications": "應用程式", "admin.dashboard.stats.applicationsActive": "個活躍", "admin.dashboard.stats.todaysLogins": "今日登入", "admin.dashboard.stats.authRequests": "驗證請求", "admin.dashboard.stats.pendingApprovals": "待審核", "admin.dashboard.stats.awaitingReview": "個等待審核", "// System Health": "", "admin.dashboard.health.title": "繫統健康狀態", "admin.dashboard.health.healthy": "健康", "admin.dashboard.health.warning": "警告", "admin.dashboard.health.critical": "嚴重", "admin.dashboard.health.lastUpdated": "最後更新", "// Quick Actions": "", "admin.dashboard.quickActions.userManagement.title": "使用者管理", "admin.dashboard.quickActions.userManagement.description": "管理使用者帳戶和權限", "admin.dashboard.quickActions.userManagement.button": "管理使用者", "admin.dashboard.quickActions.applicationManagement.title": "應用程式管理", "admin.dashboard.quickActions.applicationManagement.description": "監控和核准應用程式", "admin.dashboard.quickActions.applicationManagement.button": "管理應用程式", "admin.dashboard.quickActions.systemAnalytics.title": "繫統分析", "admin.dashboard.quickActions.systemAnalytics.description": "檢視詳細的繫統指標", "admin.dashboard.quickActions.systemAnalytics.button": "檢視分析", "// Users Tab": "", "admin.dashboard.users.title": "使用者管理", "admin.dashboard.users.viewAllUsers": "檢視所有使用者", "admin.dashboard.users.description": "管理使用者帳戶和權限", "admin.dashboard.users.noUsersFound": "找不到使用者", "admin.dashboard.users.noUsersFiltered": "請調整您的搜尋條件或篩選器", "admin.dashboard.users.noUsersRegistered": "尚未有使用者註冊", "admin.dashboard.users.unknownUser": "未知使用者", "admin.dashboard.users.noEmail": "無電子郵件", "admin.dashboard.users.role": "角色", "admin.dashboard.users.joined": "加入於", "admin.dashboard.users.unknown": "未知", "admin.dashboard.users.active": "活躍", "admin.dashboard.users.inactive": "未啟用", "admin.dashboard.users.verified": "已驗證", "admin.dashboard.users.unverified": "未驗證", "admin.dashboard.users.viewAllCount": "檢視所有 {count} 位使用者", "// Applications Tab": "", "admin.dashboard.applications.filterTitle": "篩選應用程式", "admin.dashboard.applications.searchPlaceholder": "依名稱或擁有者搜尋應用程式...", "admin.dashboard.applications.title": "應用程式管理", "admin.dashboard.applications.viewAllApplications": "檢視所有應用程式", "admin.dashboard.applications.owner": "擁有者", "admin.dashboard.applications.clientId": "客戶端 ID", "admin.dashboard.applications.created": "建立時間", "// Analytics Tab": "", "admin.dashboard.analytics.totalUsers": "使用者總數", "admin.dashboard.analytics.activeApplications": "活躍應用程式", "admin.dashboard.analytics.dailyLogins": "每日登入", "admin.dashboard.analytics.pendingApprovals": "待審核", "admin.dashboard.analytics.registeredUsers": "註冊使用者", "admin.dashboard.analytics.total": "總計", "admin.dashboard.analytics.totalCount": "總計 {count}", "admin.dashboard.analytics.todaysActivity": "今日活動", "admin.dashboard.analytics.awaitingReview": "等待審核", "admin.dashboard.analytics.activityTimeline": "活動時間軸", "admin.dashboard.analytics.activityTrends": "過去一週的每日活動趨勢", "admin.dashboard.analytics.totalLogins": "總登入次數", "admin.dashboard.analytics.newUsers": "新使用者", "admin.dashboard.analytics.applicationStatus": "應用程式狀態", "admin.dashboard.analytics.statusDistribution": "應用程式狀態分佈", "admin.dashboard.analytics.activeStatus": "活躍", "admin.dashboard.analytics.pendingStatus": "待審核", "admin.dashboard.analytics.inactiveStatus": "非活躍", "admin.dashboard.analytics.topApplications": "表現最佳的應用程式", "admin.dashboard.analytics.topApplicationsDesc": "使用者參與度最高的應用程式", "// Users Tab Search": "", "admin.dashboard.users.searchPlaceholder": "按電子郵件或姓名搜尋使用者...", "admin.dashboard.users.joinedOn": "加入於", "admin.dashboard.analytics.quickStats": "快速統計", "admin.dashboard.analytics.developers": "開發者", "admin.dashboard.analytics.weekLogins": "週登入次數", "admin.dashboard.analytics.monthLogins": "月登入次數", "// Weekly Growth": "", "admin.dashboard.analytics.weeklyGrowth": "週成長", "admin.dashboard.analytics.users": "使用者", "admin.dashboard.analytics.applications": "應用程式", "admin.dashboard.analytics.logins": "登入", "// System Status": "", "admin.dashboard.analytics.systemStatus": "繫統狀態", "admin.dashboard.analytics.apiServicesOnline": "API 服務線上", "admin.dashboard.analytics.databaseConnected": "資料庫已連接", "admin.dashboard.analytics.authenticationActive": "身份驗證啟用", "// Application Status Badges": "", "admin.dashboard.applications.approved": "已核准", "admin.dashboard.applications.pending": "待審核", "admin.dashboard.applications.active": "活躍", "admin.dashboard.applications.inactive": "非活躍"}