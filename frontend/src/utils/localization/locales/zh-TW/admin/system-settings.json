{"// Admin System Settings Page": "", "admin.systemSettings.title": "繫統設定", "admin.systemSettings.subtitle": "配置企業安全政策和繫統參數", "admin.systemSettings.refresh": "重新整理", "admin.systemSettings.resetToDefaults": "重設為預設值", "admin.systemSettings.saveChanges": "儲存變更", "admin.systemSettings.saving": "儲存中...", "// Tabs": "", "admin.systemSettings.tabs.authentication": "身份驗證", "admin.systemSettings.tabs.security": "安全性", "admin.systemSettings.tabs.system": "繫統", "admin.systemSettings.tabs.compliance": "合規性", "admin.systemSettings.tabs.notifications": "通知", "admin.systemSettings.tabs.api": "API", "// Alerts": "", "admin.systemSettings.alerts.maintenanceMode": "維護模式啟用：", "admin.systemSettings.alerts.maintenanceModeDescription": "繫統目前處於維護模式。使用者將看到維護頁面且無法存取服務。", "// Authentication Settings": "", "admin.systemSettings.authentication.title": "身份驗證配置", "admin.systemSettings.authentication.description": "配置身份驗證政策和密碼要求", "admin.systemSettings.authentication.sessionTimeout": "工作階段逾時（分鐘）", "admin.systemSettings.authentication.sessionTimeoutHelp": "閒置後自動登出", "admin.systemSettings.authentication.maxLoginAttempts": "最大登入嘗試次數", "admin.systemSettings.authentication.maxLoginAttemptsHelp": "帳戶鎖定門檻", "admin.systemSettings.authentication.passwordPolicy": "密碼政策", "admin.systemSettings.authentication.passwordMinLength": "最小長度", "admin.systemSettings.authentication.passwordRequireSpecialChars": "需要特殊字元", "admin.systemSettings.authentication.passwordRequireSpecialCharsHelp": "包含符號如 !@#$%", "admin.systemSettings.authentication.passwordRequireNumbers": "需要數字", "admin.systemSettings.authentication.passwordRequireNumbersHelp": "至少包含一個數字", "admin.systemSettings.authentication.passwordRequireUppercase": "需要大寫字母", "admin.systemSettings.authentication.passwordRequireUppercaseHelp": "至少包含一個大寫字母", "admin.systemSettings.authentication.passwordExpiryDays": "密碼到期（天）", "admin.systemSettings.authentication.passwordExpiryHelp": "強制密碼變更間隔", "admin.systemSettings.authentication.twoFactorRequired": "需要雙重驗證", "admin.systemSettings.authentication.twoFactorHelp": "所有使用者強制使用 2FA", "// Security Policies": "", "admin.systemSettings.security.title": "安全政策", "admin.systemSettings.security.description": "配置安全政策和存取控制", "admin.systemSettings.security.allowPasswordReset": "允許密碼重設", "admin.systemSettings.security.allowPasswordResetHelp": "使用者可透過電子郵件重設密碼", "admin.systemSettings.security.requireEmailVerification": "需要電子郵件驗證", "admin.systemSettings.security.requireEmailVerificationHelp": "新帳戶必須驗證電子郵件", "admin.systemSettings.security.allowSelfRegistration": "允許自行註冊", "admin.systemSettings.security.allowSelfRegistrationHelp": "使用者可獨立建立帳戶", "admin.systemSettings.security.adminApprovalRequired": "需要管理員核准", "admin.systemSettings.security.adminApprovalRequiredHelp": "新帳戶需要管理員核准", "admin.systemSettings.security.auditLogRetentionDays": "稽核日誌儲留（天）", "admin.systemSettings.security.auditLogRetentionDaysHelp": "安全日誌儲留時間", "// System Configuration": "", "admin.systemSettings.system.title": "繫統配置", "admin.systemSettings.system.description": "基本繫統設定和操作參數", "admin.systemSettings.system.systemName": "繫統名稱", "admin.systemSettings.system.systemNameHelp": "您的 SSO 服務顯示名稱", "admin.systemSettings.system.systemDescription": "繫統描述", "admin.systemSettings.system.systemDescriptionHelp": "您的服務簡要描述", "admin.systemSettings.system.supportEmail": "支援電子郵件", "admin.systemSettings.system.supportEmailHelp": "使用者支援聯絡電子郵件", "admin.systemSettings.system.maintenanceMode": "維護模式", "admin.systemSettings.system.maintenanceModeHelp": "暫時停用使用者存取", "admin.systemSettings.system.debugMode": "除錯模式", "admin.systemSettings.system.debugModeHelp": "啟用詳細日誌記錄（僅限開發）", "admin.systemSettings.system.systemStatus": "繫統狀態", "admin.systemSettings.system.active": "啟用", "// Compliance Settings": "", "admin.systemSettings.compliance.title": "合規性與隱私", "admin.systemSettings.compliance.description": "配置 GDPR 合規性和資料儲護設定", "admin.systemSettings.compliance.gdprCompliance": "GDPR 合規性", "admin.systemSettings.compliance.gdprComplianceHelp": "啟用 GDPR 隱私功能", "admin.systemSettings.compliance.gdprComplianceMode": "GDPR 合規模式", "admin.systemSettings.compliance.gdprComplianceModeHelp": "啟用歐盟資料儲護功能", "admin.systemSettings.compliance.dataRetentionDays": "資料儲留期間（天）", "admin.systemSettings.compliance.dataRetentionDaysHelp": "自動刪除非活躍使用者資料", "admin.systemSettings.compliance.cookieConsent": "<PERSON><PERSON> 同意", "admin.systemSettings.compliance.cookieConsentHelp": "需要 Cookie 同意橫幅", "admin.systemSettings.compliance.cookieConsentBanner": "<PERSON><PERSON> 同意橫幅", "admin.systemSettings.compliance.cookieConsentBannerHelp": "向使用者顯示 Cookie 同意", "admin.systemSettings.compliance.privacyPolicyUrl": "隱私政策 URL", "admin.systemSettings.compliance.privacyPolicyUrlHelp": "您的隱私政策連結", "admin.systemSettings.compliance.termsOfServiceUrl": "服務條款 URL", "admin.systemSettings.compliance.termsOfServiceUrlHelp": "您的服務條款連結", "// Notification Settings": "", "admin.systemSettings.notifications.title": "通知配置", "admin.systemSettings.notifications.description": "配置繫統通知和警示", "admin.systemSettings.notifications.securityAlertsEnabledHelp": "向管理員通報安全事件", "admin.systemSettings.notifications.emailNotificationsEnabled": "電子郵件通知", "admin.systemSettings.notifications.emailNotificationsEnabledHelp": "向使用者發送電子郵件通知", "admin.systemSettings.notifications.securityAlertsEnabled": "安全警示", "admin.systemSettings.notifications.systemMaintenanceNotifications": "維護通知", "admin.systemSettings.notifications.systemMaintenanceNotificationsHelp": "通知使用者繫統維護", "// API Settings": "", "admin.systemSettings.api.title": "API 配置", "admin.systemSettings.api.description": "配置 API 速率限制和版本控制", "admin.systemSettings.api.rateLimitEnabled": "速率限制", "admin.systemSettings.api.rateLimitEnabledHelp": "啟用 API 速率限制", "admin.systemSettings.api.rateLimitRequests": "每窗口請求數", "admin.systemSettings.api.rateLimitRequestsHelp": "每個時間窗口的最大請求數", "admin.systemSettings.api.rateLimitWindow": "窗口大小（秒）", "admin.systemSettings.api.rateLimitWindowHelp": "速率限制的時間窗口", "admin.systemSettings.api.apiVersioning": "API 版本", "admin.systemSettings.api.apiVersioningHelp": "新整合的預設 API 版本", "admin.systemSettings.api.version1": "版本 1.0（目前）", "admin.systemSettings.api.version2": "版本 2.0（測試版）", "// Toast Messages": "", "admin.systemSettings.toast.loadFailed.title": "載入設定失敗", "admin.systemSettings.toast.loadFailed.description": "無法載入繫統設定。請重試。", "admin.systemSettings.toast.saveSuccess.title": "設定已儲存", "admin.systemSettings.toast.saveSuccess.description": "繫統設定已成功更新。", "admin.systemSettings.toast.saveFailed.title": "儲存失敗", "admin.systemSettings.toast.saveFailed.description": "儲存繫統設定失敗。請重試。"}