{"admin.organizations.title": "Organization Management", "admin.organizations.subtitle": "Manage and oversee all organizations in the system", "admin.organizations.exportAll": "Export All", "admin.organizations.refresh": "Refresh", "admin.organizations.search.placeholder": "Search organizations by name, slug, owner...", "admin.organizations.filters.title": "Filter Organizations", "admin.organizations.filters.status": "Status", "admin.organizations.filters.status.all": "All Status", "admin.organizations.filters.status.active": "Active", "admin.organizations.filters.status.inactive": "Inactive", "admin.organizations.filters.visibility": "Visibility", "admin.organizations.filters.visibility.all": "All Visibility", "admin.organizations.filters.visibility.public": "Public", "admin.organizations.filters.visibility.private": "Private", "admin.organizations.status.active": "Active", "admin.organizations.status.inactive": "Inactive", "admin.organizations.visibility.public": "Public", "admin.organizations.visibility.private": "Private", "admin.organizations.members": "members", "admin.organizations.noOwner": "No Owner", "admin.organizations.noDescription": "No description provided", "admin.organizations.actions.viewDetails": "View Details", "admin.organizations.actions.viewMembers": "View Members", "admin.organizations.actions.activate": "Activate", "admin.organizations.actions.deactivate": "Deactivate", "admin.organizations.actions.editSettings": "Edit Settings", "admin.organizations.actions.delete": "Delete Organization", "admin.organizations.empty.title": "No Organizations Found", "admin.organizations.empty.filtered": "No organizations match your current filters. Try adjusting your search criteria.", "admin.organizations.empty.noOrganizations": "No organizations have been created yet.", "admin.organizations.details.title": "Organization Details", "admin.organizations.details.description": "View detailed information about this organization", "admin.organizations.details.totalMembers": "Total Members", "admin.organizations.details.activeMembers": "Active Members", "admin.organizations.details.pendingInvitations": "Pending Invitations", "admin.organizations.details.maxMembers": "Max Members", "admin.organizations.details.created": "Created", "admin.organizations.details.updated": "Last Updated", "admin.organizations.details.members": "Members", "admin.organizations.details.noMembers": "No members found", "admin.organizations.details.inactive": "Inactive", "admin.organizations.details.invitedOn": "Invited on", "admin.organizations.members.title": "Organization Members", "admin.organizations.members.description": "Manage members of {name}", "admin.organizations.members.summary": "Members Overview", "admin.organizations.members.count": "{count} total members", "admin.organizations.members.empty.title": "No Members Found", "admin.organizations.members.empty.description": "This organization has no members yet.", "admin.organizations.members.status.active": "Active", "admin.organizations.members.status.inactive": "Inactive", "admin.organizations.members.joinedOn": "Joined", "admin.organizations.members.actions.activate": "Activate Member", "admin.organizations.members.actions.deactivate": "Deactivate Member", "admin.organizations.members.actions.changeRole": "Change Role", "admin.organizations.members.actions.remove": "Remove Member", "admin.organizations.settings.title": "Organization Settings", "admin.organizations.settings.description": "Description", "admin.organizations.settings.basicInfo": "Basic Information", "admin.organizations.settings.name": "Organization Name", "admin.organizations.settings.namePlaceholder": "Enter organization name", "admin.organizations.settings.descriptionPlaceholder": "Enter organization description", "admin.organizations.settings.visibility": "Visibility & Access", "admin.organizations.settings.publicOrganization": "Public Organization", "admin.organizations.settings.publicDescription": "Allow anyone to discover and request to join this organization", "admin.organizations.settings.activeOrganization": "Active Organization", "admin.organizations.settings.activeDescription": "Organization is active and members can access it", "admin.organizations.settings.memberLimits": "Member Limits", "admin.organizations.settings.maxMembers": "Maximum Members", "admin.organizations.settings.maxMembersDescription": "Maximum number of members allowed in this organization", "admin.organizations.settings.confirmClose": "You have unsaved changes. Are you sure you want to close?", "admin.organizations.settings.unsavedChanges": "You have unsaved changes"}