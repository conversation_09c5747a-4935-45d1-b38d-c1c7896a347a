{"// Admin Security Dashboard Page": "", "admin.securityDashboard.title": "Security Dashboard", "admin.securityDashboard.subtitle": "Real-time security monitoring and threat detection", "admin.securityDashboard.refresh": "Refresh", "admin.securityDashboard.exportReport": "Export Report", "// Time Range Options": "", "admin.securityDashboard.timeRange.lastHour": "Last Hour", "admin.securityDashboard.timeRange.last24h": "Last 24h", "admin.securityDashboard.timeRange.last7d": "Last 7 days", "admin.securityDashboard.timeRange.last30d": "Last 30 days", "// Tabs": "", "admin.securityDashboard.tabs.overview": "Overview", "admin.securityDashboard.tabs.threats": "Active Threats", "admin.securityDashboard.tabs.events": "Security Events", "admin.securityDashboard.tabs.analytics": "Analytics", "// Security Risk Alert": "", "admin.securityDashboard.alerts.elevatedRisk": "Elevated Security Risk:", "admin.securityDashboard.alerts.riskScoreMessage": "Current risk score is {score}/10. {threats} active threats require immediate attention.", "// Security Metrics Cards": "", "admin.securityDashboard.metrics.activeThreats": "Active Threats", "admin.securityDashboard.metrics.totalDetected": "total detected", "admin.securityDashboard.metrics.blockedAttempts": "Blocked Attempts", "admin.securityDashboard.metrics.last24h": "Last 24 hours", "admin.securityDashboard.metrics.failedLogins": "Failed <PERSON><PERSON>", "admin.securityDashboard.metrics.suspicious": "suspicious", "admin.securityDashboard.metrics.riskScore": "Risk Score", "admin.securityDashboard.metrics.securityRiskLevel": "Security risk level", "// Overview Tab": "", "admin.securityDashboard.overview.title": "Security Overview", "admin.securityDashboard.overview.description": "Comprehensive security status and key metrics", "admin.securityDashboard.overview.monitorHealth": "Monitor overall security health and key metrics", "// Threats Tab": "", "admin.securityDashboard.threats.title": "Active Security Threats", "admin.securityDashboard.threats.description": "Current security threats requiring immediate attention ({count} total)", "admin.securityDashboard.threats.filterTitle": "Filter Threats", "admin.securityDashboard.threats.searchPlaceholder": "Search threats...", "admin.securityDashboard.threats.noThreats": "No Threats Found", "admin.securityDashboard.threats.noThreatsFiltered": "No threats match your current filters", "admin.securityDashboard.threats.noActiveThreats": "No active security threats detected", "// Events Tab": "", "admin.securityDashboard.events.title": "Security Events", "admin.securityDashboard.events.description": "Real-time security events and audit logs ({count} total)", "admin.securityDashboard.events.filterTitle": "Filter <PERSON>", "admin.securityDashboard.events.searchPlaceholder": "Search events...", "admin.securityDashboard.events.noEvents": "No Events Found", "admin.securityDashboard.events.noEventsFiltered": "No events match your current filters", "admin.securityDashboard.events.noEventsRecorded": "No security events recorded", "// Analytics Tab": "", "admin.securityDashboard.analytics.title": "Security Analytics", "admin.securityDashboard.analytics.description": "Detailed security analytics and trends", "admin.securityDashboard.analytics.comingSoon": "Security analytics coming soon", "admin.securityDashboard.analytics.advancedAnalytics": "Advanced security analytics and reporting features", "// Login Success Rate": "", "admin.securityDashboard.analytics.loginSuccessRate": "Login Success Rate", "admin.securityDashboard.analytics.authenticationSuccessMetrics": "Authentication success metrics", "admin.securityDashboard.analytics.successRate": "Success Rate", "admin.securityDashboard.analytics.successfulFailed": "{successful} successful / {failed} failed", "// Device Security": "", "admin.securityDashboard.analytics.deviceSecurity": "Device Security", "admin.securityDashboard.analytics.newDeviceRegistrations": "New device registrations", "admin.securityDashboard.analytics.newDevicesToday": "New devices registered today", "admin.securityDashboard.analytics.verifiedDevices": "Verified Devices", "// Security Reports": "", "admin.securityDashboard.analytics.securityReports": "Security Reports", "admin.securityDashboard.analytics.generateAndDownload": "Generate and download security reports", "admin.securityDashboard.analytics.dailySecurityReport": "Daily Security Report", "admin.securityDashboard.analytics.dailySecurityReportDesc": "Comprehensive daily security summary", "admin.securityDashboard.analytics.threatAnalysis": "Threat Analysis", "admin.securityDashboard.analytics.threatAnalysisDesc": "Detailed threat detection report", "admin.securityDashboard.analytics.complianceReport": "Compliance Report", "admin.securityDashboard.analytics.complianceReportDesc": "Security compliance audit", "admin.securityDashboard.analytics.download": "Download", "admin.securityDashboard.analytics.generate": "Generate"}