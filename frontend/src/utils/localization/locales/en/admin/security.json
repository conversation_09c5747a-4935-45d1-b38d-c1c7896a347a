{"// Security Dashboard": "", "admin.security.dashboard.title": "Security Dashboard", "admin.security.dashboard.subtitle": "Monitor security threats and system activity", "admin.security.dashboard.overview": "Security Overview", "admin.security.dashboard.threat.level": "Threat Level", "admin.security.dashboard.total.threats": "Total Threats", "admin.security.dashboard.critical.threats": "Critical Threats", "admin.security.dashboard.high.threats": "High Threats", "admin.security.dashboard.recent.activity": "Recent Activity", "admin.security.dashboard.view.all": "View All", "admin.security.dashboard.refresh": "Refresh", "admin.security.dashboard.last.updated": "Last Updated", "// Threat Levels": "", "admin.security.threat.level.low": "Low", "admin.security.threat.level.medium": "Medium", "admin.security.threat.level.high": "High", "admin.security.threat.level.critical": "Critical", "admin.security.threat.level.unknown": "Unknown", "// Threat Types": "", "admin.security.threat.type.suspicious.login": "Suspicious Login Activity", "admin.security.threat.type.brute.force": "Brute Force Attack", "admin.security.threat.type.account.takeover": "Account Takeover Attempt", "admin.security.threat.type.mfa.bypass": "MFA Bypass Attempt", "admin.security.threat.type.unusual.admin": "Unusual Admin Activity", "// Suspicious Login Activity": "", "admin.security.suspicious.login.title": "Suspicious Login Activity", "admin.security.suspicious.login.subtitle": "Monitor unusual login patterns and failed attempts", "admin.security.suspicious.login.ip.address": "IP Address", "admin.security.suspicious.login.failed.attempts": "Failed Attempts", "admin.security.suspicious.login.unique.users": "Unique Users Targeted", "admin.security.suspicious.login.time.window": "Time Window", "admin.security.suspicious.login.severity": "Severity", "admin.security.suspicious.login.detected.at": "Detected At", "admin.security.suspicious.login.empty": "No suspicious login activity detected", "// Brute Force Attacks": "", "admin.security.brute.force.title": "Brute Force Attacks", "admin.security.brute.force.subtitle": "Detect rapid-fire login attempts", "admin.security.brute.force.ip.address": "IP Address", "admin.security.brute.force.attempt.count": "Attempt Count", "admin.security.brute.force.rate.per.minute": "Rate per Minute", "admin.security.brute.force.first.attempt": "First Attempt", "admin.security.brute.force.last.attempt": "Last Attempt", "admin.security.brute.force.empty": "No brute force attacks detected", "// Account Takeover": "", "admin.security.takeover.title": "Account Takeover Attempts", "admin.security.takeover.subtitle": "Monitor potential account compromise", "admin.security.takeover.user.id": "User ID", "admin.security.takeover.unique.ips": "Unique IP Addresses", "admin.security.takeover.login.count": "Login <PERSON>", "admin.security.takeover.ip.addresses": "IP Addresses", "admin.security.takeover.empty": "No account takeover attempts detected", "// MFA Bypass": "", "admin.security.mfa.bypass.title": "MFA Bypass Attempts", "admin.security.mfa.bypass.subtitle": "Monitor attempts to circumvent multi-factor authentication", "admin.security.mfa.bypass.user.id": "User ID", "admin.security.mfa.bypass.ip.address": "IP Address", "admin.security.mfa.bypass.failure.count": "MFA Failure Count", "admin.security.mfa.bypass.success.after": "Successful Login After Failures", "admin.security.mfa.bypass.empty": "No MFA bypass attempts detected", "// Admin Activity": "", "admin.security.admin.activity.title": "Unusual Admin Activity", "admin.security.admin.activity.subtitle": "Monitor high-volume administrative actions", "admin.security.admin.activity.admin.user": "Admin User", "admin.security.admin.activity.total.actions": "Total Actions", "admin.security.admin.activity.unique.actions": "Unique Action Types", "admin.security.admin.activity.action.breakdown": "Action Breakdown", "admin.security.admin.activity.empty": "No unusual admin activity detected", "// IP Reputation": "", "admin.security.ip.reputation.title": "IP Reputation", "admin.security.ip.reputation.subtitle": "Analyze IP address reputation and activity", "admin.security.ip.reputation.score": "Reputation Score", "admin.security.ip.reputation.level": "Reputation Level", "admin.security.ip.reputation.total.activities": "Total Activities", "admin.security.ip.reputation.failed.logins": "Failed <PERSON><PERSON>", "admin.security.ip.reputation.successful.logins": "Successful Logins", "admin.security.ip.reputation.last.activity": "Last Activity", "admin.security.ip.reputation.activity.summary": "Activity Summary", "admin.security.ip.reputation.lookup": "Lookup IP Address", "admin.security.ip.reputation.enter.ip": "Enter IP address to analyze", "// Reputation Levels": "", "admin.security.reputation.trusted": "Trusted", "admin.security.reputation.good": "Good", "admin.security.reputation.neutral": "Neutral", "admin.security.reputation.suspicious": "Suspicious", "admin.security.reputation.malicious": "Malicious", "// Time Windows": "", "admin.security.time.window.30m": "Last 30 minutes", "admin.security.time.window.1h": "Last hour", "admin.security.time.window.6h": "Last 6 hours", "admin.security.time.window.24h": "Last 24 hours", "admin.security.time.window.7d": "Last 7 days", "admin.security.time.window.custom": "Custom", "// Actions": "", "admin.security.action.block.ip": "Block IP", "admin.security.action.investigate": "Investigate", "admin.security.action.mark.resolved": "<PERSON>solved", "admin.security.action.export": "Export Data", "admin.security.action.view.details": "View Details", "admin.security.action.dismiss": "<PERSON><PERSON><PERSON>", "// Alerts": "", "admin.security.alert.new.threat": "New security threat detected", "admin.security.alert.critical.threat": "Critical security threat requires immediate attention", "admin.security.alert.multiple.threats": "Multiple security threats detected", "admin.security.alert.system.healthy": "Security system is operating normally", "// Messages": "", "admin.security.message.data.loaded": "Security data loaded successfully", "admin.security.message.data.error": "Failed to load security data", "admin.security.message.ip.blocked": "IP address blocked successfully", "admin.security.message.threat.resolved": "Threat marked as resolved", "admin.security.message.export.success": "Security data exported successfully", "admin.security.message.export.error": "Failed to export security data", "// Errors": "", "admin.security.error.network": "Network error. Please check your connection.", "admin.security.error.unauthorized": "You are not authorized to view security data", "admin.security.error.invalid.ip": "Invalid IP address format", "admin.security.error.data.unavailable": "Security data is currently unavailable", "admin.security.error.export.failed": "Failed to export security data", "// Common": "", "admin.security.common.severity": "Severity", "admin.security.common.timestamp": "Timestamp", "admin.security.common.source": "Source", "admin.security.common.target": "Target", "admin.security.common.details": "Details", "admin.security.common.actions": "Actions", "admin.security.common.no.data": "No data available", "admin.security.common.loading": "Loading security data...", "admin.security.common.refresh.data": "Refresh Data", "admin.security.common.auto.refresh": "Auto Refresh"}