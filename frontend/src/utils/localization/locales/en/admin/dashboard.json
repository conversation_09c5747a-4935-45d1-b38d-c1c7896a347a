{"// Admin Dashboard": "", "admin.dashboard.title": "GenieAdmin Dashboard", "admin.dashboard.subtitle": "System administration and monitoring for GeNieGO SSO", "admin.dashboard.refresh": "Refresh", "admin.dashboard.systemSettings": "System Settings", "// Tabs": "", "admin.dashboard.tabs.overview": "Overview", "admin.dashboard.tabs.analytics": "Analytics", "admin.dashboard.tabs.users": "Users", "admin.dashboard.tabs.applications": "Applications", "// Quick Stats": "", "admin.dashboard.stats.totalUsers": "Total Users", "admin.dashboard.stats.activeUsers": "active users", "admin.dashboard.stats.applications": "Applications", "admin.dashboard.stats.applicationsActive": "active", "admin.dashboard.stats.todaysLogins": "Today's <PERSON><PERSON>", "admin.dashboard.stats.authRequests": "Authentication requests", "admin.dashboard.stats.pendingApprovals": "Pending Approvals", "admin.dashboard.stats.awaitingReview": "awaiting review", "// System Health": "", "admin.dashboard.health.title": "System Health", "admin.dashboard.health.healthy": "Healthy", "admin.dashboard.health.warning": "Warning", "admin.dashboard.health.critical": "Critical", "admin.dashboard.health.lastUpdated": "Last updated", "// Quick Actions": "", "admin.dashboard.quickActions.userManagement.title": "User Management", "admin.dashboard.quickActions.userManagement.description": "Manage user accounts and permissions", "admin.dashboard.quickActions.userManagement.button": "Manage Users", "admin.dashboard.quickActions.applicationManagement.title": "Application Management", "admin.dashboard.quickActions.applicationManagement.description": "Monitor and approve applications", "admin.dashboard.quickActions.applicationManagement.button": "Manage Applications", "admin.dashboard.quickActions.systemAnalytics.title": "System Analytics", "admin.dashboard.quickActions.systemAnalytics.description": "View detailed system metrics", "admin.dashboard.quickActions.systemAnalytics.button": "View Analytics", "// Users Tab": "", "admin.dashboard.users.title": "User Management", "admin.dashboard.users.viewAllUsers": "View All Users", "admin.dashboard.users.description": "Manage user accounts and permissions", "admin.dashboard.users.noUsersFound": "No Users Found", "admin.dashboard.users.noUsersFiltered": "Try adjusting your search criteria or filters", "admin.dashboard.users.noUsersRegistered": "No users have been registered yet", "admin.dashboard.users.unknownUser": "Unknown User", "admin.dashboard.users.noEmail": "No email", "admin.dashboard.users.role": "Role", "admin.dashboard.users.joined": "Joined", "admin.dashboard.users.unknown": "Unknown", "admin.dashboard.users.active": "Active", "admin.dashboard.users.inactive": "Inactive", "admin.dashboard.users.verified": "Verified", "admin.dashboard.users.unverified": "Unverified", "admin.dashboard.users.viewAllCount": "View All {count} Users", "// Applications Tab": "", "admin.dashboard.applications.filterTitle": "Filter Applications", "admin.dashboard.applications.searchPlaceholder": "Search applications by name or owner...", "admin.dashboard.applications.title": "Application Management", "admin.dashboard.applications.viewAllApplications": "View All Applications", "admin.dashboard.applications.owner": "Owner", "admin.dashboard.applications.clientId": "Client ID", "admin.dashboard.applications.created": "Created", "// Analytics Tab": "", "admin.dashboard.analytics.totalUsers": "Total Users", "admin.dashboard.analytics.activeApplications": "Active Applications", "admin.dashboard.analytics.dailyLogins": "Daily Logins", "admin.dashboard.analytics.pendingApprovals": "Pending Approvals", "admin.dashboard.analytics.registeredUsers": "Registered users", "admin.dashboard.analytics.total": "total", "admin.dashboard.analytics.totalCount": "{count} total", "admin.dashboard.analytics.todaysActivity": "Today's activity", "admin.dashboard.analytics.awaitingReview": "Awaiting review", "admin.dashboard.analytics.activityTimeline": "Activity Timeline", "admin.dashboard.analytics.activityTrends": "Daily activity trends over the past week", "admin.dashboard.analytics.totalLogins": "Total Logins", "admin.dashboard.analytics.newUsers": "New Users", "admin.dashboard.analytics.applicationStatus": "Application Status", "admin.dashboard.analytics.statusDistribution": "Distribution of application statuses", "admin.dashboard.analytics.activeStatus": "Active", "admin.dashboard.analytics.pendingStatus": "Pending", "admin.dashboard.analytics.inactiveStatus": "Inactive", "admin.dashboard.analytics.topApplications": "Top Performing Applications", "admin.dashboard.analytics.topApplicationsDesc": "Applications with highest user engagement", "// Users Tab Search": "", "admin.dashboard.users.searchPlaceholder": "Search users by email or name...", "admin.dashboard.users.joinedOn": "Joined on", "admin.dashboard.analytics.quickStats": "Quick Stats", "admin.dashboard.analytics.developers": "Developers", "admin.dashboard.analytics.weekLogins": "Week Logins", "admin.dashboard.analytics.monthLogins": "<PERSON> Logins", "// Weekly Growth": "", "admin.dashboard.analytics.weeklyGrowth": "Weekly Growth", "admin.dashboard.analytics.users": "Users", "admin.dashboard.analytics.applications": "Applications", "admin.dashboard.analytics.logins": "<PERSON><PERSON>", "// System Status": "", "admin.dashboard.analytics.systemStatus": "System Status", "admin.dashboard.analytics.apiServicesOnline": "API Services Online", "admin.dashboard.analytics.databaseConnected": "Database Connected", "admin.dashboard.analytics.authenticationActive": "Authentication Active", "// Application Status Badges": "", "admin.dashboard.applications.approved": "Approved", "admin.dashboard.applications.pending": "Pending", "admin.dashboard.applications.active": "Active", "admin.dashboard.applications.inactive": "Inactive"}