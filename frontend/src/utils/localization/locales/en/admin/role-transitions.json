{"// Admin Role Transition Management": "", "admin.role.transition.title": "Role Transition Management", "admin.role.transition.subtitle": "Review and manage developer role applications", "admin.role.transition.pending.title": "Pending Applications", "admin.role.transition.pending.empty": "No pending applications", "admin.role.transition.review.title": "Review Application", "admin.role.transition.review.approve": "Approve", "admin.role.transition.review.reject": "Reject", "admin.role.transition.review.notes.label": "Admin Notes", "admin.role.transition.review.notes.placeholder": "Add notes about your decision...", "admin.role.transition.review.notes.help": "Optional: Provide feedback or reasoning for your decision.", "admin.role.transition.review.submit": "Submit Review", "admin.role.transition.review.cancel": "Cancel", "// Messages": "", "admin.role.transition.message.approved": "Application approved successfully", "admin.role.transition.message.rejected": "Application rejected", "admin.role.transition.message.review.error": "Failed to review application", "// Confirmations": "", "admin.role.transition.confirm.approve.title": "Approve Application", "admin.role.transition.confirm.approve.message": "Are you sure you want to approve this application?", "admin.role.transition.confirm.approve.note": "The user will be granted developer access immediately.", "admin.role.transition.confirm.reject.title": "Reject Application", "admin.role.transition.confirm.reject.message": "Are you sure you want to reject this application?", "admin.role.transition.confirm.reject.note": "The user will be notified of the rejection.", "// Notifications": "", "admin.role.transition.notification.new": "New developer role application submitted", "admin.role.transition.notification.reminder": "Pending developer applications require review"}