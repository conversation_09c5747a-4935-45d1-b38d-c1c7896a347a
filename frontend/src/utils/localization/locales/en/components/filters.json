{"// Generic Filter Component Labels": "", "filters.title": "Filters", "filters.apply": "Apply", "filters.clear": "Clear", "filters.reset": "Reset", "filters.resetAll": "Reset All", "filters.search": "Search", "filters.searchPlaceholder": "Search...", "filters.activeFilters": "Active filters", "// Common Filter Categories": "", "filters.status": "Status", "filters.type": "Type", "filters.category": "Category", "filters.date": "Date", "filters.dateRange": "Date Range", "filters.from": "From", "filters.to": "To", "filters.role": "Role", "filters.permissions": "Permissions", "filters.approval": "Approval", "filters.timeframe": "Timeframe", "filters.severity": "Severity", "filters.risk": "Risk Level", "filters.environment": "Environment", "filters.metrics": "Metrics", "// Common Status Values": "", "filters.all": "All", "filters.allStatus": "All Status", "filters.allTypes": "All Types", "filters.allRoles": "All Roles", "filters.allPermissions": "All Permissions", "filters.allApprovals": "All Approvals", "filters.allTime": "All Time", "filters.allSeverities": "All Severities", "filters.allRiskLevels": "All Risk Levels", "filters.allEnvironments": "All Environments", "filters.allMetrics": "All Metrics", "filters.allActivities": "All Activities", "filters.active": "Active", "filters.inactive": "Inactive", "filters.pending": "Pending", "filters.pendingReview": "Pending Review", "filters.approved": "Approved", "filters.rejected": "Rejected", "filters.suspended": "Suspended", "// User/Role Types": "", "filters.user": "User", "filters.admin": "Admin", "filters.developer": "Developer", "// Application Types": "", "filters.webApp": "Web App", "filters.webApplication": "Web Application", "filters.mobileApp": "Mobile App", "filters.apiService": "API Service", "filters.singlePageApp": "Single Page App", "// Permission Levels": "", "filters.readOnly": "Read Only", "filters.readWrite": "Read & Write", "filters.adminAccess": "Admin Access", "// Time Periods": "", "filters.today": "Today", "filters.thisWeek": "This Week", "filters.thisMonth": "This Month", "filters.thisQuarter": "This Quarter", "filters.lastHour": "Last Hour", "filters.last24Hours": "Last 24 Hours", "filters.lastWeek": "Last Week", "filters.lastMonth": "Last Month", "// Severity Levels": "", "filters.critical": "Critical", "filters.high": "High", "filters.medium": "Medium", "filters.low": "Low", "// Activity Types": "", "filters.logins": "<PERSON><PERSON>", "filters.loginActivity": "Login Activity", "filters.appAccess": "App Access", "filters.security": "Security", "filters.authentication": "Authentication", "filters.authorization": "Authorization", "filters.accessControl": "Access Control", "filters.system": "System", "filters.userActivity": "User Activity", "// Environments": "", "filters.development": "Development", "filters.staging": "Staging", "filters.production": "Production", "// Metrics": "", "filters.userMetrics": "User Metrics", "filters.performance": "Performance", "filters.errorRates": "Error Rates", "// Threat Status": "", "filters.mitigated": "Mitigated", "filters.investigating": "Investigating", "filters.resolved": "Resolved", "// Sort Options": "", "filters.sort": "Sort", "filters.sortBy": "Sort By", "filters.orderBy": "Order By", "filters.ascending": "Ascending", "filters.descending": "Descending", "// Date/Time Filters": "", "filters.created": "Created", "filters.updated": "Updated", "filters.modified": "Modified"}