{"// Main Navigation": "", "navigation.home": "Home", "navigation.returnHome": "Return to Home", "navigation.about": "About", "navigation.blog": "Blog", "navigation.contact": "Contact", "navigation.faq": "FAQ", "navigation.dashboard": "Dashboard", "navigation.languageSwitcher": "Change Language", "navigation.services": "Services", "navigation.models": "Models", "navigation.documents": "Documents", "navigation.tasks": "Tasks", "navigation.plugins": "Plugins", "navigation.help": "Help", "navigation.users": "Users", "// Main Navigation Keys": "", "nav.main.platform": "Platform", "// User Navigation": "", "nav.user.account": "Account", "nav.user.billing": "Billing", "nav.user.notifications": "Notifications", "nav.user.language": "Language", "nav.user.theme": "Theme", "nav.user.fontSize": "Font Size", "nav.user.logout": "Log out", "nav.user.theme.light": "Light", "nav.user.theme.dark": "Dark", "nav.user.theme.system": "System", "nav.user.language.english": "English", "nav.user.language.chinese": "Chinese (Simplified)", "nav.user.language.traditional": "Chinese (Traditional)", "nav.user.upgrade": "Upgrade to Pro", "nav.user.profile": "Profile", "nav.user.security": "Security", "nav.user.settings": "Settings", "nav.user.accountSettings": "Account <PERSON><PERSON>", "nav.user.backToHome": "Back to Dashboard", "nav.user.dashboard": "Dashboard", "// Admin Navigation": "", "nav.admin.dashboard": "Administration Console", "nav.admin.users": "User List", "nav.admin.applications": "Application List", "nav.admin.organizations": "Organization List", "nav.admin.developer-applications": "Developer Applications", "nav.admin.settings": "System Settings", "nav.admin.security": "Security Settings", "// Developer Navigation": "", "nav.developer.dashboard": "Developer Dashboard", "nav.developer.applications": "Applications", "nav.developer.organizations": "Organizations", "nav.developer.register": "Register Application", "nav.developer.api.docs": "API Documentation", "nav.developer.settings": "Developer Settings", "// App Sidebar Navigation": "", "nav.app.userPortal": "Account", "nav.app.adminConsole": "Administration Console", "nav.app.developerConsole": "Developer Console", "nav.app.userManagement": "User", "nav.app.applicationsManagement": "Application", "nav.app.organizationManagement": "Organization", "nav.app.systemAdmin": "System Administration", "nav.app.account": "Account", "nav.app.development": "Development", "nav.app.organizations": "Organizations", "nav.app.toolsResources": "Tools & Resources", "nav.app.company": "GeNieGO", "nav.app.plan": "Enterprise", "nav.app.administration": "Administration", "nav.app.identityManagement": "Identity Management", "nav.app.systemConfiguration": "System Configuration", "nav.app.developerTools": "Developer Tools", "nav.app.resourcesSupport": "Resources & Support", "// User Sidebar Navigation": "", "nav.user.accountProfile": "Account & Profile", "nav.user.accountHome": "Account Home", "nav.user.personalInfo": "Personal Information", "nav.user.roleUpgrade": "Developer Access", "nav.user.securityPrivacy": "Security & Privacy", "nav.user.securityOverview": "Security Overview", "nav.user.passwordManagement": "Password Management", "nav.user.twoFactorAuth": "Two-Factor Authentication", "nav.user.deviceManagement": "Device Management", "nav.user.privacyControls": "Privacy Controls", "nav.user.applicationsData": "Applications & Data", "nav.user.connectedApps": "Connected Applications", "nav.user.invitations": "Invitations", "nav.user.organizations": "My Organizations", "nav.user.overview": "User Overview", "nav.user.permissions": "Permissions", "nav.user.activityHistory": "Activity History", "nav.user.dataExport": "Data Export", "nav.user.consentHistory": "Consent History", "nav.user.sessionManagement": "Session Management", "nav.user.preferences": "Preferences", "nav.user.backToDashboard": "Back to Dashboard", "// Projects": "", "nav.projects.title": "Projects", "nav.projects.more": "More", "nav.projects.view": "View Project", "nav.projects.share": "Share Project", "nav.projects.delete": "Delete Project", "nav.projects.design": "Design", "// Teams": "", "nav.teams.title": "Teams", "nav.teams.add": "Add team", "// Footer": "", "footer.company": "Company", "footer.support": "Support", "footer.legal": "Legal", "footer.copyright": "All rights reserved.", "footer.description": "Providing innovative solutions to help businesses succeed in a digital world.", "footer.organization.name": "GeNieGO", "footer.organization.description": "The leading AI technology ecosystem provider.", "// Social Media": "", "social.twitter": "Twitter", "social.facebook": "Facebook", "social.linkedin": "LinkedIn", "social.instagram": "Instagram", "social.github": "GitHub", "social.youtube": "YouTube", "social.follow.us": "Follow us on", "social.connect": "Connect with us", "social.stay.updated": "Stay updated"}