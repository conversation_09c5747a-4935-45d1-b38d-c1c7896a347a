{"// Dashboard Page": "", "user.dashboard.title": "My Account", "user.dashboard.subtitle": "Manage your connected applications and privacy settings", "user.dashboard.refresh": "Refresh", "user.dashboard.settings": "Settings", "// Tabs": "", "user.dashboard.tabs.overview": "Overview", "user.dashboard.tabs.applications": "Applications", "user.dashboard.tabs.activity": "Activity", "user.dashboard.tabs.permissions": "Permissions", "// Quick Stats Cards": "", "user.dashboard.stats.connectedApps": "Connected Apps", "user.dashboard.stats.connectedApps.description": "Applications with access", "user.dashboard.stats.activeSessions": "Active Sessions", "user.dashboard.stats.activeSessions.description": "Current login sessions", "user.dashboard.stats.recentActivity": "Recent Activity", "user.dashboard.stats.recentActivity.description": "Actions this week", "user.dashboard.stats.securityStatus": "Security Status", "user.dashboard.stats.securityStatus.secure": "Secure", "user.dashboard.stats.securityStatus.alerts": "alerts", "// Quick Actions": "", "user.dashboard.quickActions.connectedApps.title": "Connected Applications", "user.dashboard.quickActions.connectedApps.description": "Manage apps that have access to your account", "user.dashboard.quickActions.connectedApps.button": "Manage", "user.dashboard.quickActions.privacy.title": "Privacy & Permissions", "user.dashboard.quickActions.privacy.description": "Control what data apps can access", "user.dashboard.quickActions.privacy.button": "View", "user.dashboard.quickActions.activity.title": "Activity History", "user.dashboard.quickActions.activity.description": "View your account activity and login history", "user.dashboard.quickActions.activity.button": "View", "user.dashboard.quickActions.security.title": "Security Settings", "user.dashboard.quickActions.security.description": "Manage your account security and privacy", "user.dashboard.quickActions.security.button": "View", "// Recent Activity Section": "", "user.dashboard.recentApps.title": "Recently Connected Apps", "user.dashboard.recentApps.description": "Applications you've recently granted access to", "user.dashboard.recentApps.empty": "No connected applications yet", "user.dashboard.recentApps.viewAll": "View All Applications", "user.dashboard.recentApps.active": "Active", "user.dashboard.recentApps.inactive": "Inactive", "user.dashboard.recentApps.connected": "Connected", "// Recent Activity": "", "user.dashboard.recentActivity.title": "Recent Activity", "user.dashboard.recentActivity.description": "Your recent account activity", "user.dashboard.recentActivity.empty": "No recent activity", "user.dashboard.recentActivity.viewFull": "View Full Activity", "user.dashboard.recentActivity.success": "Success", "user.dashboard.recentActivity.failed": "Failed", "// Applications Tab": "", "user.dashboard.applications.title": "Connected Applications", "user.dashboard.applications.description": "Manage applications that have access to your account", "user.dashboard.applications.empty": "No applications connected", "user.dashboard.applications.empty.description": "You haven't connected any applications to your account yet.", "// Activity Tab": "", "user.dashboard.activity.title": "Account Activity", "user.dashboard.activity.description": "View your recent account activity and login history", "user.dashboard.activity.empty": "No activity found", "user.dashboard.activity.empty.description": "No activity matches your current filters.", "// Permissions Tab": "", "user.dashboard.permissions.title": "Security & Privacy", "user.dashboard.permissions.description": "Manage your account security and privacy settings", "user.dashboard.permissions.button": "Security Settings", "user.dashboard.permissions.heading": "Security Settings", "user.dashboard.permissions.subtext": "Configure your security and privacy preferences", "// Organization Invitations Widget": "", "user.dashboard.invitations.title": "Organization Invitations", "user.dashboard.invitations.loading": "Loading...", "user.dashboard.invitations.error": "Failed to load invitations", "user.dashboard.invitations.noPending": "No pending invitations", "user.dashboard.invitations.pendingCount.one": "Pending invitation", "user.dashboard.invitations.pendingCount.other": "Pending invitations", "user.dashboard.invitations.expired": "Expired", "user.dashboard.invitations.manageButton": "Manage Invitations", "// Filter Panels": "", "user.dashboard.filters.applications.title": "Filter Applications", "user.dashboard.filters.applications.search": "Search applications by name...", "user.dashboard.filters.applications.reset": "Reset Application Filters", "user.dashboard.filters.activity.title": "Filter Activity", "user.dashboard.filters.activity.search": "Search activity...", "user.dashboard.filters.activity.reset": "Reset Activity Filters", "// Filter Categories": "", "user.dashboard.filters.applications.status": "Application Status", "user.dashboard.filters.applications.type": "Application Type", "user.dashboard.filters.activity.type": "Activity Type", "user.dashboard.filters.activity.status": "Activity Status", "user.dashboard.filters.activity.date": "Activity Date", "// Applications Tab Content": "", "user.dashboard.applications.manage": "Manage", "user.dashboard.applications.active": "Active", "user.dashboard.applications.inactive": "Inactive", "user.dashboard.applications.connected": "Connected", "user.dashboard.applications.viewAll": "View All {count} Applications", "user.dashboard.applications.manageApplications": "Manage Applications", "user.dashboard.applications.noAppsFound": "No Applications Found", "user.dashboard.applications.noAppsFiltered": "No applications match your current filters", "user.dashboard.applications.noAppsConnected": "No applications connected to your account yet", "user.dashboard.applications.totalCount": "Manage applications that have access to your account ({count} total)", "// Activity Tab Content": "", "user.dashboard.activity.success": "Success", "user.dashboard.activity.failed": "Failed", "user.dashboard.activity.viewAll": "View All {count} Activities", "user.dashboard.activity.viewHistory": "View Activity History", "user.dashboard.activity.noActivityFound": "No Activity Found", "user.dashboard.activity.noActivityFiltered": "No activity matches your current filters", "user.dashboard.activity.noRecentActivity": "No recent activity on your account", "user.dashboard.activity.totalCount": "View your complete account activity and login history ({count} total)"}