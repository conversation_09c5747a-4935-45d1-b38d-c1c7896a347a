{"// User Sharing Page": "", "user.sharing.title": "Sharing & Collaboration", "user.sharing.subtitle": "Control data sharing with other users and services", "user.sharing.refresh": "Refresh", "user.sharing.save": "Save Changes", "user.sharing.saving": "Saving...", "// Error States": "", "user.sharing.error.loadFailed": "Failed to load sharing settings. Please try again.", "user.sharing.error.saveFailed": "Failed to save sharing settings. Please try again.", "user.sharing.success.saved": "Sharing settings updated successfully!", "// Profile Sharing": "", "user.sharing.profile.title": "Profile Sharing", "user.sharing.profile.description": "Control who can see your profile information", "user.sharing.profile.visibility": "Profile Sharing", "user.sharing.profile.visibility.public": "Public", "user.sharing.profile.visibility.friends": "Friends Only", "user.sharing.profile.visibility.private": "Private", "user.sharing.profile.activity": "Activity Sharing", "user.sharing.profile.activity.public": "Public", "user.sharing.profile.activity.friends": "Friends Only", "user.sharing.profile.activity.private": "Private", "user.sharing.profile.email": "Email <PERSON>ng", "user.sharing.profile.email.public": "Public", "user.sharing.profile.email.friends": "Friends Only", "user.sharing.profile.email.private": "Private", "// Social Features": "", "user.sharing.social.title": "Social Features", "user.sharing.social.description": "Control social interactions and collaboration", "user.sharing.social.friendRequests": "Allow Friend Requests", "user.sharing.social.friendRequests.description": "Let other users send you friend requests", "user.sharing.social.onlineStatus": "Show Online Status", "user.sharing.social.onlineStatus.description": "Let others see when you're online", "user.sharing.social.collaboration": "Allow Collaboration", "user.sharing.social.collaboration.description": "Enable collaborative features with other users", "// Sharing Rules": "", "user.sharing.rules.title": "Sharing Rules", "user.sharing.rules.description": "Manage custom sharing rules for specific data or features", "user.sharing.rules.addRule": "Add Rule", "user.sharing.rules.noRules": "No Sharing Rules", "user.sharing.rules.noRules.description": "You haven't created any custom sharing rules yet", "user.sharing.rules.createFirst": "Create your first sharing rule to get started", "user.sharing.rules.name": "Rule Name", "user.sharing.rules.type": "Rule Type", "user.sharing.rules.permissions": "Permissions", "user.sharing.rules.createdAt": "Created", "user.sharing.rules.actions": "Actions", "user.sharing.rules.edit": "Edit", "user.sharing.rules.delete": "Delete", "// Data Sharing": "", "user.sharing.data.title": "Data Sharing", "user.sharing.data.description": "Control how your data is shared with external services", "user.sharing.data.analytics": "Analytics Data", "user.sharing.data.analytics.description": "Share anonymous usage data to help improve the service", "user.sharing.data.thirdParty": "Third-Party Services", "user.sharing.data.thirdParty.description": "Allow sharing data with integrated third-party services", "user.sharing.data.marketing": "Marketing Data", "user.sharing.data.marketing.description": "Use your data for personalized marketing", "// Collaboration Settings": "", "user.sharing.collaboration.title": "Collaboration Settings", "user.sharing.collaboration.description": "Configure how you collaborate with others", "user.sharing.collaboration.defaultPermissions": "Default Permissions", "user.sharing.collaboration.defaultPermissions.view": "View Only", "user.sharing.collaboration.defaultPermissions.edit": "Edit", "user.sharing.collaboration.defaultPermissions.admin": "Admin", "user.sharing.collaboration.autoAccept": "Auto-Accept Invitations", "user.sharing.collaboration.autoAccept.description": "Automatically accept collaboration invitations from trusted users", "user.sharing.collaboration.notifications": "Collaboration Notifications", "user.sharing.collaboration.notifications.description": "Receive notifications about collaboration activities", "// Sharing Tips": "", "user.sharing.tips.title": "Sharing Tips", "user.sharing.tips.description": "Best practices for safe sharing", "user.sharing.tips.list.1": "Review your sharing settings regularly", "user.sharing.tips.list.2": "Be selective about what information you share publicly", "user.sharing.tips.list.3": "Use custom sharing rules for sensitive data", "user.sharing.tips.list.4": "Monitor collaboration activities and permissions", "user.sharing.tips.list.5": "Remove access for users you no longer trust", "// Toast Messages": "", "user.sharing.toast.loadFailed.description": "Failed to load sharing settings. Please try again.", "user.sharing.toast.saveSuccess.title": "Settings Saved", "user.sharing.toast.saveSuccess.description": "Your sharing preferences have been updated successfully.", "user.sharing.toast.saveFailed.description": "Failed to save sharing settings. Please try again.", "user.sharing.toast.ruleCreated.title": "Rule Created", "user.sharing.toast.ruleCreated.description": "New sharing rule has been created successfully.", "user.sharing.toast.createRuleFailed.description": "Failed to create sharing rule. Please try again.", "user.sharing.toast.ruleDeleted.title": "Rule Deleted", "user.sharing.toast.ruleDeleted.description": "Sharing rule has been deleted successfully.", "user.sharing.toast.deleteRuleFailed.description": "Failed to delete sharing rule. Please try again."}