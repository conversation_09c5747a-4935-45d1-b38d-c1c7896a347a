{"// User Home": "", "user.home.title": "Account Home", "user.home.subtitle": "Welcome back{name}! Manage your account and security settings.", "user.home.subtitleNoName": "Welcome back! Manage your account and security settings.", "user.home.refresh": "Refresh", "user.home.settings": "Settings", "// Security Recommendations": "", "user.home.security.recommendations": "Security Recommendations", "// Stats Cards": "", "user.home.stats.connectedApps": "Connected Apps", "user.home.stats.connectedApps.description": "Applications with access", "user.home.stats.activeSessions": "Active Sessions", "user.home.stats.activeSessions.description": "<PERSON>ces signed in", "user.home.stats.recentActivity": "Recent Activity", "user.home.stats.recentActivity.description": "Actions this week", "user.home.stats.securityStatus": "Security Status", "user.home.stats.securityStatus.good": "Good", "user.home.stats.securityStatus.noIssues": "No issues found", "user.home.stats.securityStatus.issuesReview": "Issues to review", "// Quick Actions": "", "user.home.quickActions.title": "Quick Actions", "user.home.quickActions.personalInfo.title": "Personal Information", "user.home.quickActions.personalInfo.description": "Update your profile, contact info, and preferences", "user.home.quickActions.security.title": "Security Settings", "user.home.quickActions.security.description": "Manage passwords, 2FA, and security preferences", "user.home.quickActions.applications.title": "Connected Applications", "user.home.quickActions.applications.description": "View and manage apps connected to your account", "user.home.quickActions.privacy.title": "Privacy Controls", "user.home.quickActions.privacy.description": "Control your data sharing and privacy settings", "user.home.quickActions.view": "View", "user.home.quickActions.goTo": "Go to {title}", "// Account Summary": "", "user.home.accountSummary.title": "Account Summary", "user.home.accountSummary.description": "Your account information and status", "user.home.accountSummary.profileInfo": "Profile Information", "user.home.accountSummary.securityInfo": "Security Information", "user.home.accountSummary.name": "Name:", "user.home.accountSummary.email": "Email:", "user.home.accountSummary.username": "Username:", "user.home.accountSummary.memberSince": "Member since:", "user.home.accountSummary.lastLogin": "Last login:", "user.home.accountSummary.lastLoginNever": "Never", "user.home.accountSummary.twoFactorStatus": "2FA Status:", "user.home.accountSummary.accountStatus": "Account Status:", "user.home.accountSummary.enabled": "Enabled", "user.home.accountSummary.disabled": "Disabled", "user.home.accountSummary.active": "Active", "user.home.accountSummary.inactive": "Inactive"}