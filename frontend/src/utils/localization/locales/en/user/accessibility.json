{"// User Accessibility Page": "", "user.accessibility.title": "Accessibility Options", "user.accessibility.subtitle": "Customize the interface to meet your accessibility needs", "user.accessibility.refresh": "Refresh", "user.accessibility.save": "Save Changes", "user.accessibility.saving": "Saving...", "// Error States": "", "user.accessibility.error.loadFailed": "Failed to load accessibility settings. Please try refreshing the page.", "user.accessibility.error.saveFailed": "Failed to save accessibility settings. Please try again.", "user.accessibility.success.saved": "Accessibility settings updated successfully!", "// Visual Settings": "", "user.accessibility.visual.title": "Visual Settings", "user.accessibility.visual.description": "Adjust visual elements for better readability", "user.accessibility.visual.highContrast": "High Contrast Mode", "user.accessibility.visual.highContrast.description": "Increase contrast for better visibility", "user.accessibility.visual.fontSize": "Font Size", "user.accessibility.visual.fontSize.description": "Adjust text size throughout the interface", "user.accessibility.visual.fontSize.small": "Small", "user.accessibility.visual.fontSize.medium": "Medium", "user.accessibility.visual.fontSize.large": "Large", "user.accessibility.visual.fontSize.extraLarge": "Extra Large", "user.accessibility.visual.colorBlindness": "Color Blindness Support", "user.accessibility.visual.colorBlindness.description": "Adjust colors for color vision deficiency", "user.accessibility.visual.colorBlindness.none": "None", "user.accessibility.visual.colorBlindness.protanopia": "<PERSON><PERSON><PERSON> (Red-blind)", "user.accessibility.visual.colorBlindness.deuteranopia": "<PERSON><PERSON><PERSON><PERSON> (Green-blind)", "user.accessibility.visual.colorBlindness.tritanopia": "<PERSON><PERSON><PERSON> (Blue-blind)", "// Audio Settings": "", "user.accessibility.audio.title": "Audio Settings", "user.accessibility.audio.description": "Configure audio feedback and alerts", "user.accessibility.audio.soundEffects": "Sound Effects", "user.accessibility.audio.soundEffects.description": "Play sounds for interface interactions", "user.accessibility.audio.screenReader": "Screen Reader Support", "user.accessibility.audio.screenReader.description": "Optimize interface for screen readers", "user.accessibility.audio.audioDescriptions": "Audio Descriptions", "user.accessibility.audio.audioDescriptions.description": "Enable audio descriptions for visual content", "// Motor Settings": "", "user.accessibility.motor.title": "Motor & Navigation", "user.accessibility.motor.description": "Adjust controls for easier navigation", "user.accessibility.motor.keyboardNavigation": "Enhanced Keyboard Navigation", "user.accessibility.motor.keyboardNavigation.description": "Improve keyboard-only navigation", "user.accessibility.motor.clickDelay": "<PERSON><PERSON>", "user.accessibility.motor.clickDelay.description": "Add delay to prevent accidental clicks", "user.accessibility.motor.clickDelay.none": "None", "user.accessibility.motor.clickDelay.short": "Short (0.5s)", "user.accessibility.motor.clickDelay.medium": "Medium (1s)", "user.accessibility.motor.clickDelay.long": "<PERSON> (2s)", "user.accessibility.motor.stickyKeys": "<PERSON><PERSON>", "user.accessibility.motor.stickyKeys.description": "Allow modifier keys to remain active", "// Cognitive Settings": "", "user.accessibility.cognitive.title": "Cognitive Support", "user.accessibility.cognitive.description": "Features to assist with cognitive accessibility", "user.accessibility.cognitive.reducedMotion": "Reduced Motion", "user.accessibility.cognitive.reducedMotion.description": "Minimize animations and transitions", "user.accessibility.cognitive.simplifiedInterface": "Simplified Interface", "user.accessibility.cognitive.simplifiedInterface.description": "Show fewer elements at once", "user.accessibility.cognitive.readingGuide": "Reading Guide", "user.accessibility.cognitive.readingGuide.description": "Highlight current line while reading", "// Accessibility Info": "", "user.accessibility.info.title": "Accessibility Commitment", "user.accessibility.info.description": "We're committed to making our platform accessible to everyone. If you encounter any accessibility barriers or need additional accommodations, please contact our support team for assistance."}