{"// User Two-Factor Authentication Page": "", "user.twofa.title": "Two-Factor Authentication", "user.twofa.subtitle": "Secure your account with an additional layer of protection", "// Status Section": "", "user.twofa.status.enabled": "Enabled", "user.twofa.status.disabled": "Disabled", "user.twofa.status.active": "Active", "user.twofa.status.never": "Never", "user.twofa.status.description.enabled": "2FA is active.", "user.twofa.status.description.disabled": "2FA is not enabled.", "// Setup Section": "", "user.twofa.setup.enable.title": "Enable Two-Factor Authentication", "user.twofa.setup.enable.description": "Use an authenticator app to generate verification codes", "user.twofa.setup.description": "Two-factor authentication adds an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy.", "user.twofa.setup.qr.title": "Scan QR Code", "user.twofa.setup.qr.description": "Use your authenticator app to scan this QR code", "user.twofa.setup.qr.manualHelp": "If you can't scan the QR code, enter this key manually in your authenticator app.", "user.twofa.setup.verify.title": "<PERSON><PERSON><PERSON> Setup", "user.twofa.setup.verify.description": "Enter the 6-digit code from your authenticator app", "user.twofa.setup.verify.help": "Enter the 6-digit code shown in your authenticator app", "user.twofa.setup.backup.title": "Save Backup Codes", "user.twofa.setup.backup.description": "Store these codes safely - you'll need them if you lose access to your authenticator app", "user.twofa.setup.complete.title": "2FA Enabled", "user.twofa.setup.complete.description": "Your account is now protected with two-factor authentication", "// Form Labels": "", "user.twofa.form.manualEntryKey": "Manual Entry Key", "user.twofa.form.verificationCode": "Verification Code", "user.twofa.form.verificationCode.placeholder": "000000", "// Backup Codes": "", "user.twofa.backup.important": "Important:", "user.twofa.backup.warning": "Save these backup codes in a secure location. Each code can only be used once.", "user.twofa.backup.label": "Backup Codes", "user.twofa.backup.filename": "backup-codes.txt", "// Status Information": "", "user.twofa.info.status": "Status", "user.twofa.info.lastUsed": "Last Used", "user.twofa.info.trustedDevices": "Trusted Devices", "user.twofa.info.devices": "devices", "// Trusted Devices": "", "user.twofa.trustedDevices.title": "Trusted Devices", "user.twofa.trustedDevices.description": "Devices that don't require 2FA for 30 days", "user.twofa.trustedDevices.none": "No trusted devices", "// Security Tips": "", "user.twofa.tips.title": "Security Tips", "user.twofa.tips.authenticator": "Use a dedicated authenticator app like Google Authenticator or Authy", "user.twofa.tips.backup": "Store backup codes in a secure location", "user.twofa.tips.share": "Don't share your verification codes with anyone", "user.twofa.tips.review": "Regularly review your trusted devices", "// Actions": "", "user.twofa.enable": "Enable 2FA", "user.twofa.updating": "Setting up...", "user.twofa.back": "Back", "user.twofa.continueToVerification": "Continue to Verification", "user.twofa.verifying": "Verifying...", "user.twofa.verifyCode": "Verify Code", "user.twofa.download": "Download", "user.twofa.copy": "Copy", "user.twofa.completingSetup": "Completing Setup...", "user.twofa.complete": "Complete Setup", "user.twofa.viewCodes": "View Backup Codes", "user.twofa.hideCodes": "Hide Backup Codes", "user.twofa.disabling": "Disabling...", "user.twofa.disable": "Disable 2FA", "// Prompts": "", "user.twofa.prompt.disablePassword": "Please enter your password to disable 2FA:", "// Toast Messages": "", "user.twofa.toast.enabled.title": "2FA Enabled", "user.twofa.toast.enabled.description": "Two-factor authentication has been successfully enabled.", "user.twofa.toast.setupFailed.title": "Setup Failed", "user.twofa.toast.setupFailed.description": "Failed to enable 2FA. Please try again.", "user.twofa.toast.invalidCode.title": "Invalid Code", "user.twofa.toast.invalidCode.description": "Please enter a valid 6-digit verification code.", "user.twofa.toast.codeVerified.title": "Code Verified", "user.twofa.toast.codeVerified.description": "Your authenticator app has been successfully configured.", "user.twofa.toast.verificationFailed.title": "Verification Failed", "user.twofa.toast.verificationFailed.description": "Invalid verification code. Please try again.", "user.twofa.toast.setupFailedComplete.title": "Setup Failed", "user.twofa.toast.setupFailedComplete.description": "Failed to complete 2FA setup. Please try again.", "user.twofa.toast.disabled.title": "2FA Disabled", "user.twofa.toast.disabled.description": "Two-factor authentication has been disabled for your account.", "user.twofa.toast.disableFailed.title": "Disable Failed", "user.twofa.toast.disableFailed.description": "Failed to disable 2<PERSON>. Please check your password and try again.", "user.twofa.toast.deviceRemoved.title": "<PERSON><PERSON> Removed", "user.twofa.toast.deviceRemoved.description": "The trusted device has been removed from your account.", "user.twofa.toast.removeFailed.title": "Remove Failed", "user.twofa.toast.removeFailed.description": "Failed to remove device. Please try again.", "user.twofa.toast.copied.title": "<PERSON>pied", "user.twofa.toast.copied.description": "Text copied to clipboard.", "user.twofa.toast.downloaded.title": "Downloaded", "user.twofa.toast.downloaded.description": "Backup codes have been downloaded.", "// Additional Setup Fields": "", "user.twofa.setup.device.name": "Device Name", "user.twofa.setup.device.name.placeholder": "Enter a name for this device", "user.twofa.setup.device.name.help": "Choose a name to help you identify this device", "user.twofa.setup.manual.title": "Manual Entry", "user.twofa.setup.manual.description": "If you can't scan the QR code, enter this key manually:", "user.twofa.setup.manual.key": "Manual Entry Key", "user.twofa.setup.verify.code": "Verification Code", "user.twofa.setup.verify.code.placeholder": "000000", "user.twofa.setup.backup.warning": "Each backup code can only be used once. Store them securely.", "user.twofa.setup.backup.download": "Download Codes", "user.twofa.setup.backup.copy": "Copy Codes", "user.twofa.setup.complete": "Complete Setup", "// MFA Verification (Login)": "", "user.twofa.verify.title": "Multi-Factor Authentication", "user.twofa.verify.subtitle": "Enter your authentication code", "user.twofa.verify.description": "Open your authenticator app and enter the 6-digit code", "user.twofa.verify.code": "Authentication Code", "user.twofa.verify.code.placeholder": "000000", "user.twofa.verify.submit": "Verify", "user.twofa.verify.backup.link": "Use backup code instead", "user.twofa.verify.backup.title": "Use Backup Code", "user.twofa.verify.backup.description": "Enter one of your backup codes", "user.twofa.verify.backup.code": "Backup Code", "user.twofa.verify.backup.code.placeholder": "Enter backup code", "user.twofa.verify.backup.submit": "Verify Backup Code", "user.twofa.verify.totp.link": "Use authenticator app instead", "// MFA Management": "", "user.twofa.manage.title": "Multi-Factor Authentication", "user.twofa.manage.subtitle": "Manage your MFA devices and settings", "user.twofa.manage.status.enabled": "Enabled", "user.twofa.manage.status.disabled": "Disabled", "user.twofa.manage.enable": "Enable MFA", "user.twofa.manage.disable": "Disable MFA", "user.twofa.manage.devices.title": "MFA Devices", "user.twofa.manage.devices.empty": "No MFA devices configured", "user.twofa.manage.devices.add": "Add <PERSON>", "user.twofa.manage.device.name": "Device Name", "user.twofa.manage.device.type": "Type", "user.twofa.manage.device.created": "Created", "user.twofa.manage.device.last.used": "Last Used", "user.twofa.manage.device.status": "Status", "user.twofa.manage.device.active": "Active", "user.twofa.manage.device.inactive": "Inactive", "user.twofa.manage.device.verified": "Verified", "user.twofa.manage.device.unverified": "Unverified", "user.twofa.manage.device.remove": "Remove <PERSON>ce", "user.twofa.manage.device.remove.confirm": "Are you sure you want to remove this device?", "user.twofa.manage.device.remove.warning": "This action cannot be undone.", "user.twofa.manage.backup.codes": "Backup Codes", "user.twofa.manage.backup.remaining": "codes remaining", "user.twofa.manage.backup.regenerate": "Regenerate", "user.twofa.manage.backup.view": "View Codes", "// Additional Messages": "", "user.twofa.message.setup.success": "MFA setup completed successfully", "user.twofa.message.setup.error": "Failed to set up MFA. Please try again.", "user.twofa.message.verify.success": "Code verified successfully", "user.twofa.message.verify.error": "Invalid code. Please try again.", "user.twofa.message.device.removed": "Device removed successfully", "user.twofa.message.device.remove.error": "Failed to remove device", "user.twofa.message.backup.copied": "Backup codes copied to clipboard", "user.twofa.message.backup.download": "Backup codes downloaded", "// Additional Errors": "", "user.twofa.error.invalid.code": "Invalid authentication code", "user.twofa.error.expired.code": "Authentication code has expired", "user.twofa.error.device.name.required": "Device name is required", "user.twofa.error.device.name.exists": "A device with this name already exists", "user.twofa.error.setup.failed": "Failed to set up MFA device", "user.twofa.error.verification.failed": "Verification failed", "user.twofa.error.network": "Network error. Please check your connection.", "// Types": "", "user.twofa.type.totp": "Authenticator App", "user.twofa.type.sms": "SMS", "user.twofa.type.email": "Email", "// Management": "", "// Messages": ""}