{"// User Activity Page": "", "user.activity.title": "Activity History", "user.activity.subtitle": "View your account activity and login history", "user.activity.refresh": "Refresh", "// Error States": "", "user.activity.error.title": "Error Loading Activity", "user.activity.error.tryAgain": "Try Again", "user.activity.noData.title": "No Activity Found", "user.activity.noData.description": "No activity records found for your account", "// Summary Cards": "", "user.activity.stats.totalActivity": "Total Activity", "user.activity.stats.totalActivity.description": "All recorded actions", "user.activity.stats.recentLogins": "Recent Logins", "user.activity.stats.recentLogins.description": "Login sessions this week", "user.activity.stats.appAccess": "App Access", "user.activity.stats.appAccess.description": "Application interactions", "user.activity.stats.securityEvents": "Security Events", "user.activity.stats.securityEvents.description": "Security-related activities", "// Activity Table": "", "user.activity.table.timestamp": "Timestamp", "user.activity.table.activity": "Activity", "user.activity.table.type": "Type", "user.activity.table.location": "Location", "user.activity.table.device": "<PERSON><PERSON>", "user.activity.table.ipAddress": "IP Address", "user.activity.table.status": "Status", "// Activity Types": "", "user.activity.types.login": "<PERSON><PERSON>", "user.activity.types.logout": "Logout", "user.activity.types.passwordChange": "Password Change", "user.activity.types.profileUpdate": "Profile Update", "user.activity.types.appAuthorization": "App Authorization", "user.activity.types.permissionGrant": "Permission Grant", "user.activity.types.securityEvent": "Security Event", "// Status Values": "", "user.activity.status.success": "Success", "user.activity.status.failed": "Failed", "user.activity.status.blocked": "Blocked", "user.activity.status.suspicious": "Suspicious", "// Filters": "", "user.activity.filters.title": "Filter Activity", "user.activity.filters.search": "Search activity...", "user.activity.filters.reset": "Reset Filters", "// Empty State": "", "user.activity.empty.title": "No Activity Records", "user.activity.empty.description": "Your activity history will appear here as you use the platform", "// Activity Log": "", "user.activity.log.title": "Activity Log", "user.activity.log.description": "Complete history of your account activity ({filtered} of {total} total)", "// Activity Info": "", "user.activity.info.app": "App", "user.activity.info.location": "Location", "user.activity.date.invalid": "Invalid Date", "// Activity Actions": "", "user.activity.actions.login": "<PERSON><PERSON>", "user.activity.actions.logout": "Logout", "user.activity.actions.oauth2Authorization": "OAuth2 Authorization", "user.activity.actions.passwordChange": "Password Change", "user.activity.actions.profileUpdate": "Profile Update", "user.activity.actions.appAuthorization": "App Authorization", "user.activity.actions.permissionGrant": "Permission Grant", "user.activity.actions.securityEvent": "Security Event", "// Quick Actions": "", "user.activity.quickActions.security.title": "Security Settings", "user.activity.quickActions.security.description": "Manage your account security and privacy", "user.activity.quickActions.security.button": "View Security Settings", "user.activity.quickActions.applications.title": "Connected Applications", "user.activity.quickActions.applications.description": "Manage applications with access to your account", "user.activity.quickActions.applications.button": "Manage Applications"}