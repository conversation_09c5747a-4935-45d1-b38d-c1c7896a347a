{"// Role Transition Application": "", "user.role.transition.title": "Developer Role Application", "user.role.transition.subtitle": "Apply for developer access to create and manage applications", "user.role.transition.description": "Developer Benefits", "user.role.transition.benefits.api.title": "API Access", "user.role.transition.benefits.api.description": "Create and manage OAuth2 applications with full API access", "user.role.transition.benefits.management.title": "Application Management", "user.role.transition.benefits.management.description": "Register applications, manage credentials, and monitor usage", "user.role.transition.current.role": "Current Role", "user.role.transition.target.role": "Requested Role", "user.role.transition.apply": "Apply for Developer Role", "user.role.transition.application.form.title": "Developer Application Form", "user.role.transition.application.form.subtitle": "Tell us why you need developer access", "// Application Form": "", "user.role.transition.form.reason.label": "Application Reason", "user.role.transition.form.reason.placeholder": "Explain why you need developer access and how you plan to use it...", "user.role.transition.form.reason.help": "Minimum 50 characters required. Be specific about your use case.", "user.role.transition.form.reason.min.help": "Minimum 50 characters required ({needed} more needed)", "user.role.transition.form.reason.min.met": "Minimum requirement met", "user.role.transition.form.technical.label": "Technical Background", "user.role.transition.form.technical.placeholder": "Describe your technical experience and background...", "user.role.transition.form.technical.help": "Optional: Share your relevant technical skills and experience.", "user.role.transition.form.intended.label": "Intended Use", "user.role.transition.form.intended.placeholder": "Describe how you plan to use developer features...", "user.role.transition.form.intended.help": "Optional: Explain your specific plans for using developer tools.", "user.role.transition.form.submit": "Submit Application", "user.role.transition.form.cancel": "Cancel", "// Application Status": "", "user.role.transition.status.pending": "Pending Review", "user.role.transition.status.approved": "Approved", "user.role.transition.status.rejected": "Rejected", "user.role.transition.status.withdrawn": "Withdrawn", "user.role.transition.status.under.review": "Under Review", "// My Applications": "", "user.role.transition.my.applications.title": "My Applications", "user.role.transition.my.applications.subtitle": "View and manage your role transition applications", "user.role.transition.my.applications.empty": "No applications submitted yet", "user.role.transition.my.applications.create": "Submit New Application", "user.role.transition.application.id": "Application ID", "user.role.transition.application.status": "Status", "user.role.transition.application.submitted": "Submitted", "user.role.transition.application.reviewed": "Reviewed", "user.role.transition.application.reviewer": "Reviewed By", "user.role.transition.application.view": "View Details", "user.role.transition.application.withdraw": "Withdraw", "// Application Details": "", "user.role.transition.details.title": "Application Details", "user.role.transition.details.reason": "Application Reason", "user.role.transition.details.technical": "Technical Background", "user.role.transition.details.intended": "Intended Use", "user.role.transition.details.admin.notes": "Admin Notes", "user.role.transition.details.submitted.at": "Submitted At", "user.role.transition.details.reviewed.at": "Reviewed At", "user.role.transition.details.reviewed.by": "Reviewed By", "// Applicant Information": "", "user.role.transition.applicant.info": "Applicant Information", "user.role.transition.applicant.name": "Name", "user.role.transition.applicant.email": "Email", "user.role.transition.applicant.current.role": "Current Role", "user.role.transition.applicant.joined": "Joined", "user.role.transition.applicant.last.login": "Last Login", "// Messages": "", "user.role.transition.message.submitted": "Application submitted successfully", "user.role.transition.message.submit.error": "Failed to submit application", "user.role.transition.message.withdrawn": "Application withdrawn successfully", "user.role.transition.message.withdraw.error": "Failed to withdraw application", "user.role.transition.message.already.developer": "You already have developer access", "user.role.transition.message.pending.exists": "You already have a pending application", "// Errors": "", "user.role.transition.error.reason.required": "Application reason is required", "user.role.transition.error.reason.min.length": "Application reason must be at least 50 characters", "user.role.transition.form.submitting": "Submitting...", "user.role.transition.error.reason.max.length": "Application reason cannot exceed 1000 characters", "user.role.transition.error.technical.max.length": "Technical background cannot exceed 1000 characters", "user.role.transition.error.intended.max.length": "Intended use cannot exceed 1000 characters", "user.role.transition.error.network": "Network error. Please check your connection.", "user.role.transition.error.unauthorized": "You are not authorized to perform this action", "user.role.transition.error.not.found": "Application not found", "// Confirmations": "", "user.role.transition.confirm.withdraw.title": "Withdraw Application", "user.role.transition.confirm.withdraw.message": "Are you sure you want to withdraw this application?", "user.role.transition.confirm.withdraw.warning": "This action cannot be undone.", "// Help": "", "user.role.transition.help.title": "Need Help?", "user.role.transition.help.requirements": "Developer Role Requirements", "user.role.transition.help.process": "Application Process", "user.role.transition.help.timeline": "Review Timeline", "user.role.transition.help.contact": "Contact Support", "user.role.transition.help.faq": "Frequently Asked Questions", "// Notifications": "", "user.role.transition.notification.submitted": "Your developer role application has been submitted for review", "user.role.transition.notification.approved": "Congratulations! Your developer role application has been approved", "user.role.transition.notification.rejected": "Your developer role application has been rejected"}