{"// User Security Overview Page": "", "user.security.title": "Security Overview", "user.security.subtitle": "Monitor and manage your account security settings", "user.security.refresh": "Refresh", "// Error States": "", "user.security.error.loadFailed": "Failed to load security information. Please try refreshing the page.", "// Security Score": "", "user.security.score.title": "Security Score", "user.security.score.description": "Your overall account security rating", "user.security.score.excellent": "Excellent", "user.security.score.good": "Good", "user.security.score.fair": "Fair", "user.security.score.poor": "Poor", "user.security.score.showDetails": "Show Details", "user.security.score.hideDetails": "Hide Details", "// Security Recommendations": "", "user.security.recommendations.title": "Security Recommendations", "user.security.recommendations.enable2fa": "Enable Two-Factor Authentication", "user.security.recommendations.enable2fa.description": "Add an extra layer of security to your account", "user.security.recommendations.reviewSessions": "Review Active Sessions", "user.security.recommendations.reviewSessions.description": "You have {count} active sessions. Review and remove unused ones.", "user.security.recommendations.updatePassword": "Update Your Password", "user.security.recommendations.updatePassword.description": "Your password is {days} days old. Consider updating it.", "user.security.recommendations.addTrustedDevices": "Add Trusted Devices", "user.security.recommendations.addTrustedDevices.description": "Mark your personal devices as trusted for easier access", "// Security Actions": "", "user.security.actions.title": "Security Actions", "user.security.actions.description": "Manage your account security settings", "user.security.actions.enable2fa": "Enable 2FA", "user.security.actions.changePassword": "Change Password", "user.security.actions.changePassword.description": "Update your account password", "user.security.actions.manageDevices": "Manage Devices", "user.security.actions.twoFactor": "Two-Factor Authentication", "user.security.actions.twoFactor.manage": "Manage 2FA settings", "user.security.actions.twoFactor.enable": "Enable 2FA protection", "user.security.actions.deviceManagement": "Device Management", "user.security.actions.deviceManagement.description": "Manage trusted devices and sessions", "// Settings": "", "user.security.settings": "Settings", "// Status Cards": "", "user.security.status.twoFactor": "Two-Factor Auth", "user.security.status.enabled": "Enabled", "user.security.status.disabled": "Disabled", "user.security.status.twoFactor.protected": "Account is protected", "user.security.status.twoFactor.enable": "Enable for better security", "user.security.status.activeSessions": "Active Sessions", "user.security.status.activeSessions.description": "<PERSON>ces signed in", "user.security.status.trustedDevices": "Trusted Devices", "user.security.status.trustedDevices.description": "Recognized devices", "user.security.status.securityEvents": "Security Events", "user.security.status.securityEvents.description": "Recent alerts", "// Account Information": "", "user.security.account.title": "Account Security Information", "user.security.account.description": "Important security-related account details", "user.security.account.created": "Account Created", "user.security.account.lastLogin": "Last Login", "user.security.account.passwordChanged": "Password Last Changed", "user.security.account.status": "Account Status", "user.security.account.never": "Never", "user.security.account.active": "Active", "user.security.account.inactive": "Inactive", "user.security.account.verified": "Verified", "// Score Details": "", "user.security.score.details.twoFactor": "Two-Factor Authentication:", "user.security.score.details.passwordAge": "Password Age:", "user.security.score.details.securityEvents": "Security Events:", "user.security.score.details.sessionManagement": "Session Management:", "user.security.score.details.trustedDevices": "Trusted Devices:", "user.security.score.points.plus40": "+40 points", "user.security.score.points.plus20": "+20 points", "user.security.score.points.plus10to20": "+10-20 points", "user.security.score.points.plus10": "+10 points", "user.security.score.points.zero": "0 points"}