{"// User Applications Page": "", "user.applications.title": "Connected Applications", "user.applications.subtitle": "Manage applications that have access to your account", "// Confirmation Messages": "", "user.applications.revokeConfirm": "Are you sure you want to revoke access for \"{name}\"? This will disconnect the application from your account.", "user.applications.revokeError": "Failed to revoke access. Please try again.", "user.applications.bulkRevokeConfirm": "Are you sure you want to revoke access for {count} application(s)?", "// Summary Cards": "", "user.applications.stats.total": "Total Applications", "user.applications.stats.total.description": "Connected to your account", "user.applications.stats.active": "Active Applications", "user.applications.stats.active.description": "Currently active", "user.applications.stats.permissions": "Total Permissions", "user.applications.stats.permissions.description": "Granted permissions", "// Table Headers": "", "user.applications.table.application": "Application", "user.applications.table.developer": "Developer", "user.applications.table.connected": "Connected", "user.applications.table.lastAccess": "Last Access", "user.applications.table.permissions": "Permissions", "user.applications.table.status": "Status", "user.applications.table.actions": "Actions", "// Table Content": "", "user.applications.table.noDescription": "No description", "user.applications.table.never": "Never", "user.applications.table.morePermissions": "+{count} more", "user.applications.table.active": "Active", "user.applications.table.inactive": "Inactive", "// Management Section": "", "user.applications.management.title": "Application Management", "user.applications.management.description": "View details, manage permissions, or revoke access for connected applications", "user.applications.management.searchPlaceholder": "Search applications or developers...", "user.applications.management.filterAll": "All", "user.applications.management.filterActive": "Active", "user.applications.management.filterInactive": "Inactive", "user.applications.management.bulkRevoke": "Revoke Access", "user.applications.management.emptyMessage": "No connected applications found", "// Security Notice": "", "user.applications.security.title": "Security Notice", "user.applications.security.description": "Regularly review your connected applications and revoke access for apps you no longer use. Only grant permissions that applications actually need to function properly."}