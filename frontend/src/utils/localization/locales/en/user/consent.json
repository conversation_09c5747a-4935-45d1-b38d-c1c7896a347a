{"consent.title": "Application Permissions", "consent.subtitle": "Review and manage permissions for applications", "consent.application": "Application", "consent.requestedPermissions": "Requested Permissions", "consent.grantPermissions": "Grant Permissions", "consent.denyPermissions": "<PERSON><PERSON>", "consent.cancel": "Cancel", "consent.granting": "Granting...", "consent.denying": "Denying...", "consent.error": "Failed to process consent", "consent.success": "Permissions granted successfully", "consent.denied": "Permissions denied", "consent.scope.openid": "Basic Authentication", "consent.scope.openid.desc": "Verify your identity", "consent.scope.profile": "Profile Information", "consent.scope.profile.desc": "Access your name and username", "consent.scope.email": "Email Address", "consent.scope.email.desc": "Access your email address", "consent.scope.read": "Read Access", "consent.scope.read.desc": "Read your data and settings", "consent.scope.write": "Write Access", "consent.scope.write.desc": "Modify your data and settings", "consent.scope.custom.desc": "Custom application permission", "consent.history.title": "Consent History", "consent.history.subtitle": "Manage your application permissions and view consent history", "consent.current.title": "Current Permissions", "consent.empty": "No consents found", "consent.refresh": "Refresh", "consent.filter.active": "Active Only", "consent.filter.all": "Show All", "consent.revoke": "Revoke", "consent.granted": "Granted:", "consent.expires": "Expires:", "consent.scopes.granted": "Granted Permissions", "consent.status.active": "Active", "consent.status.expired": "Expired", "consent.status.revoked": "Revoked", "consent.action.granted": "Granted", "consent.action.revoked": "Revoked", "consent.action.expired": "Expired", "consent.action.updated": "Updated", "consent.audit.title": "Consent History", "consent.audit.empty": "No consent history found", "consent.widget.title": "App Permissions", "consent.widget.active": "Active", "consent.widget.total": "Total", "consent.widget.expiring": "Expiring", "consent.widget.expiring.message": "{count} permissions expiring soon", "consent.widget.manage": "Manage", "consent.widget.recent": "Recent Activity", "consent.widget.viewAll": "View All", "consent.widget.sessions": "Sessions", "consent.widget.manageAll": "Manage all permissions", "consent.widget.error": "Unable to load consent information", "consent.renewal.title": "Permission Renewals", "consent.renewal.expires.today": "Expires today", "consent.renewal.expires.days": "Expires in {days} days", "consent.renewal.expires.weeks": "Expires in {weeks} weeks", "consent.renewal.compact.critical": "{critical} critical, {total} total permissions expiring", "consent.renewal.compact.normal": "{total} permissions expiring soon", "consent.renewal.critical": "Critical", "consent.renewal.renew": "<PERSON>w", "consent.renewal.visit": "Visit App", "consent.renewal.enable": "Enable renewal notifications", "consent.analytics.title": "Consent Analytics", "consent.analytics.refresh": "Refresh", "consent.analytics.total": "Total Consents", "consent.analytics.active": "Active", "consent.analytics.expiring": "Expiring Soon", "consent.analytics.health": "Health Score", "consent.analytics.trends": "Recent Trends", "consent.analytics.granted": "Granted (7d)", "consent.analytics.revoked": "Revoked (7d)", "consent.analytics.expired": "Expired (7d)", "consent.analytics.topApps": "Top Applications", "consent.analytics.scopes": "Permission Distribution", "consent.analytics.error": "Unable to load analytics data", "permission.status.revoked": "Revoked", "permission.status.expired": "Expired", "permission.status.expiring": "Expiring Soon", "permission.status.active": "Active", "permission.granted.title": "Granted Permissions", "permission.granted": "Granted:", "permission.expires": "Expires:", "permission.lastUsed": "Last Used:", "permission.viewDetails": "View Details", "permission.modify": "Modify", "permission.revoke": "Revoke", "permission.terminate": "Terminate", "permission.extend": "Extend", "permission.scopes.count": "permissions"}