{"// User Notifications Page": "", "user.notifications.title": "Notification Settings", "user.notifications.subtitle": "Manage your notification preferences and alerts", "user.notifications.refresh": "Refresh", "// Error States": "", "user.notifications.error.loadingTitle": "Error Loading Settings", "user.notifications.error.loadingDescription": "Unable to load your notification settings.", "user.notifications.tryAgain": "Try Again", "// Actions": "", "user.notifications.saving": "Saving...", "user.notifications.saveChanges": "Save Changes", "// Email Notifications Section": "", "user.notifications.email.title": "Email Notifications", "user.notifications.email.description": "Choose which email notifications you'd like to receive", "user.notifications.email.securityAlerts": "Security Alerts", "user.notifications.email.securityAlerts.description": "Important security events and login notifications", "user.notifications.email.productUpdates": "Product Updates", "user.notifications.email.productUpdates.description": "New features and product announcements", "user.notifications.email.newsletters": "Newsletters", "user.notifications.email.newsletters.description": "Monthly newsletters and tips", "user.notifications.email.usageReports": "Usage Reports", "user.notifications.email.usageReports.description": "Weekly activity and usage summaries", "// In-App Notifications Section": "", "user.notifications.inApp.title": "In-App Notifications", "user.notifications.inApp.description": "Control notifications shown within the application", "user.notifications.inApp.mentions": "Mentions", "user.notifications.inApp.mentions.description": "When someone mentions you in comments or discussions", "user.notifications.inApp.comments": "Comments", "user.notifications.inApp.comments.description": "New comments on your posts or activities", "user.notifications.inApp.taskAssignments": "Task Assignments", "user.notifications.inApp.taskAssignments.description": "When tasks are assigned to you", "user.notifications.inApp.statusChanges": "Status Changes", "user.notifications.inApp.statusChanges.description": "Updates on project or task status changes", "// Device Notifications Section": "", "user.notifications.device.title": "Device Notifications", "user.notifications.device.description": "Control notifications on your devices", "user.notifications.device.browser": "Browser Notifications", "user.notifications.device.browser.description": "Show notifications in your web browser", "user.notifications.device.mobile": "Mobile Notifications", "user.notifications.device.mobile.description": "Push notifications on mobile devices", "// Toast Messages": "", "user.notifications.toast.loadFailed.title": "Error", "user.notifications.toast.loadFailed.description": "Failed to load notification settings. Please try again.", "user.notifications.toast.saved.title": "Settings Saved", "user.notifications.toast.saved.description": "Your notification preferences have been updated successfully.", "user.notifications.toast.saveFailed.title": "Error", "user.notifications.toast.saveFailed.description": "Failed to save notification settings. Please try again."}