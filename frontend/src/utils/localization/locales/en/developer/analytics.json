{"// Developer Analytics Dashboard": "", "developer.analytics.title": "Developer Analytics", "developer.analytics.description": "Monitor your application performance and user engagement", "developer.analytics.refresh": "Refresh", "developer.analytics.period.1d": "Last 24h", "developer.analytics.period.7d": "Last 7 days", "developer.analytics.period.30d": "Last 30 days", "developer.analytics.period.90d": "Last 90 days", "// Metric Cards": "", "developer.analytics.metrics.totalUsers": "Total Users", "developer.analytics.metrics.totalUsersDesc": "Across all applications", "developer.analytics.metrics.activeUsers": "Active Users", "developer.analytics.metrics.activeUsersDesc": "Currently active", "developer.analytics.metrics.totalLogins": "Total Logins", "developer.analytics.metrics.totalLoginsDesc": "All time", "developer.analytics.metrics.applications": "Applications", "developer.analytics.metrics.applicationsDesc": "Active applications", "// Charts": "", "developer.analytics.charts.userActivity": "User Activity", "developer.analytics.charts.userActivityDesc": "Daily user engagement over time", "developer.analytics.charts.newUsers": "New Users", "developer.analytics.charts.logins": "<PERSON><PERSON>", "developer.analytics.charts.applicationPerformance": "Application Performance", "developer.analytics.charts.applicationPerformanceDesc": "User distribution across your applications", "developer.analytics.charts.applicationBreakdown": "Application Breakdown", "developer.analytics.charts.applicationBreakdownDesc": "Detailed performance metrics for each application", "developer.analytics.charts.users": "Users", "// Quick Actions": "", "developer.analytics.quickActions.title": "Quick Actions", "developer.analytics.quickActions.viewAllUsers": "View All Users", "developer.analytics.quickActions.activityLogs": "Activity Logs", "developer.analytics.quickActions.detailedReports": "Detailed Reports", "// Top Performing": "", "developer.analytics.topPerforming.title": "Top Performing", "developer.analytics.topPerforming.users": "users", "developer.analytics.topPerforming.noData": "No applications data available", "// Recent Activity": "", "developer.analytics.recentActivity.title": "Recent Activity", "developer.analytics.recentActivity.logins": "logins", "developer.analytics.recentActivity.new": "new", "developer.analytics.recentActivity.noData": "No recent activity data available", "// Error States and Common Text": "", "developer.analytics.error.failedToLoad": "Failed to load analytics data", "developer.analytics.error.retry": "Retry", "developer.analytics.common.unknown": "Unknown", "developer.analytics.common.users": "Users"}