{"// Developer Organizations Management": "", "developer.organizations.title": "Organizations", "developer.organizations.subtitle": "Manage your team organizations and collaborate on applications", "// Common Actions": "", "developer.organizations.common.error": "Failed to load organizations", "developer.organizations.common.retry": "Try Again", "developer.organizations.common.back": "Back to Organizations", "// Empty State": "", "developer.organizations.empty.title": "No Organizations Yet", "developer.organizations.empty.description": "Create your first organization to start collaborating with your team", "developer.organizations.empty.action": "Create Organization", "// Create Organization": "", "developer.organizations.create.title": "Create Organization", "developer.organizations.create.subtitle": "Set up a new organization for your team", "// Create Form": "", "developer.organizations.create.form.basicInfo": "Basic Information", "developer.organizations.create.form.additionalDetails": "Additional Details", "developer.organizations.create.form.settings": "Organization Settings", "developer.organizations.create.form.name.label": "Organization Name", "developer.organizations.create.form.name.placeholder": "Enter organization name", "developer.organizations.create.form.name.help": "Choose a descriptive name for your organization", "developer.organizations.create.form.slug.label": "Organization Slug", "developer.organizations.create.form.slug.placeholder": "organization-slug", "developer.organizations.create.form.slug.help": "URL-friendly identifier (lowercase, numbers, hyphens only)", "developer.organizations.create.form.description.label": "Description", "developer.organizations.create.form.description.placeholder": "Describe your organization's purpose", "developer.organizations.create.form.description.help": "Optional description of what your organization does", "developer.organizations.create.form.website.label": "Website", "developer.organizations.create.form.website.placeholder": "https://example.com", "developer.organizations.create.form.website.help": "Optional website URL for your organization", "developer.organizations.create.form.isPublic.label": "Public Organization", "developer.organizations.create.form.isPublic.help": "Allow others to discover and request to join this organization", "developer.organizations.create.form.maxMembers.label": "Maximum Members", "developer.organizations.create.form.maxMembers.help": "Set the maximum number of members allowed in this organization", "// Create Actions": "", "developer.organizations.create.actions.create": "Create Organization", "developer.organizations.create.actions.cancel": "Cancel", "developer.organizations.create.actions.creating": "Creating...", "// Create Messages": "", "developer.organizations.create.success": "Organization created successfully!", "// Create Errors": "", "developer.organizations.create.errors.nameRequired": "Organization name is required", "developer.organizations.create.errors.slugRequired": "Organization slug is required", "developer.organizations.create.errors.slugInvalid": "Slug can only contain lowercase letters, numbers, and hyphens", "developer.organizations.create.errors.slugTaken": "This slug is already taken", "developer.organizations.create.errors.websiteInvalid": "Please enter a valid website URL", "developer.organizations.create.errors.maxMembersInvalid": "Maximum members must be between 1 and 1000", "developer.organizations.create.errors.createFailed": "Failed to create organization", "// Dashboard Navigation": "", "developer.organizations.navigation.overview": "Overview", "developer.organizations.navigation.members": "Members", "developer.organizations.navigation.applications": "Applications", "developer.organizations.navigation.invitations": "Invitations", "developer.organizations.navigation.settings": "Settings", "// Dashboard Overview": "", "developer.organizations.dashboard.overview.members.title": "Members", "developer.organizations.dashboard.overview.members.count": "{count} members", "developer.organizations.dashboard.overview.members.viewAll": "View All Members", "developer.organizations.dashboard.overview.applications.title": "Applications", "developer.organizations.dashboard.overview.applications.count": "{count} applications", "developer.organizations.dashboard.overview.applications.viewAll": "View All Applications", "developer.organizations.dashboard.overview.invitations.title": "Pending Invitations", "developer.organizations.dashboard.overview.invitations.count": "{count} pending", "developer.organizations.dashboard.overview.invitations.viewAll": "View All Invitations", "// Dashboard Settings": "", "developer.organizations.dashboard.settings.general.title": "General Settings", "developer.organizations.dashboard.settings.general.name": "Organization Name", "developer.organizations.dashboard.settings.general.description": "Description", "developer.organizations.dashboard.settings.general.website": "Website", "developer.organizations.dashboard.settings.general.isPublic": "Public Organization", "developer.organizations.dashboard.settings.general.maxMembers": "Maximum Members", "// Dashboard Organization Info": "", "developer.organizations.dashboard.info.id": "Organization ID", "developer.organizations.dashboard.info.created": "Created", "developer.organizations.dashboard.info.role": "Your Role", "developer.organizations.dashboard.info.tier": "Tier", "developer.organizations.dashboard.info.status": "Status", "// Organization Status & Roles": "", "developer.organizations.status.public": "Public", "developer.organizations.status.private": "Private", "developer.organizations.role.owner": "Owner", "developer.organizations.role.admin": "Admin", "developer.organizations.role.member": "Member", "developer.organizations.members.count": "{count} members", "developer.organizations.members.single": "1 member", "developer.organizations.members.zero": "0 members", "developer.organizations.dashboard.navigation.backToOrganizations": "Back to Organizations", "// Dashboard Overview Cards": "", "developer.organizations.dashboard.overview.applications.single": "1 application", "developer.organizations.dashboard.overview.applications.zero": "0 applications", "developer.organizations.dashboard.overview.invitations.single": "1 pending", "developer.organizations.dashboard.overview.invitations.zero": "0 pending", "// Organization Info": "", "developer.organizations.dashboard.info.generalInformation": "General Information", "developer.organizations.dashboard.info.organizationName": "Organization Name", "developer.organizations.dashboard.info.maximumMembers": "Maximum Members", "developer.organizations.dashboard.info.yourRole": "Your Role", "// Organization Tiers": "", "developer.organizations.tier.free": "Free", "developer.organizations.tier.basic": "Basic", "developer.organizations.tier.premium": "Premium", "developer.organizations.tier.enterprise": "Enterprise", "// Dashboard Actions": "", "developer.organizations.dashboard.actions.inviteMembers": "Invite Members", "developer.organizations.dashboard.actions.manageApplications": "Manage Applications", "// Dashboard Tabs": "", "developer.organizations.dashboard.members.title": "Members", "developer.organizations.dashboard.members.subtitle": "Manage organization members and their roles", "developer.organizations.dashboard.members.invite": "Invite Members", "developer.organizations.dashboard.members.changeRole": "Role Changed", "developer.organizations.dashboard.members.remove": "Member Removed", "developer.organizations.dashboard.applications.title": "Applications", "developer.organizations.dashboard.applications.subtitle": "Manage applications associated with this organization", "developer.organizations.dashboard.applications.add": "Add Application", "developer.organizations.dashboard.applications.empty.title": "No applications yet", "developer.organizations.dashboard.applications.empty.description": "Add applications to group them under this organization", "developer.organizations.dashboard.applications.status.active": "Active", "developer.organizations.dashboard.applications.edit": "Application Edited", "developer.organizations.dashboard.applications.remove": "Application Removed", "developer.organizations.dashboard.applications.addDescription": "Feature coming soon - application grouping", "developer.organizations.dashboard.applications.status.inactive": "Inactive", "developer.organizations.dashboard.invitations.title": "Invitations", "developer.organizations.dashboard.invitations.subtitle": "Manage pending member invitations", "developer.organizations.dashboard.invitations.send": "Send Invitation", "developer.organizations.dashboard.invitations.empty.title": "No pending invitations", "developer.organizations.dashboard.invitations.empty.description": "Send invitations to add new members to your organization", "developer.organizations.dashboard.invitations.status.pending": "Pending", "developer.organizations.dashboard.invitations.status.accepted": "Accepted", "developer.organizations.dashboard.invitations.status.declined": "Declined", "developer.organizations.dashboard.invitations.status.rejected": "Rejected", "developer.organizations.dashboard.invitations.status.expired": "Expired", "developer.organizations.dashboard.invitations.sendDescription": "Feature coming soon - invitation system", "developer.organizations.dashboard.members.actions.makeAdmin": "Make Admin", "developer.organizations.dashboard.members.actions.makeMember": "Make Member", "developer.organizations.dashboard.members.actions.removeMember": "Remove Member", "developer.organizations.dashboard.applications.actions.edit": "Edit Application", "developer.organizations.dashboard.applications.actions.removeFromOrganization": "Remove from Organization", "// Toast Messages": "", "developer.organizations.toast.invitationSent.title": "Invitation Sen<PERSON>", "developer.organizations.toast.invitationSent.description": "Invitation sent to {email} successfully", "developer.organizations.toast.invitationCancelled.title": "Invitation Cancelled", "developer.organizations.toast.invitationCancelled.description": "Invitation cancelled successfully", "developer.organizations.toast.memberRemoved.title": "Member Removed", "developer.organizations.toast.memberRemoved.description": "Member removed from organization", "developer.organizations.toast.roleChanged.title": "Role Updated", "developer.organizations.toast.roleChanged.description": "Member role updated successfully", "developer.organizations.toast.error.title": "Error", "developer.organizations.toast.searchFailed.description": "Search failed for: {query}. Please try again.", "developer.organizations.dashboard.members.search": "Search members...", "developer.organizations.dashboard.members.sortBy": "Sort by", "developer.organizations.dashboard.members.sortByName": "Sort by Name", "developer.organizations.dashboard.members.sortByRole": "Sort by Role", "developer.organizations.dashboard.members.sortByJoined": "Sort by Joined Date", "developer.organizations.dashboard.applications.search": "Search applications...", "developer.organizations.dashboard.applications.sortBy": "Sort by", "developer.organizations.dashboard.applications.sortByName": "Sort by Name", "developer.organizations.dashboard.applications.sortByStatus": "Sort by Status", "developer.organizations.dashboard.applications.sortByAdded": "Sort by Added Date", "developer.organizations.dashboard.members.roleChanged": "Changed {user} role to {role}", "developer.organizations.dashboard.settings.edit": "Edit Settings", "developer.organizations.dashboard.settings.save": "Save Changes", "developer.organizations.dashboard.settings.cancel": "Cancel", "developer.organizations.dashboard.members.addUserDialog.title": "Add User to Organization", "developer.organizations.dashboard.members.addUserDialog.searchUser": "Search User", "developer.organizations.dashboard.members.addUserDialog.searchPlaceholder": "Search by name or email...", "developer.organizations.dashboard.members.addUserDialog.role": "Role", "developer.organizations.dashboard.members.addUserDialog.addUser": "Add User", "developer.organizations.dashboard.members.addUserDialog.sendInvitation": "Send Invitation", "developer.organizations.dashboard.members.addUserDialog.searching": "Searching users...", "developer.organizations.dashboard.members.addUserDialog.cancel": "Cancel", "developer.organizations.dashboard.members.addUserDialog.noResults": "No search results for: {query}", "developer.organizations.dashboard.members.addUserDialog.searchError": "Search failed for: {query}. Please try again.", "developer.organizations.dashboard.members.addUserDialog.allMembersAlready": "All users matching '{query}' are already members of this organization.", "developer.organizations.dashboard.members.addUserDialog.alreadyInvited": "Users matching '{query}' have already been invited to this organization.", "developer.organizations.dashboard.invitations.cancel": "Cancel Invitation", "developer.organizations.dashboard.members.addUserDialog.message": "Message (Optional)", "developer.organizations.dashboard.members.addUserDialog.messagePlaceholder": "Enter invitation message", "developer.organizations.dashboard.members.addUserDialog.userCannotBeAdmin": "System users can only be invited as members, not administrators", "developer.organizations.dashboard.settings.general.maxMembers.10": "10 members", "developer.organizations.dashboard.settings.general.maxMembers.25": "25 members", "developer.organizations.dashboard.settings.general.maxMembers.50": "50 members", "developer.organizations.dashboard.settings.general.maxMembers.100": "100 members", "developer.organizations.dashboard.settings.general.maxMembers.250": "250 members", "developer.organizations.dashboard.settings.general.maxMembers.500": "500 members", "developer.organizations.dashboard.settings.general.maxMembers.1000": "1000 members", "developer.organizations.dashboard.applications.addApplicationDialog.title": "Add Application to Organization", "developer.organizations.dashboard.applications.addApplicationDialog.selectApplication": "Select Application", "developer.organizations.dashboard.applications.addApplicationDialog.loadingApplications": "Loading applications...", "developer.organizations.dashboard.applications.addApplicationDialog.noApplications": "No available applications to add", "developer.organizations.dashboard.applications.addApplicationDialog.selectPlaceholder": "Select an application", "developer.organizations.dashboard.applications.addApplicationDialog.description": "Description (Optional)", "developer.organizations.dashboard.applications.addApplicationDialog.descriptionPlaceholder": "Enter application description", "developer.organizations.dashboard.applications.addApplicationDialog.cancel": "Cancel", "developer.organizations.dashboard.applications.addApplicationDialog.addApplication": "Add Application", "developer.organizations.dashboard.settings.title": "Settings", "developer.organizations.dashboard.settings.subtitle": "Configure organization settings and preferences", "developer.organizations.dashboard.settings.general.isPublicHelp": "Allow others to discover and request to join this organization", "developer.organizations.dashboard.settings.danger.title": "Danger Zone", "developer.organizations.dashboard.settings.danger.description": "These actions cannot be undone. Please be careful.", "developer.organizations.dashboard.settings.danger.delete": "Delete Organization"}