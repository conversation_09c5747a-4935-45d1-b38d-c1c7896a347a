{"// Developer Settings": "", "developer.settings.title": "Developer Settings", "developer.settings.description": "Configure your developer account, API keys, and integration settings", "developer.settings.refresh": "Refresh", "developer.settings.save": "Save Changes", "developer.settings.saving": "Saving...", "// Tabs": "", "developer.settings.tabs.general": "General", "developer.settings.tabs.apiKeys": "API Keys", "developer.settings.tabs.webhooks": "Webhooks", "developer.settings.tabs.security": "Security", "developer.settings.tabs.notifications": "Notifications", "developer.settings.tabs.environments": "Environments", "// General Settings": "", "developer.settings.general.title": "General Settings", "developer.settings.general.description": "Configure your developer account settings", "developer.settings.general.developerName": "Developer Name", "developer.settings.general.developerNameHelp": "Your name as it appears in the developer portal", "developer.settings.general.email": "Email Address", "developer.settings.general.emailHelp": "Email for developer notifications and alerts", "developer.settings.general.organization": "Organization", "developer.settings.general.organizationHelp": "Your company or organization name", "developer.settings.general.defaultRedirectUri": "Default Redirect URI", "developer.settings.general.defaultRedirectUriHelp": "Default URI for OAuth redirects", "developer.settings.general.defaultScopes": "<PERSON><PERSON><PERSON>", "developer.settings.general.defaultScopesHelp": "Default OAuth scopes for new applications", "// API Keys": "", "developer.settings.apiKeys.title": "API Keys", "developer.settings.apiKeys.description": "Manage your API keys for accessing the GeNieGO API", "developer.settings.apiKeys.createNew": "Create New API Key", "developer.settings.apiKeys.name": "Key Name", "developer.settings.apiKeys.permissions": "Permissions", "developer.settings.apiKeys.created": "Created", "developer.settings.apiKeys.expires": "Expires", "developer.settings.apiKeys.lastUsed": "Last Used", "developer.settings.apiKeys.status": "Status", "developer.settings.apiKeys.active": "Active", "developer.settings.apiKeys.inactive": "Inactive", "developer.settings.apiKeys.copy": "Copy", "developer.settings.apiKeys.delete": "Delete", "developer.settings.apiKeys.regenerate": "Regenerate", "developer.settings.apiKeys.show": "Show", "developer.settings.apiKeys.hide": "<PERSON>de", "developer.settings.apiKeys.never": "Never", "developer.settings.apiKeys.noExpiration": "No Expiration", "developer.settings.apiKeys.scopes": "scopes", "developer.settings.apiKeys.noKeys": "No API Keys", "developer.settings.apiKeys.noKeysDesc": "You haven't created any API keys yet", "developer.settings.apiKeys.noKeysLong": "You haven't generated any API keys yet. Create your first API key to start integrating with GeNieGO SSO.", "developer.settings.apiKeys.generateFirst": "Generate Your First API Key", "// Webhooks": "", "developer.settings.webhooks.title": "Webhooks", "developer.settings.webhooks.description": "Configure webhooks for real-time event notifications", "developer.settings.webhooks.createNew": "Create New Webhook", "developer.settings.webhooks.url": "Webhook URL", "developer.settings.webhooks.events": "Events", "developer.settings.webhooks.secret": "Secret", "developer.settings.webhooks.status": "Status", "developer.settings.webhooks.active": "Active", "developer.settings.webhooks.inactive": "Inactive", "developer.settings.webhooks.lastTriggered": "Last Triggered", "developer.settings.webhooks.noWebhooks": "No Webhooks", "developer.settings.webhooks.noWebhooksDesc": "You haven't created any webhooks yet", "// Security": "", "developer.settings.security.title": "Security Settings", "developer.settings.security.description": "Configure security settings for your developer account", "developer.settings.security.twoFactor": "Two-Factor Authentication", "developer.settings.security.twoFactorHelp": "Require 2FA for developer account access", "developer.settings.security.ipRestrictions": "IP Restrictions", "developer.settings.security.ipRestrictionsHelp": "Restrict API access to specific IP addresses", "developer.settings.security.sessionTimeout": "Session Timeout", "developer.settings.security.sessionTimeoutHelp": "Time in minutes before developer sessions expire", "// Notifications": "", "developer.settings.notifications.title": "Notification Settings", "developer.settings.notifications.description": "Configure how you receive developer notifications", "developer.settings.notifications.email": "Email Notifications", "developer.settings.notifications.emailHelp": "Receive notifications via email", "developer.settings.notifications.apiUsage": "API Usage Alerts", "developer.settings.notifications.apiUsageHelp": "Get notified about API usage thresholds", "developer.settings.notifications.securityAlerts": "Security Alerts", "developer.settings.notifications.securityAlertsHelp": "Receive security-related notifications", "// Environments": "", "developer.settings.environments.title": "Environment Settings", "developer.settings.environments.description": "Configure development, staging, and production environments", "developer.settings.environments.defaultEnvironment": "Default Environment", "developer.settings.environments.defaultEnvironmentHelp": "Default environment for new applications", "developer.settings.environments.development": "Development", "developer.settings.environments.staging": "Staging", "developer.settings.environments.production": "Production", "// Toast Messages": "", "developer.settings.toast.copied": "Copied to clipboard", "developer.settings.toast.copiedDesc": "Text has been copied to your clipboard", "developer.settings.toast.saved": "Settings Saved", "developer.settings.toast.savedDesc": "Developer settings have been updated successfully", "developer.settings.toast.saveFailed": "Save Failed", "developer.settings.toast.saveFailedDesc": "Failed to save settings. Please try again", "developer.settings.toast.loadFailed": "Failed to Load Data", "developer.settings.toast.loadFailedDesc": "Unable to load API keys and webhooks. Please try again", "developer.settings.toast.settingsLoadFailed": "Failed to Load Settings", "developer.settings.toast.settingsLoadFailedDesc": "Unable to load developer settings. Please try again", "developer.settings.toast.comingSoon": "Feature Coming Soon", "developer.settings.toast.comingSoonDesc": "This feature will be available in a future update", "// Account Information": "", "developer.settings.accountInformation": "Account Information", "developer.settings.accountInformationDesc": "Update your developer account details", "developer.settings.companyName": "Company Name", "developer.settings.companyNamePlaceholder": "Your Company Name", "developer.settings.contactEmail": "Contact Email", "developer.settings.contactEmailPlaceholder": "<EMAIL>", "developer.settings.supportUrl": "Support URL", "developer.settings.supportUrlPlaceholder": "https://yourcompany.com/support", "// Development Environment": "", "developer.settings.developmentEnvironment": "Development Environment", "developer.settings.developmentEnvironmentDesc": "Configure your development and testing environment", "developer.settings.sandboxMode": "Sandbox Mode", "developer.settings.sandboxModeDesc": "Use test environment for development", "developer.settings.debugMode": "Debug Mode", "developer.settings.debugModeDesc": "Enable detailed API response logging", "developer.settings.rateLimitBypass": "Rate Limit Bypass", "developer.settings.rateLimitBypassDesc": "Bypass rate limits for testing (sandbox only)", "// Notification Preferences": "", "developer.settings.notificationPreferences": "Notification Preferences", "developer.settings.notificationPreferencesDesc": "Configure how you receive updates and alerts", "developer.settings.emailNotifications": "Email Notifications", "developer.settings.emailNotificationsDesc": "Receive general updates via email", "developer.settings.webhookFailureAlerts": "Webhook Failure Alerts", "developer.settings.webhookFailureAlertsDesc": "Get notified when webhooks fail", "developer.settings.usageAlerts": "<PERSON><PERSON>", "developer.settings.usageAlertsDesc": "Alerts for API usage limits", "developer.settings.securityAlerts": "Security Alerts", "developer.settings.securityAlertsDesc": "Important security notifications", "// Webhook Management": "", "developer.settings.noWebhooksConfigured": "No Webhooks Configured", "developer.settings.noWebhooksConfiguredDesc": "Set up webhook endpoints to receive real-time notifications about events in your applications.", "developer.settings.addFirstWebhook": "Add Your First Webhook", "developer.settings.webhookActive": "Active", "developer.settings.webhookInactive": "Inactive", "developer.settings.webhookCreated": "Created:", "developer.settings.webhookLastTriggered": "Last Triggered:", "developer.settings.webhookNever": "Never", "developer.settings.webhookEvents": "Events:", "developer.settings.webhookEventsSubscribed": "subscribed", "developer.settings.webhookSecret": "Secret:", "// API Configuration": "", "developer.settings.defaultApiVersion": "Default API Version", "developer.settings.requestTimeout": "Request Timeout (seconds)"}