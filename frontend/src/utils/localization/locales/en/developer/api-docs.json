{"// Developer API Documentation": "", "developer.apiDocs.title": "API Documentation", "developer.apiDocs.description": "Complete API reference and integration guides for GeNieGO SSO", "developer.apiDocs.backToDashboard": "Back to Dashboard", "developer.apiDocs.search": "Search endpoints...", "developer.apiDocs.allCategories": "All Categories", "developer.apiDocs.authentication": "Authentication", "developer.apiDocs.users": "Users", "developer.apiDocs.applications": "Applications", "developer.apiDocs.tokens": "Tokens", "// Getting Started": "", "developer.apiDocs.gettingStarted.title": "Getting Started", "developer.apiDocs.gettingStarted.description": "Quick start guide for integrating with GeNieGO SSO API", "developer.apiDocs.gettingStarted.step1": "1. Get API Keys", "developer.apiDocs.gettingStarted.step1Desc": "Generate API keys from your developer dashboard", "developer.apiDocs.gettingStarted.step2": "2. Authentication", "developer.apiDocs.gettingStarted.step2Desc": "Use OAuth 2.0 or API keys for authentication", "developer.apiDocs.gettingStarted.step3": "3. Make Requests", "developer.apiDocs.gettingStarted.step3Desc": "Start making API calls to our endpoints", "developer.apiDocs.gettingStarted.getStarted": "Get Started", "// Authentication Guide": "", "developer.apiDocs.authGuide.title": "Authentication Guide", "developer.apiDocs.authGuide.description": "Learn how to authenticate with the GeNieGO SSO API", "developer.apiDocs.authGuide.oauth": "OAuth 2.0", "developer.apiDocs.authGuide.oauthDesc": "Recommended for web applications", "developer.apiDocs.authGuide.apiKeys": "API Keys", "developer.apiDocs.authGuide.apiKeysDesc": "For server-to-server communication", "developer.apiDocs.authGuide.learnMore": "Learn More", "// Code Examples": "", "developer.apiDocs.codeExamples.title": "Code Examples", "developer.apiDocs.codeExamples.description": "Ready-to-use code snippets in popular languages", "developer.apiDocs.codeExamples.javascript": "JavaScript", "developer.apiDocs.codeExamples.python": "Python", "developer.apiDocs.codeExamples.curl": "cURL", "developer.apiDocs.codeExamples.copy": "Copy", "developer.apiDocs.codeExamples.copied": "Copied!", "// API Endpoints": "", "developer.apiDocs.endpoints.title": "API Endpoints", "developer.apiDocs.endpoints.method": "Method", "developer.apiDocs.endpoints.endpoint": "Endpoint", "developer.apiDocs.endpoints.auth": "Auth Required", "developer.apiDocs.endpoints.yes": "Yes", "developer.apiDocs.endpoints.no": "No", "developer.apiDocs.endpoints.tryIt": "Try It", "developer.apiDocs.endpoints.parameters": "Parameters", "developer.apiDocs.endpoints.responses": "Responses", "developer.apiDocs.endpoints.example": "Example", "developer.apiDocs.endpoints.required": "Required", "developer.apiDocs.endpoints.optional": "Optional", "// SDKs": "", "developer.apiDocs.sdks.title": "SDKs & Libraries", "developer.apiDocs.sdks.description": "Official SDKs for popular programming languages", "developer.apiDocs.sdks.javascript": "JavaScript SDK", "developer.apiDocs.sdks.python": "Python SDK", "developer.apiDocs.sdks.php": "PHP SDK", "developer.apiDocs.sdks.ruby": "Ruby SDK", "developer.apiDocs.sdks.download": "Download", "developer.apiDocs.sdks.viewDocs": "View Docs", "// Support": "", "developer.apiDocs.support.title": "Developer Support", "developer.apiDocs.support.description": "Get help with your integration", "developer.apiDocs.support.community": "Community Forum", "developer.apiDocs.support.communityDesc": "Ask questions and share knowledge", "developer.apiDocs.support.contact": "Contact Support", "developer.apiDocs.support.contactDesc": "Get direct help from our team", "developer.apiDocs.support.status": "API Status", "developer.apiDocs.support.statusDesc": "Check current API status and uptime", "// Actions and Buttons": "", "developer.apiDocs.apiSettings": "API Settings", "developer.apiDocs.openApiSpec": "OpenAPI Spec", "developer.apiDocs.registerApp": "Register App", "developer.apiDocs.viewExamples": "View Examples", "developer.apiDocs.tryApi": "Try API", "// Quick Start Guide": "", "developer.apiDocs.quickStartGuide": "Quick Start Guide", "developer.apiDocs.quickStartDesc": "Get started with GeNieGO SSO API in minutes", "developer.apiDocs.getApiKeys": "1. Get API Keys", "developer.apiDocs.getApiKeysDesc": "Register your application and obtain client credentials", "developer.apiDocs.authenticate": "2. <PERSON><PERSON>nti<PERSON>", "developer.apiDocs.authenticateDesc": "Implement OAuth2 flow or use API key authentication", "developer.apiDocs.makeRequests": "3. Make Requests", "developer.apiDocs.makeRequestsDesc": "Start making API calls to manage users and applications", "// Categories": "", "developer.apiDocs.allEndpoints": "All Endpoints", "developer.apiDocs.userManagement": "User Management", "developer.apiDocs.oauth2": "OAuth2", "// Toast Messages": "", "developer.apiDocs.toast.copied.title": "Copied to clipboard", "developer.apiDocs.toast.copied.description": "Code example has been copied to your clipboard.", "// Missing hardcoded text": "", "developer.apiDocs.searchPlaceholder": "Search endpoints...", "developer.apiDocs.authRequired": "Auth Required", "developer.apiDocs.codeExample": "Code Example", "developer.apiDocs.copy": "Copy", "developer.apiDocs.responses": "Responses", "developer.apiDocs.tryThisEndpoint": "Try This Endpoint", "developer.apiDocs.codeNotAvailable": "Code example not available for this language."}