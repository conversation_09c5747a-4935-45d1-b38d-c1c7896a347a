{"// Developer Pages": "", "developer.register.title": "注册新子系統", "developer.register.subtitle": "将您的应用程式注册到 GeNieGO SSO，为您的用户啟用单點登录", "developer.register.backToDashboard": "返回儀表板", "developer.register.basicInfo": "基本信息", "developer.register.applicationName": "子系統名稱", "developer.register.applicationNamePlaceholder": "例如：GenieMove、MyApp", "developer.register.description": "描述", "developer.register.descriptionPlaceholder": "您应用程式的簡要描述", "developer.register.redirectUris": "重定向 URI", "developer.register.redirectUrisDescription": "添加用户认证后将被重定向到的 URL", "developer.register.redirectUriPlaceholder": "https://yourapp.com/auth/callback", "developer.register.addRedirectUri": "添加另一个重定向 URI", "developer.register.permissions": "权限（範圍）", "developer.register.permissionsDescription": "選擇您的应用程式需要访问的用户信息", "developer.register.clientCredentials": "客户端凭据", "developer.register.credentialsDialogDescription": "您的应用程式凭据已生成。请安全地保存它们，因为客户端密鑰不会再次顯示。", "developer.register.securityNotice": "重要安全提醒", "developer.register.securityNoticeText": "请安全地存保这些凭据。客户端密鑰僅顯示一次，无法恢復。", "developer.register.clientId": "客户端 ID", "developer.register.clientSecret": "客户端密鑰", "developer.register.regenerateCredentials": "重新生成凭据", "developer.register.downloadCSV": "下载 CSV", "developer.register.close": "关闭", "developer.register.agree": "同意", "developer.register.termsAndConditions": "條款和條件", "developer.register.agreeTo": "我同意", "developer.register.termsOfService": "服務條款", "developer.register.privacyPolicy": "隱私政策", "developer.register.cancel": "取消", "developer.register.generateCredentials": "生成凭据", "developer.register.registerApplication": "注册子系統", "developer.register.generating": "生成中...", "developer.register.registering": "注册中...", "developer.register.nextSteps": "下一步", "developer.register.nextStepsText": "注册后，您将能夠将 GeNieGO SSO 整合到您的应用程式中。查看我们的整合文檔以獲取實施指南和代码示例。", "developer.register.integrationDocs": "整合文檔", "developer.register.error.applicationNameRequired": "子系統名稱是必填项", "developer.register.error.applicationNameTooShort": "子系統名稱必须至少3个字符", "developer.register.error.applicationNameInvalid": "子系統名稱只能包含字母、数字、空格、连字符和下劃線", "developer.register.error.descriptionRequired": "描述是必填项", "developer.register.error.descriptionTooShort": "描述必须至少10个字符", "developer.register.error.redirectUriRequired": "至少需要一个重定向 URI", "developer.register.error.redirectUriInvalid": "所有重定向 URI 必须是有效的 HTTP/HTTPS URL", "developer.register.error.termsRequired": "您必须接受服務條款", "developer.register.error.privacyRequired": "您必须接受隱私政策", "developer.register.error.registrationFailed": "注册失败，请重试。", "// Toast Messages": "", "developer.register.toast.success.title": "应用程式已注册", "developer.register.toast.success.description": "{name} 已成功注册。", "developer.register.toast.error.title": "注册失败", "developer.register.toast.error.description": "注册应用程式失败。请重试。", "// Scope Options": "", "developer.register.scope.openid.name": "OpenID Connect", "developer.register.scope.openid.description": "基本身份驗证", "developer.register.scope.profile.name": "个人资料", "developer.register.scope.profile.description": "使用者个人资料资讯", "developer.register.scope.email.name": "电子邮件", "developer.register.scope.email.description": "使用者电子邮件地址", "developer.register.scope.phone.name": "电话", "developer.register.scope.phone.description": "使用者电话号码", "developer.register.scope.address.name": "地址", "developer.register.scope.address.description": "使用者地址资讯"}