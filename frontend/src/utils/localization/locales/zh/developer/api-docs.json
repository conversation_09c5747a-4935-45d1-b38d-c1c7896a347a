{"// Developer API Documentation": "", "developer.apiDocs.title": "API 文件", "developer.apiDocs.description": "GeNieGO SSO 的完整 API 參考和整合指南", "developer.apiDocs.backToDashboard": "返回儀表板", "developer.apiDocs.search": "搜尋端點...", "developer.apiDocs.allCategories": "所有類別", "developer.apiDocs.authentication": "身份驗证", "developer.apiDocs.users": "使用者", "developer.apiDocs.applications": "应用程式", "developer.apiDocs.tokens": "权杖", "// Getting Started": "", "developer.apiDocs.gettingStarted.title": "开始使用", "developer.apiDocs.gettingStarted.description": "與 GeNieGO SSO API 整合的快速入門指南", "developer.apiDocs.gettingStarted.step1": "1. 取得 API 金鑰", "developer.apiDocs.gettingStarted.step1Desc": "从您的开发者儀表板產生 API 金鑰", "developer.apiDocs.gettingStarted.step2": "2. 身份驗证", "developer.apiDocs.gettingStarted.step2Desc": "使用 OAuth 2.0 或 API 金鑰進行身份驗证", "developer.apiDocs.gettingStarted.step3": "3. 发出请求", "developer.apiDocs.gettingStarted.step3Desc": "开始對我们的端點发出 API 呼叫", "developer.apiDocs.gettingStarted.getStarted": "开始使用", "// Authentication Guide": "", "developer.apiDocs.authGuide.title": "身份驗证指南", "developer.apiDocs.authGuide.description": "了解如何使用 GeNieGO SSO API 進行身份驗证", "developer.apiDocs.authGuide.oauth": "OAuth 2.0", "developer.apiDocs.authGuide.oauthDesc": "建议用于网路应用程式", "developer.apiDocs.authGuide.apiKeys": "API 金鑰", "developer.apiDocs.authGuide.apiKeysDesc": "用于伺服器對伺服器通讯", "developer.apiDocs.authGuide.learnMore": "了解更多", "// Code Examples": "", "developer.apiDocs.codeExamples.title": "程式码範例", "developer.apiDocs.codeExamples.description": "熱門语言的即用型程式码片段", "developer.apiDocs.codeExamples.javascript": "JavaScript", "developer.apiDocs.codeExamples.python": "Python", "developer.apiDocs.codeExamples.curl": "cURL", "developer.apiDocs.codeExamples.copy": "複製", "developer.apiDocs.codeExamples.copied": "已複製！", "// API Endpoints": "", "developer.apiDocs.endpoints.title": "API 端點", "developer.apiDocs.endpoints.method": "方法", "developer.apiDocs.endpoints.endpoint": "端點", "developer.apiDocs.endpoints.auth": "需要驗证", "developer.apiDocs.endpoints.yes": "是", "developer.apiDocs.endpoints.no": "否", "developer.apiDocs.endpoints.tryIt": "试用", "developer.apiDocs.endpoints.parameters": "參数", "developer.apiDocs.endpoints.responses": "回应", "developer.apiDocs.endpoints.example": "範例", "developer.apiDocs.endpoints.required": "必填", "developer.apiDocs.endpoints.optional": "選填", "// SDKs": "", "developer.apiDocs.sdks.title": "SDK 和函式庫", "developer.apiDocs.sdks.description": "熱門程式语言的官方 SDK", "developer.apiDocs.sdks.javascript": "JavaScript SDK", "developer.apiDocs.sdks.python": "Python SDK", "developer.apiDocs.sdks.php": "PHP SDK", "developer.apiDocs.sdks.ruby": "Ruby SDK", "developer.apiDocs.sdks.download": "下载", "developer.apiDocs.sdks.viewDocs": "檢视文件", "// Support": "", "developer.apiDocs.support.title": "开发者支援", "developer.apiDocs.support.description": "獲得整合協助", "developer.apiDocs.support.community": "社群論壇", "developer.apiDocs.support.communityDesc": "提问和分享知識", "developer.apiDocs.support.contact": "联絡支援", "developer.apiDocs.support.contactDesc": "从我们的團隊獲得直接協助", "developer.apiDocs.support.status": "API 状态", "developer.apiDocs.support.statusDesc": "檢查目前 API 状态和正常運行时间", "// Actions and Buttons": "", "developer.apiDocs.apiSettings": "API 设定", "developer.apiDocs.openApiSpec": "OpenAPI 規格", "developer.apiDocs.registerApp": "注册应用程式", "developer.apiDocs.viewExamples": "查看範例", "developer.apiDocs.tryApi": "试用 API", "// Quick Start Guide": "", "developer.apiDocs.quickStartGuide": "快速入門指南", "developer.apiDocs.quickStartDesc": "幾分鐘內开始使用 GeNieGO SSO API", "developer.apiDocs.getApiKeys": "1. 取得 API 金鑰", "developer.apiDocs.getApiKeysDesc": "注册您的应用程式并取得客户端凭证", "developer.apiDocs.authenticate": "2. 身份驗证", "developer.apiDocs.authenticateDesc": "實作 OAuth2 流程或使用 API 金鑰驗证", "developer.apiDocs.makeRequests": "3. 发出请求", "developer.apiDocs.makeRequestsDesc": "开始发出 API 呼叫来管理使用者和应用程式", "// Categories": "", "developer.apiDocs.allEndpoints": "所有端點", "developer.apiDocs.userManagement": "使用者管理", "developer.apiDocs.oauth2": "OAuth2", "// Toast Messages": "", "developer.apiDocs.toast.copied.title": "已複製到剪貼簿", "developer.apiDocs.toast.copied.description": "程式码範例已複製到您的剪貼簿。", "// Missing hardcoded text": "", "developer.apiDocs.searchPlaceholder": "搜尋端點...", "developer.apiDocs.authRequired": "需要驗证", "developer.apiDocs.codeExample": "程式码範例", "developer.apiDocs.copy": "複製", "developer.apiDocs.responses": "回应", "developer.apiDocs.tryThisEndpoint": "试用此端點", "developer.apiDocs.codeNotAvailable": "此语言没有可用的程式码範例。"}