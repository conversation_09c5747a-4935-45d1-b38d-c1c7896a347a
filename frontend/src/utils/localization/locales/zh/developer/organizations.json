{"// Developer Organizations Management": "", "developer.organizations.title": "组织管理", "developer.organizations.subtitle": "管理您的团队组织并协作开发应用程序", "// Common Actions": "", "developer.organizations.common.error": "载入组织失败", "developer.organizations.common.retry": "重试", "developer.organizations.common.back": "返回组织列表", "// Empty State": "", "developer.organizations.empty.title": "暂无组织", "developer.organizations.empty.description": "创建您的第一个组织以开始与团队协作", "developer.organizations.empty.action": "创建组织", "// Create Organization": "", "developer.organizations.create.title": "创建组织", "developer.organizations.create.subtitle": "为您的团队设置新组织", "// Create Form": "", "developer.organizations.create.form.basicInfo": "基本信息", "developer.organizations.create.form.additionalDetails": "附加详情", "developer.organizations.create.form.settings": "组织设置", "developer.organizations.create.form.name.label": "组织名称", "developer.organizations.create.form.name.placeholder": "输入组织名称", "developer.organizations.create.form.name.help": "为您的组织选择一个描述性名称", "developer.organizations.create.form.slug.label": "组织标识", "developer.organizations.create.form.slug.placeholder": "organization-slug", "developer.organizations.create.form.slug.help": "URL友好标识符（仅限小写字母、数字和连字符）", "developer.organizations.create.form.description.label": "描述", "developer.organizations.create.form.description.placeholder": "描述您组织的目的", "developer.organizations.create.form.description.help": "可选的组织用途描述", "developer.organizations.create.form.website.label": "网站", "developer.organizations.create.form.website.placeholder": "https://example.com", "developer.organizations.create.form.website.help": "可选的组织网站URL", "developer.organizations.create.form.isPublic.label": "公开组织", "developer.organizations.create.form.isPublic.help": "允许其他人发现并请求加入此组织", "developer.organizations.create.form.maxMembers.label": "最大成员数", "developer.organizations.create.form.maxMembers.help": "设置此组织允许的最大成员数量", "// Create Actions": "", "developer.organizations.create.actions.create": "创建组织", "developer.organizations.create.actions.cancel": "取消", "developer.organizations.create.actions.creating": "创建中...", "// Create Messages": "", "developer.organizations.create.success": "组织创建成功！", "// Create Errors": "", "developer.organizations.create.errors.nameRequired": "组织名称为必填项", "developer.organizations.create.errors.slugRequired": "组织标识为必填项", "developer.organizations.create.errors.slugInvalid": "标识只能包含小写字母、数字和连字符", "developer.organizations.create.errors.slugTaken": "此标识已被使用", "developer.organizations.create.errors.websiteInvalid": "请输入有效的网站URL", "developer.organizations.create.errors.maxMembersInvalid": "最大成员数必须在1到1000之间", "developer.organizations.create.errors.createFailed": "创建组织失败", "// Dashboard Navigation": "", "developer.organizations.navigation.overview": "概览", "developer.organizations.navigation.members": "成员", "developer.organizations.navigation.applications": "应用程序", "developer.organizations.navigation.invitations": "邀请", "developer.organizations.navigation.settings": "设置", "// Dashboard Overview": "", "developer.organizations.dashboard.overview.members.title": "成员", "developer.organizations.dashboard.overview.members.count": "{count} 名成员", "developer.organizations.dashboard.overview.members.viewAll": "查看所有成员", "developer.organizations.dashboard.overview.applications.title": "应用程序", "developer.organizations.dashboard.overview.applications.count": "{count} 个应用", "developer.organizations.dashboard.overview.applications.viewAll": "查看所有应用", "developer.organizations.dashboard.overview.invitations.title": "待处理邀请", "developer.organizations.dashboard.overview.invitations.count": "{count} 个待处理", "developer.organizations.dashboard.overview.invitations.viewAll": "查看所有邀请", "// Dashboard Settings": "", "developer.organizations.dashboard.settings.general.title": "常规设置", "developer.organizations.dashboard.settings.general.name": "组织名称", "developer.organizations.dashboard.settings.general.description": "描述", "developer.organizations.dashboard.settings.general.website": "网站", "developer.organizations.dashboard.settings.general.isPublic": "公开组织", "developer.organizations.dashboard.settings.general.maxMembers": "最大成员数", "// Dashboard Organization Info": "", "developer.organizations.dashboard.info.id": "组织ID", "developer.organizations.dashboard.info.created": "创建时间", "developer.organizations.dashboard.info.role": "您的角色", "developer.organizations.dashboard.info.tier": "等级", "developer.organizations.dashboard.info.status": "状态", "// Organization Status & Roles": "", "developer.organizations.status.public": "公开", "developer.organizations.status.private": "私有", "developer.organizations.role.owner": "所有者", "developer.organizations.role.admin": "管理员", "developer.organizations.role.member": "成员", "developer.organizations.members.count": "{count} 名成员", "developer.organizations.members.single": "1 名成员", "developer.organizations.members.zero": "0 名成员", "developer.organizations.dashboard.navigation.backToOrganizations": "返回组织列表", "// Dashboard Overview Cards": "", "developer.organizations.dashboard.overview.applications.single": "1 个应用", "developer.organizations.dashboard.overview.applications.zero": "0 个应用", "developer.organizations.dashboard.overview.invitations.single": "1 个待处理", "developer.organizations.dashboard.overview.invitations.zero": "0 个待处理", "// Organization Info": "", "developer.organizations.dashboard.info.generalInformation": "基本信息", "developer.organizations.dashboard.info.organizationName": "组织名称", "developer.organizations.dashboard.info.maximumMembers": "最大成员数", "developer.organizations.dashboard.info.yourRole": "您的角色", "// Organization Tiers": "", "developer.organizations.tier.free": "免费版", "developer.organizations.tier.basic": "基础版", "developer.organizations.tier.premium": "高级版", "developer.organizations.tier.enterprise": "企业版", "// Dashboard Actions": "", "developer.organizations.dashboard.actions.inviteMembers": "邀请成员", "developer.organizations.dashboard.actions.manageApplications": "管理应用", "// Dashboard Tabs": "", "developer.organizations.dashboard.members.title": "成员", "developer.organizations.dashboard.members.subtitle": "管理组织成员及其角色", "developer.organizations.dashboard.members.invite": "邀请成员", "developer.organizations.dashboard.members.changeRole": "角色已更改", "developer.organizations.dashboard.members.remove": "成员已移除", "developer.organizations.dashboard.applications.title": "应用", "developer.organizations.dashboard.applications.subtitle": "管理与此组织关联的应用", "developer.organizations.dashboard.applications.add": "添加应用", "developer.organizations.dashboard.applications.empty.title": "暂无应用", "developer.organizations.dashboard.applications.empty.description": "添加应用以将其归类到此组织下", "developer.organizations.dashboard.applications.status.active": "活跃", "developer.organizations.dashboard.applications.edit": "应用已编辑", "developer.organizations.dashboard.applications.remove": "应用已移除", "developer.organizations.dashboard.applications.addDescription": "功能即将推出 - 应用分组", "developer.organizations.dashboard.applications.status.inactive": "非活跃", "developer.organizations.dashboard.invitations.title": "邀请", "developer.organizations.dashboard.invitations.subtitle": "管理待处理的成员邀请", "developer.organizations.dashboard.invitations.send": "发送邀请", "developer.organizations.dashboard.invitations.empty.title": "暂无待处理邀请", "developer.organizations.dashboard.invitations.empty.description": "发送邀请以添加新成员到您的组织", "developer.organizations.dashboard.invitations.status.pending": "待处理", "developer.organizations.dashboard.invitations.status.accepted": "已接受", "developer.organizations.dashboard.invitations.status.declined": "已拒絕", "developer.organizations.dashboard.invitations.status.rejected": "已拒绝", "developer.organizations.dashboard.invitations.status.expired": "已过期", "developer.organizations.dashboard.invitations.sendDescription": "功能即将推出 - 邀请系统", "developer.organizations.dashboard.members.actions.makeAdmin": "设为管理员", "developer.organizations.dashboard.members.actions.makeMember": "设为成员", "developer.organizations.dashboard.members.actions.removeMember": "移除成员", "developer.organizations.dashboard.applications.actions.edit": "编辑应用", "developer.organizations.dashboard.applications.actions.removeFromOrganization": "从组织中移除", "// Toast Messages": "", "developer.organizations.toast.invitationSent.title": "邀请已发送", "developer.organizations.toast.invitationSent.description": "已成功向 {email} 发送邀请", "developer.organizations.toast.invitationCancelled.title": "邀请已取消", "developer.organizations.toast.invitationCancelled.description": "邀请已成功取消", "developer.organizations.toast.memberRemoved.title": "成员已移除", "developer.organizations.toast.memberRemoved.description": "成员已从組織中移除", "developer.organizations.toast.roleChanged.title": "角色已更新", "developer.organizations.toast.roleChanged.description": "成员角色已成功更新", "developer.organizations.toast.error.title": "错误", "developer.organizations.toast.searchFailed.description": "搜尋失败：{query}。请重试。", "developer.organizations.dashboard.members.search": "搜索成员...", "developer.organizations.dashboard.members.sortBy": "排序方式", "developer.organizations.dashboard.members.sortByName": "按姓名排序", "developer.organizations.dashboard.members.sortByRole": "按角色排序", "developer.organizations.dashboard.members.sortByJoined": "按加入日期排序", "developer.organizations.dashboard.applications.search": "搜索应用...", "developer.organizations.dashboard.applications.sortBy": "排序方式", "developer.organizations.dashboard.applications.sortByName": "按名称排序", "developer.organizations.dashboard.applications.sortByStatus": "按状态排序", "developer.organizations.dashboard.applications.sortByAdded": "按添加日期排序", "developer.organizations.dashboard.members.roleChanged": "已将 {user} 的角色更改为 {role}", "developer.organizations.dashboard.settings.edit": "编辑设置", "developer.organizations.dashboard.settings.save": "保存更改", "developer.organizations.dashboard.settings.cancel": "取消", "developer.organizations.dashboard.members.addUserDialog.title": "添加用户到组织", "developer.organizations.dashboard.members.addUserDialog.searchUser": "搜索用户", "developer.organizations.dashboard.members.addUserDialog.searchPlaceholder": "按姓名或邮箱搜索...", "developer.organizations.dashboard.members.addUserDialog.role": "角色", "developer.organizations.dashboard.members.addUserDialog.addUser": "添加用户", "developer.organizations.dashboard.members.addUserDialog.sendInvitation": "发送邀请", "developer.organizations.dashboard.members.addUserDialog.searching": "搜索用户中...", "developer.organizations.dashboard.members.addUserDialog.cancel": "取消", "developer.organizations.dashboard.members.addUserDialog.noResults": "没有找到搜索结果：{query}", "developer.organizations.dashboard.members.addUserDialog.searchError": "搜索失败：{query}。请重试。", "developer.organizations.dashboard.members.addUserDialog.allMembersAlready": "所有匹配'{query}'的用户都已经是该组织的成员。", "developer.organizations.dashboard.members.addUserDialog.alreadyInvited": "匹配'{query}'的用户已经被邀请加入该组织。", "developer.organizations.dashboard.invitations.cancel": "取消邀请", "developer.organizations.dashboard.members.addUserDialog.message": "消息（可选）", "developer.organizations.dashboard.members.addUserDialog.messagePlaceholder": "输入邀请消息", "developer.organizations.dashboard.members.addUserDialog.userCannotBeAdmin": "系統使用者只能被邀请为成员，不能成为管理员", "developer.organizations.dashboard.settings.general.maxMembers.10": "10 名成员", "developer.organizations.dashboard.settings.general.maxMembers.25": "25 名成员", "developer.organizations.dashboard.settings.general.maxMembers.50": "50 名成员", "developer.organizations.dashboard.settings.general.maxMembers.100": "100 名成员", "developer.organizations.dashboard.settings.general.maxMembers.250": "250 名成员", "developer.organizations.dashboard.settings.general.maxMembers.500": "500 名成员", "developer.organizations.dashboard.settings.general.maxMembers.1000": "1000 名成员", "developer.organizations.dashboard.applications.addApplicationDialog.title": "添加应用到组织", "developer.organizations.dashboard.applications.addApplicationDialog.selectApplication": "选择应用", "developer.organizations.dashboard.applications.addApplicationDialog.loadingApplications": "加载应用中...", "developer.organizations.dashboard.applications.addApplicationDialog.noApplications": "没有可添加的应用", "developer.organizations.dashboard.applications.addApplicationDialog.selectPlaceholder": "选择一个应用", "developer.organizations.dashboard.applications.addApplicationDialog.description": "描述（可选）", "developer.organizations.dashboard.applications.addApplicationDialog.descriptionPlaceholder": "输入应用描述", "developer.organizations.dashboard.applications.addApplicationDialog.cancel": "取消", "developer.organizations.dashboard.applications.addApplicationDialog.addApplication": "添加应用", "developer.organizations.dashboard.settings.title": "设置", "developer.organizations.dashboard.settings.subtitle": "配置组织设置和偏好", "developer.organizations.dashboard.settings.general.isPublicHelp": "允许其他人发现并请求加入此组织", "developer.organizations.dashboard.settings.danger.title": "危险区域", "developer.organizations.dashboard.settings.danger.description": "这些操作无法撤销，请谨慎操作。", "developer.organizations.dashboard.settings.danger.delete": "删除组织"}