{"// Developer Settings": "", "developer.settings.title": "开发者设定", "developer.settings.description": "配置您的开发者账户、API 金鑰和整合设定", "developer.settings.refresh": "重新整理", "developer.settings.save": "保存變更", "developer.settings.saving": "保存中...", "// Tabs": "", "developer.settings.tabs.general": "一般", "developer.settings.tabs.apiKeys": "API 金鑰", "developer.settings.tabs.webhooks": "Webhook", "developer.settings.tabs.security": "安全性", "developer.settings.tabs.notifications": "通知", "developer.settings.tabs.environments": "環境", "// General Settings": "", "developer.settings.general.title": "一般设定", "developer.settings.general.description": "配置您的开发者账户设定", "developer.settings.general.developerName": "开发者姓名", "developer.settings.general.developerNameHelp": "您在开发者入口网站中顯示的姓名", "developer.settings.general.email": "电子邮件地址", "developer.settings.general.emailHelp": "用于开发者通知和警示的电子邮件", "developer.settings.general.organization": "組織", "developer.settings.general.organizationHelp": "您的公司或組織名稱", "developer.settings.general.defaultRedirectUri": "预设重定向 URI", "developer.settings.general.defaultRedirectUriHelp": "OAuth 重定向的预设 URI", "developer.settings.general.defaultScopes": "预设範圍", "developer.settings.general.defaultScopesHelp": "新应用程式的预设 OAuth 範圍", "// API Keys": "", "developer.settings.apiKeys.title": "API 金鑰", "developer.settings.apiKeys.description": "管理您用于存取 GeNieGO API 的 API 金鑰", "developer.settings.apiKeys.createNew": "產生新金鑰", "developer.settings.apiKeys.name": "金鑰名稱", "developer.settings.apiKeys.permissions": "权限", "developer.settings.apiKeys.created": "建立", "developer.settings.apiKeys.expires": "到期", "developer.settings.apiKeys.lastUsed": "最后使用", "developer.settings.apiKeys.status": "状态", "developer.settings.apiKeys.active": "活跃", "developer.settings.apiKeys.inactive": "非活跃", "developer.settings.apiKeys.copy": "複製", "developer.settings.apiKeys.delete": "刪除", "developer.settings.apiKeys.regenerate": "重新產生", "developer.settings.apiKeys.show": "顯示", "developer.settings.apiKeys.hide": "隱藏", "developer.settings.apiKeys.never": "从未", "developer.settings.apiKeys.noExpiration": "无到期日", "developer.settings.apiKeys.scopes": "範圍", "developer.settings.apiKeys.noKeys": "无 API 金鑰", "developer.settings.apiKeys.noKeysDesc": "您尚未建立任何 API 金鑰", "developer.settings.apiKeys.noKeysLong": "您尚未產生任何 API 金鑰。建立您的第一个 API 金鑰以开始與 GeNieGO SSO 整合。", "developer.settings.apiKeys.generateFirst": "產生您的第一个 API 金鑰", "// Webhooks": "", "developer.settings.webhooks.title": "Webhook", "developer.settings.webhooks.description": "配置用于即时事件通知的 Webhook", "developer.settings.webhooks.createNew": "新增 Webhook", "developer.settings.webhooks.url": "Webhook URL", "developer.settings.webhooks.events": "事件", "developer.settings.webhooks.secret": "密鑰", "developer.settings.webhooks.status": "状态", "developer.settings.webhooks.active": "活跃", "developer.settings.webhooks.inactive": "非活跃", "developer.settings.webhooks.lastTriggered": "最后觸发", "developer.settings.webhooks.noWebhooks": "无 Webhook", "developer.settings.webhooks.noWebhooksDesc": "您尚未建立任何 Webhook", "// Security": "", "developer.settings.security.title": "安全设定", "developer.settings.security.description": "配置您开发者账户的安全设定", "developer.settings.security.twoFactor": "雙重驗证", "developer.settings.security.twoFactorHelp": "开发者账户存取需要 2FA", "developer.settings.security.ipRestrictions": "IP 限制", "developer.settings.security.ipRestrictionsHelp": "限制 API 存取特定 IP 地址", "developer.settings.security.sessionTimeout": "工作階段逾时", "developer.settings.security.sessionTimeoutHelp": "开发者工作階段到期前的时间（分鐘）", "// Notifications": "", "developer.settings.notifications.title": "通知设定", "developer.settings.notifications.description": "配置您如何接收开发者通知", "developer.settings.notifications.email": "电子邮件通知", "developer.settings.notifications.emailHelp": "透过电子邮件接收通知", "developer.settings.notifications.apiUsage": "API 使用警示", "developer.settings.notifications.apiUsageHelp": "獲得 API 使用閾值通知", "developer.settings.notifications.securityAlerts": "安全警示", "developer.settings.notifications.securityAlertsHelp": "接收安全相关通知", "// Environments": "", "developer.settings.environments.title": "環境设定", "developer.settings.environments.description": "配置开发、測试和生產環境", "developer.settings.environments.defaultEnvironment": "预设環境", "developer.settings.environments.defaultEnvironmentHelp": "新应用程式的预设環境", "developer.settings.environments.development": "开发", "developer.settings.environments.staging": "測试", "developer.settings.environments.production": "生產", "// Toast Messages": "", "developer.settings.toast.copied": "已複製到剪貼簿", "developer.settings.toast.copiedDesc": "文字已複製到您的剪貼簿", "developer.settings.toast.saved": "设定已保存", "developer.settings.toast.savedDesc": "开发者设定已成功更新", "developer.settings.toast.saveFailed": "保存失败", "developer.settings.toast.saveFailedDesc": "保存设定失败。请重试", "developer.settings.toast.loadFailed": "载入资料失败", "developer.settings.toast.loadFailedDesc": "无法载入 API 金鑰和 Webhook。请重试", "developer.settings.toast.settingsLoadFailed": "载入设定失败", "developer.settings.toast.settingsLoadFailedDesc": "无法载入开发者设定。请重试", "developer.settings.toast.comingSoon": "功能即将推出", "developer.settings.toast.comingSoonDesc": "此功能将在未来更新中提供", "// Account Information": "", "developer.settings.accountInformation": "账户资讯", "developer.settings.accountInformationDesc": "更新您的开发者账户详细资料", "developer.settings.companyName": "公司名稱", "developer.settings.companyNamePlaceholder": "您的公司名稱", "developer.settings.contactEmail": "联絡电子邮件", "developer.settings.contactEmailPlaceholder": "<EMAIL>", "developer.settings.supportUrl": "支援 URL", "developer.settings.supportUrlPlaceholder": "https://yourcompany.com/support", "// Development Environment": "", "developer.settings.developmentEnvironment": "开发環境", "developer.settings.developmentEnvironmentDesc": "配置您的开发和測试環境", "developer.settings.sandboxMode": "沙盒模式", "developer.settings.sandboxModeDesc": "使用測试環境進行开发", "developer.settings.debugMode": "除错模式", "developer.settings.debugModeDesc": "啟用详细的 API 回应记录", "developer.settings.rateLimitBypass": "速率限制繞过", "developer.settings.rateLimitBypassDesc": "繞过測试的速率限制（僅限沙盒）", "// Notification Preferences": "", "developer.settings.notificationPreferences": "通知偏好设定", "developer.settings.notificationPreferencesDesc": "配置您接收更新和警報的方式", "developer.settings.emailNotifications": "电子邮件通知", "developer.settings.emailNotificationsDesc": "透过电子邮件接收一般更新", "developer.settings.webhookFailureAlerts": "Webhook 失败警報", "developer.settings.webhookFailureAlertsDesc": "當 webhook 失败时收到通知", "developer.settings.usageAlerts": "使用量警報", "developer.settings.usageAlertsDesc": "API 使用量限制警報", "developer.settings.securityAlerts": "安全警報", "developer.settings.securityAlertsDesc": "重要的安全通知", "// Webhook Management": "", "developer.settings.noWebhooksConfigured": "尚未配置 Webhook", "developer.settings.noWebhooksConfiguredDesc": "设定 webhook 端點以接收有关应用程式事件的即时通知。", "developer.settings.addFirstWebhook": "新增您的第一个 Webhook", "developer.settings.webhookActive": "啟用", "developer.settings.webhookInactive": "停用", "developer.settings.webhookCreated": "建立时间：", "developer.settings.webhookLastTriggered": "最后觸发：", "developer.settings.webhookNever": "从未", "developer.settings.webhookEvents": "事件：", "developer.settings.webhookEventsSubscribed": "已訂閱", "developer.settings.webhookSecret": "密鑰：", "// API Configuration": "", "developer.settings.defaultApiVersion": "预设 API 版本", "developer.settings.requestTimeout": "请求逾时（秒）"}