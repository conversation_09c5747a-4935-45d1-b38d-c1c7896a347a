{"// Admin Dashboard": "", "admin.dashboard.title": "GenieAdmin 儀表板", "admin.dashboard.subtitle": "GeNieGO SSO 系統管理和監控", "admin.dashboard.refresh": "重新整理", "admin.dashboard.systemSettings": "系統设定", "// Tabs": "", "admin.dashboard.tabs.overview": "概览", "admin.dashboard.tabs.analytics": "分析", "admin.dashboard.tabs.users": "使用者", "admin.dashboard.tabs.applications": "应用程式", "// Quick Stats": "", "admin.dashboard.stats.totalUsers": "使用者總数", "admin.dashboard.stats.activeUsers": "位活跃使用者", "admin.dashboard.stats.applications": "应用程式", "admin.dashboard.stats.applicationsActive": "个活跃", "admin.dashboard.stats.todaysLogins": "今日登入", "admin.dashboard.stats.authRequests": "驗证请求", "admin.dashboard.stats.pendingApprovals": "待審核", "admin.dashboard.stats.awaitingReview": "个等待審核", "// System Health": "", "admin.dashboard.health.title": "系統健康状态", "admin.dashboard.health.healthy": "健康", "admin.dashboard.health.warning": "警告", "admin.dashboard.health.critical": "嚴重", "admin.dashboard.health.lastUpdated": "最后更新", "// Quick Actions": "", "admin.dashboard.quickActions.userManagement.title": "使用者管理", "admin.dashboard.quickActions.userManagement.description": "管理使用者账户和权限", "admin.dashboard.quickActions.userManagement.button": "管理使用者", "admin.dashboard.quickActions.applicationManagement.title": "应用程式管理", "admin.dashboard.quickActions.applicationManagement.description": "監控和核准应用程式", "admin.dashboard.quickActions.applicationManagement.button": "管理应用程式", "admin.dashboard.quickActions.systemAnalytics.title": "系統分析", "admin.dashboard.quickActions.systemAnalytics.description": "檢视详细的系統指标", "admin.dashboard.quickActions.systemAnalytics.button": "檢视分析", "// Users Tab": "", "admin.dashboard.users.title": "使用者管理", "admin.dashboard.users.viewAllUsers": "檢视所有使用者", "admin.dashboard.users.description": "管理使用者账户和权限", "admin.dashboard.users.noUsersFound": "找不到使用者", "admin.dashboard.users.noUsersFiltered": "请調整您的搜尋條件或篩選器", "admin.dashboard.users.noUsersRegistered": "尚未有使用者注册", "admin.dashboard.users.unknownUser": "未知使用者", "admin.dashboard.users.noEmail": "无电子邮件", "admin.dashboard.users.role": "角色", "admin.dashboard.users.joined": "加入于", "admin.dashboard.users.unknown": "未知", "admin.dashboard.users.active": "活跃", "admin.dashboard.users.inactive": "未啟用", "admin.dashboard.users.verified": "已驗证", "admin.dashboard.users.unverified": "未驗证", "admin.dashboard.users.viewAllCount": "檢视所有 {count} 位使用者", "// Applications Tab": "", "admin.dashboard.applications.filterTitle": "篩選应用程式", "admin.dashboard.applications.searchPlaceholder": "依名稱或擁有者搜尋应用程式...", "admin.dashboard.applications.title": "应用程式管理", "admin.dashboard.applications.viewAllApplications": "檢视所有应用程式", "admin.dashboard.applications.owner": "擁有者", "admin.dashboard.applications.clientId": "客户端 ID", "admin.dashboard.applications.created": "建立时间", "// Analytics Tab": "", "admin.dashboard.analytics.totalUsers": "使用者總数", "admin.dashboard.analytics.activeApplications": "活跃应用程式", "admin.dashboard.analytics.dailyLogins": "每日登入", "admin.dashboard.analytics.pendingApprovals": "待審核", "admin.dashboard.analytics.registeredUsers": "注册使用者", "admin.dashboard.analytics.total": "總计", "admin.dashboard.analytics.totalCount": "總计 {count}", "admin.dashboard.analytics.todaysActivity": "今日活动", "admin.dashboard.analytics.awaitingReview": "等待審核", "admin.dashboard.analytics.activityTimeline": "活动时间軸", "admin.dashboard.analytics.activityTrends": "过去一週的每日活动趨勢", "admin.dashboard.analytics.totalLogins": "總登入次数", "admin.dashboard.analytics.newUsers": "新使用者", "admin.dashboard.analytics.applicationStatus": "应用程式状态", "admin.dashboard.analytics.statusDistribution": "应用程式状态分佈", "admin.dashboard.analytics.activeStatus": "活跃", "admin.dashboard.analytics.pendingStatus": "待審核", "admin.dashboard.analytics.inactiveStatus": "非活跃", "admin.dashboard.analytics.topApplications": "表现最佳的应用程式", "admin.dashboard.analytics.topApplicationsDesc": "使用者參與度最高的应用程式", "// Users Tab Search": "", "admin.dashboard.users.searchPlaceholder": "按电子邮件或姓名搜尋使用者...", "admin.dashboard.users.joinedOn": "加入于", "admin.dashboard.analytics.quickStats": "快速統计", "admin.dashboard.analytics.developers": "开发者", "admin.dashboard.analytics.weekLogins": "週登入次数", "admin.dashboard.analytics.monthLogins": "月登入次数", "// Weekly Growth": "", "admin.dashboard.analytics.weeklyGrowth": "週成長", "admin.dashboard.analytics.users": "使用者", "admin.dashboard.analytics.applications": "应用程式", "admin.dashboard.analytics.logins": "登入", "// System Status": "", "admin.dashboard.analytics.systemStatus": "系統状态", "admin.dashboard.analytics.apiServicesOnline": "API 服務線上", "admin.dashboard.analytics.databaseConnected": "资料庫已连接", "admin.dashboard.analytics.authenticationActive": "身份驗证啟用", "// Application Status Badges": "", "admin.dashboard.applications.approved": "已核准", "admin.dashboard.applications.pending": "待審核", "admin.dashboard.applications.active": "活跃", "admin.dashboard.applications.inactive": "非活跃"}