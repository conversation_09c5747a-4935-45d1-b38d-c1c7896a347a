{"// Admin System Settings Page": "", "admin.systemSettings.title": "系統设定", "admin.systemSettings.subtitle": "配置企業安全政策和系統參数", "admin.systemSettings.refresh": "重新整理", "admin.systemSettings.resetToDefaults": "重设为预设值", "admin.systemSettings.saveChanges": "保存變更", "admin.systemSettings.saving": "保存中...", "// Tabs": "", "admin.systemSettings.tabs.authentication": "身份驗证", "admin.systemSettings.tabs.security": "安全性", "admin.systemSettings.tabs.system": "系統", "admin.systemSettings.tabs.compliance": "合規性", "admin.systemSettings.tabs.notifications": "通知", "admin.systemSettings.tabs.api": "API", "// Alerts": "", "admin.systemSettings.alerts.maintenanceMode": "维護模式啟用：", "admin.systemSettings.alerts.maintenanceModeDescription": "系統目前处于维護模式。使用者将看到维護页面且无法存取服務。", "// Authentication Settings": "", "admin.systemSettings.authentication.title": "身份驗证配置", "admin.systemSettings.authentication.description": "配置身份驗证政策和密码要求", "admin.systemSettings.authentication.sessionTimeout": "工作階段逾时（分鐘）", "admin.systemSettings.authentication.sessionTimeoutHelp": "閒置后自动登出", "admin.systemSettings.authentication.maxLoginAttempts": "最大登入尝试次数", "admin.systemSettings.authentication.maxLoginAttemptsHelp": "账户鎖定門檻", "admin.systemSettings.authentication.passwordPolicy": "密码政策", "admin.systemSettings.authentication.passwordMinLength": "最小長度", "admin.systemSettings.authentication.passwordRequireSpecialChars": "需要特殊字元", "admin.systemSettings.authentication.passwordRequireSpecialCharsHelp": "包含符号如 !@#$%", "admin.systemSettings.authentication.passwordRequireNumbers": "需要数字", "admin.systemSettings.authentication.passwordRequireNumbersHelp": "至少包含一个数字", "admin.systemSettings.authentication.passwordRequireUppercase": "需要大寫字母", "admin.systemSettings.authentication.passwordRequireUppercaseHelp": "至少包含一个大寫字母", "admin.systemSettings.authentication.passwordExpiryDays": "密码到期（天）", "admin.systemSettings.authentication.passwordExpiryHelp": "强制密码變更间隔", "admin.systemSettings.authentication.twoFactorRequired": "需要雙重驗证", "admin.systemSettings.authentication.twoFactorHelp": "所有使用者强制使用 2FA", "// Security Policies": "", "admin.systemSettings.security.title": "安全政策", "admin.systemSettings.security.description": "配置安全政策和存取控制", "admin.systemSettings.security.allowPasswordReset": "允許密码重设", "admin.systemSettings.security.allowPasswordResetHelp": "使用者可透过电子邮件重设密码", "admin.systemSettings.security.requireEmailVerification": "需要电子邮件驗证", "admin.systemSettings.security.requireEmailVerificationHelp": "新账户必须驗证电子邮件", "admin.systemSettings.security.allowSelfRegistration": "允許自行注册", "admin.systemSettings.security.allowSelfRegistrationHelp": "使用者可獨立建立账户", "admin.systemSettings.security.adminApprovalRequired": "需要管理员核准", "admin.systemSettings.security.adminApprovalRequiredHelp": "新账户需要管理员核准", "admin.systemSettings.security.auditLogRetentionDays": "稽核日誌保留（天）", "admin.systemSettings.security.auditLogRetentionDaysHelp": "安全日誌保留时间", "// System Configuration": "", "admin.systemSettings.system.title": "系統配置", "admin.systemSettings.system.description": "基本系統设定和操作參数", "admin.systemSettings.system.systemName": "系統名稱", "admin.systemSettings.system.systemNameHelp": "您的 SSO 服務顯示名稱", "admin.systemSettings.system.systemDescription": "系統描述", "admin.systemSettings.system.systemDescriptionHelp": "您的服務簡要描述", "admin.systemSettings.system.supportEmail": "支援电子邮件", "admin.systemSettings.system.supportEmailHelp": "使用者支援联絡电子邮件", "admin.systemSettings.system.maintenanceMode": "维護模式", "admin.systemSettings.system.maintenanceModeHelp": "暫时停用使用者存取", "admin.systemSettings.system.debugMode": "除错模式", "admin.systemSettings.system.debugModeHelp": "啟用详细日誌记录（僅限开发）", "admin.systemSettings.system.systemStatus": "系統状态", "admin.systemSettings.system.active": "啟用", "// Compliance Settings": "", "admin.systemSettings.compliance.title": "合規性與隱私", "admin.systemSettings.compliance.description": "配置 GDPR 合規性和资料保護设定", "admin.systemSettings.compliance.gdprCompliance": "GDPR 合規性", "admin.systemSettings.compliance.gdprComplianceHelp": "啟用 GDPR 隱私功能", "admin.systemSettings.compliance.gdprComplianceMode": "GDPR 合規模式", "admin.systemSettings.compliance.gdprComplianceModeHelp": "啟用歐盟资料保護功能", "admin.systemSettings.compliance.dataRetentionDays": "资料保留期间（天）", "admin.systemSettings.compliance.dataRetentionDaysHelp": "自动刪除非活跃使用者资料", "admin.systemSettings.compliance.cookieConsent": "<PERSON><PERSON> 同意", "admin.systemSettings.compliance.cookieConsentHelp": "需要 Cookie 同意橫幅", "admin.systemSettings.compliance.cookieConsentBanner": "<PERSON><PERSON> 同意橫幅", "admin.systemSettings.compliance.cookieConsentBannerHelp": "向使用者顯示 Cookie 同意", "admin.systemSettings.compliance.privacyPolicyUrl": "隱私政策 URL", "admin.systemSettings.compliance.privacyPolicyUrlHelp": "您的隱私政策连结", "admin.systemSettings.compliance.termsOfServiceUrl": "服務條款 URL", "admin.systemSettings.compliance.termsOfServiceUrlHelp": "您的服務條款连结", "// Notification Settings": "", "admin.systemSettings.notifications.title": "通知配置", "admin.systemSettings.notifications.description": "配置系統通知和警示", "admin.systemSettings.notifications.securityAlertsEnabledHelp": "向管理员通報安全事件", "admin.systemSettings.notifications.emailNotificationsEnabled": "电子邮件通知", "admin.systemSettings.notifications.emailNotificationsEnabledHelp": "向使用者发送电子邮件通知", "admin.systemSettings.notifications.securityAlertsEnabled": "安全警示", "admin.systemSettings.notifications.systemMaintenanceNotifications": "维護通知", "admin.systemSettings.notifications.systemMaintenanceNotificationsHelp": "通知使用者系統维護", "// API Settings": "", "admin.systemSettings.api.title": "API 配置", "admin.systemSettings.api.description": "配置 API 速率限制和版本控制", "admin.systemSettings.api.rateLimitEnabled": "速率限制", "admin.systemSettings.api.rateLimitEnabledHelp": "啟用 API 速率限制", "admin.systemSettings.api.rateLimitRequests": "每窗口请求数", "admin.systemSettings.api.rateLimitRequestsHelp": "每个时间窗口的最大请求数", "admin.systemSettings.api.rateLimitWindow": "窗口大小（秒）", "admin.systemSettings.api.rateLimitWindowHelp": "速率限制的时间窗口", "admin.systemSettings.api.apiVersioning": "API 版本", "admin.systemSettings.api.apiVersioningHelp": "新整合的预设 API 版本", "admin.systemSettings.api.version1": "版本 1.0（目前）", "admin.systemSettings.api.version2": "版本 2.0（測试版）", "// Toast Messages": "", "admin.systemSettings.toast.loadFailed.title": "载入设定失败", "admin.systemSettings.toast.loadFailed.description": "无法载入系統设定。请重试。", "admin.systemSettings.toast.saveSuccess.title": "设定已保存", "admin.systemSettings.toast.saveSuccess.description": "系統设定已成功更新。", "admin.systemSettings.toast.saveFailed.title": "保存失败", "admin.systemSettings.toast.saveFailed.description": "保存系統设定失败。请重试。"}