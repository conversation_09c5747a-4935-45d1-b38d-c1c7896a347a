/**
 * Object Utilities
 *
 * This module provides utility functions for object manipulation,
 * deep cloning, merging, and property access
 */

// ============================================================================
// CORE OBJECT OPERATIONS
// ============================================================================

/**
 * Deep clone an object
 *
 * @param obj - Object to clone
 * @returns Deep cloned object
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T;
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  return obj;
};

/**
 * Deep merge two or more objects
 *
 * @param target - Target object
 * @param sources - Source objects to merge
 * @returns Merged object
 */
export const deepMerge = <T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T => {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key] as any, source[key] as any);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
};

/**
 * Check if value is an object
 *
 * @param item - Value to check
 * @returns True if value is an object
 */
export const isObject = (item: any): item is Record<string, any> => {
  return item && typeof item === 'object' && !Array.isArray(item);
};

/**
 * Check if object is empty
 *
 * @param obj - Object to check
 * @returns True if object is empty
 */
export const isEmpty = (obj: Record<string, any>): boolean => {
  if (!obj || typeof obj !== 'object') return true;
  return Object.keys(obj).length === 0;
};

/**
 * Compare two objects for deep equality
 *
 * @param obj1 - First object
 * @param obj2 - Second object
 * @returns True if objects are deeply equal
 */
export const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return obj1 === obj2;

  if (typeof obj1 !== typeof obj2) return false;

  if (typeof obj1 !== 'object') return obj1 === obj2;

  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) return false;
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) return false;
    }
    return true;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

/**
 * Compare two objects for deep equality (alias for deepEqual)
 *
 * @param obj1 - First object
 * @param obj2 - Second object
 * @returns True if objects are deeply equal
 */
export const isEqual = (obj1: any, obj2: any): boolean => {
  return deepEqual(obj1, obj2);
};

// ============================================================================
// PROPERTY ACCESS AND MANIPULATION
// ============================================================================

/**
 * Get nested property value using dot notation
 *
 * @param obj - Object to get property from
 * @param path - Property path (e.g., 'user.profile.name')
 * @param defaultValue - Default value if property doesn't exist
 * @returns Property value or default value
 */
export const getNestedValue = <T = any>(
  obj: Record<string, any>,
  path: string,
  defaultValue?: T
): T => {
  const keys = path.split('.');
  let result: any = obj;

  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue as T;
    }
    result = result[key];
  }

  return result !== undefined ? (result as T) : (defaultValue as T);
};

/**
 * Get nested property value using dot notation (alias for getNestedValue)
 *
 * @param obj - Object to get value from
 * @param path - Property path (e.g., 'user.profile.name')
 * @param defaultValue - Default value if property doesn't exist
 * @returns Property value or default value
 */
export const get = <T = any>(obj: any, path: string, defaultValue?: T): T => {
  return getNestedValue(obj, path, defaultValue);
};

/**
 * Set nested property value using dot notation
 *
 * @param obj - Object to set property on
 * @param path - Property path (e.g., 'user.profile.name')
 * @param value - Value to set
 * @returns Modified object
 */
export const setNestedValue = <T extends Record<string, any>>(
  obj: T,
  path: string,
  value: any
): T => {
  const keys = path.split('.');
  const lastKey = keys.pop();

  if (!lastKey) return obj;

  let current: any = obj;
  for (const key of keys) {
    if (!(key in current) || !isObject(current[key])) {
      current[key] = {};
    }
    current = current[key] as Record<string, any>;
  }

  current[lastKey] = value;
  return obj;
};

/**
 * Set nested property value using dot notation (alias for setNestedValue)
 *
 * @param obj - Object to set value in
 * @param path - Property path (e.g., 'user.profile.name')
 * @param value - Value to set
 * @returns Modified object
 */
export const set = <T extends object>(obj: T, path: string, value: any): T => {
  return setNestedValue(obj as Record<string, any>, path, value) as T;
};

/**
 * Remove property from object using dot notation
 *
 * @param obj - Object to remove property from
 * @param path - Property path to remove
 * @returns Modified object
 */
export const removeNestedValue = <T extends Record<string, any>>(
  obj: T,
  path: string
): T => {
  const keys = path.split('.');
  const lastKey = keys.pop();

  if (!lastKey) return obj;

  let current: any = obj;
  for (const key of keys) {
    if (!(key in current) || !isObject(current[key])) {
      return obj; // Path doesn't exist
    }
    current = current[key] as Record<string, any>;
  }

  delete current[lastKey];
  return obj;
};

/**
 * Remove property from object using dot notation (alias for removeNestedValue)
 *
 * @param obj - Object to remove property from
 * @param path - Property path (e.g., 'user.profile.name')
 * @returns Modified object
 */
export const unset = <T extends object>(obj: T, path: string): T => {
  return removeNestedValue(obj as Record<string, any>, path) as T;
};

/**
 * Check if object has nested property
 *
 * @param obj - Object to check
 * @param path - Property path to check
 * @returns True if property exists
 */
export const hasNestedProperty = (
  obj: Record<string, any>,
  path: string
): boolean => {
  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current == null || typeof current !== 'object' || !(key in current)) {
      return false;
    }
    current = current[key];
  }

  return true;
};

/**
 * Check if object has property using dot notation (alias for hasNestedProperty)
 *
 * @param obj - Object to check
 * @param path - Property path (e.g., 'user.profile.name')
 * @returns True if property exists
 */
export const has = (obj: any, path: string): boolean => {
  return hasNestedProperty(obj, path);
};

// ============================================================================
// OBJECT TRANSFORMATION
// ============================================================================

/**
 * Pick specific properties from object
 *
 * @param obj - Source object
 * @param keys - Keys to pick
 * @returns New object with picked properties
 */
export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>;

  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }

  return result;
};

/**
 * Omit specific properties from object
 *
 * @param obj - Source object
 * @param keys - Keys to omit
 * @returns New object without omitted properties
 */
export const omit = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj };

  for (const key of keys) {
    delete result[key];
  }

  return result as Omit<T, K>;
};

// ============================================================================
// PATH AND KEY OPERATIONS
// ============================================================================

/**
 * Get all property paths from nested object
 *
 * @param obj - Object to get paths from
 * @param prefix - Path prefix
 * @returns Array of property paths
 */
export const getPropertyPaths = (
  obj: Record<string, any>,
  prefix: string = ''
): string[] => {
  const paths: string[] = [];

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;

      if (isObject(obj[key])) {
        paths.push(...getPropertyPaths(obj[key], currentPath));
      } else {
        paths.push(currentPath);
      }
    }
  }

  return paths;
};

/**
 * Get all keys from object including nested keys (alias for getPropertyPaths)
 *
 * @param obj - Object to get keys from
 * @param prefix - Prefix for nested keys
 * @returns Array of all keys
 */
export const getAllKeys = (obj: object, prefix: string = ''): string[] => {
  return getPropertyPaths(obj as Record<string, any>, prefix);
};

// ============================================================================
// FLATTEN AND UNFLATTEN OPERATIONS
// ============================================================================

/**
 * Flatten nested object to single level with dot notation keys
 *
 * @param obj - Object to flatten
 * @param prefix - Key prefix
 * @returns Flattened object
 */
export const flatten = (
  obj: Record<string, any>,
  prefix: string = ''
): Record<string, any> => {
  const flattened: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const currentKey = prefix ? `${prefix}.${key}` : key;

      if (isObject(obj[key]) && !Array.isArray(obj[key])) {
        Object.assign(flattened, flatten(obj[key], currentKey));
      } else {
        flattened[currentKey] = obj[key];
      }
    }
  }

  return flattened;
};

/**
 * Unflatten object with dot notation keys back to nested structure
 *
 * @param obj - Flattened object
 * @returns Nested object
 */
export const unflatten = (obj: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      setNestedValue(result, key, obj[key]);
    }
  }

  return result;
};
