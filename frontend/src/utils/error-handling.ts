/**
 * Error Handling Utilities
 *
 * Comprehensive error handling utilities for API interactions,
 * user feedback, and error recovery mechanisms.
 */

import { toast } from 'sonner';

/**
 * Standard error types for the application
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Application error interface
 */
export interface AppError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  timestamp: Date;
  context?: string;
}

/**
 * Error handling configuration
 */
interface ErrorConfig {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToService?: boolean;
  retryable?: boolean;
  context?: string;
}

/**
 * Default error configuration
 */
const DEFAULT_ERROR_CONFIG: ErrorConfig = {
  showToast: true,
  logToConsole: true,
  reportToService: false,
  retryable: false,
};

/**
 * Error messages for different error types
 */
const ERROR_MESSAGES = {
  [ErrorType.NETWORK]: 'Network connection error. Please check your internet connection.',
  [ErrorType.AUTHENTICATION]: 'Authentication failed. Please log in again.',
  [ErrorType.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ErrorType.VALIDATION]: 'Invalid data provided. Please check your input.',
  [ErrorType.NOT_FOUND]: 'The requested resource was not found.',
  [ErrorType.SERVER]: 'Server error occurred. Please try again later.',
  [ErrorType.UNKNOWN]: 'An unexpected error occurred. Please try again.',
};

/**
 * Error classification utility
 */
export function classifyError(error: any): ErrorType {
  if (!error) return ErrorType.UNKNOWN;

  // Network errors
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return ErrorType.NETWORK;
  }

  // HTTP status code based classification
  if (error.response?.status) {
    const status = error.response.status;
    
    if (status === 401) return ErrorType.AUTHENTICATION;
    if (status === 403) return ErrorType.AUTHORIZATION;
    if (status === 404) return ErrorType.NOT_FOUND;
    if (status >= 400 && status < 500) return ErrorType.VALIDATION;
    if (status >= 500) return ErrorType.SERVER;
  }

  // Axios specific errors
  if (error.code === 'ECONNABORTED') return ErrorType.NETWORK;
  if (error.code === 'ERR_NETWORK') return ErrorType.NETWORK;

  return ErrorType.UNKNOWN;
}

/**
 * Create standardized application error
 */
export function createAppError(
  error: any,
  context?: string,
  customMessage?: string
): AppError {
  const type = classifyError(error);
  const message = customMessage || 
    error.response?.data?.message || 
    error.message || 
    ERROR_MESSAGES[type];

  return {
    type,
    message,
    code: error.response?.status || error.code,
    details: error.response?.data || error,
    timestamp: new Date(),
    context,
  };
}

/**
 * Handle errors with comprehensive error management
 */
export function handleError(
  error: any,
  config: ErrorConfig = {}
): AppError {
  const finalConfig = { ...DEFAULT_ERROR_CONFIG, ...config };
  const appError = createAppError(error, finalConfig.context);

  // Log to console if enabled
  if (finalConfig.logToConsole) {
    console.error(`[${appError.type}] ${appError.context || 'Unknown Context'}:`, {
      message: appError.message,
      code: appError.code,
      details: appError.details,
      timestamp: appError.timestamp,
    });
  }

  // Show toast notification if enabled
  if (finalConfig.showToast) {
    showErrorToast(appError);
  }

  // Report to error service if enabled
  if (finalConfig.reportToService) {
    reportError(appError);
  }

  return appError;
}

/**
 * Show error toast notification
 */
export function showErrorToast(error: AppError): void {
  const title = getErrorTitle(error.type);
  
  toast.error(title, {
    description: error.message,
    duration: getToastDuration(error.type),
    action: error.type === ErrorType.NETWORK ? {
      label: 'Retry',
      onClick: () => window.location.reload(),
    } : undefined,
  });
}

/**
 * Show success toast notification
 */
export function showSuccessToast(message: string, description?: string): void {
  toast.success(message, {
    description,
    duration: 3000,
  });
}

/**
 * Show info toast notification
 */
export function showInfoToast(message: string, description?: string): void {
  toast.info(message, {
    description,
    duration: 4000,
  });
}

/**
 * Show warning toast notification
 */
export function showWarningToast(message: string, description?: string): void {
  toast.warning(message, {
    description,
    duration: 5000,
  });
}

/**
 * Get error title based on error type
 */
function getErrorTitle(type: ErrorType): string {
  switch (type) {
    case ErrorType.NETWORK:
      return 'Connection Error';
    case ErrorType.AUTHENTICATION:
      return 'Authentication Error';
    case ErrorType.AUTHORIZATION:
      return 'Permission Denied';
    case ErrorType.VALIDATION:
      return 'Validation Error';
    case ErrorType.NOT_FOUND:
      return 'Not Found';
    case ErrorType.SERVER:
      return 'Server Error';
    default:
      return 'Error';
  }
}

/**
 * Get toast duration based on error type
 */
function getToastDuration(type: ErrorType): number {
  switch (type) {
    case ErrorType.NETWORK:
      return 8000; // Longer for network issues
    case ErrorType.AUTHENTICATION:
      return 6000;
    case ErrorType.AUTHORIZATION:
      return 6000;
    case ErrorType.SERVER:
      return 7000;
    default:
      return 5000;
  }
}

/**
 * Report error to external service (placeholder)
 */
function reportError(error: AppError): void {
  // TODO: Implement error reporting to external service
  // This could be Sentry, LogRocket, or custom error tracking
  console.log('Error reported:', error);
}

/**
 * Retry mechanism for failed operations
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoff: boolean = true
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Don't retry on certain error types
      const errorType = classifyError(error);
      if (errorType === ErrorType.AUTHENTICATION || 
          errorType === ErrorType.AUTHORIZATION ||
          errorType === ErrorType.VALIDATION) {
        throw error;
      }
      
      // If this was the last attempt, throw the error
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Calculate delay with optional exponential backoff
      const currentDelay = backoff ? delay * Math.pow(2, attempt - 1) : delay;
      
      console.warn(`Operation failed (attempt ${attempt}/${maxRetries}), retrying in ${currentDelay}ms...`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay));
    }
  }
  
  throw lastError;
}

/**
 * Error boundary helper for React components
 */
export function createErrorBoundaryHandler(componentName: string) {
  return (error: Error, errorInfo: any) => {
    const appError = createAppError(error, `React Error Boundary: ${componentName}`);
    handleError(appError, {
      showToast: true,
      logToConsole: true,
      reportToService: true,
    });
  };
}

/**
 * Async error handler for promises
 */
export function handleAsyncError(promise: Promise<any>, context?: string): Promise<any> {
  return promise.catch(error => {
    handleError(error, { context });
    throw error;
  });
}

/**
 * Validation error helper
 */
export function createValidationError(field: string, message: string): AppError {
  return {
    type: ErrorType.VALIDATION,
    message: `${field}: ${message}`,
    timestamp: new Date(),
    context: 'Form Validation',
  };
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: any): boolean {
  const type = classifyError(error);
  return type === ErrorType.NETWORK || type === ErrorType.SERVER;
}

/**
 * Format error for display
 */
export function formatErrorForDisplay(error: AppError): string {
  return `${getErrorTitle(error.type)}: ${error.message}`;
}

/**
 * Error recovery suggestions
 */
export function getErrorRecoverySuggestions(error: AppError): string[] {
  switch (error.type) {
    case ErrorType.NETWORK:
      return [
        'Check your internet connection',
        'Try refreshing the page',
        'Contact support if the problem persists',
      ];
    case ErrorType.AUTHENTICATION:
      return [
        'Log out and log back in',
        'Clear your browser cache',
        'Contact support if you continue having issues',
      ];
    case ErrorType.AUTHORIZATION:
      return [
        'Contact your administrator for access',
        'Verify you have the correct permissions',
        'Try logging out and back in',
      ];
    case ErrorType.VALIDATION:
      return [
        'Check your input data',
        'Ensure all required fields are filled',
        'Verify data formats are correct',
      ];
    case ErrorType.SERVER:
      return [
        'Try again in a few minutes',
        'Contact support if the issue persists',
        'Check our status page for known issues',
      ];
    default:
      return [
        'Try refreshing the page',
        'Contact support if the problem continues',
      ];
  }
}
