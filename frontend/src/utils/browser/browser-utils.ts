/**
 * Browser Utilities
 *
 * This module provides browser-specific utility functions for window management,
 * device detection, and browser API interactions
 */

// Type definitions for DOM APIs
type ScrollBehavior = 'auto' | 'smooth';
import { useLayoutEffect, useState } from 'react';

/**
 * Hook to get current window size and listen for resize events
 *
 * @returns Array containing [width, height] of the window
 */
export function useWindowSize(): [number, number] {
  const [size, setSize] = useState<[number, number]>([0, 0]);

  useLayoutEffect(() => {
    function updateSize() {
      setSize([window.innerWidth, window.innerHeight]);
    }

    window.addEventListener('resize', updateSize);
    updateSize();

    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return size;
}

/**
 * Hook to detect if device is mobile based on window width
 *
 * @param breakpoint - Width breakpoint for mobile detection (default: 768)
 * @returns True if window width is below breakpoint
 */
export function useIsMobile(breakpoint: number = 768): boolean {
  const [width] = useWindowSize();
  return width < breakpoint;
}

/**
 * Hook to detect if device is tablet based on window width
 *
 * @param minBreakpoint - Minimum width for tablet (default: 768)
 * @param maxBreakpoint - Maximum width for tablet (default: 1024)
 * @returns True if window width is between breakpoints
 */
export function useIsTablet(
  minBreakpoint: number = 768,
  maxBreakpoint: number = 1024
): boolean {
  const [width] = useWindowSize();
  return width >= minBreakpoint && width < maxBreakpoint;
}

/**
 * Hook to detect if device is desktop based on window width
 *
 * @param breakpoint - Minimum width for desktop detection (default: 1024)
 * @returns True if window width is above breakpoint
 */
export function useIsDesktop(breakpoint: number = 1024): boolean {
  const [width] = useWindowSize();
  return width >= breakpoint;
}

/**
 * Get device type based on window width
 *
 * @returns Device type: 'mobile', 'tablet', or 'desktop'
 */
export const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  if (typeof window === 'undefined') return 'desktop';

  const width = window.innerWidth;
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
};

/**
 * Get viewport dimensions
 *
 * @returns Object with viewport width and height
 */
export const getViewportSize = (): { width: number; height: number } => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
};

/**
 * Check if code is running in browser environment
 *
 * @returns True if running in browser
 */
export const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

/**
 * Get user agent string
 *
 * @returns User agent string or empty string if not in browser
 */
export const getUserAgent = (): string => {
  return isBrowser() ? navigator.userAgent : '';
};

/**
 * Detect if user is on mobile device (basic detection)
 *
 * @returns True if on mobile device
 */
export const isMobileDevice = (): boolean => {
  if (!isBrowser()) return false;

  const userAgent = getUserAgent().toLowerCase();
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    userAgent
  );
};

/**
 * Detect if user is on iOS device
 *
 * @returns True if on iOS device
 */
export const isIOS = (): boolean => {
  if (!isBrowser()) return false;

  const userAgent = getUserAgent().toLowerCase();
  return /iphone|ipad|ipod/i.test(userAgent);
};

/**
 * Detect if user is on Android device
 *
 * @returns True if on Android device
 */
export const isAndroid = (): boolean => {
  if (!isBrowser()) return false;

  const userAgent = getUserAgent().toLowerCase();
  return /android/i.test(userAgent);
};

/**
 * Get scroll position of window
 *
 * @returns Object with x and y scroll positions
 */
export const getScrollPosition = (): { x: number; y: number } => {
  if (!isBrowser()) return { x: 0, y: 0 };

  return {
    x: window.pageXOffset || document.documentElement.scrollLeft,
    y: window.pageYOffset || document.documentElement.scrollTop,
  };
};

/**
 * Scroll to top of page smoothly
 */
export const scrollToTop = (): void => {
  if (!isBrowser()) return;

  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth',
  });
};

/**
 * Scroll to specific element
 *
 * @param elementId - ID of element to scroll to
 * @param behavior - Scroll behavior ('smooth' or 'auto')
 */
export const scrollToElement = (
  elementId: string,
  behavior: ScrollBehavior = 'smooth'
): void => {
  if (!isBrowser()) return;

  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior });
  }
};

/**
 * Copy text to clipboard
 *
 * @param text - Text to copy to clipboard
 * @returns Promise resolving to success status
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const success = document.execCommand('copy');
      textArea.remove();
      return success;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Download data as file
 *
 * @param data - Data to download
 * @param filename - Name of the file
 * @param mimeType - MIME type of the file
 */
export const downloadFile = (
  data: string | Blob,
  filename: string,
  mimeType: string = 'text/plain'
): void => {
  if (!isBrowser()) return;

  const blob =
    data instanceof Blob ? data : new Blob([data], { type: mimeType });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Check if user prefers reduced motion
 *
 * @returns True if user prefers reduced motion
 */
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Get browser name and version
 *
 * @returns Object containing browser information
 */
export const getBrowserInfo = (): { name: string; version: string } => {
  const userAgent = navigator.userAgent;
  let name = 'Unknown';
  let version = 'Unknown';

  if (userAgent.indexOf('Chrome') > -1) {
    name = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    version = match ? match[1] : 'Unknown';
  } else if (userAgent.indexOf('Firefox') > -1) {
    name = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    version = match ? match[1] : 'Unknown';
  } else if (userAgent.indexOf('Safari') > -1) {
    name = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    version = match ? match[1] : 'Unknown';
  } else if (userAgent.indexOf('Edge') > -1) {
    name = 'Edge';
    const match = userAgent.match(/Edge\/(\d+)/);
    version = match ? match[1] : 'Unknown';
  }

  return { name, version };
};
