/**
 * Translation utilities for accessing localized content
 */

import { Translation } from '@/types/shared';

/**
 * Get the appropriate translation value for a given locale
 * @param translation - Translation object or string value
 * @param locale - Current locale (e.g., 'en', 'zh', 'zh-TW')
 * @param fallback - Fallback value if translation is not found
 * @returns Translated string
 */
export function getTranslation(
  translation: Translation | string,
  locale: string,
  fallback?: string
): string {
  // If it's already a string, return it (for backward compatibility)
  if (typeof translation === 'string') {
    return translation;
  }

  // If it's a Translation object, try to get the appropriate locale
  if (translation && typeof translation === 'object') {
    // Try exact locale match first (e.g., 'zh-TW')
    if (translation[locale]) {
      return translation[locale];
    }

    // Try language without region (e.g., 'zh' from 'zh-TW')
    const language = locale.split('-')[0];
    if (language !== locale && translation[language]) {
      return translation[language];
    }

    // Fallback to English
    if (translation.en) {
      return translation.en;
    }

    // If no English, return first available translation
    const firstAvailable = Object.values(translation).find(val => val);
    if (firstAvailable) {
      return firstAvailable;
    }
  }

  // If all else fails, return fallback or empty string
  return fallback || '';
}

/**
 * Check if a value is a Translation object
 * @param value - Value to check
 * @returns True if the value is a Translation object
 */
export function isTranslation(value: any): value is Translation {
  return value && typeof value === 'object' && typeof value.en === 'string';
}

/**
 * Convert a string to a Translation object with English only
 * @param text - String to convert
 * @returns Translation object
 */
export function stringToTranslation(text: string): Translation {
  return { en: text };
}

/**
 * Hook for using translations in React components
 * @param translation - Translation object or string
 * @param locale - Current locale
 * @returns Translated string
 */
export function useTranslation(
  translation: Translation | string,
  locale: string
): string {
  return getTranslation(translation, locale);
}

/**
 * Re-export Translation type for convenience
 */
export type { Translation } from '@/types/shared';
