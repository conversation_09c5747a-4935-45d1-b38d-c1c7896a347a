/**
 * API Utilities
 *
 * This module provides HTTP client utilities and API service configurations
 */

import axios from 'axios';

// API URL from environment variable with fallback
const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:5550';

/**
 * Axios instance configured with base URL and interceptors
 *
 * @description Main HTTP client for API requests with error handling
 */
const axiosServices = axios.create({
  baseURL: API_BASE_URL,
});

// Request interceptor for authentication and error handling
axiosServices.interceptors.request.use(
  config => {
    // Always include credentials for cookie-based auth (session cookies)
    config.withCredentials = true;

    // Only add Authorization header for OAuth2 endpoints that need JWT tokens
    // Session-based endpoints (admin, user, developer) use cookies
    const isOAuth2Endpoint = config.url?.includes('/oauth2/') ||
                            config.url?.includes('/token') ||
                            config.url?.includes('/userinfo');

    if (isOAuth2Endpoint) {
      const token = localStorage.getItem('session_token') || localStorage.getItem('serviceToken');
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor for error handling
axiosServices.interceptors.response.use(
  response => response,
  error =>
    Promise.reject((error.response && error.response.data) || error.message || 'API Error')
);

export default axiosServices;
