{"id": "geniego-sso-env-2024", "name": "GeNieGO SSO Server - Development", "values": [{"key": "base_url", "value": "http://localhost:5550", "type": "default", "enabled": true}, {"key": "redirect_uri", "value": "http://localhost:5550/auth/callback", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "authorization_code", "value": "", "type": "default", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "client_id", "value": "application-client", "type": "default", "enabled": true}, {"key": "client_secret", "value": "application-secret", "type": "secret", "enabled": true}, {"key": "test_username", "value": "testuser", "type": "default", "enabled": true}, {"key": "test_password", "value": "testpassword123", "type": "secret", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}