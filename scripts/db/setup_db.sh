#!/bin/bash

# Database setup script for GeNieGO SSO Server

echo "🗄️  GeNieGO SSO Server Database Setup"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "🐳 Starting GeNieGO SSO MySQL database..."
docker-compose up -d mysql redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 15

# Check if database is ready
while ! docker-compose exec mysql mysql -u root -pgeniego_sso_root_password -e "SELECT 1" > /dev/null 2>&1; do
    echo "   Database not ready yet, waiting..."
    sleep 5
done

echo "✅ Database is ready!"

# Run migrations
echo "🔄 Running database migrations..."
if ! python -m alembic upgrade head; then
    echo "❌ Migration failed. Please check the error messages above."
    exit 1
fi

echo "✅ Database migrations completed!"

# Install missing dependencies
echo "📦 Installing missing dependencies..."
source venv/bin/activate 2>/dev/null || echo "Virtual environment not activated"
pip install python-jose aiomysql

# Start the GeNieGO SSO Server
echo "🚀 Starting GeNieGO SSO Server..."
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 5550

echo "🎉 Setup complete! Your GeNieGO SSO Server is running at http://localhost:5550"
echo "📚 API documentation available at http://localhost:5550/docs"
echo "🔐 OAuth2 Authorization: http://localhost:5550/oauth2/authorize"
