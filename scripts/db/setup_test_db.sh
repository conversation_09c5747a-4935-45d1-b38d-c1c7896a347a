#!/bin/bash

# MySQL Test Setup Script for GeNieGO SSO Server
# This script ensures MySQL is properly configured for integration testing

echo "🔧 GeNieGO SSO MySQL Test Setup"
echo "==============================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if MySQL container is running
if ! docker ps --filter name=geniengo-sso-mysql --filter status=running | grep -q geniengo-sso-mysql; then
    echo "🐳 Starting GeNieGO SSO MySQL container..."
    docker-compose up -d mysql

    # Wait for MySQL to be ready
    echo "⏳ Waiting for MySQL to be ready..."
    for i in {1..30}; do
        if docker exec geniengo-sso-mysql mysql -u root -pgeniego_sso_root_password -e "SELECT 1" > /dev/null 2>&1; then
            echo "✅ MySQL is ready!"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ MySQL failed to start after 30 attempts"
            exit 1
        fi
        sleep 2
    done
else
    echo "✅ MySQL container is already running"
fi

# Create test database and grant permissions
echo "🔐 Setting up GeNieGO SSO test database permissions..."
docker exec geniengo-sso-mysql mysql -u root -pgeniego_sso_root_password -e "
    CREATE DATABASE IF NOT EXISTS geniego_sso_db_test;
    GRANT ALL PRIVILEGES ON geniego_sso_db_test.* TO 'geniego_sso_user'@'%';
    FLUSH PRIVILEGES;
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Test database permissions configured"
else
    echo "❌ Failed to configure test database permissions"
    exit 1
fi

# Verify test database connection
echo "🔍 Verifying test database connection..."
if docker exec geniengo-sso-mysql mysql -u geniego_sso_user -pgeniego_sso_password -e "USE geniego_sso_db_test; SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Test database connection verified"
else
    echo "❌ Test database connection failed"
    exit 1
fi

echo ""
echo "🎉 GeNieGO SSO MySQL test setup completed successfully!"
echo ""
echo "✅ Key Features:"
echo "  - MySQL 8.0 integration testing for OAuth2 SSO system"
echo "  - Multi-tenant database testing"
echo "  - OAuth2 flow integration testing"
echo "  - Complete user, application, and SSO management testing"
echo ""
echo "🧪 Test Commands:"
echo "  make test                                            # Run all tests"
echo "  python -m pytest tests/test_oauth2.py -v            # OAuth2-specific tests"
echo "  python -m pytest tests/test_users.py -v             # User management tests"
echo "  python -m pytest tests/test_applications.py -v      # Application tests"
echo ""
echo "📊 Container Details:"
echo "  - MySQL Host: localhost:3306"
echo "  - Test Database: geniego_sso_db_test"
echo "  - User: geniego_sso_user"
echo "  - Password: geniego_sso_password"
echo "  - API URL: http://localhost:5550"
