# GeNieGO - Scripts

This directory contains all utility scripts organized by purpose following FastAPI best practices.

## 📂 Directory Structure

```
scripts/
├── dev/           # Development and testing scripts
├── db/            # Database setup and management scripts
├── deploy/        # Deployment and infrastructure scripts
└── README.md      # This file
```



## 🔧 Development Scripts (`scripts/dev/`)

### `reset_db.py`
**Purpose**: Database reset script for development
**Usage**: `python scripts/dev/reset_db.py`
**Environment**: Development only
**Description**: Drops all tables and recreates them with default data

### `test-email-service.py`
**Purpose**: Email service testing (local environment)
**Usage**: `python scripts/dev/test-email-service.py`
**Environment**: Development/Testing
**Description**: Tests SMTP configuration and email sending functionality locally

### `test-email-docker.sh`
**Purpose**: Email service testing via Docker container
**Usage**: `./scripts/dev/test-email-docker.sh`
**Environment**: Docker/Production-like
**Description**: Tests email sending inside Docker container with production environment

### `translation-key-manager.sh`
**Purpose**: Translation key analysis and management tool
**Usage**: `./scripts/dev/translation-key-manager.sh [command] [options]`
**Environment**: Development/Maintenance
**Description**: Manages translation keys across all localization files

**Commands:**
- `duplicates [--remove]` - Find (and optionally remove) duplicate keys within each language
- `unused [--remove]` - Find (and optionally remove) unused keys not referenced in code
- `missing` - Find missing keys used in code but not in translation files
- `all` - Run all checks (duplicates, unused, missing)

**Options:**
- `--remove` - Apply changes (remove duplicates/unused keys)
- `--verbose` - Show detailed output
- `--help` - Show help message

**Examples:**
```bash
# Find duplicate keys
./scripts/dev/translation-key-manager.sh duplicates

# Remove duplicate keys
./scripts/dev/translation-key-manager.sh duplicates --remove

# Find unused keys with details
./scripts/dev/translation-key-manager.sh unused --verbose

# Remove unused keys
./scripts/dev/translation-key-manager.sh unused --remove

# Find missing keys (never added automatically)
./scripts/dev/translation-key-manager.sh missing

# Run all checks
./scripts/dev/translation-key-manager.sh all
```

**Key Features:**
- **Scalable**: Dynamically detects all languages (not hardcoded)
- **Safe**: Missing keys are never added automatically
- **Accurate**: Handles mixed translation key patterns (direct keys vs prefixed keys)
- **Flexible**: Can find issues or apply fixes with --remove flag
- **Comprehensive**: Accounts for automatic prefixing system from `locales.tsx`

**Requirements:** `jq` (JSON processor)
- macOS: `brew install jq`
- Ubuntu: `apt-get install jq`

### `reset_tokens.py`
**Purpose**: Clear OAuth2 tokens and testing data during development
**Usage**: `python scripts/dev/reset_tokens.py`
**Environment**: Development/Testing only
**Description**: Interactive script to clean up tokens between test cycles

### `test-api.sh`
**Purpose**: API endpoint testing script
**Usage**: `bash scripts/dev/test-api.sh`
**Environment**: Development/Testing
**Description**: Tests core API endpoints and functionality

### `test-postman-endpoints.sh`
**Purpose**: Run Postman collection tests
**Usage**: `bash scripts/dev/test-postman-endpoints.sh`
**Environment**: Development/Testing
**Description**: Executes the Postman test collection

## 🗄️ Database Scripts (`scripts/db/`)

### `setup_db.sh`
**Purpose**: Initialize production/development database
**Usage**: `bash scripts/db/setup_db.sh`
**Environment**: Development/Production
**Description**: Sets up MySQL database, runs migrations, starts application

### `setup_test_db.sh`
**Purpose**: Initialize test database environment
**Usage**: `bash scripts/db/setup_test_db.sh`
**Environment**: Testing
**Description**: Sets up isolated test database for running tests

## 🚀 Deployment Scripts (`scripts/deploy/`)

### `docker.sh`
**Purpose**: Docker container management
**Usage**: `bash scripts/deploy/docker.sh`
**Environment**: Production/Development
**Description**: Manages Docker containers and services

### `start.sh`
**Purpose**: Application startup script
**Usage**: `bash scripts/deploy/start.sh`
**Environment**: Production/Development
**Description**: Starts the GeNieGO backend application

---

## 🚀 Quick Start

### Development Setup
```bash
# 1. Setup database
bash scripts/db/setup_db.sh

# 2. Run tests
bash scripts/dev/test-api.sh

# 3. Clean up test data
python scripts/dev/reset_tokens.py
```

### Production Deployment
```bash
# 1. Setup database
bash scripts/db/setup_db.sh

# 2. Start application
bash scripts/deploy/start.sh
```

### Docker Development
```bash
# 1. Start with Docker
bash scripts/deploy/docker.sh

# 2. Run tests
bash scripts/dev/test-postman-endpoints.sh
```

---

## 📋 Script Dependencies

### Required for all scripts:
- Bash shell environment
- Python 3.8+ (for Python scripts)
- Docker and Docker Compose (for Docker scripts)
- MySQL client (for database scripts)

### Environment files:
- `.env` - Environment configuration
- `docker-compose.yml` - Docker services
- `requirements.txt` - Python dependencies

---

## ⚠️ Important Notes

### Development Scripts
- **Never run development scripts in production**
- Test scripts may modify or clear data
- Always backup data before running destructive operations

### Database Scripts
- Ensure MySQL is running before executing database scripts
- Check connection settings in `.env` file
- Database scripts will create/modify database structure

### Deployment Scripts
- Review configuration before production deployment
- Ensure all environment variables are properly set
- Monitor logs during deployment

---

## 🔐 Security Considerations

- Scripts may contain sensitive operations
- Review script contents before execution
- Use appropriate permissions (executable only for owner)
- Never commit credentials in scripts
- Use environment variables for configuration

---

## 📝 Usage Examples

### Development Workflow
```bash
# Start development environment
bash scripts/db/setup_db.sh

# Code and test...
# (application running at http://localhost:5001)

# Run API tests
bash scripts/dev/test-api.sh

# Clean up test tokens
python scripts/dev/reset_tokens.py

# Run comprehensive tests
bash scripts/dev/test-postman-endpoints.sh
```

### Production Deployment
```bash
# Deploy with Docker
bash scripts/deploy/docker.sh

# Or deploy manually
bash scripts/db/setup_db.sh
bash scripts/deploy/start.sh
```

---

## 🐛 Troubleshooting

### Common Issues

**Permission Denied**
```bash
chmod +x scripts/*/*.sh scripts/*/*.py
```

**Database Connection Failed**
```bash
# Check MySQL is running
docker-compose ps mysql

# Check connection settings
cat .env | grep DATABASE
```

**Port Already in Use**
```bash
# Check what's using port 5001
lsof -i :5001

# Kill process if needed
kill -9 <PID>
```

---

**Last Updated**: January 20, 2025  
**Version**: 1.0.0  
**Compatibility**: GeNieGO v2.0.0+ 