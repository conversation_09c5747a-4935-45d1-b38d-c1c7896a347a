#!/bin/bash

# Docker management script for GeNieGO

COMPOSE_FILE="docker-compose.yml"

show_usage() {
    echo "Docker Management Script for GeNieGO"
    echo "=============================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start              Start development environment (api-server + mysql + redis)"
    echo "  stop               Stop all services"
    echo "  status             Show status of all containers"
    echo "  logs               Show logs for all services"
    echo "  logs [service]     Show logs for specific service (api-server|mysql|redis)"
    echo "  clean              Stop and remove containers, networks, and volumes"
    echo ""
    echo "Examples:"
    echo "  $0 start           # Start development environment"
    echo "  $0 logs api-server # Show api-server logs"
    echo "  $0 clean           # Clean up everything"
}

check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

start_dev() {
    echo "🚀 Starting GeNieGO SSO Server development environment..."
    check_docker
    docker compose -f $COMPOSE_FILE up -d --build
    echo ""
    echo "⏳ Waiting for services to be ready..."
    sleep 15
    
    if curl -f http://localhost:5550/health > /dev/null 2>&1; then
        echo "✅ GeNieGO SSO Server services are running!"
        docker compose ps
        echo ""
        echo "🌐 GeNieGO SSO API: http://localhost:5550"
        echo "📖 Documentation: http://localhost:5550/docs"
        echo "🔐 OAuth2 Authorization: http://localhost:5550/oauth2/authorize"
        echo "🏥 Health Check: http://localhost:5550/health"
        echo "🗄️  MySQL Database: localhost:3306"
        echo "🔧 Redis: localhost:6379"
    else
        echo "❌ Services failed to start. Check logs with: $0 logs"
    fi
}

# Main command handling
case "$1" in
    "start")
        start_dev
        ;;
    "stop")
        echo "🛑 Stopping GeNieGO SSO Server services..."
        docker compose -f $COMPOSE_FILE down
        echo "✅ Services stopped"
        ;;
    "status")
        echo "📊 GeNieGO SSO Server Container Status:"
        docker compose -f $COMPOSE_FILE ps
        ;;
    "logs")
        if [ -n "$2" ]; then
            echo "📋 Showing logs for $2..."
            docker compose -f $COMPOSE_FILE logs -f "$2"
        else
            echo "📋 Showing logs for all GeNieGO SSO Server services..."
            docker compose -f $COMPOSE_FILE logs -f
        fi
        ;;
    "clean")
        echo "🧹 Cleaning up GeNieGO SSO Server containers, networks, and volumes..."
        docker compose -f $COMPOSE_FILE down -v --remove-orphans
        echo "✅ Cleanup completed"
        ;;
    "help"|"--help"|"-h")
        show_usage
        ;;
    "")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
