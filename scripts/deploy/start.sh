#!/bin/bash

# GeNieGO Startup Script

echo "🚀 Starting GeNieGO..."

# Function to start with Python directly
start_with_python() {
    echo "🐍 Starting with Python virtual environment..."
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "📦 Creating virtual environment..."
        python3 -m venv venv
    fi
    
    echo "🔧 Activating virtual environment..."
    source venv/bin/activate
    
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
    
    echo "🚀 Starting GeNieGO server..."
    echo "✅ Server will be available at: http://localhost:5001"
    echo "📖 API Documentation: http://localhost:5001/docs"
    echo "🔐 OAuth2 Authorization: http://localhost:5001/oauth/authorize"
    echo "🏥 Health Check: http://localhost:5001/health"
    echo ""
    echo "Press Ctrl+C to stop the server"
    echo ""
    
    uvicorn app.main:app --host 0.0.0.0 --port 5001 --reload
}

# Check command line arguments
if [ "$1" = "--docker" ]; then
    echo "ℹ️  For Docker management, please use: bash scripts/deploy/docker.sh start"
    echo "ℹ️  This provides better Docker workflow management"
    exit 0
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "GeNieGO Startup Script"
    echo "==============================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h    Show this help message"
    echo ""
    echo "For Docker deployment, use: bash scripts/deploy/docker.sh start"
echo "For API testing, use: bash scripts/dev/test-api.sh"
    echo ""
    echo "Default: Starts with Python virtual environment on port 5001"
    exit 0
else
    echo "🐍 Starting GeNieGO with Python virtual environment..."
    echo "💡 Tip: Use 'bash scripts/deploy/docker.sh start' for Docker deployment"
    echo ""
    start_with_python
fi
