#!/usr/bin/env python3
"""
Database reset script for development.
This script drops all tables and recreates them with default data.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import get_settings
from app.core.database import create_tables, drop_tables, get_session_factory
from app.config.sso_defaults import initialize_default_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def reset_database():
    """Reset the database by dropping and recreating all tables."""
    try:
        logger.info("🗑️  Dropping all database tables...")
        await drop_tables()
        
        logger.info("🔨 Creating all database tables...")
        await create_tables()
        
        logger.info("📊 Initializing default data...")
        session_factory = get_session_factory()
        async with session_factory() as db:
            if await initialize_default_data(db):
                logger.info("✅ Database reset completed successfully!")
                logger.info("📧 Default admin: GenieAdmin / GenieAdmin123!")
                logger.info("👨‍💻 Default developer: GenieDeveloper / GenieDev123!")
                logger.info("👤 Default user: GenieUser / GenieUser123!")
                return True
            else:
                logger.error("❌ Failed to initialize default data")
                return False
                
    except Exception as e:
        logger.error(f"❌ Database reset failed: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(reset_database())
