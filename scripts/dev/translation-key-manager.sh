#!/bin/bash

# Translation Key Manager
# Optimized script to manage translation keys across localization system
#
# Usage: ./scripts/translation-key-manager.sh [command] [options]
#
# Commands:
#   duplicates [--remove]  - Find (and optionally remove) duplicate keys within each language
#   unused [--remove]      - Find (and optionally remove) unused keys not referenced in code
#   missing                - Find missing keys used in code but not in translation files
#   all                    - Run all checks (duplicates, unused, missing)
#
# Options:
#   --remove    Apply changes (remove duplicates/unused keys)
#   --verbose   Show detailed output
#   --help      Show this help message

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOCALES_DIR="$FRONTEND_DIR/src/utils/localization/locales"
COMPONENTS_DIR="$FRONTEND_DIR/src/components"
PAGES_DIR="$FRONTEND_DIR/src/pages"

# Options
REMOVE_ACTION=false
VERBOSE=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_verbose() { if [[ "$VERBOSE" == true ]]; then echo -e "${BLUE}[VERBOSE]${NC} $1"; fi; }

# Help function
show_help() {
    cat << EOF
Translation Key Manager

Manages translation keys across the localization system:
1. Find duplicate keys between .json files within each language
2. Remove unused keys not referenced in components/pages
3. Find missing keys used in code but not in translation files

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    duplicates [--remove]  Find duplicate keys within each language
    unused [--remove]      Find unused keys not referenced in code
    missing                Find missing keys used in code
    all                    Run all checks

Options:
    --remove     Apply changes (remove duplicates/unused keys)
    --verbose    Show detailed output
    --help       Show this help message

Examples:
    $0 duplicates                    # Find duplicate keys
    $0 duplicates --remove           # Remove duplicate keys
    $0 unused --verbose              # Find unused keys with details
    $0 unused --remove               # Remove unused keys
    $0 missing                       # Find missing keys
    $0 all                           # Run all checks

Note: Missing keys are never added automatically - you must add them manually.
EOF
}

# Get all supported languages dynamically
get_languages() {
    find "$LOCALES_DIR" -maxdepth 1 -type d -not -path "$LOCALES_DIR" -exec basename {} \; | sort
}

# Get all translation keys used in source files
get_used_keys() {
    log_verbose "Scanning source files for translation keys..."

    find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \) -print0 | \
        xargs -0 grep -oE "intl.formatMessage({ id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
        cut -d"'" -f2 | \
        sort -u || true
}

# Get all available translation keys for a language (accounting for prefixing system)
get_available_keys() {
    local lang="$1"

    # Get keys from root-level JSON files (direct keys)
    find "$LOCALES_DIR/$lang" -maxdepth 1 -name "*.json" -type f | while read -r json_file; do
        jq -r 'keys[]' "$json_file" 2>/dev/null | grep -v '^//' || true
    done

    # Get keys from nested subcategory files (with automatic prefixing)
    find "$LOCALES_DIR/$lang" -mindepth 2 -name "*.json" -type f | while read -r json_file; do
        local relative_path="${json_file#$LOCALES_DIR/$lang/}"
        local parent_dir=$(dirname "$relative_path")

        jq -r 'keys[]' "$json_file" 2>/dev/null | grep -v '^//' | while read -r key; do
            if [[ -n "$key" ]]; then
                # Apply prefixing logic based on directory structure
                if [[ "$key" == "$parent_dir."* ]]; then
                    echo "$key"
                else
                    echo "$parent_dir.$key"
                fi
            fi
        done || true
    done
}

# Find duplicate keys within each language
find_duplicates() {
    log_info "Finding duplicate keys within each language..."

    local languages=()
    while IFS= read -r lang; do
        languages+=("$lang")
    done < <(get_languages)

    local duplicates_found=false

    for lang in "${languages[@]}"; do
        log_info "Checking language: $lang"

        local temp_file=$(mktemp)

        # Get all keys with their source files
        find "$LOCALES_DIR/$lang" -name "*.json" -type f | while read -r json_file; do
            local relative_path="${json_file#$LOCALES_DIR/$lang/}"
            jq -r 'keys[]' "$json_file" 2>/dev/null | grep -v '^//' | while read -r key; do
                if [[ -n "$key" ]]; then
                    echo "$key|$relative_path"
                fi
            done || true
        done | sort > "$temp_file"

        # Find duplicates
        local lang_duplicates_found=false
        cut -d'|' -f1 "$temp_file" | sort | uniq -d | while read -r dup_key; do
            if [[ -n "$dup_key" ]]; then
                log_warning "Duplicate key '$dup_key' in language '$lang' found in:"
                grep "^$dup_key|" "$temp_file" | cut -d'|' -f2 | while read -r file; do
                    log_warning "  - $file"
                done
                lang_duplicates_found=true
                duplicates_found=true

                # Remove duplicates if requested
                if [[ "$REMOVE_ACTION" == true ]]; then
                    # Keep the key in the first file, remove from others
                    local first_file=""
                    local other_files=()
                    while IFS= read -r file; do
                        if [[ -z "$first_file" ]]; then
                            first_file="$file"
                        else
                            other_files+=("$file")
                        fi
                    done < <(grep "^$dup_key|" "$temp_file" | cut -d'|' -f2)

                    for file in "${other_files[@]}"; do
                        local full_path="$LOCALES_DIR/$lang/$file"
                        log_verbose "Removing '$dup_key' from $file"
                        local temp_json=$(mktemp)
                        jq --arg key "$dup_key" 'del(.[$key])' "$full_path" > "$temp_json"
                        mv "$temp_json" "$full_path"
                    done
                    log_success "Removed duplicate '$dup_key' from ${#other_files[@]} files in $lang"
                fi
            fi
        done

        rm -f "$temp_file"

        if [[ "$lang_duplicates_found" == false ]]; then
            log_success "No duplicate keys found in language: $lang"
        fi
    done

    if [[ "$duplicates_found" == false ]]; then
        log_success "No duplicate keys found in any language"
    fi
}

# Find unused keys
find_unused() {
    log_info "Finding unused keys..."

    local used_keys=()
    while IFS= read -r key; do
        [[ -n "$key" ]] && used_keys+=("$key")
    done < <(get_used_keys)

    if [[ ${#used_keys[@]} -eq 0 ]]; then
        log_warning "No translation keys found in source code"
        return
    fi

    log_verbose "Found ${#used_keys[@]} used translation keys"

    local languages=()
    while IFS= read -r lang; do
        languages+=("$lang")
    done < <(get_languages)

    for lang in "${languages[@]}"; do
        log_info "Checking unused keys in language: $lang"

        local available_keys=()
        while IFS= read -r key; do
            [[ -n "$key" ]] && available_keys+=("$key")
        done < <(get_available_keys "$lang")

        local unused_keys=()
        for available_key in "${available_keys[@]}"; do
            local found=false
            for used_key in "${used_keys[@]}"; do
                if [[ "$used_key" == "$available_key" ]]; then
                    found=true
                    break
                fi
            done
            if [[ "$found" == false ]]; then
                unused_keys+=("$available_key")
            fi
        done

        if [[ ${#unused_keys[@]} -gt 0 ]]; then
            log_warning "Found ${#unused_keys[@]} unused keys in language: $lang"
            for key in "${unused_keys[@]}"; do
                log_verbose "  - $key"
            done

            if [[ "$REMOVE_ACTION" == true ]]; then
                log_warning "Unused key removal not fully implemented yet - manual removal required"
            fi
        else
            log_success "No unused keys found in language: $lang"
        fi
    done
}

# Find missing keys
find_missing() {
    log_info "Finding missing keys..."

    local used_keys=()
    while IFS= read -r key; do
        [[ -n "$key" ]] && used_keys+=("$key")
    done < <(get_used_keys)

    if [[ ${#used_keys[@]} -eq 0 ]]; then
        log_warning "No translation keys found in source code"
        return
    fi

    local languages=()
    while IFS= read -r lang; do
        languages+=("$lang")
    done < <(get_languages)

    for lang in "${languages[@]}"; do
        log_info "Checking missing keys in language: $lang"

        local available_keys=()
        while IFS= read -r key; do
            [[ -n "$key" ]] && available_keys+=("$key")
        done < <(get_available_keys "$lang")

        local missing_keys=()
        for used_key in "${used_keys[@]}"; do
            local found=false
            for available_key in "${available_keys[@]}"; do
                if [[ "$available_key" == "$used_key" ]]; then
                    found=true
                    break
                fi
            done
            if [[ "$found" == false ]]; then
                missing_keys+=("$used_key")
            fi
        done

        if [[ ${#missing_keys[@]} -gt 0 ]]; then
            log_warning "Found ${#missing_keys[@]} missing keys in language: $lang"
            for key in "${missing_keys[@]}"; do
                log_warning "  - $key"
            done
            log_info "Note: Missing keys must be added manually - automatic addition is disabled"
        else
            log_success "No missing keys found in language: $lang"
        fi
    done
}

# Parse command line arguments
parse_args() {
    local command=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            duplicates|unused|missing|all)
                command="$1"
                shift
                ;;
            --remove)
                REMOVE_ACTION=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    if [[ -z "$command" ]]; then
        log_error "No command specified"
        show_help
        exit 1
    fi

    echo "$command"
}

# Check dependencies
check_dependencies() {
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install it:"
        log_info "  macOS: brew install jq"
        log_info "  Ubuntu: apt-get install jq"
        exit 1
    fi
}

# Validate project structure
validate_structure() {
    if [[ ! -d "$LOCALES_DIR" ]]; then
        log_error "Locales directory not found: $LOCALES_DIR"
        exit 1
    fi

    if [[ ! -d "$COMPONENTS_DIR" ]]; then
        log_error "Components directory not found: $COMPONENTS_DIR"
        exit 1
    fi

    if [[ ! -d "$PAGES_DIR" ]]; then
        log_error "Pages directory not found: $PAGES_DIR"
        exit 1
    fi
}

# Main function
main() {
    local command
    command=$(parse_args "$@")

    check_dependencies
    validate_structure

    log_info "Starting Translation Key Manager"

    if [[ "$REMOVE_ACTION" == true ]]; then
        log_warning "Remove action enabled - changes will be made to files"
    fi

    case "$command" in
        duplicates)
            find_duplicates
            ;;
        unused)
            find_unused
            ;;
        missing)
            find_missing
            ;;
        all)
            find_duplicates
            find_unused
            find_missing
            ;;
    esac

    log_success "Translation Key Manager completed"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi