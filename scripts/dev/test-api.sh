#!/bin/bash

# API testing script for GeNieGO SSO Server

API_BASE="http://localhost:5550"
CONTENT_TYPE="Content-Type: application/json"

echo "🚀 Testing GeNieGO SSO Server API"
echo "================================="

# Test 1: Health Check
echo ""
echo "1️⃣ Testing Health Check..."
echo "Request: GET $API_BASE/health"
HEALTH_RESPONSE=$(curl -s -X GET "$API_BASE/health")
echo "Response: $HEALTH_RESPONSE"

# Test 2: Root endpoint
echo ""
echo "2️⃣ Testing Root Endpoint..."
echo "Request: GET $API_BASE/"
ROOT_RESPONSE=$(curl -s -X GET "$API_BASE/")
echo "Response: [HTML Content]"

# Test 3: Documentation endpoints
echo ""
echo "3️⃣ Testing Documentation Endpoints..."
echo "Request: GET $API_BASE/docs"
DOCS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/docs")
echo "Response Status: $DOCS_STATUS"

echo ""
echo "Request: GET $API_BASE/redoc"
REDOC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/redoc")
echo "Response Status: $REDOC_STATUS"

# Test 4: OAuth2 Authorization Endpoint
echo ""
echo "4️⃣ Testing OAuth2 Authorization Endpoint..."
echo "Request: GET $API_BASE/oauth/authorize?client_id=move-web-client&response_type=code&redirect_uri=http://localhost:5550/auth/callback"
AUTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/oauth/authorize?client_id=move-web-client&response_type=code&redirect_uri=http://localhost:5550/auth/callback")
echo "Response Status: $AUTH_STATUS"

# Test 5: OAuth2 Token Endpoint (without credentials - should fail)
echo ""
echo "5️⃣ Testing OAuth2 Token Endpoint..."
echo "Request: POST $API_BASE/oauth/token"
TOKEN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_BASE/oauth/token" -H "$CONTENT_TYPE" -d '{"grant_type":"authorization_code","code":"test","client_id":"move-web-client"}')
echo "Response Status: $TOKEN_STATUS (Expected 400/401 - no valid credentials)"

# Test 6: User Info Endpoint (without token - should fail)
echo ""
echo "6️⃣ Testing User Info Endpoint..."
echo "Request: GET $API_BASE/oauth/userinfo"
USERINFO_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/oauth/userinfo")
echo "Response Status: $USERINFO_STATUS (Expected 401 - no token)"

# Test 7: Check Username Availability
echo ""
echo "7️⃣ Testing Username Availability Check..."
echo "Request: POST $API_BASE/oauth/checkUsername"
USERNAME_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_BASE/oauth/checkUsername" -H "$CONTENT_TYPE" -d '{"username":"testuser"}')
echo "Response Status: $USERNAME_STATUS"

echo ""
echo "✅ GeNieGO API testing completed!"
echo "📖 For more detailed testing, visit: $API_BASE/docs"
echo "🔐 OAuth2 Authorization: $API_BASE/oauth/authorize"
echo ""
