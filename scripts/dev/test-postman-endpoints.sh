#!/bin/bash

# Comprehensive API Testing Script for GeNieGO
# Tests all endpoints in the Postman collection against Docker environment

echo "🧪 GeNieGO - Comprehensive API Testing"
echo "================================================"

BASE_URL="http://localhost:5001"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local expected_status=$4
    local headers=$5
    local data=$6
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing: $description... "
    
    if [ "$method" = "GET" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -w "%{http_code}" -H "$headers" "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "%{http_code}" "$BASE_URL$endpoint")
        fi
    elif [ "$method" = "POST" ]; then
        if [ -n "$data" ]; then
            if [ -n "$headers" ]; then
                response=$(curl -s -w "%{http_code}" -X POST -H "$headers" -d "$data" "$BASE_URL$endpoint")
            else
                response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/x-www-form-urlencoded" -d "$data" "$BASE_URL$endpoint")
            fi
        else
            response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL$endpoint")
        fi
    elif [ "$method" = "DELETE" ]; then
        response=$(curl -s -w "%{http_code}" -X DELETE -H "$headers" "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last 3 characters)
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "   Response: ${response_body:0:100}..."
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

echo "🔧 Starting Docker services..."
    bash scripts/deploy/docker.sh start > /dev/null 2>&1

echo "⏳ Waiting for services to be ready..."
sleep 10

echo ""
echo "🏥 Testing Health Endpoints"
echo "=========================="
test_endpoint "GET" "/health" "Basic Health Check" "200"
test_endpoint "GET" "/api/health" "API Health Check" "200"

echo ""
echo "🔐 Testing OAuth2 Authentication"
echo "==============================="

# Get access token first
echo "Getting access token..."
TOKEN_RESPONSE=$(curl -s -X POST "$BASE_URL/oauth2/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=system&client_secret=system-secret-change-in-production")

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token' 2>/dev/null)

if [ "$ACCESS_TOKEN" = "null" ] || [ -z "$ACCESS_TOKEN" ]; then
    echo -e "${RED}❌ Failed to get access token${NC}"
    echo "Response: $TOKEN_RESPONSE"
    exit 1
else
    echo -e "${GREEN}✅ Access token obtained${NC}"
fi

test_endpoint "POST" "/oauth2/token" "OAuth2 Token Endpoint" "200" "" "grant_type=client_credentials&client_id=system&client_secret=system-secret-change-in-production"
test_endpoint "GET" "/oauth2/userinfo" "OAuth2 User Info" "200" "Authorization: Bearer $ACCESS_TOKEN"

echo ""
echo "👥 Testing User Management"
echo "========================="
test_endpoint "GET" "/api/v1/users" "List Users" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "POST" "/api/v1/users/check-username" "Check Username" "422" "Authorization: Bearer $ACCESS_TOKEN" # No data provided, expect validation error

echo ""
echo "🏢 Testing Organisation Management"  
echo "================================="
test_endpoint "GET" "/api/v1/organisations" "Get Current Organisation" "422" "Authorization: Bearer $ACCESS_TOKEN" # System client has no org
test_endpoint "POST" "/api/v1/organisations/query" "Query Organisations" "422" "Authorization: Bearer $ACCESS_TOKEN" # No data provided

echo ""
echo "📄 Testing License Management"
echo "============================"
test_endpoint "POST" "/api/v1/licenses/license" "Create License" "422" "Authorization: Bearer $ACCESS_TOKEN" # No data provided
test_endpoint "GET" "/api/v1/licenses/license/quota" "License Quota" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/v1/licenses/license/dashboard" "License Dashboard" "200" "Authorization: Bearer $ACCESS_TOKEN"

echo ""
echo "🔧 Testing Debug Endpoints (Development Only)"
echo "============================================="
test_endpoint "GET" "/api/debug/" "Debug Overview" "200"
test_endpoint "GET" "/api/debug/system/simple" "Simple Connectivity Test" "200"
test_endpoint "GET" "/api/debug/auth/test-auth" "Test Authentication" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/auth/jwt-decode" "JWT Token Debug" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/token/debug-tokens" "Debug Token Storage" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/token/validate-token" "Validate Token" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/token/test-token" "Test Token (Compatibility)" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/system/config" "Debug Configuration" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/system/metrics" "Application Metrics" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/system/redis" "Redis Status" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "GET" "/api/debug/system/tasks" "Background Tasks Status" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "DELETE" "/api/debug/system/cache" "Clear Cache" "200" "Authorization: Bearer $ACCESS_TOKEN"
test_endpoint "POST" "/api/debug/system/tasks/test" "Create Test Background Task" "200" "Authorization: Bearer $ACCESS_TOKEN"

echo ""
echo "📊 Test Results Summary"
echo "======================"
echo -e "Total Tests: ${YELLOW}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"  
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! The Postman collection should work perfectly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please check the endpoints above.${NC}"
    exit 1
fi
