FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN addgroup --gid 1000 appuser && \
    adduser --disabled-password --gecos '' --uid 1000 --gid 1000 appuser && \
    chown -R appuser:appuser /app
USER appuser

# Expose port 5550 for GeNieGO SSO Server
EXPOSE 5550

# Run the GeNieGO SSO Server application with startup delay for database readiness
CMD ["sh", "-c", "sleep 5 && uvicorn app.main:app --host 0.0.0.0 --port 5550"]
