---
description: 
globs: 
alwaysApply: false
---
# Git Commit Message Guidelines

When asking for a git commit message, follow these instructions:

@git-commit - Write a commit message that follows these best practices:

0. Run `git status` to understand the changes we have made
1. Scale the subject line based on change complexity:
   - Small changes: Keep under 50 characters
   - Medium changes: Up to 72 characters 
   - Complex changes: Up to 100 characters
2. Use the imperative mood (e.g., "Add feature" not "Added feature")
3. Capitalize the first word in the subject line
4. No period at the end of the subject line
5. Be specific about what changed but adjust detail level to match complexity
6. For non-trivial changes, add a body separated by a blank line that explains:
   - What changed in detail
   - Why the change was necessary
   - Any important implementation details
7. Focus on what and why, not how

## Commit Type Prefixes

Include one of these common prefixes:

- `feat:` - A new feature 
- `fix:` - A bug fix
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `perf:` - Performance improvements
- `test:` - Adding/updating tests
- `chore:` - Maintenance tasks
- `ci:` - CI/CD changes
- `build:` - Build system changes
- `revert:` - Reverting a commit

## Message Structure For Complex Changes
```
<type>: <concise summary>

<detailed explanation>

<additional notes, breaking changes, related issues>
```

## Example Good Messages:
```
# Simple change
fix: Resolve navigation bug in mobile view

# Medium complexity
feat: Add user authentication with email verification

# Complex change
refactor: Restructure data processing pipeline for improved performance

The previous implementation had O(n²) complexity which caused timeouts
with large datasets. This change implements a more efficient algorithm
that reduces processing time by 75%.

Related to issue #142
```

## Example Bad Messages:
```
updated stuff
fixed a bug that was happening sometimes when users click
Added a lot of new features for the authentication system that I spent all day working on yesterday
```

## Final Step
After drafting the commit message, run `git add .` and `git commit -m "COMMIT MESSAGE"` using the message you write. For complex messages with multiple paragraphs, use:
```
git commit -m "Subject line" -m "Body paragraph" -m "Additional notes"
```
Or use the commit editor with `git commit` (no -m flag). 