---
description: 
globs: 
alwaysApply: false
---

# React Component Structure and Naming Rules

## 🚨 CRITICAL: Always Follow These Rules When Creating New Components

### **1. Component File Naming**
- ✅ **USE**: `kebab-case` for all component files
- ❌ **DON'T USE**: `PascalCase` for file names
- **Examples**:
  - ✅ `role-transition-form.tsx`
  - ✅ `security-dashboard.tsx`
  - ❌ `RoleTransitionForm.tsx`

### **2. Component Location Structure**
- **Base Path**: `frontend/src/components/features/`
- **Organization by User Role/Context**:
  - `features/auth/` - Authentication-related components (login, MFA verification)
  - `features/user/` - User-specific components (settings, applications, MFA management)
  - `features/admin/` - Admin-specific components (dashboards, management)
  - `features/developer/` - Developer-specific components
  - `features/common/` - Shared components across roles

### **3. Service File Naming**
- ✅ **USE**: `name-api.ts` suffix
- ❌ **DON'T USE**: `name-service.ts` suffix
- **Examples**:
  - ✅ `mfa-api.ts`
  - ✅ `role-transition-api.ts`
  - ✅ `security-api.ts`
  - ❌ `mfa-service.ts`
  - ❌ `role-transition-service.ts`

### **4. Translation File Organization**
- **Base Path**: `frontend/src/utils/localization/locales/`
- **Structure**: `{language}/{context}/{feature}.json`
- **Contexts**:
  - `user/` - User-facing translations
  - `admin/` - Admin-facing translations
  - `developer/` - Developer-facing translations
  - `components/` - Shared component translations
- **Examples**:
  - ✅ `en/user/twofa.json` (extend existing files when possible)
  - ✅ `en/user/role-transitions.json`
  - ✅ `en/admin/security.json`
  - ❌ `en/mfa.json` (top-level feature files)

### **5. Translation Key Naming**
- **Pattern**: `{context}.{feature}.{section}.{key}`
- **Examples**:
  - ✅ `user.twofa.setup.title`
  - ✅ `admin.security.dashboard.title`
  - ✅ `user.role.transition.form.submit`
  - ❌ `mfa.setup.title` (missing context)

### **6. Component Export Naming**
- **Component Name**: Use `PascalCase` for the actual component name
- **File Name**: Use `kebab-case` for the file name
- **Example**:
  ```typescript
  // File: mfa-setup.tsx
  export const MFASetup: React.FC<MFASetupProps> = ({ ... }) => {
    // component implementation
  };
  ```

### **7. Service Export Pattern**
- **Class Name**: `{Feature}ApiService`
- **Instance Name**: `{feature}Api`
- **Example**:
  ```typescript
  // File: mfa-api.ts
  export class MFAApiService {
    // implementation
  }
  export const mfaApi = new MFAApiService();
  ```

### **8. Locales.tsx Updates**
- **Always update** `frontend/src/components/base/locales.tsx`
- **Add new subcategories** to the `nestedCategories` array
- **Example**:
  ```typescript
  {
    parent: 'user',
    subcategories: ['twofa', 'role-transitions', 'settings', ...],
  },
  {
    parent: 'admin', 
    subcategories: ['security', 'role-transitions', 'dashboard', ...],
  }
  ```

### **9. Import Path Examples**
```typescript
// Services
import { mfaApi } from '@/services/mfa-api';
import { roleTransitionApi } from '@/services/role-transition-api';

// Components
import { MFASetup } from '@/components/features/user/mfa-setup';
import { SecurityDashboard } from '@/components/features/admin/security-dashboard';
```

### **10. CRITICAL Component Requirements**
- **🌓 Dark/Light Mode**: ALL components MUST support theme switching
  - Use `className` with theme-aware CSS classes
  - Test in both light and dark modes
- **📱 Responsive Design**: ALL components MUST work on all screen sizes
  - Use responsive CSS classes (sm:, md:, lg:, xl:)
  - Test on mobile, tablet, and desktop
- **🌍 Internationalization**: ALL text MUST use translation keys
  - Use `useIntl()` hook for all user-facing text
  - Test with Chinese locale to verify translations work
  - Never hardcode text strings

### **11. Component Template Structure**
```typescript
import React from 'react';
import { useIntl } from 'react-intl';
// ... other imports

interface ComponentProps {
  // props definition
}

export const ComponentName: React.FC<ComponentProps> = ({ ...props }) => {
  const intl = useIntl();

  return (
    <div className="responsive-classes dark:dark-classes">
      <h1>{intl.formatMessage({ id: 'context.feature.title' })}</h1>
      {/* Component content with theme and responsive support */}
    </div>
  );
};
```

### **12. Before Creating New Components**
1. ✅ Check existing component structure in `frontend/src/components/features/`
2. ✅ Check existing translation files in the appropriate context directory
3. ✅ Extend existing translation files when possible (like `twofa.json`)
4. ✅ Use proper kebab-case naming for files
5. ✅ Place components in the correct feature context directory
6. ✅ Update `locales.tsx` with new translation subcategories
7. ✅ **CRITICAL**: Implement dark/light mode support
8. ✅ **CRITICAL**: Implement responsive design
9. ✅ **CRITICAL**: Use i18n for all text

## **Remember: ALWAYS follow the established project patterns and NEVER skip the critical requirements!**
