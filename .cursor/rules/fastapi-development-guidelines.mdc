---
description: 
globs: 
alwaysApply: false
---
# FastAPI Development Best Practices

## Project Structure & Organization
- Entry point: [app/main.py](mdc:app/main.py) - FastAPI application with middleware and error handling
- Configuration: [app/config/settings.py](mdc:app/config/settings.py) - Environment-based settings
- API routing: [app/api/api.py](mdc:app/api/api.py) - Versioned API structure
- Schemas: [app/schemas/__init__.py](mdc:app/schemas/__init__.py) - Pydantic validation models
- Scripts: [scripts/README.md](mdc:scripts/README.md) - Organized utility scripts
- Tests: [tests/](mdc:tests) - Comprehensive test suite

## Best Practice Guidelines

### 1. Configuration Management
- Use environment variables for all configuration via [app/config/settings.py](mdc:app/config/settings.py)
- Never hardcode sensitive values (URLs, secrets, keys)
- Provide sensible defaults for development
- Use the `get_settings()` function for dependency injection

### 2. API Design
- **Versioning**: Always use versioned endpoints (`/api/v1/`)
- **Async**: Use `async def` for all endpoint handlers
- **Type Hints**: Add type hints for parameters and return values
- **Response Models**: Define Pydantic schemas for responses
- **HTTP Status Codes**: Use appropriate status codes (200, 201, 404, 422, etc.)

### 3. Repository Hygiene & Organization
- **⚠️ CRITICAL**: Always clean up after debug or development work
- Remove temporary files, debug print statements, and test data
- Delete any temporary scripts or files created during development
- Use proper `.gitignore` patterns for temporary files
- Never commit debug code, temporary endpoints, or development-only modifications
- Clean up any temporary environment variables or configuration changes

### 4. Script Management
- **⚠️ NEVER place scripts randomly in project root**
- Use the organized [scripts/](mdc:scripts) directory structure:
  - [scripts/dev/](mdc:scripts/dev) - Development and testing scripts
  - [scripts/db/](mdc:scripts/db) - Database setup and management
  - [scripts/deploy/](mdc:scripts/deploy) - Deployment and infrastructure
- Follow the documentation in [scripts/README.md](mdc:scripts/README.md)
- Make scripts executable: `chmod +x scripts/*/*.sh`
- Use descriptive names and include usage comments

### 5. Code Integration Guidelines
- **⚠️ RESPECT EXISTING PROJECT STRUCTURE** - Do not improvise new patterns
- Follow established directory organization and naming conventions
- Use existing utilities, helpers, and patterns before creating new ones
- Reference existing implementations in [app/](mdc:app) before adding new code
- Maintain consistency with established code patterns and architecture
- When adding new features, integrate with existing systems rather than creating parallel solutions

### 6. Request/Response Validation
- Create Pydantic models in `/app/schemas/` for all request/response data
- Use `BaseModel` from Pydantic for data validation
- Follow the existing error response patterns in [app/schemas/__init__.py](mdc:app/schemas/__init__.py)
- Include `timestamp` and `status_code` in error responses

### 7. Error Handling
- Use FastAPI's `HTTPException` for expected errors
- Follow the global exception handlers in [app/main.py](mdc:app/main.py)
- Return consistent error format using `ErrorResponse` schema
- Log errors appropriately based on severity

### 8. Database & Data Access
- **CRITICAL: NO MIGRATIONS** - Implement all database changes directly in Python models
- **Auto-create tables**: Use SQLAlchemy model definitions, never Alembic migrations
- Use the repository pattern: `/app/repositories/` for data access
- Keep business logic in `/app/services/`
- Use dependency injection for database connections
- Always use async database operations
- **UUID Primary Keys**: Use 36-char UUID identifiers consistently

### 9. Security Best Practices
- **Rate Limiting**: Custom Redis-based middleware (`app/middleware/rate_limiting.py`)
  - Replaces slowapi (completely removed)
  - Fail-open design: allows requests if Redis unavailable
  - Endpoint-specific limits: login (strict), MFA, admin, OAuth2, API
- **Audit Logging**: Dual system architecture
  - `AuditService`: Security events → `audit_logs` table
  - `ActivityLoggerService`: OAuth2 access → `user_access_logs` table
  - Middleware: `app/middleware/audit_logging.py`
  - Fail-safe: audit failures don't break application
- Validate all inputs using Pydantic schemas
- Use CORS configuration from [app/config/settings.py](mdc:app/config/settings.py)
- Implement proper authentication/authorization for protected endpoints

### 10. Logging & Monitoring
- **Structured Logging**: Configured in app startup
- **Log Levels**: DEBUG, INFO, WARNING, ERROR
- **Context**: Include user_id, request_id, etc. in log messages
- **Security**: Never log sensitive information
- **Middleware Stack Order** (main.py):
  1. RequestIDMiddleware (request tracking)
  2. SecurityHeadersMiddleware (security headers)
  3. RequestLoggingMiddleware (request logging)
  4. CORSMiddleware (CORS handling)
  5. RateLimitingMiddleware (rate limiting)
  6. AuditLoggingMiddleware (audit logging)

### 11. Testing Guidelines
- **⚠️ IMPLEMENT COMPREHENSIVE TESTS** in [tests/](mdc:tests) directory
- **AVOID REDUNDANCY**: Check existing tests before writing new ones
- **NO OVER-ENGINEERING**: Write focused, minimal tests that verify specific functionality
- Use the established test patterns in [tests/conftest.py](mdc:tests/conftest.py)
- Follow existing test organization:
  - [tests/test_oauth2.py](mdc:tests/test_oauth2.py) - OAuth2 functionality
  - [tests/test_license.py](mdc:tests/test_license.py) - License management
  - [tests/test_mysql_integration.py](mdc:tests/test_mysql_integration.py) - Database integration
- Use pytest with async support (configured in [pyproject.toml](mdc:pyproject.toml))
- Test both success and failure scenarios
- Use dependency overrides for testing
- Write tests that would catch compilation and runtime errors
- Focus on critical business logic, not trivial getters/setters

### 12. Performance
- Use async/await for I/O operations
- Implement caching where appropriate (Redis available)
- Use background tasks for non-blocking operations
- Monitor with health checks at `/health`

### 13. Code Quality
- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Keep functions small and focused on single responsibility

## Common Patterns

### Endpoint Structure
```python
from fastapi import APIRouter, Depends, HTTPException
from app.schemas import ResponseModel, RequestModel
from app.services import MyService

router = APIRouter()

@router.post("/items/", response_model=ResponseModel)
async def create_item(
    item: RequestModel,
    service: MyService = Depends()
) -> ResponseModel:
    """Create a new item."""
    try:
        result = await service.create_item(item)
        return ResponseModel(data=result)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
```

### Dependency Injection
```python
from fastapi import Depends
from app.config import get_settings, Settings

async def get_current_user(
    settings: Settings = Depends(get_settings)
):
    # Authentication logic
    pass
```

### Error Responses
```python
from app.schemas import ErrorResponse
from datetime import datetime

error_response = ErrorResponse(
    error="Resource not found",
    detail="Item with ID 123 does not exist",
    timestamp=datetime.now(),
    status_code=404
)
```

## Development Commands
- **Start development**: `bash scripts/deploy/start.sh` or `uvicorn app.main:app --reload`
- **Run tests**: `make test` (identifies bugs, ensures useful test cases)
- **Run with coverage**: `pytest --cov=app`
- **Docker development**: `bash scripts/deploy/docker.sh start`
- **Build & deploy**: `make bd` (builds frontend + docker)
- **Production deployment**: Always test on port 5550
- **API testing**: `bash scripts/dev/test-api.sh`
- **Reset development data**: `python scripts/dev/reset_tokens.py`
- **API documentation**: Visit `/docs` or `/redoc`

## Frontend Development Rules
- **CRITICAL**: Follow project file structure and naming conventions
- **CRITICAL**: Use `rule_new_react_component.mdc` for all new components
- **Required**: Dark/light mode support in all components
- **Required**: Responsive design for all screen sizes
- **Required**: Internationalization (i18n) support
- **Testing**: Use Chinese locale to verify translations work

## Production-Ready Systems

### Rate Limiting (Redis-based)
- **Service**: `app/services/rate_limiting_service.py`
- **Middleware**: `app/middleware/rate_limiting.py`
- **Features**: Endpoint-specific limits, graceful Redis failure, fail-open design
- **Endpoints**: Login (10/15min), MFA (30/10min), Admin (20/1hr), API (200/1hr)

### Audit Logging (Dual system)
- **Security Events**: `app/services/audit_service.py` → `audit_logs` table
- **OAuth2 Access**: `app/services/connections/activity_logger.py` → `user_access_logs` table
- **Middleware**: `app/middleware/audit_logging.py`
- **Features**: Fail-safe design, comprehensive endpoint coverage

### Session Management (SSO)
- **Service**: `app/services/session_service.py` (CentralSessionManager)
- **Models**: `SSOSession`, `SessionBinding` (app/models/user.py)
- **Features**: Redis caching, Single Logout (SLO), cross-app session tracking

### Multi-Factor Authentication
- **Service**: `app/services/mfa_service.py` (MFAManager)
- **Models**: `MFADevice`, `MFABackupCode` (app/models/user.py)
- **Features**: TOTP, QR codes, backup codes, device management

## Environment Setup
- Create `.env` file for local development
- Use virtual environment: `python -m venv venv`
- Install dependencies: `pip install -r requirements.txt`
- Configure environment variables as shown in [app/config/settings.py](mdc:app/config/settings.py)
- Use [scripts/db/setup_db.sh](mdc:scripts/db/setup_db.sh) for database setup
