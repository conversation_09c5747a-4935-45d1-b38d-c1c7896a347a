---
description: 
globs: 
alwaysApply: false
---
# MySQL Database Changes - Best Practices

When making changes to the MySQL database structure in this FastAPI project, follow these simple and specific steps:

## 📋 **Required Steps for Database Changes**

### 1. Model Changes First
- Update SQLAlchemy models in [app/models/](mdc:app/models) directory
- Add proper type hints and column constraints
- Include indexes for commonly queried fields
- Follow the existing pattern from [item.py](mdc:app/models/item.py)

### 2. Generate Migration
```bash
# Always generate migration after model changes
alembic revision --autogenerate -m "descriptive_message_here"
```

### 3. Review Migration File
- Check generated migration in [alembic/versions/](mdc:alembic/versions)
- Verify `upgrade()` and `downgrade()` functions
- Test migration locally before committing
- Follow the pattern from existing migrations

### 4. Update Related Components
- **Schemas**: Update Pydantic models in [app/schemas/](mdc:app/schemas)
- **Repositories**: Update database queries in [app/repositories/](mdc:app/repositories)
- **Tests**: Update or add tests for new fields/tables

## 🚫 **Common Mistakes to Avoid**

- ❌ Don't edit migration files manually (use `alembic revision --autogenerate`)
- ❌ Don't skip the downgrade function in migrations
- ❌ Don't forget to update Pydantic schemas after model changes
- ❌ Don't commit without testing the migration locally

## ✅ **Simple Testing Workflow**

```bash
# Test migration up
alembic upgrade head

# Test migration down
alembic downgrade -1

# Test migration up again
alembic upgrade head
```

## 📝 **Column Naming Conventions**

- Use snake_case for column names
- Add `_at` suffix for timestamps (`created_at`, `updated_at`)
- Add `is_` prefix for booleans (`is_active`, `is_deleted`)
- Use descriptive names, avoid abbreviations

## 🔍 **Index Guidelines**

- Add indexes for:
  - Foreign keys
  - Columns used in WHERE clauses
  - Columns used for sorting (ORDER BY)
  - Boolean flags that filter large datasets

## 📂 **File Update Checklist**

When adding a new table or major changes:
- [ ] Create/update model in `app/models/`
- [ ] Generate Alembic migration
- [ ] Update `app/models/__init__.py` imports
- [ ] Create Pydantic schemas in `app/schemas/`
- [ ] Update repository pattern in `app/repositories/`
- [ ] Add/update tests
- [ ] Test migration locally

## 🎯 **Keep It Simple**

- One logical change per migration
- Clear, descriptive migration messages
- Test locally before pushing
- Follow existing patterns in the codebase
