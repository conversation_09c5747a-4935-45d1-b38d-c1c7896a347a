---
description: i18n Translation for page.tsx
globs: 
alwaysApply: false
---
1. Find all ui text in the target page.tsx, including text in all nested components, and change all the text to intl.formatMessage({ id: 'xxxxxxxxxxxx'})
2. Use shell script to find all the translation key you made in step 1, including nested components
3. Compare the list of keys with .json in english, add all missing translation key, remove all unused key and organise it
4. Translate the english .json to zh and ask for my feedback via mcp
5. Repeat 2-4 until i told you all translation is resolve and can move on to next page!!!