# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
venv/
.venv/
env/
.env
pip-log.txt

# Development
.git/
.gitignore
.vscode/
*.md
*.log

# Frontend development files (only built files needed)
frontend/

# Base portal dashboard development files
base-portal-dashboard/node_modules/
base-portal-dashboard/src/
base-portal-dashboard/public/
base-portal-dashboard/package*.json
base-portal-dashboard/vite.config.ts
base-portal-dashboard/tsconfig*.json
base-portal-dashboard/.eslintrc*
base-portal-dashboard/tailwind.config.js
base-portal-dashboard/postcss.config.js

# OS
.DS_Store
Thumbs.db

# Documentation
docs/
*.md
README*
INSTRUCTION*
PROJECT_SUMMARY*
EMAIL_UPDATE*
COMPLETION_SUMMARY*

# Test scripts
scripts/dev/test-api.sh
scripts/deploy/start.sh

# IDE
.idea/
*.swp
*.swo

# Coverage
.coverage
.coverage.*
htmlcov/
.pytest_cache/
