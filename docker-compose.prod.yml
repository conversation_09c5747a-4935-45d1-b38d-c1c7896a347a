services:
  redis:
    image: redis:7-alpine
    container_name: geniego-sso-redis-prod
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - geniego-sso-network

  api-server:
    build: .
    container_name: geniego-sso-api-prod
    ports:
      - "5550:5550"
    environment:
      - PROJECT_NAME=GeNieGO SSO Server
      - VERSION=1.0.0
      - API_PREFIX=/api
      - API_VERSION=v1
      - HOST=0.0.0.0
      - PORT=5550
      - DEBUG=false
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=${DATABASE_URL}
      - CORS_ORIGINS=${CORS_ORIGINS:-https://geniego.genieland.ai,https://www.geniego.genieland.ai}
      - LOG_LEVEL=WARNING
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=${JWT_ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - JWT_REFRESH_TOKEN_EXPIRE_DAYS=${JWT_REFRESH_TOKEN_EXPIRE_DAYS:-7}
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60
      # OAuth2 Security settings
      - ENABLE_DOCS=${ENABLE_DOCS:-false}
      - ENABLE_ADMIN_ENDPOINTS=${ENABLE_ADMIN_ENDPOINTS:-false}
      # Email settings for production
      - SMTP_TLS=${SMTP_TLS:-true}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - EMAILS_FROM_NAME=${EMAILS_FROM_NAME:-GeNieGO SSO}
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5550/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - geniego-sso-network

networks:
  geniego-sso-network:
    driver: bridge

volumes:
  redis_data:
