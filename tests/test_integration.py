"""
Comprehensive Integration Tests for GeNieGO SSO Server.

This module tests complete end-to-end workflows with proper test data setup,
specific assertions, and comprehensive user scenarios.
"""

import uuid
from typing import Optional

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import RegisteredApplication, User


class TestApplicationHealthIntegration:
    """Test application health and basic connectivity."""

    @pytest.mark.asyncio
    async def test_health_endpoint_functional(self, async_client: AsyncClient):
        """Test that health endpoint returns proper status."""
        response = await async_client.get("/health")

        # Should return specific status
        assert response.status_code == 200

        health_data = response.json()
        assert "status" in health_data
        assert "services" in health_data
        assert health_data["status"] in ["healthy", "unhealthy"]

    @pytest.mark.asyncio
    async def test_api_health_endpoint_functional(self, async_client: AsyncClient):
        """Test that API health endpoint returns proper status."""
        response = await async_client.get("/api/health")

        # Should return specific status
        assert response.status_code == 200

        health_data = response.json()
        assert "status" in health_data
        assert "timestamp" in health_data


class TestCompleteUserWorkflow:
    """Test complete user registration and authentication workflow."""

    @pytest.mark.asyncio
    async def test_complete_user_registration_login_workflow(
        self, async_client: AsyncClient
    ):
        """Test complete user workflow from registration to login."""
        test_id = str(uuid.uuid4())[:8]

        # Step 1: Register new user
        user_data = {
            "email": f"integrationtest{test_id}@example.com",
            "username": f"integrationtest{test_id}",
            "password": "SecurePassword123!",
            "first_name": "Integration",
            "last_name": "Test",
        }

        registration_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )

        # Should succeed with specific status
        assert registration_response.status_code == 200

        registration_data = registration_response.json()
        assert "id" in registration_data
        assert registration_data["email"] == user_data["email"]
        assert registration_data["username"] == user_data["username"]
        assert "password" not in registration_data  # Password should not be returned

        # Step 2: Login with registered user
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"],
        }

        login_response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Should succeed with specific status
        assert login_response.status_code == 200

        login_response_data = login_response.json()
        assert "session_token" in login_response_data
        assert "expires_at" in login_response_data
        assert "user" in login_response_data
        assert login_response_data["user"]["email"] == user_data["email"]

    @pytest.mark.asyncio
    async def test_user_login_with_invalid_credentials(self, async_client: AsyncClient):
        """Test user login with invalid credentials returns specific error."""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password",
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Should return specific unauthorized status
        assert response.status_code == 401

        error_data = response.json()
        assert "detail" in error_data


class TestCompleteOAuth2Workflow:
    """Test complete OAuth2 authorization workflow."""

    @pytest.fixture
    async def oauth2_integration_setup(self, async_db_session: AsyncSession):
        """Set up complete OAuth2 test scenario."""
        test_id = str(uuid.uuid4())[:8]

        # Create test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"oauth_integration_{test_id}@example.com",
            username=f"oauth_integration_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        user.set_password("integration_password")
        async_db_session.add(user)

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"oauth_dev_{test_id}@example.com",
            username=f"oauth_dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        # Create test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"integration_client_{test_id}",
            application_name=f"Integration Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile", "email"],
            is_active=True,
        )
        application.set_client_secret("integration_secret")
        async_db_session.add(application)

        await async_db_session.commit()
        return {"user": user, "developer": developer, "application": application}

    @pytest.mark.asyncio
    async def test_oauth2_authorization_requires_authentication(
        self, async_client: AsyncClient, oauth2_integration_setup
    ):
        """Test OAuth2 authorization endpoint requires authentication."""
        data = oauth2_integration_setup

        params = {
            "response_type": "code",
            "client_id": data["application"].client_id,
            "redirect_uri": "http://localhost:3000/callback",
            "scope": "openid profile",
        }

        response = await async_client.get("/oauth2/authorize", params=params)

        # Should redirect to login (specific status)
        assert response.status_code == 302

        redirect_url = response.headers["location"]
        assert "/auth/login" in redirect_url

    @pytest.mark.asyncio
    async def test_oauth2_userinfo_requires_proper_data(
        self, async_client: AsyncClient
    ):
        """Test OAuth2 userinfo endpoint requires proper request data."""
        response = await async_client.post("/oauth2/userinfo")

        # Should return specific validation error
        assert response.status_code == 422

        error_data = response.json()
        assert "detail" in error_data or "error" in error_data

    @pytest.mark.asyncio
    async def test_oauth2_token_endpoint_validation(self, async_client: AsyncClient):
        """Test OAuth2 token endpoint validates request data."""
        # Test with missing required fields
        response = await async_client.post("/oauth2/token", data={})

        # Should return specific validation error
        assert response.status_code == 422

        error_data = response.json()
        assert "detail" in error_data or "error" in error_data
