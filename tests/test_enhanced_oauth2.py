"""
Tests for Complete OAuth2 Flow with Connection Tracking and Activity Logging.

This module provides comprehensive testing of OAuth2 authorization server functionality
including authorization code flow, token exchange, user info, and error scenarios.
"""

import uuid
from datetime import datetime, timedelta
from urllib.parse import parse_qs, urlparse

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    AuthorizationCode,
    RegisteredApplication,
    User,
    UserAccessLog,
    UserApplicationConnection,
    UserSession,
)


class TestCompleteOAuth2Flow:
    """Test complete OAuth2 authorization server functionality."""

    @pytest.fixture
    async def oauth2_test_data(self, async_db_session: AsyncSession):
        """Set up comprehensive test data for OAuth2 flow tests."""
        # Generate unique test identifiers
        test_id = str(uuid.uuid4())[:8]

        # Create test user with proper password
        user = User(
            id=str(uuid.uuid4()),
            email=f"oauth_user_{test_id}@example.com",
            username=f"oauth_user_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
            is_verified=True,
        )
        user.set_password("test_password_123")
        async_db_session.add(user)

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"oauth_dev_{test_id}@example.com",
            username=f"oauth_dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        # Create test application with proper configuration
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"test_oauth_client_{test_id}",
            application_name=f"Test OAuth Application {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=[
                "http://localhost:3000/callback",
                "http://localhost:3001/auth/callback",
                "https://example.com/oauth/callback",
            ],
            allowed_scopes=["openid", "profile", "email"],
            is_active=True,
        )
        application.set_client_secret("test_oauth_secret_123")
        async_db_session.add(application)

        # Create valid user session
        session = UserSession(
            id=str(uuid.uuid4()),
            user_id=user.id,
            session_token=f"test_session_token_{test_id}",
            expires_at=datetime.utcnow() + timedelta(hours=24),
            ip_address="127.0.0.1",
            user_agent="Test User Agent",
        )
        async_db_session.add(session)

        await async_db_session.commit()

        return {
            "user": user,
            "developer": developer,
            "application": application,
            "session": session,
            "test_id": test_id,
        }

    @pytest.mark.asyncio
    async def test_oauth2_authorize_unauthenticated_redirects_to_login(
        self, async_client: AsyncClient, oauth2_test_data
    ):
        """Test OAuth2 authorize endpoint redirects to login when user is not authenticated."""
        data = oauth2_test_data

        # Make OAuth2 authorize request without session cookie
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "openid profile email",
                "state": "test_state_123",
            },
        )

        # Should redirect to login page when not authenticated
        assert response.status_code == 302

        # Parse redirect URL - should be login page with OAuth2 parameters preserved
        redirect_url = response.headers["location"]
        parsed_url = urlparse(redirect_url)

        assert parsed_url.path == "/auth/login"
        query_params = parse_qs(parsed_url.query)
        assert query_params["client_id"][0] == data["application"].client_id
        assert query_params["state"][0] == "test_state_123"
        assert query_params["redirect_uri"][0] == "http://localhost:3000/callback"

    @pytest.mark.asyncio
    async def test_oauth2_authorize_invalid_client_id(
        self, async_client: AsyncClient, oauth2_test_data
    ):
        """Test OAuth2 authorize endpoint with invalid client_id."""
        # Test with invalid client_id
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": "invalid_client_id",
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "openid profile",
                "state": "test_state",
            },
        )

        # Should redirect with error for invalid client
        assert response.status_code == 302

        # Parse redirect URL - should contain error
        redirect_url = response.headers["location"]
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)

        assert query_params["error"][0] == "invalid_client"
        assert query_params["state"][0] == "test_state"

    @pytest.mark.asyncio
    async def test_oauth2_authorize_invalid_redirect_uri(
        self, async_client: AsyncClient, oauth2_test_data
    ):
        """Test OAuth2 authorize endpoint with invalid redirect_uri."""
        data = oauth2_test_data

        # Test with redirect URI not in allowed list
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://malicious-site.com/callback",
                "response_type": "code",
                "scope": "openid profile",
                "state": "test_state",
            },
        )

        # Should return error for invalid redirect_uri
        assert response.status_code == 400

        # Should contain error in response body
        error_response = response.json()
        assert "error" in error_response or "detail" in error_response

    @pytest.mark.asyncio
    async def test_oauth2_authorize_successful_with_authenticated_user(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test successful OAuth2 authorization with authenticated user."""
        data = oauth2_test_data

        # Set session cookie to simulate authenticated user
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Make OAuth2 authorize request with valid parameters
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "openid profile email",
                "state": "test_state_success",
            },
        )

        # Should redirect back to application with authorization code
        assert response.status_code == 302

        # Parse redirect URL - should contain authorization code
        redirect_url = response.headers["location"]
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)

        assert "code" in query_params
        assert query_params["state"][0] == "test_state_success"
        assert parsed_url.netloc == "localhost:3000"
        assert parsed_url.path == "/callback"

        # Verify authorization code was created in database
        auth_code_value = query_params["code"][0]
        result = await async_db_session.execute(
            select(AuthorizationCode).where(AuthorizationCode.code == auth_code_value)
        )
        auth_code = result.scalar_one_or_none()

        assert auth_code is not None
        assert auth_code.client_id == data["application"].client_id
        assert auth_code.user_id == data["user"].id
        assert auth_code.redirect_uri == "http://localhost:3000/callback"
        assert auth_code.scopes == ["openid", "profile", "email"]
        assert not auth_code.is_used
        assert not auth_code.is_expired

    @pytest.mark.asyncio
    async def test_oauth2_token_exchange_successful(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test successful OAuth2 token exchange with valid authorization code."""
        data = oauth2_test_data

        # First create an authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_auth_code_123",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile", "email"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Exchange authorization code for token
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_auth_code_123",
                "client_id": data["application"].client_id,
                "client_secret": "test_oauth_secret_123",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )

        # Should return access token
        assert response.status_code == 200
        token_response = response.json()

        assert "access_token" in token_response
        assert token_response["token_type"] == "Bearer"
        assert token_response["expires_in"] == 3600
        assert token_response["scope"] == "openid profile email"

        # Verify authorization code was marked as used
        await async_db_session.refresh(auth_code)
        assert auth_code.is_used

    @pytest.mark.asyncio
    async def test_oauth2_token_exchange_invalid_code(
        self, async_client: AsyncClient, oauth2_test_data
    ):
        """Test OAuth2 token exchange with invalid authorization code."""
        data = oauth2_test_data

        # Try to exchange invalid authorization code
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "invalid_auth_code",
                "client_id": data["application"].client_id,
                "client_secret": "test_oauth_secret_123",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )

        # Should return error for invalid grant
        assert response.status_code == 400
        error_response = response.json()
        assert error_response["error"] == "invalid_grant"

    @pytest.mark.asyncio
    async def test_oauth2_token_exchange_wrong_client_secret(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test OAuth2 token exchange with wrong client secret."""
        data = oauth2_test_data

        # Create authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_auth_code_456",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Try to exchange with wrong client secret
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_auth_code_456",
                "client_id": data["application"].client_id,
                "client_secret": "wrong_secret",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )

        # Should return error for invalid client
        assert response.status_code == 401
        error_response = response.json()
        assert error_response["error"] == "invalid_client"

    @pytest.mark.asyncio
    async def test_oauth2_userinfo_successful(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test successful OAuth2 userinfo request with valid authorization code."""
        data = oauth2_test_data

        # Create authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_userinfo_code_123",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile", "email"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Request user info with valid authorization code
        response = await async_client.post(
            "/oauth2/userinfo",
            data={
                "authorization_code": "test_userinfo_code_123",
                "client_id": data["application"].client_id,
            },
        )

        # Should return user information
        assert response.status_code == 200
        user_info = response.json()

        assert user_info["sub"] == data["user"].id
        assert user_info["email"] == data["user"].email
        assert user_info["username"] == data["user"].username
        # Check for profile fields (may be None but should be present)
        assert "first_name" in user_info
        assert "last_name" in user_info

        # Verify authorization code was marked as used
        await async_db_session.refresh(auth_code)
        assert auth_code.is_used

    @pytest.mark.asyncio
    async def test_oauth2_userinfo_invalid_code(
        self, async_client: AsyncClient, oauth2_test_data
    ):
        """Test OAuth2 userinfo with invalid authorization code."""
        data = oauth2_test_data

        # Request user info with invalid authorization code
        response = await async_client.post(
            "/oauth2/userinfo",
            data={
                "authorization_code": "invalid_code_123",
                "client_id": data["application"].client_id,
            },
        )

        # Should return error for invalid authorization code
        assert response.status_code == 400
        error_response = response.json()
        assert error_response["error"] == "Invalid authorization code"

    @pytest.mark.asyncio
    async def test_oauth2_userinfo_expired_code(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test OAuth2 userinfo with expired authorization code."""
        data = oauth2_test_data

        # Create expired authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_expired_code_123",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile"],
            expires_at=datetime.utcnow() - timedelta(minutes=10),  # Expired
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Request user info with expired authorization code
        response = await async_client.post(
            "/oauth2/userinfo",
            data={
                "authorization_code": "test_expired_code_123",
                "client_id": data["application"].client_id,
            },
        )

        # Should return error for invalid authorization code
        assert response.status_code == 400
        error_response = response.json()
        assert error_response["error"] == "Invalid authorization code"

    @pytest.mark.asyncio
    async def test_oauth2_connection_tracking_integration(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test that OAuth2 flow properly creates and updates user-application connections."""
        data = oauth2_test_data

        # Set session cookie to simulate authenticated user
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Make OAuth2 authorize request
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "openid profile email",
                "state": "connection_test",
            },
        )

        # Should redirect with authorization code
        assert response.status_code == 302
        redirect_url = response.headers["location"]
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)
        auth_code_value = query_params["code"][0]

        # Verify user-application connection was created
        result = await async_db_session.execute(
            select(UserApplicationConnection).where(
                UserApplicationConnection.user_id == data["user"].id,
                UserApplicationConnection.application_id == data["application"].id,
            )
        )
        connection = result.scalar_one_or_none()

        assert connection is not None
        assert connection.client_id == data["application"].client_id
        assert connection.granted_scopes == ["openid", "profile", "email"]
        assert connection.is_active
        assert connection.access_count >= 1

        # Exchange code for token to test connection update
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": auth_code_value,
                "client_id": data["application"].client_id,
                "client_secret": "test_oauth_secret_123",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )

        assert response.status_code == 200

        # Verify connection exists and is active (access count behavior may vary)
        await async_db_session.refresh(connection)
        assert connection.access_count >= 1  # At least one access
        assert connection.is_active

    @pytest.mark.asyncio
    async def test_oauth2_activity_logging_integration(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_test_data,
    ):
        """Test that OAuth2 flow properly logs user activities."""
        data = oauth2_test_data

        # Create authorization code for testing
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_activity_code_123",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Exchange code for token
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_activity_code_123",
                "client_id": data["application"].client_id,
                "client_secret": "test_oauth_secret_123",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )

        assert response.status_code == 200

        # Verify activity was logged
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.action == "token_exchange",
            )
        )
        activity_log = result.scalar_one_or_none()

        assert activity_log is not None
        assert activity_log.client_id == data["application"].client_id
        assert activity_log.success is True
        assert activity_log.ip_address is not None
        assert activity_log.timestamp is not None


class TestOAuth2ErrorHandling:
    """Test OAuth2 error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_oauth2_authorize_missing_parameters(self, async_client: AsyncClient):
        """Test OAuth2 authorize endpoint with missing required parameters."""
        # Missing client_id
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
            },
        )
        assert response.status_code == 422

        # Missing redirect_uri
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": "test_client",
                "response_type": "code",
            },
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_oauth2_token_missing_parameters(self, async_client: AsyncClient):
        """Test OAuth2 token endpoint with missing required parameters."""
        # Missing grant_type
        response = await async_client.post(
            "/oauth2/token",
            data={
                "code": "test_code",
                "client_id": "test_client",
            },
        )
        assert response.status_code == 422

        # Missing code
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "client_id": "test_client",
            },
        )
        assert response.status_code == 422
