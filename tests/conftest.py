"""
Test configuration and fixtures for GeNieGO SSO.
"""

import asyncio
import json
import os
import time
import uuid
from datetime import datetime
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from httpx import ASGITransport, AsyncClient
from passlib.context import CryptContext
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

# Set environment variable to disable redis during tests
os.environ["TESTING"] = "true"

from app.core.database import get_db
from app.main import app
from app.models.base import Base


# MySQL test database configuration
# Detect if running inside Docker container
def get_mysql_host():
    """Get MySQL host based on environment."""
    # Check if running inside Docker container
    if os.path.exists("/.dockerenv") or os.environ.get("DOCKER_CONTAINER"):
        return "geniengo-sso-mysql:3306"
    else:
        return "localhost:3307"


MYSQL_HOST = get_mysql_host()


# Use environment variables for credentials
def get_mysql_credentials():
    """Get MySQL credentials from environment variables."""
    return {
        "user": os.environ.get("MYSQL_USER", "geniengo_user"),
        "password": os.environ.get("MYSQL_PASSWORD", "geniengo_password"),
        "database": os.environ.get("MYSQL_TEST_DATABASE", "geniengo_sso_db_test"),
        "root_password": os.environ.get(
            "MYSQL_ROOT_PASSWORD", "geniengo_root_password"
        ),
    }


MYSQL_CREDS = get_mysql_credentials()
MYSQL_DATABASE_URL = f"mysql+pymysql://{MYSQL_CREDS['user']}:{MYSQL_CREDS['password']}@{MYSQL_HOST}/{MYSQL_CREDS['database']}"
MYSQL_ASYNC_DATABASE_URL = f"mysql+aiomysql://{MYSQL_CREDS['user']}:{MYSQL_CREDS['password']}@{MYSQL_HOST}/{MYSQL_CREDS['database']}"


def wait_for_mysql_connection(max_retries: int = 10, delay: float = 0.5) -> bool:
    """Wait for MySQL to be available."""
    print("Waiting for MySQL connection...")
    for attempt in range(max_retries):
        try:
            # Use root user to test connection
            temp_engine = create_engine(
                f"mysql+pymysql://root:{MYSQL_CREDS['root_password']}@{MYSQL_HOST}/mysql",
                pool_pre_ping=True,
                pool_timeout=5,
                pool_recycle=300,
            )
            with temp_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            temp_engine.dispose()
            print("MySQL connection established!")
            return True
        except Exception as e:
            if attempt < max_retries - 1:
                print(
                    f"MySQL not ready, retrying in {delay} seconds... (attempt {attempt + 1}/{max_retries}) - Error: {e}"
                )
                time.sleep(delay)
            else:
                print(
                    f"Failed to connect to MySQL after maximum retries. Last error: {e}"
                )
                return False
    return False


def create_test_database():
    """Create test database if it doesn't exist."""
    try:
        # Connect to MySQL server as root to create database
        admin_engine = create_engine(
            f"mysql+pymysql://root:{MYSQL_CREDS['root_password']}@{MYSQL_HOST}/mysql",
            pool_pre_ping=True,
        )

        with admin_engine.connect() as conn:
            # Create test database if it doesn't exist
            conn.execute(
                text(f"CREATE DATABASE IF NOT EXISTS {MYSQL_CREDS['database']}")
            )

            # Grant permissions to the user for the test database
            conn.execute(
                text(
                    f"GRANT ALL PRIVILEGES ON {MYSQL_CREDS['database']}.* TO '{MYSQL_CREDS['user']}'@'%'"
                )
            )
            conn.execute(text("FLUSH PRIVILEGES"))
            conn.commit()

        admin_engine.dispose()
        print("Test database created/verified and permissions granted")
    except Exception as e:
        print(f"Error creating test database: {e}")
        raise


# Wait for MySQL and create test database
def ensure_mysql_ready():
    """Ensure MySQL is ready and test database exists."""
    if not wait_for_mysql_connection():
        raise RuntimeError(
            "Could not connect to MySQL. Make sure the MySQL container is running."
        )
    create_test_database()


# Lazy initialization - only run when tests actually need it
mysql_initialized = False


def get_sync_test_engine():
    """Get sync test database engine for schema operations."""
    global mysql_initialized
    if not mysql_initialized:
        ensure_mysql_ready()
        mysql_initialized = True

    # Create sync engine only for schema operations
    sync_engine = create_engine(
        MYSQL_DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False,
    )
    return sync_engine


@pytest_asyncio.fixture(scope="session")
async def test_database_setup():
    """Set up test database schema once per test session."""
    print("Setting up test database schema...")

    # Use sync engine for schema creation (DDL operations)
    sync_engine = get_sync_test_engine()

    try:
        # Drop and recreate all tables to ensure clean state
        Base.metadata.drop_all(bind=sync_engine)
        Base.metadata.create_all(bind=sync_engine)

        # Verify tables were created
        with sync_engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            print(f"Test database tables created successfully: {tables}")

        yield sync_engine

    finally:
        # Clean up after all tests
        print("Cleaning up test database...")
        try:
            Base.metadata.drop_all(bind=sync_engine)
            print("Test database cleaned up")
        except Exception as e:
            print(f"Warning: Could not clean up test database: {e}")
        finally:
            sync_engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def async_engine(test_database_setup):
    """Create async engine for each test function."""
    # Import NullPool to avoid connection pooling issues in tests
    from sqlalchemy.pool import NullPool

    # Create async engine with NullPool to avoid event loop conflicts
    engine = create_async_engine(
        MYSQL_ASYNC_DATABASE_URL,
        poolclass=NullPool,  # No connection pooling for tests
        echo=False,
    )

    yield engine

    # Cleanup
    await engine.dispose()


@pytest_asyncio.fixture
async def async_db_session(async_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create an async database session for testing."""
    async_session_maker = async_sessionmaker(
        bind=async_engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False,
    )

    async with async_session_maker() as session:
        try:
            yield session
        finally:
            try:
                await session.rollback()
            except Exception:
                # Ignore rollback errors during cleanup
                pass
            finally:
                await session.close()


@pytest_asyncio.fixture
async def async_client(
    test_database_setup, async_engine
) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client for the GeNieGO SSO."""

    # Clear test data at start of test using the session-scoped sync engine
    try:
        with test_database_setup.begin() as conn:
            # Disable foreign key checks temporarily
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))

            # Get all table names and truncate them
            tables = Base.metadata.tables.keys()
            for table_name in tables:
                try:
                    # Escape table name with backticks to handle reserved words like 'grant'
                    conn.execute(text(f"TRUNCATE TABLE `{table_name}`"))
                except Exception as table_error:
                    print(
                        f"Warning: Could not truncate table {table_name}: {table_error}"
                    )

            # Re-enable foreign key checks
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))

    except Exception as e:
        print(f"Warning: Could not setup test data: {e}")

    # Create session maker for dependency override
    async_session_maker = async_sessionmaker(
        bind=async_engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False,
    )

    # Override the database dependency with async version
    async def get_test_db():
        async with async_session_maker() as session:
            yield session

    app.dependency_overrides[get_db] = get_test_db

    # Create async client
    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://testserver"
    ) as client:
        yield client

    # Clean up
    app.dependency_overrides.clear()


def _recreate_oauth2_system_clients(engine):
    """Recreate OAuth2 system clients needed for testing."""
    import json
    import uuid

    from passlib.context import CryptContext

    from app.config.settings import get_settings
    from app.models.user import RegisteredApplication

    settings = get_settings()
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    try:
        with engine.begin() as conn:
            # Create the system client specifically needed for tests
            client_data = settings.oauth2_clients["system"]
            print(f"Recreating system client with secret: {client_data['secret']}")

            conn.execute(
                text(
                    """
                INSERT INTO registered_applications (
                    id, client_id, client_secret_hash, application_name,
                    allowed_redirect_uris, allowed_scopes, is_active, created_at
                ) VALUES (
                    :id, :client_id, :client_secret_hash, :application_name,
                    :allowed_redirect_uris, :allowed_scopes, :is_active, :created_at
                )
                ON DUPLICATE KEY UPDATE
                    client_secret_hash = VALUES(client_secret_hash),
                    application_name = VALUES(application_name),
                    allowed_redirect_uris = VALUES(allowed_redirect_uris),
                    allowed_scopes = VALUES(allowed_scopes),
                    is_active = VALUES(is_active)
                """
                ),
                {
                    "id": str(uuid.uuid4()),
                    "client_id": client_data["client_id"],
                    "client_secret_hash": pwd_context.hash(client_data["secret"]),
                    "application_name": "System Client",
                    "allowed_redirect_uris": json.dumps(
                        client_data.get("redirect_uris", [])
                    ),
                    "allowed_scopes": json.dumps(
                        client_data.get("scopes", ["openid", "profile"])
                    ),
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                },
            )

            # Verify insertion
            result = conn.execute(
                text(
                    "SELECT client_id FROM registered_applications WHERE client_id = :client_id"
                ),
                {"client_id": client_data["client_id"]},
            )
            system_client = result.fetchone()
            if system_client:
                print(f"System client verified: {system_client[0]}")
            else:
                print("ERROR: System client not found after insertion!")

    except Exception as e:
        print(f"ERROR: Could not recreate OAuth2 system clients: {e}")
        import traceback

        traceback.print_exc()


# Legacy fixture for backwards compatibility (but uses async under the hood)
@pytest_asyncio.fixture
async def client(async_client) -> AsyncClient:
    """Legacy sync-style client fixture that actually returns async client."""
    return async_client


@pytest.fixture
def db_session(setup_test_database) -> Generator[Session, None, None]:
    """Provide a sync database session for direct database testing (legacy)."""
    sync_engine = get_sync_test_engine()
    TestingSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=sync_engine
    )
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        sync_engine.dispose()
