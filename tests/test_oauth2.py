"""
OAuth2 Core Functionality and Application Tests for GeNieGO SSO Server.

This module tests OAuth2 protocol compliance, application functionality,
and core endpoint behavior with proper validation.
"""

import uuid
from datetime import datetime, timedelta

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import AuthorizationCode, RegisteredApplication, User, UserSession


class TestApplicationCore:
    """Test core application functionality and health."""

    @pytest.mark.asyncio
    async def test_application_startup(self, async_client: AsyncClient):
        """Test that the application starts up correctly."""
        # Test root endpoint
        response = await async_client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    @pytest.mark.asyncio
    async def test_api_documentation_accessible(self, async_client: AsyncClient):
        """Test API documentation is accessible."""
        response = await async_client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    @pytest.mark.asyncio
    async def test_openapi_schema_valid(self, async_client: AsyncClient):
        """Test OpenAPI schema is valid and contains OAuth2 endpoints."""
        response = await async_client.get("/openapi.json")
        assert response.status_code == 200
        assert "application/json" in response.headers["content-type"]

        # Verify it's valid JSON with expected structure
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert "paths" in data

        # Verify OAuth2 endpoints are documented
        paths = data["paths"]
        assert "/oauth2/authorize" in paths
        assert "/oauth2/token" in paths
        assert "/oauth2/userinfo" in paths


class TestOAuth2ProtocolCompliance:
    """Test OAuth2 protocol compliance and proper behavior."""

    @pytest.fixture
    async def oauth2_setup(self, async_db_session: AsyncSession):
        """Set up OAuth2 test data."""
        test_id = str(uuid.uuid4())[:8]

        # Create test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"oauth_test_{test_id}@example.com",
            username=f"oauth_test_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"dev_{test_id}@example.com",
            username=f"dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        # Create test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"oauth_test_client_{test_id}",
            application_name=f"OAuth Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile", "email"],
            is_active=True,
        )
        application.set_client_secret("oauth_test_secret")
        async_db_session.add(application)

        await async_db_session.commit()
        return {"user": user, "developer": developer, "application": application}

    @pytest.mark.asyncio
    async def test_oauth2_authorize_parameter_validation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test OAuth2 authorize endpoint validates required parameters."""
        data = oauth2_setup

        # Test missing client_id
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
            },
        )
        assert response.status_code == 422

        # Test missing redirect_uri
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "response_type": "code",
            },
        )
        assert response.status_code == 422

        # Test invalid response_type
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "token",  # Not supported
            },
        )
        assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    async def test_oauth2_token_parameter_validation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test OAuth2 token endpoint validates required parameters."""
        data = oauth2_setup

        # Test missing grant_type
        response = await async_client.post(
            "/oauth2/token",
            data={
                "code": "test_code",
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 422

        # Test invalid grant_type
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "implicit",  # Not supported
                "code": "test_code",
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code in [400, 422]

        # Test missing code
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_oauth2_userinfo_parameter_validation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test OAuth2 userinfo endpoint validates required parameters."""
        data = oauth2_setup

        # Test missing authorization_code
        response = await async_client.post(
            "/oauth2/userinfo",
            data={
                "client_id": data["application"].client_id,
            },
        )
        assert response.status_code == 422

        # Test missing client_id
        response = await async_client.post(
            "/oauth2/userinfo",
            data={
                "authorization_code": "test_code",
            },
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_oauth2_scope_validation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test OAuth2 scope validation and enforcement."""
        data = oauth2_setup

        # Test with invalid scope
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "invalid_scope",
            },
        )
        # Should redirect to login or return error for invalid scope
        assert response.status_code in [302, 400]

        # Test with scope not allowed by application
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "admin write",  # Not in allowed_scopes
            },
        )
        # Should redirect to login or return error
        assert response.status_code in [302, 400]


@pytest.fixture
async def oauth2_setup(async_db_session: AsyncSession):
    """Set up OAuth2 test data for security features testing."""
    test_id = str(uuid.uuid4())[:8]

    # Create test user
    user = User(
        id=str(uuid.uuid4()),
        email=f"oauth_sec_{test_id}@example.com",
        username=f"oauth_sec_{test_id}",
        password_hash="hashed_password",
        role="user",
        is_active=True,
    )
    async_db_session.add(user)

    # Create test developer
    developer = User(
        id=str(uuid.uuid4()),
        email=f"oauth_dev_{test_id}@example.com",
        username=f"oauth_dev_{test_id}",
        password_hash="hashed_password",
        role="developer",
        is_active=True,
    )
    async_db_session.add(developer)

    # Create test application
    application = RegisteredApplication(
        id=str(uuid.uuid4()),
        client_id=f"oauth_sec_client_{test_id}",
        application_name=f"OAuth Security Test {test_id}",
        developer_id=developer.id,
        admin_approved=True,
        allowed_redirect_uris=["http://localhost:3000/callback"],
        allowed_scopes=["openid", "profile"],
        is_active=True,
    )
    application.set_client_secret("oauth_security_secret")
    async_db_session.add(application)

    await async_db_session.commit()
    return {"user": user, "developer": developer, "application": application}


class TestOAuth2SecurityFeatures:
    """Test OAuth2 security features and protections."""

    @pytest.mark.asyncio
    async def test_oauth2_state_parameter_preservation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test that state parameter is preserved through OAuth2 flow."""
        data = oauth2_setup

        # Test with state parameter
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
                "response_type": "code",
                "scope": "openid profile",
                "state": "test_state_12345",
            },
        )

        # Should redirect (to login or back to app)
        assert response.status_code == 302

        # State should be preserved in redirect
        redirect_url = response.headers["location"]
        assert "state=test_state_12345" in redirect_url

    @pytest.mark.asyncio
    async def test_oauth2_redirect_uri_validation(
        self, async_client: AsyncClient, oauth2_setup
    ):
        """Test OAuth2 redirect URI validation for security."""
        data = oauth2_setup

        # Test with redirect URI not in allowed list
        response = await async_client.get(
            "/oauth2/authorize",
            params={
                "client_id": data["application"].client_id,
                "redirect_uri": "http://malicious-site.com/steal-codes",
                "response_type": "code",
                "scope": "openid profile",
            },
        )

        # Should return error or redirect with error
        assert response.status_code in [302, 400]

        if response.status_code == 302:
            # If redirecting, should contain error
            redirect_url = response.headers["location"]
            assert "error=" in redirect_url


class TestAuthenticationIntegration:
    """Test authentication integration with OAuth2 flow."""

    @pytest.mark.asyncio
    async def test_user_registration_functionality(self, async_client: AsyncClient):
        """Test user registration endpoint functionality."""
        test_id = str(uuid.uuid4())[:8]

        user_data = {
            "email": f"testuser{test_id}@example.com",
            "username": f"testuser{test_id}",
            "password": "secure_password_123",
            "first_name": "Test",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Should successfully create user
        if response.status_code != 200:
            print(f"Registration failed with status {response.status_code}")
            print(f"Response: {response.text}")
        assert response.status_code == 200
        user_response = response.json()

        assert user_response["email"] == user_data["email"]
        assert user_response["username"] == user_data["username"]
        assert user_response["first_name"] == user_data["first_name"]
        assert user_response["last_name"] == user_data["last_name"]
        assert "id" in user_response
        assert "password" not in user_response  # Password should not be returned

    @pytest.mark.asyncio
    async def test_user_login_functionality(self, async_client: AsyncClient):
        """Test user login endpoint functionality."""
        test_id = str(uuid.uuid4())[:8]

        # First register a user
        user_data = {
            "email": f"logintest{test_id}@example.com",
            "username": f"logintest{test_id}",
            "password": "login_password_123",
            "first_name": "Login",
            "last_name": "Test",
        }

        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 200

        # Now test login
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"],
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Should successfully authenticate
        assert response.status_code == 200
        login_response = response.json()

        assert "session_token" in login_response
        assert "expires_at" in login_response
        assert "user" in login_response
        assert login_response["user"]["email"] == user_data["email"]

    @pytest.mark.asyncio
    async def test_user_login_invalid_credentials(self, async_client: AsyncClient):
        """Test user login with invalid credentials."""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password",
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Should reject invalid credentials
        assert response.status_code == 401
        error_response = response.json()
        assert "detail" in error_response

    @pytest.mark.asyncio
    async def test_user_logout_functionality(self, async_client: AsyncClient):
        """Test user logout endpoint functionality."""
        # Test logout without session
        response = await async_client.post("/api/v1/auth/logout")

        # Should handle logout request (may succeed even without session)
        assert response.status_code in [200, 401]


class TestSchemaValidation:
    """Test schema validation and data structures."""

    def test_oauth2_schemas_validation(self):
        """Test OAuth2 schema validation."""
        from app.schemas.auth import OAuth2AuthorizeRequest
        from app.schemas.oauth2 import OAuth2TokenRequest, OAuth2TokenResponse

        # Test OAuth2AuthorizeRequest validation
        auth_request_data = {
            "client_id": "test_client",
            "redirect_uri": "http://localhost:5550/auth/callback",
            "response_type": "code",
            "scope": "openid profile",
            "state": "test_state",
        }

        auth_request = OAuth2AuthorizeRequest(**auth_request_data)
        assert auth_request.client_id == "test_client"
        assert auth_request.response_type == "code"
        assert auth_request.scope == "openid profile"

        # Test OAuth2TokenRequest validation
        token_request_data = {
            "grant_type": "authorization_code",
            "code": "test_code",
            "client_id": "test_client",
            "redirect_uri": "http://localhost:3000/callback",
        }

        token_request = OAuth2TokenRequest(**token_request_data)
        assert token_request.grant_type == "authorization_code"
        assert token_request.code == "test_code"

    def test_user_schemas_validation(self):
        """Test user schema validation."""
        from app.schemas.auth import UserLoginRequest, UserRegistrationRequest
        from app.schemas.user import UserResponse

        # Test UserRegistrationRequest validation
        registration_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "secure_password_123",
            "first_name": "Test",
            "last_name": "User",
        }

        registration_request = UserRegistrationRequest(**registration_data)
        assert registration_request.email == "<EMAIL>"
        assert registration_request.username == "testuser"
        assert registration_request.first_name == "Test"

        # Test UserLoginRequest validation
        login_data = {
            "email": "<EMAIL>",
            "password": "secure_password_123",
        }

        login_request = UserLoginRequest(**login_data)
        assert login_request.email == "<EMAIL>"
        assert login_request.password == "secure_password_123"

    def test_schema_imports_successful(self):
        """Test that all required schemas can be imported."""
        try:
            # OAuth2 schemas
            # Auth schemas
            from app.schemas.auth import (
                OAuth2AuthorizeRequest,
                OAuth2UserInfoRequest,
                OAuth2UserInfoResponse,
                SessionResponse,
                UserLoginRequest,
                UserRegistrationRequest,
            )
            from app.schemas.oauth2 import (
                OAuth2AuthorizationCodeResponse,
                OAuth2TokenRequest,
                OAuth2TokenResponse,
            )

            # User schemas
            from app.schemas.user import UserResponse, UserUpdate

            # Verify all imports succeeded
            assert all(
                [
                    OAuth2TokenRequest,
                    OAuth2TokenResponse,
                    OAuth2AuthorizationCodeResponse,
                    OAuth2AuthorizeRequest,
                    OAuth2UserInfoRequest,
                    OAuth2UserInfoResponse,
                    UserRegistrationRequest,
                    UserLoginRequest,
                    SessionResponse,
                    UserResponse,
                    UserUpdate,
                ]
            )

        except ImportError as e:
            pytest.fail(f"Schema imports failed: {e}")


class TestApplicationIntegrity:
    """Test application integrity and module imports."""

    def test_critical_modules_importable(self):
        """Test that critical application modules can be imported."""
        try:
            # Core modules
            # API modules
            from app.api.v1.endpoints import admin, auth, developer, oauth2, users
            from app.core.database import get_db
            from app.core.security import create_access_token
            from app.main import app

            # Model modules
            from app.models.user import (
                AuthorizationCode,
                RegisteredApplication,
                User,
                UserSession,
            )

            # Service modules
            from app.services.auth_service import AuthService
            from app.services.connections.activity_logger import ActivityLoggerService
            from app.services.connections.connection_manager import (
                ConnectionManagerService,
            )

            # Verify all imports succeeded
            assert all(
                [
                    app,
                    get_db,
                    create_access_token,
                    AuthService,
                    ConnectionManagerService,
                    ActivityLoggerService,
                    User,
                    UserSession,
                    AuthorizationCode,
                    RegisteredApplication,
                    oauth2,
                    auth,
                    users,
                    admin,
                    developer,
                ]
            )

        except ImportError as e:
            pytest.fail(f"Critical module imports failed: {e}")

    @pytest.mark.asyncio
    async def test_health_endpoints_functional(self, async_client: AsyncClient):
        """Test that health endpoints are functional."""
        # Test main health endpoint
        response = await async_client.get("/health")
        assert response.status_code in [
            200,
            503,
        ]  # Healthy or unhealthy, but functional

        health_data = response.json()
        assert "status" in health_data
        assert "services" in health_data

        # Test API health endpoint
        response = await async_client.get("/api/health")
        assert response.status_code in [200, 503]
