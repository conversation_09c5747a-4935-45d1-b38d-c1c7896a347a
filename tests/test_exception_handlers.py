"""
Tests for custom exception handlers with datetime serialization.
"""

from datetime import datetime

import pytest
from httpx import AsyncClient


class TestExceptionHandlers:
    """Test cases for custom exception handlers with datetime serialization."""

    @pytest.mark.asyncio
    async def test_http_exception_handler_method_not_allowed(
        self, async_client: AsyncClient
    ):
        """Test HTTP exception handler for method not allowed."""
        # Use a known endpoint with invalid method to trigger HTTP exception
        response = await async_client.put("/health")
        assert response.status_code == 405

        # Should return JSON with proper error structure
        data = response.json()
        assert "detail" in data
        assert data["detail"] == "Method Not Allowed"

    @pytest.mark.asyncio
    async def test_validation_exception_handler(self, async_client: AsyncClient):
        """Test validation exception handler for invalid request data."""
        # Test with invalid OAuth2 authorize request (missing required parameters)
        response = await async_client.get("/oauth2/authorize")
        assert response.status_code == 422

        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], list)

        # Should have validation error details
        # The detail is now a list of strings, not objects
        assert len(data["detail"]) > 0
        # Check that the validation error mentions the missing field
        detail_str = str(data["detail"])
        assert (
            "client_id" in detail_str
        ), f"Expected 'client_id' in validation errors: {data['detail']}"

    @pytest.mark.asyncio
    async def test_not_found_exception_handler(self, async_client: AsyncClient):
        """Test not found exception handler."""
        response = await async_client.get("/nonexistent-endpoint")
        assert response.status_code == 404

        data = response.json()
        assert "detail" in data
        # The actual response is "HTTP 404 error"
        assert data["detail"] == "HTTP 404 error"
