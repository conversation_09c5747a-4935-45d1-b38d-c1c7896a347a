"""
Tests for health check endpoints.
"""

import pytest
from httpx import AsyncClient


class TestHealthEndpoint:
    """Test cases for health check functionality."""

    @pytest.mark.asyncio
    async def test_health_check_basic(self, async_client: AsyncClient):
        """Test basic health check endpoint."""
        response = await async_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data
        assert data["status"] in ["healthy", "unhealthy"]

    @pytest.mark.asyncio
    async def test_health_check_content_type(self, async_client: AsyncClient):
        """Test health check returns JSON content type."""
        response = await async_client.get("/health")
        assert "application/json" in response.headers["content-type"]

    @pytest.mark.asyncio
    async def test_api_health_check(self, async_client: AsyncClient):
        """Test API health check endpoint."""
        response = await async_client.get("/api/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data
        assert data["status"] in ["healthy", "unhealthy"]

    @pytest.mark.asyncio
    async def test_health_check_structure(self, async_client: AsyncClient):
        """Test health check response structure."""
        response = await async_client.get("/health")
        assert response.status_code == 200
        data = response.json()

        # Verify required fields
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data

        # Verify services structure
        services = data["services"]
        assert "database" in services
        assert services["database"]["status"] in ["healthy", "unhealthy"]

    @pytest.mark.asyncio
    async def test_health_check_response_structure(self, async_client: AsyncClient):
        """Test health check response structure is complete."""
        response = await async_client.get("/health")
        assert response.status_code == 200
        data = response.json()

        # Check required top-level fields
        required_fields = ["status", "timestamp", "services"]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"

        # Check services structure
        services = data["services"]
        assert "database" in services
        database_service = services["database"]
        assert "status" in database_service
        assert database_service["status"] in ["healthy", "unhealthy"]

    @pytest.mark.asyncio
    async def test_api_health_check_structure(self, async_client: AsyncClient):
        """Test API health check response structure."""
        response = await async_client.get("/api/health")
        assert response.status_code == 200
        data = response.json()

        # Verify required fields
        assert "status" in data
        assert "timestamp" in data
        assert data["status"] in ["healthy", "unhealthy"]

        # Verify timestamp format
        assert isinstance(data["timestamp"], str)

    @pytest.mark.asyncio
    async def test_health_endpoints_consistency(self, async_client: AsyncClient):
        """Test that both health endpoints return consistent data."""
        main_response = await async_client.get("/health")
        api_response = await async_client.get("/api/health")

        assert main_response.status_code == 200
        assert api_response.status_code == 200

        main_data = main_response.json()
        api_data = api_response.json()

        # Both should have status field
        assert "status" in main_data
        assert "status" in api_data
