"""
Comprehensive Security and Authorization Tests for GeNieGO SSO Server.

This module tests role-based access control (RBAC), input validation,
authentication bypass prevention, and security vulnerabilities.
"""

import uuid
from datetime import datetime, timedelta

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token
from app.models.user import RegisteredApplication, User


class TestRoleBasedAccessControl:
    """Test role-based access control across all API endpoints."""

    @pytest.fixture
    async def security_test_users(self, async_db_session: AsyncSession):
        """Create users with different roles for RBAC testing."""
        test_id = str(uuid.uuid4())[:8]

        # Create regular user
        user = User(
            id=str(uuid.uuid4()),
            email=f"user_{test_id}@example.com",
            username=f"user_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        user.set_password("test_password")
        async_db_session.add(user)

        # Create developer user
        developer = User(
            id=str(uuid.uuid4()),
            email=f"dev_{test_id}@example.com",
            username=f"dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        developer.set_password("test_password")
        async_db_session.add(developer)

        # Create admin user
        admin = User(
            id=str(uuid.uuid4()),
            email=f"admin_{test_id}@example.com",
            username=f"admin_{test_id}",
            password_hash="hashed_password",
            role="admin",
            is_active=True,
        )
        admin.set_password("test_password")
        async_db_session.add(admin)

        # Create test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"security_test_client_{test_id}",
            application_name=f"Security Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile", "email"],
            is_active=True,
        )
        application.set_client_secret("security_test_secret")
        async_db_session.add(application)

        await async_db_session.commit()

        return {
            "user": user,
            "developer": developer,
            "admin": admin,
            "application": application,
            "test_id": test_id,
        }

    @pytest.mark.asyncio
    async def test_admin_endpoints_require_admin_role(
        self, async_client: AsyncClient, security_test_users
    ):
        """Test that admin endpoints reject non-admin users."""
        data = security_test_users

        # Test with regular user token
        user_token = create_access_token(data={"sub": data["user"].id})
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Test admin applications endpoint
        response = await async_client.get(
            "/api/v1/admin/applications", headers=user_headers
        )
        assert response.status_code == 403

        # Test admin analytics endpoint
        response = await async_client.get(
            "/api/v1/admin/analytics/system", headers=user_headers
        )
        assert response.status_code == 403

        # Test with developer token
        dev_token = create_access_token(data={"sub": data["developer"].id})
        dev_headers = {"Authorization": f"Bearer {dev_token}"}

        response = await async_client.get(
            "/api/v1/admin/applications", headers=dev_headers
        )
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_developer_endpoints_require_developer_or_admin_role(
        self, async_client: AsyncClient, security_test_users
    ):
        """Test that developer endpoints reject regular users."""
        data = security_test_users

        # Test with regular user token
        user_token = create_access_token(data={"sub": data["user"].id})
        user_headers = {"Authorization": f"Bearer {user_token}"}

        # Test developer applications endpoint
        response = await async_client.get(
            "/api/v1/developer/applications", headers=user_headers
        )
        assert response.status_code == 403

        # Test developer analytics endpoint
        response = await async_client.get(
            "/api/v1/developer/analytics/overview", headers=user_headers
        )
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_admin_can_access_all_endpoints(
        self, async_client: AsyncClient, security_test_users
    ):
        """Test that admin users can access all endpoints."""
        data = security_test_users

        # Create admin token
        admin_token = create_access_token(data={"sub": data["admin"].id})
        admin_headers = {"Authorization": f"Bearer {admin_token}"}

        # Test admin endpoints
        response = await async_client.get(
            "/api/v1/admin/applications", headers=admin_headers
        )
        assert response.status_code == 200

        # Test developer endpoints (admin should have access)
        response = await async_client.get(
            "/api/v1/developer/applications", headers=admin_headers
        )
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_unauthenticated_requests_rejected(self, async_client: AsyncClient):
        """Test that unauthenticated requests are rejected."""
        # Test admin endpoints without authentication
        response = await async_client.get("/api/v1/admin/applications")
        assert response.status_code == 401

        # Test developer endpoints without authentication
        response = await async_client.get("/api/v1/developer/applications")
        assert response.status_code == 401

        # Test user endpoints without authentication
        response = await async_client.get("/api/v1/user/profile")
        assert response.status_code == 401


class TestInputValidationSecurity:
    """Test input validation and sanitization for security vulnerabilities."""

    @pytest.mark.asyncio
    async def test_sql_injection_prevention_in_registration(
        self, async_client: AsyncClient
    ):
        """Test SQL injection prevention in user registration."""
        # Attempt SQL injection in email field
        malicious_data = {
            "email": "<EMAIL>'; DROP TABLE users; --",
            "username": "testuser",
            "password": "password123",
            "first_name": "Test",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=malicious_data)

        # Should either reject the input or sanitize it safely
        assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    async def test_xss_prevention_in_user_data(self, async_client: AsyncClient):
        """Test XSS prevention in user data fields."""
        # Attempt XSS in user fields
        malicious_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "password123",
            "first_name": "<script>alert('XSS')</script>",
            "last_name": "<img src=x onerror=alert('XSS')>",
        }

        response = await async_client.post("/api/v1/auth/register", json=malicious_data)

        if response.status_code == 200:
            # If registration succeeds, verify data is sanitized
            user_data = response.json()
            assert "<script>" not in user_data.get("first_name", "")
            assert "<img" not in user_data.get("last_name", "")

    @pytest.mark.asyncio
    async def test_oversized_input_rejection(self, async_client: AsyncClient):
        """Test rejection of oversized inputs."""
        # Attempt to register with oversized data
        oversized_data = {
            "email": "<EMAIL>",
            "username": "a" * 1000,  # Oversized username
            "password": "password123",
            "first_name": "Test",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=oversized_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_invalid_email_format_rejection(self, async_client: AsyncClient):
        """Test rejection of invalid email formats."""
        invalid_emails = [
            "not-an-email",
            "@example.com",
            "test@",
            "<EMAIL>",
            "test@example",
        ]

        for invalid_email in invalid_emails:
            user_data = {
                "email": invalid_email,
                "username": "testuser",
                "password": "password123",
                "first_name": "Test",
                "last_name": "User",
            }

            response = await async_client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 422


class TestAuthenticationBypassPrevention:
    """Test prevention of authentication bypass vulnerabilities."""

    @pytest.mark.asyncio
    async def test_invalid_jwt_token_rejection(self, async_client: AsyncClient):
        """Test rejection of invalid JWT tokens."""
        invalid_tokens = [
            "invalid.jwt.token",
            "Bearer invalid_token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
            "",
            "null",
        ]

        for invalid_token in invalid_tokens:
            headers = {"Authorization": f"Bearer {invalid_token}"}
            response = await async_client.get(
                "/api/v1/admin/applications", headers=headers
            )
            assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_expired_jwt_token_rejection(self, async_client: AsyncClient):
        """Test rejection of expired JWT tokens."""
        # Create an expired token
        expired_token = create_access_token(
            data={"sub": "test_user_id"},
            expires_delta=timedelta(seconds=-1),  # Already expired
        )

        headers = {"Authorization": f"Bearer {expired_token}"}
        response = await async_client.get("/api/v1/admin/applications", headers=headers)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_token_with_invalid_user_id_rejection(
        self, async_client: AsyncClient
    ):
        """Test rejection of tokens with non-existent user IDs."""
        # Create token with non-existent user ID
        fake_token = create_access_token(data={"sub": "non-existent-user-id"})

        headers = {"Authorization": f"Bearer {fake_token}"}
        response = await async_client.get("/api/v1/admin/applications", headers=headers)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_disabled_user_token_rejection(
        self, async_client: AsyncClient, async_db_session: AsyncSession
    ):
        """Test rejection of tokens for disabled users."""
        # Create disabled user
        disabled_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="disabled_user",
            password_hash="hashed_password",
            role="user",
            is_active=False,  # Disabled
        )
        async_db_session.add(disabled_user)
        await async_db_session.commit()

        # Create token for disabled user
        disabled_token = create_access_token(data={"sub": disabled_user.id})

        headers = {"Authorization": f"Bearer {disabled_token}"}
        response = await async_client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 401


class TestOAuth2SecurityVulnerabilities:
    """Test OAuth2-specific security vulnerabilities and protections."""

    @pytest.fixture
    async def oauth2_security_setup(self, async_db_session: AsyncSession):
        """Set up OAuth2 security test data."""
        test_id = str(uuid.uuid4())[:8]

        # Create test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"oauth_sec_{test_id}@example.com",
            username=f"oauth_sec_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"oauth_dev_{test_id}@example.com",
            username=f"oauth_dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        # Create test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"oauth_sec_client_{test_id}",
            application_name=f"OAuth Security Test {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        application.set_client_secret("oauth_security_secret")
        async_db_session.add(application)

        await async_db_session.commit()
        return {"user": user, "developer": developer, "application": application}

    @pytest.mark.asyncio
    async def test_authorization_code_replay_attack_prevention(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_security_setup,
    ):
        """Test prevention of authorization code replay attacks."""
        from app.models.user import AuthorizationCode

        data = oauth2_security_setup

        # Create authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_replay_code",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # First token exchange should succeed
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_replay_code",
                "client_id": data["application"].client_id,
                "client_secret": "oauth_security_secret",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 200

        # Second attempt should fail (code already used)
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_replay_code",
                "client_id": data["application"].client_id,
                "client_secret": "oauth_security_secret",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_client_secret_validation(
        self,
        async_client: AsyncClient,
        async_db_session: AsyncSession,
        oauth2_security_setup,
    ):
        """Test client secret validation in token exchange."""
        from app.models.user import AuthorizationCode

        data = oauth2_security_setup

        # Create authorization code
        auth_code = AuthorizationCode(
            id=str(uuid.uuid4()),
            code="test_client_secret_code",
            client_id=data["application"].client_id,
            user_id=data["user"].id,
            redirect_uri="http://localhost:3000/callback",
            scopes=["openid", "profile"],
            expires_at=datetime.utcnow() + timedelta(minutes=10),
        )
        async_db_session.add(auth_code)
        await async_db_session.commit()

        # Test with wrong client secret
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_client_secret_code",
                "client_id": data["application"].client_id,
                "client_secret": "wrong_secret",
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 401

        # Test with missing client secret
        response = await async_client.post(
            "/oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": "test_client_secret_code",
                "client_id": data["application"].client_id,
                "redirect_uri": "http://localhost:3000/callback",
            },
        )
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_redirect_uri_validation_security(
        self, async_client: AsyncClient, oauth2_security_setup
    ):
        """Test redirect URI validation for security."""
        data = oauth2_security_setup

        malicious_redirect_uris = [
            "http://malicious-site.com/steal-codes",
            "javascript:alert('XSS')",
            "data:text/html,<script>alert('XSS')</script>",
            "http://localhost:3000/callback/../../../etc/passwd",
            "http://localhost:3000/callback?evil=true",
        ]

        for malicious_uri in malicious_redirect_uris:
            response = await async_client.get(
                "/oauth2/authorize",
                params={
                    "client_id": data["application"].client_id,
                    "redirect_uri": malicious_uri,
                    "response_type": "code",
                    "scope": "openid profile",
                },
            )

            # Should reject malicious redirect URIs
            assert response.status_code in [302, 400]

            if response.status_code == 302:
                # If redirecting, should contain error
                redirect_url = response.headers["location"]
                assert "error=" in redirect_url

    @pytest.mark.asyncio
    async def test_scope_injection_prevention(
        self, async_client: AsyncClient, oauth2_security_setup
    ):
        """Test prevention of scope injection attacks."""
        data = oauth2_security_setup

        malicious_scopes = [
            "openid profile admin",  # Trying to inject admin scope
            "openid profile; DROP TABLE users; --",  # SQL injection attempt
            "openid profile<script>alert('XSS')</script>",  # XSS attempt
            "openid profile\nwrite\ndelete",  # Newline injection
        ]

        for malicious_scope in malicious_scopes:
            response = await async_client.get(
                "/oauth2/authorize",
                params={
                    "client_id": data["application"].client_id,
                    "redirect_uri": "http://localhost:5550/auth/callback",
                    "response_type": "code",
                    "scope": malicious_scope,
                },
            )

            # Should reject or sanitize malicious scopes
            assert response.status_code in [302, 400]


class TestRateLimitingAndAbusePrevention:
    """Test rate limiting and abuse prevention mechanisms."""

    @pytest.mark.asyncio
    async def test_registration_rate_limiting(self, async_client: AsyncClient):
        """Test rate limiting on user registration."""
        # Attempt multiple rapid registrations
        for i in range(10):
            user_data = {
                "email": f"test{i}@example.com",
                "username": f"testuser{i}",
                "password": "password123",
                "first_name": "Test",
                "last_name": "User",
            }

            response = await async_client.post("/api/v1/auth/register", json=user_data)

            # First few should succeed, then rate limiting should kick in
            if i < 5:
                assert response.status_code in [200, 400]  # Success or duplicate
            else:
                # Later requests might be rate limited
                assert response.status_code in [200, 400, 429]

    @pytest.mark.asyncio
    async def test_login_brute_force_protection(self, async_client: AsyncClient):
        """Test brute force protection on login attempts."""
        # First register a user
        user_data = {
            "email": "<EMAIL>",
            "username": "bruteforceuser",
            "password": "correct_password",
            "first_name": "Brute",
            "last_name": "Force",
        }

        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 200

        # Attempt multiple failed logins
        for i in range(10):
            login_data = {
                "email": "<EMAIL>",
                "password": f"wrong_password_{i}",
            }

            response = await async_client.post("/api/v1/auth/login", json=login_data)

            # Should consistently return 401 for wrong password
            # Rate limiting might kick in after several attempts
            assert response.status_code in [401, 429]

    @pytest.mark.asyncio
    async def test_oauth2_authorization_rate_limiting(
        self, async_client: AsyncClient, async_db_session: AsyncSession
    ):
        """Test rate limiting on OAuth2 authorization requests."""
        # Create test application for rate limiting test
        test_id = str(uuid.uuid4())[:8]

        developer = User(
            id=str(uuid.uuid4()),
            email=f"rate_dev_{test_id}@example.com",
            username=f"rate_dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"rate_test_client_{test_id}",
            application_name=f"Rate Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        application.set_client_secret("rate_test_secret")
        async_db_session.add(application)
        await async_db_session.commit()

        # Attempt multiple rapid authorization requests
        for i in range(10):  # Reduced from 20 to 10 for faster testing
            response = await async_client.get(
                "/oauth2/authorize",
                params={
                    "client_id": application.client_id,
                    "redirect_uri": "http://localhost:5550/auth/callback",
                    "response_type": "code",
                    "scope": "openid profile",
                    "state": f"test_state_{i}",
                },
            )

            # Should handle requests appropriately
            # Rate limiting might kick in for excessive requests
            assert response.status_code in [302, 400, 429]
