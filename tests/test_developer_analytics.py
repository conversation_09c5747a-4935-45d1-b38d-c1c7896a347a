"""
Tests for DeveloperAnalyticsService.

This module tests the developer analytics functionality for user management and reporting.
"""

import uuid
from datetime import datetime, timedelta

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    RegisteredApplication,
    User,
    UserAccessLog,
    UserApplicationConnection,
)
from app.services.analytics.developer_analytics import DeveloperAnalyticsService


class TestDeveloperAnalyticsService:
    """Test DeveloperAnalyticsService functionality."""

    @pytest.fixture
    async def analytics_test_data(self, async_db_session: AsyncSession):
        """Set up simplified test data for developer analytics tests."""
        test_id = str(uuid.uuid4())[:8]

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"dev_{test_id}@example.com",
            username=f"developer_{test_id}",
            password_hash="hashed_password",
            role="developer",
        )
        async_db_session.add(developer)

        # Create single test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"user_{test_id}@example.com",
            username=f"testuser_{test_id}",
            password_hash="hashed_password",
            role="user",
        )
        async_db_session.add(user)

        # Create single test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"test_client_{test_id}",
            application_name=f"Test Application {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile", "email"],
        )
        application.set_client_secret("test_secret")
        async_db_session.add(application)

        await async_db_session.flush()

        # Create user-application connection
        connection = UserApplicationConnection(
            id=str(uuid.uuid4()),
            user_id=user.id,
            application_id=application.id,
            client_id=application.client_id,
            granted_scopes=["openid", "profile", "email"],
            first_connected_at=datetime.utcnow() - timedelta(days=1),
            last_accessed_at=datetime.utcnow() - timedelta(hours=1),
            access_count=10,
            is_active=True,
        )
        async_db_session.add(connection)

        # Create access log
        log = UserAccessLog(
            id=str(uuid.uuid4()),
            user_id=user.id,
            application_id=application.id,
            client_id=application.client_id,
            action="oauth2_authorization",
            success=True,
            timestamp=datetime.utcnow() - timedelta(hours=1),
        )
        async_db_session.add(log)

        await async_db_session.commit()

        return {
            "developer": developer,
            "users": [user],
            "applications": [application],
            "user": user,
            "application": application,
            "connection": connection,
            "log": log,
        }

    @pytest.mark.asyncio
    async def test_get_developer_users(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting all users across developer's applications."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get developer users
        users = await service.get_developer_users(
            developer_id=data["developer"].id, limit=10, offset=0
        )

        # Should return users who have connections to developer's applications
        assert len(users) > 0

        # Verify user data structure
        for user in users:
            assert "id" in user
            assert "email" in user
            assert "username" in user
            assert "connection_count" in user
            assert "last_access" in user
            assert "total_accesses" in user

    @pytest.mark.asyncio
    async def test_get_user_details(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting detailed information about a specific user."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get details for the test user
        user_id = data["user"].id
        user_details = await service.get_user_details(
            developer_id=data["developer"].id, user_id=user_id
        )

        assert user_details is not None
        assert user_details["user"]["id"] == user_id
        assert "connections" in user_details
        assert "recent_activity" in user_details
        assert "total_connections" in user_details
        assert "total_accesses" in user_details

        # Verify connections data
        connections = user_details["connections"]
        assert len(connections) > 0
        for connection in connections:
            assert "application_id" in connection
            assert "application_name" in connection
            assert "granted_scopes" in connection
            assert "access_count" in connection

    @pytest.mark.asyncio
    async def test_get_user_details_unauthorized(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting user details for user not connected to developer's applications."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Create another developer with unique email
        test_id = str(uuid.uuid4())[:8]
        other_developer = User(
            id=str(uuid.uuid4()),
            email=f"other_{test_id}@example.com",
            username=f"otherdeveloper_{test_id}",
            password_hash="hashed_password",
            role="developer",
        )
        async_db_session.add(other_developer)
        await async_db_session.commit()

        # Try to get user details with wrong developer
        user_details = await service.get_user_details(
            developer_id=other_developer.id, user_id=data["users"][0].id
        )

        assert user_details is None

    @pytest.mark.asyncio
    async def test_get_application_users(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting users connected to a specific application."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get users for the test application
        application_id = data["application"].id
        users = await service.get_application_users(
            developer_id=data["developer"].id,
            application_id=application_id,
            limit=10,
            offset=0,
        )

        assert len(users) > 0

        # Verify user data structure
        for user in users:
            assert "id" in user
            assert "email" in user
            assert "granted_scopes" in user
            assert "access_count" in user
            assert "first_connected_at" in user
            assert "last_accessed_at" in user

    @pytest.mark.asyncio
    async def test_get_application_users_unauthorized(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting application users with wrong developer."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Create another developer with unique email
        test_id = str(uuid.uuid4())[:8]
        other_developer = User(
            id=str(uuid.uuid4()),
            email=f"other_{test_id}@example.com",
            username=f"otherdeveloper_{test_id}",
            password_hash="hashed_password",
            role="developer",
        )
        async_db_session.add(other_developer)
        await async_db_session.commit()

        # Try to get application users with wrong developer
        users = await service.get_application_users(
            developer_id=other_developer.id,
            application_id=data["applications"][0].id,
            limit=10,
            offset=0,
        )

        assert len(users) == 0

    @pytest.mark.asyncio
    async def test_get_application_analytics(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting comprehensive analytics for a specific application."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get analytics for the test application
        application_id = data["application"].id
        analytics = await service.get_application_analytics(
            developer_id=data["developer"].id, application_id=application_id
        )

        assert analytics is not None
        assert "application" in analytics
        assert "metrics" in analytics

        # Verify application data
        application_data = analytics["application"]
        assert application_data["id"] == application_id
        assert "name" in application_data
        assert "client_id" in application_data

        # Verify metrics
        metrics = analytics["metrics"]
        assert "total_users" in metrics
        assert "active_users_30d" in metrics
        assert "total_accesses" in metrics
        assert "success_rate_30d" in metrics

    @pytest.mark.asyncio
    async def test_get_analytics_overview(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting comprehensive analytics overview for developer."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get analytics overview
        overview = await service.get_analytics_overview(
            developer_id=data["developer"].id
        )

        assert "total_applications" in overview
        assert "active_applications" in overview
        assert "approved_applications" in overview
        assert "total_users" in overview
        assert "active_users_30d" in overview
        assert "total_accesses" in overview
        assert "success_rate_30d" in overview
        assert "applications" in overview

        # Verify applications summary
        applications = overview["applications"]
        assert len(applications) == len(data["applications"])
        for application in applications:
            assert "id" in application
            assert "name" in application
            assert "users_count" in application
            assert "is_active" in application
            assert "admin_approved" in application

    @pytest.mark.asyncio
    async def test_get_activity_logs(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting activity logs across all developer's applications."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get activity logs
        logs = await service.get_activity_logs(
            developer_id=data["developer"].id, limit=20, offset=0
        )

        assert len(logs) > 0

        # Verify log data structure
        for log in logs:
            assert "id" in log
            assert "action" in log
            assert "timestamp" in log
            assert "success" in log
            assert "user" in log
            assert "application" in log

    @pytest.mark.asyncio
    async def test_get_activity_logs_with_filters(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting activity logs with filters."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get logs with action filter
        logs = await service.get_activity_logs(
            developer_id=data["developer"].id,
            limit=20,
            offset=0,
            action_filter="action_0",
        )

        # All logs should have the filtered action
        for log in logs:
            assert log["action"] == "action_0"

        # Get logs with success filter
        logs = await service.get_activity_logs(
            developer_id=data["developer"].id, limit=20, offset=0, success_filter=True
        )

        # All logs should be successful
        for log in logs:
            assert log["success"] is True

    @pytest.mark.asyncio
    async def test_get_usage_statistics(
        self, async_db_session: AsyncSession, analytics_test_data
    ):
        """Test getting usage statistics and trends."""
        data = analytics_test_data
        service = DeveloperAnalyticsService(async_db_session)

        # Get usage statistics
        stats = await service.get_usage_statistics(
            developer_id=data["developer"].id, days=30
        )

        assert "daily_stats" in stats
        assert "trends" in stats
        assert "summary" in stats

        # Verify trends data
        trends = stats["trends"]
        assert "request_trend_7d" in trends
        assert "user_trend_7d" in trends
        assert "period_days" in trends

        # Verify summary data
        summary = stats["summary"]
        assert "total_requests" in summary
        assert "average_daily_requests" in summary
        assert "overall_success_rate" in summary
