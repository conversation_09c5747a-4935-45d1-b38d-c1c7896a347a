"""
Tests for Developer Portal API endpoints.

This module tests all developer-specific endpoints for application management,
credentials generation, and analytics.
"""

import uuid
from datetime import datetime

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token
from app.models.user import RegisteredApplication, User


# Shared fixtures to eliminate duplication
@pytest.fixture
async def developer_auth(async_db_session: AsyncSession):
    """Set up authenticated developer user for API tests."""
    test_id = str(uuid.uuid4())[:8]

    developer = User(
        id=str(uuid.uuid4()),
        email=f"dev_{test_id}@example.com",
        username=f"developer_{test_id}",
        password_hash="hashed_password",
        role="developer",
    )
    async_db_session.add(developer)
    await async_db_session.commit()

    access_token = create_access_token(data={"sub": developer.id})

    return {
        "developer": developer,
        "access_token": access_token,
        "headers": {"Authorization": f"Bearer {access_token}"},
    }


@pytest.fixture
async def regular_user_auth(async_db_session: AsyncSession):
    """Set up authenticated regular user for unauthorized access tests."""
    test_id = str(uuid.uuid4())[:8]

    user = User(
        id=str(uuid.uuid4()),
        email=f"user_{test_id}@example.com",
        username=f"user_{test_id}",
        password_hash="hashed_password",
        role="user",
    )
    async_db_session.add(user)
    await async_db_session.commit()

    access_token = create_access_token(data={"sub": user.id})

    return {
        "user": user,
        "access_token": access_token,
        "headers": {"Authorization": f"Bearer {access_token}"},
    }


class TestDeveloperApplicationAPI:
    """Test developer application management endpoints."""

    async def test_list_applications_empty(self, client: AsyncClient, developer_auth):
        """Test listing applications when none exist."""
        auth_data = developer_auth
        response = await client.get(
            "/api/v1/developer/applications", headers=auth_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0

    async def test_list_applications_with_data(
        self, client: AsyncClient, developer_auth
    ):
        """Test listing applications with existing data."""
        auth_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "TestApp",
            "description": "Test app for listing",
            "allowed_redirect_uris": ["http://localhost:3000/auth/callback"],
            "allowed_scopes": ["openid", "profile"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert create_response.status_code == 200

        # Now test listing
        response = await client.get(
            "/api/v1/developer/applications", headers=auth_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

        # Check structure of first application
        if data:
            application = data[0]
            assert "id" in application
            assert "client_id" in application
            assert "application_name" in application
            assert "allowed_redirect_uris" in application
            assert "allowed_scopes" in application
            assert "is_active" in application
            assert "created_at" in application
            assert "stats" in application

            # Check stats structure
            stats = application["stats"]
            assert "total_users" in stats
            assert "monthly_requests" in stats
            assert "last_request" in stats

    async def test_create_application(self, client: AsyncClient, developer_auth):
        """Test creating a new application."""
        auth_data = developer_auth

        application_data = {
            "application_name": "TestApp",
            "description": "Test application for SSO integration",
            "allowed_redirect_uris": ["http://localhost:3000/auth/callback"],
            "allowed_scopes": ["openid", "profile", "email"],
        }

        response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()

        assert data["application_name"] == "TestApp"
        assert data["description"] == "Test application for SSO integration"
        assert data["allowed_redirect_uris"] == ["http://localhost:3000/auth/callback"]
        assert data["allowed_scopes"] == ["openid", "profile", "email"]
        assert data["is_active"] is True
        assert "client_id" in data
        assert data["client_id"].startswith("gngo_")
        assert "id" in data
        assert "created_at" in data
        assert "stats" in data

    async def test_create_application_invalid_data(
        self, client: AsyncClient, developer_auth
    ):
        """Test creating application with invalid data."""
        auth_data = developer_auth

        # Missing required fields
        response = await client.post(
            "/api/v1/developer/applications", json={}, headers=auth_data["headers"]
        )
        assert response.status_code == 422

        # Invalid redirect URI format
        invalid_data = {
            "application_name": "TestApp",
            "description": "Test app",
            "allowed_redirect_uris": [],  # Empty array
            "allowed_scopes": ["openid"],
        }
        response = await client.post(
            "/api/v1/developer/applications",
            json=invalid_data,
            headers=auth_data["headers"],
        )
        assert response.status_code == 422

    async def test_get_application_by_id(self, client: AsyncClient, developer_auth):
        """Test getting a specific application by ID."""
        auth_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "GetTestApp",
            "description": "Test app for get endpoint",
            "allowed_redirect_uris": ["http://localhost:3001/auth/callback"],
            "allowed_scopes": ["openid", "profile"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Now get it by ID
        response = await client.get(
            f"/api/v1/developer/applications/{application_id}",
            headers=auth_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()

        assert data["id"] == application_id
        assert data["application_name"] == "GetTestApp"
        assert data["description"] == "Test app for get endpoint"

    async def test_get_nonexistent_application(self, client: AsyncClient):
        """Test getting a application that doesn't exist."""
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = await client.get(f"/api/v1/developer/applications/{fake_id}")
        assert response.status_code == 404

    async def test_update_application(self, client: AsyncClient, developer_auth):
        """Test updating a application."""
        auth_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "UpdateTestApp",
            "description": "Original description",
            "allowed_redirect_uris": ["http://localhost:3002/auth/callback"],
            "allowed_scopes": ["openid", "profile"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Update it
        update_data = {
            "application_name": "UpdatedTestApp",
            "description": "Updated description",
            "allowed_scopes": ["openid", "profile", "email"],
            "is_active": False,
        }

        response = await client.put(
            f"/api/v1/developer/applications/{application_id}",
            json=update_data,
            headers=auth_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()

        assert data["application_name"] == "UpdatedTestApp"
        assert data["description"] == "Updated description"
        assert data["allowed_scopes"] == ["openid", "profile", "email"]
        assert data["is_active"] is False

    async def test_delete_application(self, client: AsyncClient, developer_auth):
        """Test deleting (deactivating) a application."""
        auth_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "DeleteTestApp",
            "description": "App to be deleted",
            "allowed_redirect_uris": ["http://localhost:3003/auth/callback"],
            "allowed_scopes": ["openid"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Delete it
        response = await client.delete(
            f"/api/v1/developer/applications/{application_id}",
            headers=auth_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "deactivated" in data["message"].lower()

        # Verify it's deactivated
        get_response = await client.get(
            f"/api/v1/developer/applications/{application_id}",
            headers=auth_data["headers"],
        )
        assert get_response.status_code == 200
        application_data = get_response.json()
        assert application_data["is_active"] is False

    async def test_regenerate_client_secret(self, client: AsyncClient, developer_auth):
        """Test regenerating client secret for a application."""
        auth_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "SecretTestApp",
            "description": "App for secret regeneration test",
            "allowed_redirect_uris": ["http://localhost:3004/auth/callback"],
            "allowed_scopes": ["openid"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=auth_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Regenerate secret
        response = await client.post(
            f"/api/v1/developer/applications/{application_id}/regenerate-secret",
            headers=auth_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()

        assert "client_id" in data
        assert "client_secret" in data
        assert "message" in data
        assert data["client_secret"].startswith("gngo_secret_")
        assert "regenerated" in data["message"].lower()

    async def test_unauthorized_access_regular_user(
        self, client: AsyncClient, regular_user_auth
    ):
        """Test that regular users cannot access developer endpoints."""
        auth_data = regular_user_auth

        # Try to list applications
        response = await client.get(
            "/api/v1/developer/applications", headers=auth_data["headers"]
        )
        assert response.status_code == 403

        # Try to create application
        response = await client.post(
            "/api/v1/developer/applications",
            json={"application_name": "Test"},
            headers=auth_data["headers"],
        )
        assert response.status_code == 403

    async def test_unauthorized_access_no_token(self, client: AsyncClient):
        """Test that endpoints require authentication."""
        # Try to list applications without token
        response = await client.get("/api/v1/developer/applications")
        assert response.status_code == 401

        # Try to create application without token
        response = await client.post(
            "/api/v1/developer/applications", json={"application_name": "Test"}
        )
        assert response.status_code == 401

    async def test_create_application_edge_cases(
        self, client: AsyncClient, developer_auth
    ):
        """Test application creation with edge cases."""
        auth_data = developer_auth

        # Test with very long application name
        long_name_data = {
            "application_name": "A" * 300,  # Very long name
            "description": "Test app",
            "allowed_redirect_uris": ["http://localhost:3000/callback"],
            "allowed_scopes": ["openid"],
        }
        response = await client.post(
            "/api/v1/developer/applications",
            json=long_name_data,
            headers=auth_data["headers"],
        )
        assert response.status_code == 422  # Should fail validation

        # Test with invalid redirect URI
        invalid_uri_data = {
            "application_name": "TestApp",
            "description": "Test app",
            "allowed_redirect_uris": ["not-a-valid-uri"],
            "allowed_scopes": ["openid"],
        }
        response = await client.post(
            "/api/v1/developer/applications",
            json=invalid_uri_data,
            headers=auth_data["headers"],
        )
        assert response.status_code == 422  # Should fail validation


class TestDeveloperAnalyticsAPI:
    """Test developer analytics endpoints."""

    async def test_developer_analytics_overview(
        self, client: AsyncClient, developer_auth
    ):
        """Test getting developer analytics overview."""
        auth_data = developer_auth
        response = await client.get(
            "/api/v1/developer/analytics/overview", headers=auth_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()

        assert "total_applications" in data
        assert "total_users" in data
        assert "monthly_requests" in data
        assert "weekly_requests" in data
        assert "average_daily_requests" in data
        assert "period" in data

        # Check data types
        assert isinstance(data["total_applications"], int)
        assert isinstance(data["total_users"], int)
        assert isinstance(data["monthly_requests"], int)
        assert isinstance(data["weekly_requests"], int)
        assert isinstance(data["average_daily_requests"], (int, float))
