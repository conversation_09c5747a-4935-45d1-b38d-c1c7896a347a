"""
Comprehensive Repository Layer Tests for GeNieGO SSO Server.

This module tests repository functionality including CRUD operations,
database interactions, error handling, and transaction management.
"""

import uuid
from datetime import datetime, timedelta

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import RegisteredApplication, User, UserSession
from app.repositories.base import BaseRepository, InMemoryRepository


class TestBaseRepository:
    """Test base repository functionality."""

    def test_base_repository_interface(self):
        """Test that BaseRepository defines the expected interface."""
        assert BaseRepository is not None
        assert hasattr(BaseRepository, "__init__")

        # Check for abstract methods
        expected_methods = [
            "create",
            "get_by_id",
            "get_all",
            "update",
            "delete",
            "count",
        ]
        for method in expected_methods:
            assert hasattr(
                BaseRepository, method
            ), f"BaseRepository should have {method} method"


class TestInMemoryRepository:
    """Test in-memory repository implementation."""

    @pytest.fixture
    def user_repository(self):
        """Create an in-memory repository for User model testing."""
        return InMemoryRepository(User)

    def test_repository_instantiation(self, user_repository):
        """Test that InMemoryRepository can be instantiated."""
        assert user_repository is not None
        assert hasattr(user_repository, "_data")
        assert hasattr(user_repository, "model")
        assert user_repository.model == User

    def test_create_operation(self, user_repository):
        """Test creating items in the repository."""
        # Create test user data
        user_data = {
            "id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "username": "testuser",
            "password_hash": "hashed_password",
            "role": "user",
            "is_active": True,
        }

        # Create user
        created_user = user_repository.create(user_data)

        assert created_user is not None
        assert created_user["id"] == user_data["id"]
        assert created_user["email"] == user_data["email"]
        assert created_user["username"] == user_data["username"]

    def test_get_by_id_operation(self, user_repository):
        """Test retrieving items by ID."""
        # Create test user
        user_id = str(uuid.uuid4())
        user_data = {
            "id": user_id,
            "email": "<EMAIL>",
            "username": "get_test_user",
            "password_hash": "hashed_password",
            "role": "user",
            "is_active": True,
        }
        user_repository.create(user_data)

        # Retrieve user
        retrieved_user = user_repository.get_by_id(user_id)

        assert retrieved_user is not None
        assert retrieved_user["id"] == user_id
        assert retrieved_user["email"] == "<EMAIL>"
        assert retrieved_user["username"] == "get_test_user"

    def test_get_by_id_nonexistent(self, user_repository):
        """Test retrieving non-existent items."""
        result = user_repository.get_by_id("nonexistent-id")
        assert result is None

    def test_update_operation(self, user_repository):
        """Test updating items in the repository."""
        # Create test user
        user_id = str(uuid.uuid4())
        user_data = {
            "id": user_id,
            "email": "<EMAIL>",
            "username": "update_test_user",
            "password_hash": "hashed_password",
            "role": "user",
            "is_active": True,
        }
        user_repository.create(user_data)

        # Update user
        updated_data = {
            "id": user_id,
            "first_name": "Updated",
            "last_name": "User",
            "is_active": False,
        }
        updated_user = user_repository.update(updated_data)

        assert updated_user is not None
        assert updated_user["id"] == user_id
        assert updated_user["first_name"] == "Updated"
        assert updated_user["last_name"] == "User"
        assert updated_user["is_active"] is False

    def test_update_nonexistent(self, user_repository):
        """Test updating non-existent items."""
        result = user_repository.update(
            {"id": "nonexistent-id", "first_name": "Updated"}
        )
        # Should return the entity even if it doesn't exist in the repository
        assert result is not None
        assert result["id"] == "nonexistent-id"

    def test_delete_operation(self, user_repository):
        """Test deleting items from the repository."""
        # Create test user
        user_id = str(uuid.uuid4())
        user_data = {
            "id": user_id,
            "email": "<EMAIL>",
            "username": "delete_test_user",
            "password_hash": "hashed_password",
            "role": "user",
            "is_active": True,
        }
        user_repository.create(user_data)

        # Verify user exists
        assert user_repository.get_by_id(user_id) is not None

        # Delete user
        deleted = user_repository.delete(user_id)
        assert deleted is True

        # Verify user is gone
        assert user_repository.get_by_id(user_id) is None

    def test_delete_nonexistent(self, user_repository):
        """Test deleting non-existent items."""
        result = user_repository.delete("nonexistent-id")
        assert result is False

    def test_get_all_operation(self, user_repository):
        """Test listing all items in the repository."""
        # Start with empty repository
        initial_users = user_repository.get_all()
        initial_count = len(initial_users)

        # Add test users
        test_users = []
        for i in range(3):
            user_data = {
                "id": str(uuid.uuid4()),
                "email": f"list_test_{i}@example.com",
                "username": f"list_test_user_{i}",
                "password_hash": "hashed_password",
                "role": "user",
                "is_active": True,
            }
            user_repository.create(user_data)
            test_users.append(user_data)

        # List all users
        all_users = user_repository.get_all()

        assert len(all_users) == initial_count + 3

        # Verify all test users are present
        user_ids = [user["id"] for user in all_users]
        for test_user in test_users:
            assert test_user["id"] in user_ids

    def test_count_operation(self, user_repository):
        """Test counting items in the repository."""
        # Get initial count
        initial_count = user_repository.count()

        # Add test users
        for i in range(5):
            user_data = {
                "id": str(uuid.uuid4()),
                "email": f"count_test_{i}@example.com",
                "username": f"count_test_user_{i}",
                "password_hash": "hashed_password",
                "role": "user",
                "is_active": True,
            }
            user_repository.create(user_data)

        # Verify count
        final_count = user_repository.count()
        assert final_count == initial_count + 5


class TestDatabaseRepositoryIntegration:
    """Test repository integration with actual database operations."""

    @pytest.mark.asyncio
    async def test_user_crud_operations(self, async_db_session: AsyncSession):
        """Test CRUD operations with User model in database."""
        test_id = str(uuid.uuid4())[:8]

        # Create user
        user = User(
            id=str(uuid.uuid4()),
            email=f"repo_test_{test_id}@example.com",
            username=f"repo_test_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)
        await async_db_session.commit()

        # Read user
        result = await async_db_session.execute(select(User).where(User.id == user.id))
        retrieved_user = result.scalar_one_or_none()

        assert retrieved_user is not None
        assert retrieved_user.email == user.email
        assert retrieved_user.username == user.username

        # Update user
        retrieved_user.first_name = "Test"
        retrieved_user.last_name = "User"
        await async_db_session.commit()

        # Verify update
        await async_db_session.refresh(retrieved_user)
        assert retrieved_user.first_name == "Test"
        assert retrieved_user.last_name == "User"

        # Delete user
        await async_db_session.delete(retrieved_user)
        await async_db_session.commit()

        # Verify deletion
        result = await async_db_session.execute(select(User).where(User.id == user.id))
        deleted_user = result.scalar_one_or_none()
        assert deleted_user is None

    @pytest.mark.asyncio
    async def test_application_crud_operations(self, async_db_session: AsyncSession):
        """Test CRUD operations with RegisteredApplication model."""
        test_id = str(uuid.uuid4())[:8]

        # Create developer user first
        developer = User(
            id=str(uuid.uuid4()),
            email=f"dev_{test_id}@example.com",
            username=f"dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)
        await async_db_session.commit()

        # Create application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"test_client_{test_id}",
            application_name=f"Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        application.set_client_secret("test_secret")
        async_db_session.add(application)
        await async_db_session.commit()

        # Read application
        result = await async_db_session.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application.id
            )
        )
        retrieved_app = result.scalar_one_or_none()

        assert retrieved_app is not None
        assert retrieved_app.client_id == application.client_id
        assert retrieved_app.application_name == application.application_name
        assert retrieved_app.developer_id == developer.id

        # Update application
        retrieved_app.application_name = "Updated Test App"
        retrieved_app.description = "Updated description"
        await async_db_session.commit()

        # Verify update
        await async_db_session.refresh(retrieved_app)
        assert retrieved_app.application_name == "Updated Test App"
        assert retrieved_app.description == "Updated description"

        # Delete application
        await async_db_session.delete(retrieved_app)
        await async_db_session.commit()

        # Verify deletion
        result = await async_db_session.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application.id
            )
        )
        deleted_app = result.scalar_one_or_none()
        assert deleted_app is None

    @pytest.mark.asyncio
    async def test_session_crud_operations(self, async_db_session: AsyncSession):
        """Test CRUD operations with UserSession model."""
        test_id = str(uuid.uuid4())[:8]

        # Create user first
        user = User(
            id=str(uuid.uuid4()),
            email=f"session_test_{test_id}@example.com",
            username=f"session_test_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)
        await async_db_session.commit()

        # Create session
        session = UserSession(
            id=str(uuid.uuid4()),
            user_id=user.id,
            session_token=f"test_session_{test_id}",
            expires_at=datetime.utcnow() + timedelta(hours=24),
            ip_address="127.0.0.1",
            user_agent="Test User Agent",
        )
        async_db_session.add(session)
        await async_db_session.commit()

        # Read session
        result = await async_db_session.execute(
            select(UserSession).where(UserSession.id == session.id)
        )
        retrieved_session = result.scalar_one_or_none()

        assert retrieved_session is not None
        assert retrieved_session.user_id == user.id
        assert retrieved_session.session_token == session.session_token
        assert retrieved_session.ip_address == "127.0.0.1"

        # Update session
        retrieved_session.ip_address = "***********"
        retrieved_session.user_agent = "Updated User Agent"
        await async_db_session.commit()

        # Verify update
        await async_db_session.refresh(retrieved_session)
        assert retrieved_session.ip_address == "***********"
        assert retrieved_session.user_agent == "Updated User Agent"

        # Delete session
        await async_db_session.delete(retrieved_session)
        await async_db_session.commit()

        # Verify deletion
        result = await async_db_session.execute(
            select(UserSession).where(UserSession.id == session.id)
        )
        deleted_session = result.scalar_one_or_none()
        assert deleted_session is None

    @pytest.mark.asyncio
    async def test_transaction_rollback(self, async_db_session: AsyncSession):
        """Test transaction rollback functionality."""
        test_id = str(uuid.uuid4())[:8]

        # Create user
        user = User(
            id=str(uuid.uuid4()),
            email=f"rollback_test_{test_id}@example.com",
            username=f"rollback_test_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)

        # Simulate error and rollback
        try:
            # This should cause a constraint violation
            duplicate_user = User(
                id=str(uuid.uuid4()),
                email=f"rollback_test_{test_id}@example.com",  # Same email
                username=f"rollback_test_{test_id}",  # Same username
                password_hash="hashed_password",
                role="user",
                is_active=True,
            )
            async_db_session.add(duplicate_user)
            await async_db_session.commit()
        except Exception:
            await async_db_session.rollback()

        # Verify original user was not committed
        result = await async_db_session.execute(
            select(User).where(User.email == f"rollback_test_{test_id}@example.com")
        )
        users = result.scalars().all()
        assert len(users) == 0  # No users should exist due to rollback
