"""
Comprehensive Tests for ActivityLoggerService.

This module tests the activity logging functionality with real database operations,
proper error handling, and comprehensive logging scenarios.
"""

import uuid
from datetime import datetime, timedelta

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import RegisteredApplication, User, UserAccessLog
from app.services.connections.activity_logger import ActivityLoggerService


class TestActivityLoggerService:
    """Test ActivityLoggerService functionality with real database operations."""

    @pytest.fixture
    async def activity_logger_setup(self, async_db_session: AsyncSession):
        """Set up test data for activity logger tests."""
        test_id = str(uuid.uuid4())[:8]

        # Create test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"activity_test_{test_id}@example.com",
            username=f"activity_test_{test_id}",
            password_hash="hashed_password",
            role="user",
            is_active=True,
        )
        async_db_session.add(user)

        # Create test developer
        developer = User(
            id=str(uuid.uuid4()),
            email=f"activity_dev_{test_id}@example.com",
            username=f"activity_dev_{test_id}",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        # Create test application
        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id=f"activity_client_{test_id}",
            application_name=f"Activity Test App {test_id}",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        application.set_client_secret("activity_secret")
        async_db_session.add(application)

        await async_db_session.commit()

        # Create activity logger service
        service = ActivityLoggerService(async_db_session)

        return {
            "user": user,
            "developer": developer,
            "application": application,
            "service": service,
        }

    @pytest.mark.asyncio
    async def test_activity_logger_initialization(self, async_db_session: AsyncSession):
        """Test ActivityLoggerService initialization."""
        service = ActivityLoggerService(async_db_session)
        assert service.db == async_db_session

    @pytest.mark.asyncio
    async def test_successful_oauth2_authorization_logging(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging successful OAuth2 authorization."""
        data = activity_logger_setup

        # Log successful OAuth2 authorization
        await data["service"].log_oauth2_authorization(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            action="oauth2_authorization",
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=True,
        )

        # Commit the transaction to make the log entry visible
        await async_db_session.commit()

        # Verify log entry was created
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert log_entry.user_id == data["user"].id
        assert log_entry.application_id == data["application"].id
        assert log_entry.client_id == data["application"].client_id
        assert log_entry.action == "oauth2_authorization"
        assert log_entry.ip_address == "***********"
        assert log_entry.user_agent == "Mozilla/5.0 Test Browser"
        assert log_entry.success is True
        assert log_entry.error_message is None
        assert log_entry.timestamp is not None

    @pytest.mark.asyncio
    async def test_failed_oauth2_authorization_logging(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging failed OAuth2 authorization."""
        data = activity_logger_setup

        # Log failed OAuth2 authorization
        await data["service"].log_oauth2_authorization(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            action="oauth2_authorization",
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=False,
            error_message="Invalid redirect URI",
        )

        # Commit the transaction to make the log entry visible
        await async_db_session.commit()

        # Verify log entry was created
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.success == False,
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert log_entry.user_id == data["user"].id
        assert log_entry.application_id == data["application"].id
        assert log_entry.client_id == data["application"].client_id
        assert log_entry.action == "oauth2_authorization"
        assert log_entry.ip_address == "***********"
        assert log_entry.user_agent == "Mozilla/5.0 Test Browser"
        assert log_entry.success is False
        assert log_entry.error_message == "Invalid redirect URI"
        assert log_entry.timestamp is not None

    @pytest.mark.asyncio
    async def test_token_exchange_logging(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging OAuth2 token exchange."""
        data = activity_logger_setup

        # Log token exchange
        await data["service"].log_oauth2_token_exchange(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=True,
        )

        # Commit the transaction to make the log entry visible
        await async_db_session.commit()

        # Verify log entry was created
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.action == "token_exchange",
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert log_entry.action == "token_exchange"
        assert log_entry.success is True

    @pytest.mark.asyncio
    async def test_userinfo_access_logging(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging OAuth2 userinfo access."""
        data = activity_logger_setup

        # Log userinfo access
        await data["service"].log_oauth2_userinfo_access(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=True,
        )

        # Commit the transaction to make the log entry visible
        await async_db_session.commit()

        # Verify log entry was created
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.action == "user_info_access",
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert log_entry.action == "user_info_access"
        assert log_entry.success is True

    @pytest.mark.asyncio
    async def test_multiple_activity_logs_for_user(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging multiple activities for the same user."""
        data = activity_logger_setup

        # Log multiple activities
        activities = [
            ("oauth2_authorization", True, None),
            ("oauth2_token_exchange", True, None),
            ("oauth2_userinfo_access", True, None),
            ("oauth2_authorization", False, "Invalid client"),
        ]

        for action, success, error_message in activities:
            await data["service"].log_activity(
                user_id=data["user"].id,
                application_id=data["application"].id,
                client_id=data["application"].client_id,
                action=action,
                ip_address="***********",
                user_agent="Mozilla/5.0 Test Browser",
                success=success,
                error_message=error_message,
            )

        # Verify all log entries were created
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
            )
        )
        log_entries = result.scalars().all()

        assert len(log_entries) >= 4  # At least 4 new entries

        # Verify different action types are present
        actions = [entry.action for entry in log_entries]
        assert "oauth2_authorization" in actions
        assert "oauth2_token_exchange" in actions
        assert "oauth2_userinfo_access" in actions

    @pytest.mark.asyncio
    async def test_activity_log_with_different_ip_addresses(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test logging activities from different IP addresses."""
        data = activity_logger_setup

        ip_addresses = ["************", "********", "************"]

        for ip in ip_addresses:
            await data["service"].log_activity(
                user_id=data["user"].id,
                application_id=data["application"].id,
                client_id=data["application"].client_id,
                action="oauth2_authorization",
                ip_address=ip,
                user_agent="Mozilla/5.0 Test Browser",
                success=True,
            )

        # Verify log entries with different IPs
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
            )
        )
        log_entries = result.scalars().all()

        logged_ips = [entry.ip_address for entry in log_entries]
        for ip in ip_addresses:
            assert ip in logged_ips

    @pytest.mark.asyncio
    async def test_activity_log_timestamp_accuracy(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test that activity log timestamps are accurate."""
        data = activity_logger_setup

        # Record time before logging with a small buffer
        from datetime import timedelta

        before_time = datetime.utcnow() - timedelta(seconds=1)

        await data["service"].log_activity(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            action="oauth2_authorization",
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=True,
        )

        # Record time after logging with a small buffer
        after_time = datetime.utcnow() + timedelta(seconds=1)

        # Verify log entry timestamp
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.ip_address == "***********",
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert before_time <= log_entry.timestamp <= after_time

    @pytest.mark.asyncio
    async def test_activity_log_error_handling(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test activity logging with various error scenarios."""
        data = activity_logger_setup

        # Test with None values (should handle gracefully)
        await data["service"].log_activity(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            action="oauth2_authorization",
            ip_address=None,  # None IP
            user_agent=None,  # None user agent
            success=False,
            error_message="Test error with None values",
        )

        # Verify log entry was created despite None values
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.application_id == data["application"].id,
                UserAccessLog.error_message == "Test error with None values",
            )
        )
        log_entry = result.scalar_one_or_none()

        assert log_entry is not None
        assert log_entry.success is False
        assert log_entry.error_message == "Test error with None values"

    @pytest.mark.asyncio
    async def test_activity_log_query_by_date_range(
        self, activity_logger_setup, async_db_session: AsyncSession
    ):
        """Test querying activity logs by date range."""
        data = activity_logger_setup

        # Log activity
        await data["service"].log_activity(
            user_id=data["user"].id,
            application_id=data["application"].id,
            client_id=data["application"].client_id,
            action="oauth2_authorization",
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
            success=True,
        )

        # Query logs from the last hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        result = await async_db_session.execute(
            select(UserAccessLog).where(
                UserAccessLog.user_id == data["user"].id,
                UserAccessLog.timestamp >= one_hour_ago,
            )
        )
        recent_logs = result.scalars().all()

        assert len(recent_logs) > 0
        for log in recent_logs:
            assert log.timestamp >= one_hour_ago
