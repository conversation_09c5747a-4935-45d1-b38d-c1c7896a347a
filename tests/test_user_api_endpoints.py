"""
Comprehensive Tests for User API Endpoints.

This module tests actual user API endpoints with real HTTP requests,
proper authentication, and comprehensive user management scenarios.
"""

import uuid
from datetime import datetime

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    RegisteredApplication,
    User,
    UserApplicationConnection,
    UserSession,
)


class TestUserAPIEndpoints:
    """Test actual User API endpoints with HTTP requests."""

    @pytest.fixture
    async def user_api_setup(self, async_db_session: AsyncSession):
        """Set up test data for user API endpoint tests."""
        test_id = str(uuid.uuid4())[:8]

        # Create test user
        user = User(
            id=str(uuid.uuid4()),
            email=f"user_api_{test_id}@example.com",
            username=f"user_api_{test_id}",
            password_hash="hashed_password",
            role="user",
            first_name="API",
            last_name="Test",
            is_active=True,
            is_verified=True,
        )
        user.set_password("api_test_password")
        async_db_session.add(user)

        # Create user session for authentication
        session = UserSession(
            id=str(uuid.uuid4()),
            user_id=user.id,
            session_token=f"api_session_{test_id}",
            expires_at=datetime.utcnow().replace(year=datetime.utcnow().year + 1),
            ip_address="127.0.0.1",
            user_agent="Test User Agent",
        )
        async_db_session.add(session)

        await async_db_session.commit()
        return {"user": user, "session": session}

    @pytest.mark.asyncio
    async def test_user_profile_endpoint(
        self, async_client: AsyncClient, user_api_setup
    ):
        """Test user profile endpoint returns correct user data."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        response = await async_client.get("/api/v1/user/profile")

        assert response.status_code == 200

        profile_data = response.json()
        assert profile_data["id"] == data["user"].id
        assert profile_data["email"] == data["user"].email
        assert profile_data["username"] == data["user"].username
        assert profile_data["first_name"] == data["user"].first_name
        assert profile_data["last_name"] == data["user"].last_name
        assert profile_data["role"] == data["user"].role
        assert profile_data["is_active"] == data["user"].is_active
        assert profile_data["is_verified"] == data["user"].is_verified
        assert "password_hash" not in profile_data  # Should not expose password

    @pytest.mark.asyncio
    async def test_user_profile_update_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user profile update endpoint."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Update user profile
        update_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "display_name": "Updated Display Name",
        }

        response = await async_client.put("/api/v1/user/profile", json=update_data)

        assert response.status_code == 200

        updated_profile = response.json()
        assert updated_profile["first_name"] == "Updated"
        assert updated_profile["last_name"] == "Name"

        # Verify changes in database
        await async_db_session.refresh(data["user"])
        assert data["user"].first_name == "Updated"
        assert data["user"].last_name == "Name"

    @pytest.mark.asyncio
    async def test_user_connected_applications_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user connected applications endpoint."""
        data = user_api_setup

        # Create test application and connection
        developer = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="app_dev",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id="connected_app_client",
            application_name="Connected Test App",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        async_db_session.add(application)

        connection = UserApplicationConnection(
            id=str(uuid.uuid4()),
            user_id=data["user"].id,
            application_id=application.id,
            client_id=application.client_id,
            granted_scopes=["openid", "profile"],
            first_connected_at=datetime.utcnow(),
            last_accessed_at=datetime.utcnow(),
            access_count=3,
            is_active=True,
        )
        async_db_session.add(connection)
        await async_db_session.commit()

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        response = await async_client.get("/api/v1/user/applications")

        assert response.status_code == 200

        response_data = response.json()
        assert "applications" in response_data
        connections_data = response_data["applications"]
        assert len(connections_data) >= 1

        # Find our test connection
        test_connection = next(
            (
                conn
                for conn in connections_data
                if conn["client_id"] == "connected_app_client"
            ),
            None,
        )
        assert test_connection is not None
        assert test_connection["name"] == "Connected Test App"
        assert test_connection["connection"]["granted_scopes"] == ["openid", "profile"]
        assert test_connection["connection"]["access_count"] == 3
        assert test_connection["connection"]["is_active"] is True

    @pytest.mark.asyncio
    async def test_user_revoke_application_access_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user can revoke application access."""
        data = user_api_setup

        # Create test application and connection
        developer = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="revoke_dev",
            password_hash="hashed_password",
            role="developer",
            is_active=True,
        )
        async_db_session.add(developer)

        application = RegisteredApplication(
            id=str(uuid.uuid4()),
            client_id="revoke_app_client",
            application_name="Revoke Test App",
            developer_id=developer.id,
            admin_approved=True,
            allowed_redirect_uris=["http://localhost:3000/callback"],
            allowed_scopes=["openid", "profile"],
            is_active=True,
        )
        async_db_session.add(application)

        connection = UserApplicationConnection(
            id=str(uuid.uuid4()),
            user_id=data["user"].id,
            application_id=application.id,
            client_id=application.client_id,
            granted_scopes=["openid", "profile"],
            first_connected_at=datetime.utcnow(),
            last_accessed_at=datetime.utcnow(),
            access_count=1,
            is_active=True,
        )
        async_db_session.add(connection)
        await async_db_session.commit()

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Revoke application access
        response = await async_client.post(
            f"/api/v1/user/applications/{application.id}/revoke"
        )

        assert response.status_code == 200

        # Verify connection is deactivated
        await async_db_session.refresh(connection)
        assert connection.is_active is False

    @pytest.mark.asyncio
    async def test_user_change_password_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user password change endpoint."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Change password - use a password that meets validation requirements
        password_data = {
            "current_password": "api_test_password",
            "new_password": "NewSecure123!",
        }

        response = await async_client.put(
            "/api/v1/user/security/password", json=password_data
        )

        assert response.status_code == 200

        # Verify password was changed
        await async_db_session.refresh(data["user"])
        assert data["user"].verify_password("NewSecure123!") is True
        assert data["user"].verify_password("api_test_password") is False

    @pytest.mark.asyncio
    async def test_user_api_requires_authentication(self, async_client: AsyncClient):
        """Test that user API endpoints require authentication."""
        endpoints = [
            "/api/v1/user/profile",
            "/api/v1/user/applications",
        ]

        for endpoint in endpoints:
            response = await async_client.get(endpoint)
            assert response.status_code == 401  # Unauthorized

    @pytest.mark.asyncio
    async def test_user_profile_validation_errors(
        self, async_client: AsyncClient, user_api_setup
    ):
        """Test user profile update with validation errors."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Try to update with invalid data (field too long)
        invalid_data = {
            "first_name": "A" * 101,  # Exceeds max_length=100
            "last_name": "Valid",
        }

        response = await async_client.put("/api/v1/user/profile", json=invalid_data)

        # Should return validation error
        assert response.status_code == 422

        error_data = response.json()
        assert "detail" in error_data or "error" in error_data

    @pytest.mark.asyncio
    async def test_user_activity_log_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user activity log endpoint."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        response = await async_client.get("/api/v1/user/activity")

        assert response.status_code == 200

        activity_data = response.json()
        assert isinstance(activity_data, dict)
        assert "activity" in activity_data
        # Activity log might be empty for new user, but endpoint should work

    @pytest.mark.asyncio
    async def test_user_account_deletion_endpoint(
        self, async_client: AsyncClient, user_api_setup, async_db_session: AsyncSession
    ):
        """Test user account deletion endpoint."""
        data = user_api_setup

        # Set authentication cookie
        async_client.cookies.set("geniengo_session", data["session"].session_token)

        # Request account deletion
        deletion_data = {
            "password": "api_test_password",
            "confirmation": "DELETE_MY_ACCOUNT",
        }

        response = await async_client.post(
            "/api/v1/user/delete-account", json=deletion_data
        )

        # Should succeed or return appropriate status
        assert response.status_code in [
            200,
            202,
        ]  # 200 = deleted, 202 = scheduled for deletion


class TestUserModelValidation:
    """Test User model validation and properties."""

    def test_user_model_structure(self):
        """Test User model structure and required fields."""
        user_id = str(uuid.uuid4())

        user = User(
            id=user_id,
            email="<EMAIL>",
            username="model_test_user",
            password_hash="hashed_password",
            role="user",
            first_name="Model",
            last_name="Test",
            is_active=True,
            is_verified=True,
        )

        assert user.id == user_id
        assert user.email == "<EMAIL>"
        assert user.username == "model_test_user"
        assert user.role == "user"
        assert user.first_name == "Model"
        assert user.last_name == "Test"
        assert user.is_active is True
        assert user.is_verified is True

    def test_user_password_management(self):
        """Test user password management methods."""
        user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="password_user",
            password_hash="initial_hash",
            role="user",
        )

        # Test setting password
        new_password = "new_secure_password"
        user.set_password(new_password)

        # Test verifying password
        assert user.verify_password(new_password) is True
        assert user.verify_password("wrong_password") is False

    def test_user_full_name_property(self):
        """Test user full name property."""
        # Test with both names
        user_with_names = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="fullname_user",
            password_hash="hashed_password",
            role="user",
            first_name="John",
            last_name="Doe",
        )
        assert user_with_names.full_name == "John Doe"

        # Test with only first name
        user_first_only = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="firstname_user",
            password_hash="hashed_password",
            role="user",
            first_name="Jane",
        )
        assert user_first_only.full_name == "Jane"

        # Test with no names (returns username)
        user_no_names = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="nonames_user",
            password_hash="hashed_password",
            role="user",
        )
        assert user_no_names.full_name == "nonames_user"

    def test_user_roles_validation(self):
        """Test user roles validation."""
        valid_roles = ["user", "developer", "admin"]

        for role in valid_roles:
            user = User(
                id=str(uuid.uuid4()),
                email=f"{role}<EMAIL>",
                username=f"{role}_user",
                password_hash="hashed_password",
                role=role,
                is_active=True,
            )
            assert user.role == role

    def test_user_password_management(self):
        """Test user password management methods."""
        user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="password_user",
            password_hash="initial_hash",
            role="user",
        )

        # Test setting password
        new_password = "new_secure_password"
        user.set_password(new_password)

        # Test verifying password
        assert user.verify_password(new_password) is True
        assert user.verify_password("wrong_password") is False

    def test_user_full_name_property(self):
        """Test user full name property."""
        # Test with both names
        user_with_names = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="fullname_user",
            password_hash="hashed_password",
            role="user",
            first_name="John",
            last_name="Doe",
        )
        assert user_with_names.full_name == "John Doe"

        # Test with only first name
        user_first_only = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="firstname_user",
            password_hash="hashed_password",
            role="user",
            first_name="Jane",
        )
        assert user_first_only.full_name == "Jane"

        # Test with only last name
        user_last_only = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="lastname_user",
            password_hash="hashed_password",
            role="user",
            last_name="Smith",
        )
        assert user_last_only.full_name == "Smith"

        # Test with no names (returns username)
        user_no_names = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="nonames_user",
            password_hash="hashed_password",
            role="user",
        )
        assert user_no_names.full_name == "nonames_user"

    def test_user_application_connections(self):
        """Test user application connections for API."""
        user_id = str(uuid.uuid4())
        application_id = str(uuid.uuid4())

        connection = UserApplicationConnection(
            id=str(uuid.uuid4()),
            user_id=user_id,
            application_id=application_id,
            client_id="test_client",
            granted_scopes=["openid", "profile", "email"],
            first_connected_at=datetime.utcnow(),
            last_accessed_at=datetime.utcnow(),
            access_count=5,
            is_active=True,
        )

        assert connection.user_id == user_id
        assert connection.application_id == application_id
        assert connection.granted_scopes == ["openid", "profile", "email"]
        assert connection.access_count == 5
        assert connection.is_active is True

    def test_user_roles_validation(self):
        """Test user roles for API validation."""
        valid_roles = ["user", "developer", "admin"]

        for role in valid_roles:
            user = User(
                id=str(uuid.uuid4()),
                email=f"{role}<EMAIL>",
                username=f"{role}_user",
                password_hash="hashed_password",
                role=role,
                is_active=True,
            )
            assert user.role == role

    def test_user_status_fields(self):
        """Test user status fields for API operations."""
        user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="status_user",
            password_hash="hashed_password",
            role="user",
            is_active=True,
            is_verified=False,
        )

        # Test initial status
        assert user.is_active is True
        assert user.is_verified is False

        # Test status updates
        user.is_active = False
        user.is_verified = True
        assert user.is_active is False
        assert user.is_verified is True

    def test_user_google_integration(self):
        """Test user Google OAuth integration fields."""
        user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="google_user",
            password_hash="hashed_password",
            role="user",
            google_id="google_oauth_id_123",
            is_active=True,
        )

        assert user.google_id == "google_oauth_id_123"
        assert user.email == "<EMAIL>"

    def test_user_timestamps(self):
        """Test user timestamp fields."""
        now = datetime.utcnow()

        user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="timestamp_user",
            password_hash="hashed_password",
            role="user",
            created_at=now,
            updated_at=now,
        )

        assert user.created_at == now
        assert user.updated_at == now
