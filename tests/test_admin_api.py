"""
Tests for Admin Portal API endpoints.

This module tests all admin-specific endpoints for user management,
application oversight, and system analytics.
"""

import uuid
from datetime import datetime

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token, get_password_hash
from app.models.user import RegisteredApplication, User


# Shared fixtures to avoid duplication
@pytest.fixture
async def admin_auth(async_db_session: AsyncSession):
    """Set up authenticated admin user for API tests."""
    test_id = str(uuid.uuid4())[:8]

    admin = User(
        id=str(uuid.uuid4()),
        email=f"admin_{test_id}@example.com",
        username=f"admin_{test_id}",
        password_hash="hashed_password",
        role="admin",
    )
    async_db_session.add(admin)
    await async_db_session.commit()

    access_token = create_access_token(data={"sub": admin.id})

    return {
        "admin": admin,
        "access_token": access_token,
        "headers": {"Authorization": f"Bearer {access_token}"},
    }


@pytest.fixture
async def developer_auth(async_db_session: AsyncSession):
    """Set up authenticated developer user for API tests."""
    test_id = str(uuid.uuid4())[:8]

    developer = User(
        id=str(uuid.uuid4()),
        email=f"dev_{test_id}@example.com",
        username=f"dev_{test_id}",
        password_hash="hashed_password",
        role="developer",
    )
    async_db_session.add(developer)
    await async_db_session.commit()

    access_token = create_access_token(data={"sub": developer.id})

    return {
        "developer": developer,
        "access_token": access_token,
        "headers": {"Authorization": f"Bearer {access_token}"},
    }


class TestAdminApplicationAPI:
    """Test admin application management endpoints."""

    async def test_list_all_applications(self, client: AsyncClient, admin_auth):
        """Test listing all applications for admin oversight."""
        admin_data = admin_auth
        response = await client.get(
            "/api/v1/admin/applications", headers=admin_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

        # Check structure if data exists
        if data:
            application = data[0]
            assert "id" in application
            assert "client_id" in application
            assert "application_name" in application
            assert "owner_email" in application
            assert "owner_name" in application
            assert "allowed_redirect_uris" in application
            assert "allowed_scopes" in application
            assert "is_active" in application
            assert "is_approved" in application
            assert "created_at" in application
            assert "stats" in application
            assert "status" in application

    async def test_list_applications_with_filters(
        self, client: AsyncClient, admin_auth
    ):
        """Test listing applications with various filters."""
        admin_data = admin_auth

        # Test active filter
        response = await client.get(
            "/api/v1/admin/applications?is_active=true", headers=admin_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

        # Test search filter
        response = await client.get(
            "/api/v1/admin/applications?search=genie", headers=admin_data["headers"]
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

        # Test pagination
        response = await client.get(
            "/api/v1/admin/applications?page=1&per_page=5",
            headers=admin_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 5

    async def test_admin_update_application(
        self, client: AsyncClient, admin_auth, developer_auth
    ):
        """Test admin updating a application."""
        admin_data = admin_auth
        dev_data = developer_auth

        # First create a application via developer API
        application_data = {
            "application_name": "AdminUpdateTest",
            "description": "Test app for admin update",
            "allowed_redirect_uris": ["http://localhost:3005/auth/callback"],
            "allowed_scopes": ["openid", "profile"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=dev_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Admin update
        admin_update_data = {
            "is_active": False,
            "allowed_scopes": ["openid"],  # Admin restricting scopes
        }

        response = await client.put(
            f"/api/v1/admin/applications/{application_id}",
            json=admin_update_data,
            headers=admin_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()

        assert data["is_active"] is False
        assert data["allowed_scopes"] == ["openid"]

    async def test_admin_delete_application(
        self, client: AsyncClient, admin_auth, developer_auth
    ):
        """Test admin deleting a application."""
        admin_data = admin_auth
        dev_data = developer_auth

        # First create a application
        application_data = {
            "application_name": "AdminDeleteTest",
            "description": "Test app for admin deletion",
            "allowed_redirect_uris": ["http://localhost:3006/auth/callback"],
            "allowed_scopes": ["openid"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=dev_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # Admin delete
        response = await client.delete(
            f"/api/v1/admin/applications/{application_id}",
            headers=admin_data["headers"],
        )
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "deactivated" in data["message"].lower()


class TestAdminAnalyticsAPI:
    """Test admin analytics endpoints."""

    async def test_system_analytics(self, client: AsyncClient, admin_auth):
        """Test getting comprehensive system analytics."""
        response = await client.get(
            "/api/v1/admin/analytics/system", headers=admin_auth["headers"]
        )
        assert response.status_code == 200
        data = response.json()

        # Check all required fields
        required_fields = [
            "total_users",
            "active_users",
            "total_applications",
            "active_applications",
            "total_requests_30d",
            "total_requests_7d",
            "new_users_30d",
            "top_applications",
            "user_growth",
            "request_trends",
        ]

        for field in required_fields:
            assert field in data

        # Check data types
        assert isinstance(data["total_users"], int)
        assert isinstance(data["active_users"], int)
        assert isinstance(data["total_applications"], int)
        assert isinstance(data["active_applications"], int)
        assert isinstance(data["total_requests_30d"], int)
        assert isinstance(data["total_requests_7d"], int)
        assert isinstance(data["new_users_30d"], int)
        assert isinstance(data["top_applications"], list)
        assert isinstance(data["user_growth"], list)
        assert isinstance(data["request_trends"], list)

        # Check array structures
        if data["user_growth"]:
            growth_item = data["user_growth"][0]
            assert "date" in growth_item
            assert "users" in growth_item

        if data["request_trends"]:
            trend_item = data["request_trends"][0]
            assert "date" in trend_item
            assert "requests" in trend_item


class TestAdminUserManagementAPI:
    """Test admin user management endpoints."""

    async def test_admin_reset_user_password(self, client: AsyncClient):
        """Test admin initiating password reset for a user."""
        # First create a test user
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser123",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        }

        create_response = await client.post("/api/v1/auth/register", json=user_data)
        assert create_response.status_code == 200
        created_user = create_response.json()
        user_id = created_user["id"]

        # Now test password reset
        response = await client.post(f"/api/v1/admin/users/{user_id}/reset-password")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "email" in data["message"].lower()

    async def test_admin_reset_nonexistent_user_password(self, client: AsyncClient):
        """Test admin password reset for nonexistent user."""
        fake_user_id = "00000000-0000-0000-0000-000000000000"
        response = await client.post(
            f"/api/v1/admin/users/{fake_user_id}/reset-password"
        )
        assert response.status_code == 404

    async def test_admin_export_user_data(self, client: AsyncClient):
        """Test admin exporting individual user data."""
        # First create a test user
        user_data = {
            "email": "<EMAIL>",
            "username": "exportuser123",
            "password": "testpassword123",
            "first_name": "Export",
            "last_name": "User",
        }

        create_response = await client.post("/api/v1/auth/register", json=user_data)
        assert create_response.status_code == 200
        created_user = create_response.json()
        user_id = created_user["id"]

        # Now test export
        response = await client.get(f"/api/v1/admin/users/{user_id}/export")
        assert response.status_code == 200
        data = response.json()

        # Check required fields
        required_fields = [
            "user_id",
            "username",
            "email",
            "first_name",
            "last_name",
            "role",
            "is_active",
            "is_verified",
            "created_at",
            "authorization_history",
            "exported_at",
            "exported_by",
        ]

        for field in required_fields:
            assert field in data

        assert data["user_id"] == user_id
        assert data["exported_by"] == "admin"
        assert isinstance(data["authorization_history"], list)

    async def test_admin_export_nonexistent_user(self, client: AsyncClient):
        """Test admin export for nonexistent user."""
        fake_user_id = "00000000-0000-0000-0000-000000000000"
        response = await client.get(f"/api/v1/admin/users/{fake_user_id}/export")
        assert response.status_code == 404

    async def test_admin_export_all_users(self, client: AsyncClient):
        """Test admin exporting all users data."""
        response = await client.get("/api/v1/admin/users/export")
        assert response.status_code == 200
        data = response.json()

        assert "total_users" in data
        assert "filters" in data
        assert "users" in data
        assert "exported_at" in data
        assert "exported_by" in data

        assert isinstance(data["total_users"], int)
        assert isinstance(data["users"], list)
        assert data["exported_by"] == "admin"

        # Check user structure if users exist
        if data["users"]:
            user = data["users"][0]
            user_fields = [
                "user_id",
                "username",
                "email",
                "first_name",
                "last_name",
                "role",
                "is_active",
                "is_verified",
                "created_at",
            ]
            for field in user_fields:
                assert field in user

    async def test_admin_export_users_with_filters(self, client: AsyncClient):
        """Test admin exporting users with filters."""
        # Test with active filter
        response = await client.get("/api/v1/admin/users/export?is_active=true")
        assert response.status_code == 200
        data = response.json()
        assert data["filters"]["is_active"] is True

        # Test with verified filter
        response = await client.get("/api/v1/admin/users/export?is_verified=true")
        assert response.status_code == 200
        data = response.json()
        assert data["filters"]["is_verified"] is True


class TestAdminAPIIntegration:
    """Integration tests for admin API functionality."""

    async def test_full_application_lifecycle_admin_oversight(
        self, client: AsyncClient, admin_auth, developer_auth
    ):
        """Test complete application lifecycle with admin oversight."""
        admin_data = admin_auth
        dev_data = developer_auth

        # 1. Developer creates application
        application_data = {
            "application_name": "LifecycleTest",
            "description": "Full lifecycle test app",
            "allowed_redirect_uris": ["http://localhost:3007/auth/callback"],
            "allowed_scopes": ["openid", "profile", "email"],
        }

        create_response = await client.post(
            "/api/v1/developer/applications",
            json=application_data,
            headers=dev_data["headers"],
        )
        assert create_response.status_code == 200
        created_application = create_response.json()
        application_id = created_application["id"]

        # 2. Admin views all applications
        list_response = await client.get(
            "/api/v1/admin/applications", headers=admin_data["headers"]
        )
        assert list_response.status_code == 200
        applications = list_response.json()
        assert any(sub["id"] == application_id for sub in applications)

        # 3. Admin modifies application (restricts scopes)
        admin_update = {"allowed_scopes": ["openid", "profile"]}  # Remove email scope
        update_response = await client.put(
            f"/api/v1/admin/applications/{application_id}",
            json=admin_update,
            headers=admin_data["headers"],
        )
        assert update_response.status_code == 200
        updated_application = update_response.json()
        assert updated_application["allowed_scopes"] == ["openid", "profile"]

        # 4. Admin deactivates application
        delete_response = await client.delete(
            f"/api/v1/admin/applications/{application_id}"
        )
        assert delete_response.status_code == 200

        # 5. Verify application is deactivated
        final_list_response = await client.get(
            "/api/v1/admin/applications", headers=admin_data["headers"]
        )
        assert final_list_response.status_code == 200
        final_applications = final_list_response.json()
        deactivated_application = next(
            (sub for sub in final_applications if sub["id"] == application_id), None
        )
        assert deactivated_application is not None
        assert deactivated_application["is_active"] is False
