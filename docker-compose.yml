services:
  mysql:
    image: mysql:8.0
    container_name: geniengo-sso-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: geniengo_root_password
      MYSQL_DATABASE: geniengo_sso_db
      MYSQL_USER: geniengo_user
      MYSQL_PASSWORD: geniengo_password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - geniengo-sso-network

  redis:
    image: redis:7-alpine
    container_name: geniengo-sso-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - geniengo-sso-network

  sso-server:
    build: .
    container_name: geniengo-sso-server
    ports:
      - "5550:5550"
    environment:
      - PROJECT_NAME=GeNieGO SSO Server
      - VERSION=1.0.0
      - API_PREFIX=/api
      - API_VERSION=v1
      - HOST=0.0.0.0
      - PORT=5550
      - DEBUG=true
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=mysql+aiomysql://geniengo_user:geniengo_password@mysql:3306/geniengo_sso_db
      - JWT_SECRET_KEY=geniengo-jwt-secret-key-change-in-production
      - ENCRYPTION_KEY=geniengo-32-byte-encryption-key-change-this
      - JWT_ALGORITHM=HS256
      - SESSION_EXPIRE_HOURS=24
      - AUTHORIZATION_CODE_EXPIRE_MINUTES=10
      - CORS_ORIGINS=https://geniego.genieland.ai,http://localhost:5550,https://geniemove.genieland.ai
      - SMTP_USE_TLS=false
      - SMTP_USE_SSL=true
      - SMTP_PORT=465
      - SMTP_HOST=smtp.gmail.com
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=wgag gyjf utgh vxcx
      - EMAILS_FROM_EMAIL=<EMAIL>
      - EMAILS_FROM_NAME=GeNieGO
      - SSO_DOMAIN=geniego.genieland.ai
      - SSO_BASE_URL=https://geniego.genieland.ai
      - GOOGLE_CLIENT_ID=753487643909-7s8tj7ea95pl54ino3m1mm2kjoqp6a8r.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - GOOGLE_REDIRECT_URI=https://geniego.genieland.ai/api/v1/auth/google/callback
      - GOOGLE_REDIRECT_URI_LOCAL=http://localhost:5550/api/v1/auth/google/callback
      - OAUTH2_CLIENT_GENIEGO=geniego-v4
      - OAUTH2_CLIENT_SYSTEM=system
      - OAUTH2_CLIENT_ADMIN=geniengo-admin-client
      - OAUTH2_CLIENT_GENIEGO_SECRET=geniego-secret-change-in-production
      - OAUTH2_CLIENT_SYSTEM_SECRET=system-secret-change-in-production
      - OAUTH2_CLIENT_ADMIN_SECRET=admin-secret-change-in-production
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5550/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - geniengo-sso-network

networks:
  geniengo-sso-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
