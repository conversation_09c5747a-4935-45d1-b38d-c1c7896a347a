# GeNieGO SSO Server

A production-ready OAuth2 authorization server and identity provider built with FastAPI, designed as the central authentication hub for the Genieland ecosystem.

## ✅ Status: GeNieGO SSO Server v3.0 - Enterprise Ready

**Last Updated**: July 18, 2025
**Version**: 3.0.0 - SSO Enterprise Enhancement Complete
**Central OAuth2 authorization server** ✅
**Identity provider for applications** ✅
**Authorization code flow implementation** ✅
**User information endpoint** ✅
**Centralized SSO session management** ✅
**Single Logout (SLO) across applications** ✅
**Multi-Factor Authentication (MFA/2FA)** ✅
**Role transition workflows** ✅
**Comprehensive security monitoring** ✅
**Enterprise-grade audit logging** ✅
**Redis-based rate limiting** ✅
**Application integration ready** ✅

## Features

### 🔐 Core SSO Capabilities
- **Central Identity Provider**: Single sign-on for all Genieland applications
- **OAuth2 Authorization Server**: Issues authorization codes (NOT access tokens)
- **User Information Endpoint**: Provides user data to authorized applications
- **Centralized Session Management**: Cross-application SSO sessions with Redis caching
- **Single Logout (SLO)**: Logout from all connected applications simultaneously
- **Session Binding**: OAuth2 tokens tied to SSO session lifecycle

### 🚀 OAuth2 Implementation
- **Authorization Endpoint**: `/oauth2/authorize` for application redirects
- **User Info Endpoint**: `/oauth2/userinfo` for application user data requests
- **PKCE Support**: Proof Key for Code Exchange for enhanced security
- **Authorization Code Flow**: Standard OAuth2 flow for application integration

### 👥 User Management
- **User Registration**: Self-service user account creation
- **Authentication**: Secure login with bcrypt password hashing
- **Profile Management**: User profile updates and management
- **Password Reset**: Email-based password recovery

### 🔒 Security Features
- **Multi-Factor Authentication**: TOTP-based MFA with QR codes and backup codes
- **Redis-based Rate Limiting**: Endpoint-specific protection against brute force attacks
- **Comprehensive Audit Logging**: Security events and OAuth2 access tracking
- **Role Transition Workflows**: User-to-developer role upgrade system
- **Security Monitoring**: Real-time threat detection and suspicious activity alerts
- **Session Security**: Secure SSO session tokens with proper expiration
- **Input Validation**: Comprehensive request validation with Pydantic
- **CORS Configuration**: Proper cross-origin resource sharing for applications

### 🏗️ Technical Stack
- **FastAPI Framework**: Modern, fast web framework with automatic OpenAPI documentation
- **SQLAlchemy Integration**: Database ORM with async support and MySQL backend
- **Database Migrations**: Alembic for database schema management
- **Redis Integration**: Session caching, rate limiting, and authorization code storage
- **Email Integration**: SMTP support for notifications
- **Docker Support**: Complete containerized deployment

## OAuth2 Implementation

This authentication server implements the following OAuth2 flows:

- **Authorization Code Flow**: For web applications with secure backend
- **Refresh Token Flow**: For token renewal without re-authentication
- **Client Credentials Flow**: For service-to-service authentication

### Supported Endpoints

- `GET/POST /oauth2/authorize` - OAuth2 authorization endpoint
- `POST /oauth2/token` - Token endpoint (authorization_code, refresh_token, client_credentials)
- `POST /oauth2/revoke` - Token revocation endpoint
- `GET /oauth2/userinfo` - User information endpoint
- `POST /oauth2/client` - OAuth2 client management

## Architecture Overview

This system follows a clean architecture pattern optimized for OAuth2 authentication:

```
📁 app/
├── 📁 api/           # OAuth2 and management API endpoints
├── 📁 core/          # Security, database, and middleware
├── 📁 models/        # Database models (16 tables)
├── 📁 repositories/  # Data access layer
├── 📁 services/      # Business logic and email services
├── 📁 schemas/       # Pydantic models for OAuth2 and API
├── 📁 static/        # Static files and React frontend builds
├── 📁 templates/     # Jinja2 templates for API landing page
└── 📁 config/        # OAuth2 and application configuration

📁 frontend/          # React TypeScript frontend
├── 📁 src/           # React source code
├── 📁 public/        # Static assets
└── package.json      # Frontend dependencies
```

### Database Schema (16 Tables)

- **Authentication**: Users, Accounts, Roles, ResetPassword
- **OAuth2**: OAuth2Clients, OAuth2Tokens, OAuth2Grants
- **Organisation**: Organisations, OrganisationRoles for multi-tenant support
- **Licensing**: LicenseKeys, Features, FeatureAssociations
- **Permissions**: FeaturesAcl, UserAclGrants for hierarchical access control

## 🧪 Testing

### Test Structure

The GeNieGO SSO Server includes a comprehensive test suite with **112 tests** organized across **12 test modules**:

```
tests/
├── test_activity_logger.py        # 12 tests - OAuth2 activity logging service
├── test_admin_api.py              # 13 tests - Admin portal endpoints
├── test_connection_manager.py     # 10 tests - User-application connections
├── test_developer_analytics.py    # 10 tests - Developer analytics service
├── test_developer_api.py          # 11 tests - Developer portal endpoints
├── test_enhanced_oauth2.py        #  5 tests - Enhanced OAuth2 flow
├── test_exception_handlers.py     #  2 tests - Error handling
├── test_health.py                 #  9 tests - Health monitoring
├── test_integration.py            #  5 tests - End-to-end integration
├── test_oauth2.py                 # 25 tests - Core OAuth2 & app functionality
├── test_repositories.py           #  3 tests - Data access layer
└── test_user_api_endpoints.py     #  9 tests - End user API
```

### Test Categories

- **Unit Tests**: Service layer functionality (40+ tests)
- **API Integration Tests**: Endpoint behavior (60+ tests)
- **System Integration Tests**: End-to-end flows (5+ tests)
- **Infrastructure Tests**: Health checks and core functionality (7+ tests)

### Running Tests

```bash
# Run all tests using the Makefile
make test

# Alternative: Run tests directly in Docker
docker exec geniengo-sso-server pytest

# Run specific test file
docker exec geniengo-sso-server pytest tests/test_oauth2.py -v

# Run tests with coverage
docker exec geniengo-sso-server pytest --cov=app tests/
```

### Test Coverage

- **112/112 tests passing** (100% success rate)
- **0 warnings** (clean test output)
- **Comprehensive coverage** of all Phase 1.5 enhanced features:
  - OAuth2 connection tracking
  - Activity logging
  - Developer analytics
  - Admin management
  - End user API

### Development Server

For development and testing, the server runs on:

```bash
# Start development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 5550

# Access the application
# Main app: http://localhost:5550
# API docs: http://localhost:5550/docs
# Health check: http://localhost:5550/health
```

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <repository>
cd geniego

# 2. Complete setup (includes dependencies, dev tools, and formatting)
make setup

# 3. Setup environment
cp env.example .env
# Edit .env with your database and Redis settings

# 4. Build frontend (if developing frontend)
cd frontend && npm install && npm run build && cd ..

# 5. Start with Docker (includes database setup)
make docker-up

# 6. Access application
open http://localhost:5550          # Main landing page
open http://localhost:5550/docs     # API documentation
```

## 📁 Available Commands

Use the Makefile for all development operations:

```bash
# Setup
make install     # Install dependencies
make dev         # Install dev dependencies  
make setup       # Complete project setup

# Development
make run         # Start development server
make test        # Run tests
make test-watch  # Run tests in watch mode

# Code Quality
make format      # Format code (black + isort)
make lint        # Run linter (flake8)
make type-check  # Run type checking (mypy + pyright)

# Docker
make docker-build # Build image
make docker-up    # Start containers
make docker-down  # Stop containers

# Utilities
make clean       # Clean temporary files
```

## 🧪 Testing

```bash
# Run all tests
make test

# Run tests in watch mode
make test-watch

# Run specific test files
pytest tests/test_oauth2.py -v
pytest tests/test_license.py -v

# Check test coverage
pytest tests/ --cov=app --cov-report=html
```

## 🎨 Frontend Development

The React frontend is located in the `frontend/` directory and provides a modern OAuth2 login portal.

### Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Lint code
npm run lint
```

### Frontend Features
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Intl** for internationalization (English/Chinese)
- **Vite** for fast development and building
- **ESLint** for code quality

### Frontend Structure
```
📁 frontend/src/
├── 📁 components/     # Reusable UI components
├── 📁 pages/          # OAuth2 page components
├── 📁 contexts/       # React contexts (config, etc.)
├── 📁 constants/      # Site configuration
├── 📁 utils/          # Utilities and localization
└── main.tsx           # Application entry point
```

## Configuration

Copy `env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL=mysql+aiomysql://move_auth_user:move_auth_password@localhost:3307/move_auth_db

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth2 Clients
OAUTH2_CLIENT_WEB=move-web-client
OAUTH2_CLIENT_MOBILE=move-mobile-client
OAUTH2_CLIENT_API=move-api-client

# Email (for password reset)
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

## Application Access

Once running, visit:
- **Main Landing Page**: http://localhost:5550/ - Professional landing page with API links
- **OAuth2 Login Portal**: http://localhost:5550/oauth2/authorize?response_type=code&client_id=system&redirect_uri=https://oauth.pstmn.io/v1/callback&scope=basic&state=test123
- **Interactive API Docs**: http://localhost:5550/docs
- **ReDoc**: http://localhost:5550/redoc
- **Health Check**: http://localhost:5550/health

### Default Login Credentials
- **Admin**: `GenieAdmin` / `GenieAdmin123!`
- **Developer**: `GenieDeveloper` / `GenieDev123!`
- **User**: `GenieUser` / `GenieUser123!`

### OAuth2 Flow Example

1. **Authorization Request**:
   ```
   GET /oauth2/authorize?client_id=move-web-client&response_type=code&redirect_uri=http://localhost:5550/auth/callback&scope=read write
   ```

2. **Token Exchange**:
   ```bash
   curl -X POST http://localhost:5550/oauth2/token \
     -H "Content-Type: application/json" \
     -d '{
       "grant_type": "authorization_code",
       "code": "AUTH_CODE_FROM_STEP_1",
       "client_id": "move-web-client",
       "client_secret": "client-secret",
       "redirect_uri": "http://localhost:3000/callback"
     }'
   ```

3. **API Access**:
   ```bash
   curl -X GET http://localhost:5550/oauth2/userinfo \
     -H "Authorization: Bearer ACCESS_TOKEN"
   ```

## RESTful API Endpoints

### User Management (RESTful CRUD)
```bash
# List users with filtering and pagination
GET /api/v1/users?skip=0&limit=10&search=john&role=user&enabled=true

# Create a new user
POST /api/v1/users

# Get user by UUID
GET /api/v1/users/{uuid}

# Update user (full update)
PUT /api/v1/users/{uuid}

# Delete user
DELETE /api/v1/users/{uuid}

# Get current user info
GET /api/v1/users/me

# Check username availability
GET /api/v1/users/username/{username}/availability
```

### Organisation Management (RESTful CRUD)
```bash
# List organisations with pagination
GET /api/v1/organisations?skip=0&limit=10&search=test

# Create new organisation
POST /api/v1/organisations

# Get organisation by ID
GET /api/v1/organisations/{id}

# Update organisation
PUT /api/v1/organisations/{id}

# Delete organisation
DELETE /api/v1/organisations/{id}

# Get current user's organisation
GET /api/v1/organisations/me

# Update current user's organisation
PATCH /api/v1/organisations/me

# Get organisation users
GET /api/v1/organisations/{id}/users

# Get organisation statistics
GET /api/v1/organisations/{id}/stats
```

### License Management (RESTful CRUD)
```bash
# List licenses with pagination
GET /api/v1/licenses?skip=0&limit=10

# Create new license
POST /api/v1/licenses

# Get license by key
GET /api/v1/licenses/{key}

# Update license
PUT /api/v1/licenses/{key}

# Delete license
DELETE /api/v1/licenses/{key}

# Validate license
GET /api/v1/licenses/{key}/validate

# Get current user's license quota
GET /api/v1/licenses/me/quota

# Get dashboard statistics
GET /api/v1/licenses/dashboard/stats

# Feature management
GET /api/v1/licenses/features
POST /api/v1/licenses/features
```

## Multi-tenant Features

### Organisation Management
- Create and manage organisation entities
- Staff management with role-based permissions
- Organisation-level settings and branding

### User Roles
- **Super Admin**: System-wide administration
- **Admin**: Organisation administration
- **Staff**: Standard organisation user
- **User**: Basic authenticated user

### License Management
- Feature-based licensing system
- Quota tracking and enforcement
- Usage statistics and reporting

## Production Deployment

### Environment Variables
Required environment variables for production:

```bash
# Security
JWT_SECRET_KEY=<strong-secret-key>
DATABASE_URL=<production-database-url>

# Email
SMTP_HOST=<smtp-server>
SMTP_USER=<smtp-username>
SMTP_PASSWORD=<smtp-password>

# CORS
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### Docker Production
```bash
# Using simplified Docker commands
make docker-up      # Start containers
make docker-down    # Stop containers
make docker-build   # Build image

# Or manually with docker compose
docker compose up --build
docker compose down
```

### API Configuration for Production

The system is optimized for production deployment with proper API path configuration:

- **Frontend Configuration**: Axios baseURL set to domain root (no `/api` prefix)
- **Backend Endpoints**: All API endpoints properly prefixed with `/api/v1/`
- **Health Monitoring**: Consolidated health endpoints at `/health` and `/api/health`
- **Authentication**: Improved session cookie detection to eliminate console errors
- **Clean Architecture**: Removed development artifacts and example code

For production deployment at `geniego.genieland.ai`:
```bash
# Frontend will make requests to:
# https://geniego.genieland.ai/api/v1/auth/profile
# https://geniego.genieland.ai/api/v1/user/profile
# https://geniego.genieland.ai/api/health

# Backend serves API endpoints at:
# /api/v1/* (all API routes)
# /health (health check)
```

## Database Operations

```bash
# Database migrations
alembic upgrade head     # Apply migrations
alembic downgrade -1     # Rollback one migration
alembic revision --autogenerate -m "description"  # Create new migration

# Development database management
docker compose up -d mysql redis  # Start services
docker exec -it move-auth-mysql mysql -u move_auth_user -pmove_auth_password move_auth_db

# Quick database reset (development only)
docker compose down -v  # Stop containers and remove volumes
make docker-up         # Start containers with fresh database
```

## Security Features

- **JWT Token Security**: RS256/HS256 algorithms with configurable expiration
- **OAuth2 Compliance**: RFC 6749 compliant implementation
- **CORS Protection**: Configurable CORS origins
- **Rate Limiting**: API protection against abuse
- **Password Security**: Bcrypt hashing with salt
- **Multi-tenant Isolation**: Data isolation between organisation entities

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

