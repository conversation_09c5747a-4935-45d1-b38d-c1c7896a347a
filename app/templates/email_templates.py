"""
Email templates for the authentication system.
"""

from typing import Any, Dict


class EmailTemplates:
    """Centralized email templates for the authentication system."""

    @staticmethod
    def get_password_reset_template(reset_url: str) -> Dict[str, str]:
        """
        Get password reset email template.

        Args:
            reset_url: The password reset URL

        Returns:
            Dictionary with 'subject', 'html', and 'text' keys
        """
        subject = "GeNieGO - Password Reset Request"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); padding: 30px 20px; text-align: center; border-radius: 12px 12px 0 0; }}
                .content {{ background-color: #ffffff; padding: 40px 30px; border-left: 1px solid #e9ecef; border-right: 1px solid #e9ecef; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; font-size: 14px; color: #6c757d; border: 1px solid #e9ecef; border-top: none; }}
                .btn {{ display: inline-block; padding: 14px 28px; background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; }}
                .btn:hover {{ background: linear-gradient(135deg, #22c55e 0%, #10b981 100%); transform: translateY(-1px); }}
                .warning {{ background-color: #f0f9ff; border: 1px solid #50C878; border-radius: 8px; padding: 20px; margin: 25px 0; }}
                .logo {{ width: 40px; height: 40px; margin-bottom: 10px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; color: white; font-size: 28px; font-weight: 600;">Password Reset Request</h1>
                    <p style="margin: 8px 0 0 0; color: rgba(255,255,255,0.9); font-size: 16px;">GeNieGO SSO Server</p>
                </div>
                <div class="content">
                    <p style="font-size: 16px; margin-bottom: 20px;">Hello,</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">You have requested to reset your password for your GeNieGO account.</p>
                    <p style="font-size: 16px; margin-bottom: 30px;">Click the button below to proceed with resetting your password:</p>
                    <p style="text-align: center; margin: 35px 0;">
                        <a href="{reset_url}" class="btn">Reset Password</a>
                    </p>
                    <p style="font-size: 14px; margin-bottom: 10px; color: #6c757d;">Or copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #50C878; font-size: 14px; background-color: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 25px;">{reset_url}</p>
                    <div class="warning">
                        <strong style="color: #50C878; font-size: 16px;">🔒 Security Information:</strong>
                        <ul style="margin: 15px 0 0 20px; color: #495057;">
                            <li style="margin-bottom: 8px;">This link will expire in 24 hours</li>
                            <li style="margin-bottom: 8px;">If you did not request this reset, please ignore this email</li>
                            <li style="margin-bottom: 8px;">For security reasons, do not share this link with anyone</li>
                        </ul>
                    </div>
                </div>
                <div class="footer">
                    <p style="margin: 0; font-weight: 600; color: #50C878;">GeNieGO SSO Server</p>
                    <p style="margin: 5px 0 0 0; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        GeNieGO - Password Reset Request
        ===============================

        Hello,

        You have requested to reset your password for your GeNieGO account.

        Please visit the following link to reset your password:
        {reset_url}

        SECURITY INFORMATION:
        - This link will expire in 24 hours
        - If you did not request this reset, please ignore this email
        - For security reasons, do not share this link with anyone

        If you're having trouble clicking the link, copy and paste it into your web browser.

        ---
        GeNieGO SSO Server
        This is an automated message, please do not reply to this email.
        """

        return {"subject": subject, "html": html_body, "text": text_body}

    @staticmethod
    def get_welcome_template(username: str) -> Dict[str, str]:
        """
        Get welcome email template.

        Args:
            username: The new user's username

        Returns:
            Dictionary with 'subject', 'html', and 'text' keys
        """
        subject = "Welcome to GeNieGO"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to GeNieGO</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); padding: 40px 20px; text-align: center; border-radius: 12px 12px 0 0; }}
                .content {{ background-color: #ffffff; padding: 40px 30px; border-left: 1px solid #e9ecef; border-right: 1px solid #e9ecef; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; font-size: 14px; color: #6c757d; border: 1px solid #e9ecef; border-top: none; }}
                .welcome-message {{ background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 1px solid #50C878; border-radius: 12px; padding: 30px; margin: 30px 0; text-align: center; }}
                .features {{ background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #50C878; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; color: white; font-size: 32px; font-weight: 600;">Welcome to GeNieGO</h1>
                    <p style="margin: 10px 0 0 0; color: rgba(255,255,255,0.9); font-size: 18px;">Your account is ready</p>
                </div>
                <div class="content">
                    <div class="welcome-message">
                        <h2 style="margin-top: 0; color: #50C878; font-size: 24px; font-weight: 600;">Hello {username}!</h2>
                        <p style="margin-bottom: 0; font-size: 18px; color: #495057;">Your GeNieGO account has been successfully created.</p>
                    </div>

                    <p style="font-size: 16px; margin-bottom: 25px;">Welcome to GeNieGO! We're excited to have you join our secure authentication platform.</p>

                    <div class="features">
                        <h3 style="margin-top: 0; color: #50C878; font-size: 18px; font-weight: 600;">What you can do now:</h3>
                        <ul style="margin: 15px 0 0 20px; color: #495057;">
                            <li style="margin-bottom: 10px;">Sign in to access your dashboard</li>
                            <li style="margin-bottom: 10px;">Manage your account security settings</li>
                            <li style="margin-bottom: 10px;">Connect with authorized applications</li>
                            <li style="margin-bottom: 10px;">Monitor your account activity</li>
                        </ul>
                    </div>

                    <p style="font-size: 16px; margin-bottom: 20px;">If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

                    <p style="font-size: 16px; margin-bottom: 0; color: #50C878; font-weight: 600;">Thank you for choosing GeNieGO!</p>
                </div>
                <div class="footer">
                    <p style="margin: 0; font-weight: 600; color: #50C878;">GeNieGO SSO Server</p>
                    <p style="margin: 5px 0 0 0; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        Welcome to GeNieGO!
        ===================

        Hello {username}!

        Your GeNieGO account has been successfully created and you're now part of our secure authentication platform!

        What you can do now:
        - Sign in to access your dashboard
        - Manage your account security settings
        - Connect with authorized applications
        - Monitor your account activity

        We're excited to have you on board and look forward to providing you with a secure and seamless authentication experience.

        If you have any questions or need assistance, please don't hesitate to contact our support team.

        Thank you for choosing GeNieGO!

        ---
        GeNieGO SSO Server
        This is an automated message, please do not reply to this email.
        """

        return {"subject": subject, "html": html_body, "text": text_body}

    @staticmethod
    def get_two_factor_setup_template(
        username: str, backup_codes: list
    ) -> Dict[str, str]:
        """
        Get two-factor authentication setup email template.

        Args:
            username: The user's username
            backup_codes: List of backup codes

        Returns:
            Dictionary with 'subject', 'html', and 'text' keys
        """
        subject = "GeNieGO - Two-Factor Authentication Enabled"

        backup_codes_html = "<br>".join(
            [f"<code style='background-color: #f8f9fa; padding: 8px 12px; border-radius: 6px; font-family: monospace; font-size: 14px; color: #50C878; font-weight: 600; display: inline-block; margin: 4px 8px;'>{code}</code>" for code in backup_codes]
        )
        backup_codes_text = "\n".join([f"  {code}" for code in backup_codes])

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Two-Factor Authentication Enabled</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); padding: 30px 20px; text-align: center; border-radius: 12px 12px 0 0; }}
                .content {{ background-color: #ffffff; padding: 40px 30px; border-left: 1px solid #e9ecef; border-right: 1px solid #e9ecef; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; font-size: 14px; color: #6c757d; border: 1px solid #e9ecef; border-top: none; }}
                .security-notice {{ background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 1px solid #50C878; border-radius: 12px; padding: 25px; margin: 25px 0; }}
                .backup-codes {{ background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 25px; margin: 25px 0; border-left: 4px solid #50C878; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; color: white; font-size: 28px; font-weight: 600;">Two-Factor Authentication Enabled</h1>
                    <p style="margin: 8px 0 0 0; color: rgba(255,255,255,0.9); font-size: 16px;">GeNieGO SSO Server</p>
                </div>
                <div class="content">
                    <p style="font-size: 16px; margin-bottom: 25px;">Hello {username},</p>

                    <div class="security-notice">
                        <h3 style="margin-top: 0; color: #50C878; font-size: 20px; font-weight: 600;">Enhanced Security Activated</h3>
                        <p style="margin-bottom: 0; font-size: 16px; color: #495057;">Two-factor authentication has been successfully enabled on your GeNieGO account. Your account is now more secure!</p>
                    </div>

                    <h3 style="color: #50C878; font-size: 18px; font-weight: 600; margin-bottom: 15px;">Your Backup Codes</h3>
                    <p style="font-size: 16px; margin-bottom: 20px;">Please save these backup codes in a safe place. You can use them to access your account if you lose access to your authenticator device:</p>

                    <div class="backup-codes">
                        <strong style="color: #50C878; font-size: 16px;">Backup Codes:</strong><br><br>
                        {backup_codes_html}
                    </div>

                    <div style="background-color: #f0f9ff; border: 1px solid #50C878; border-radius: 8px; padding: 20px; margin: 25px 0;">
                        <strong style="color: #50C878; font-size: 16px;">Security Information:</strong>
                        <ul style="margin: 15px 0 0 20px; color: #495057;">
                            <li style="margin-bottom: 8px;">Store these codes in a secure location</li>
                            <li style="margin-bottom: 8px;">Each code can only be used once</li>
                            <li style="margin-bottom: 8px;">Keep them separate from your authenticator device</li>
                            <li style="margin-bottom: 8px;">Do not share these codes with anyone</li>
                        </ul>
                    </div>

                    <p style="font-size: 16px; margin-bottom: 0;">From now on, you'll need to provide a code from your authenticator app when logging in.</p>
                </div>
                <div class="footer">
                    <p style="margin: 0; font-weight: 600; color: #50C878;">GeNieGO SSO Server</p>
                    <p style="margin: 5px 0 0 0; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        GeNieGO - Two-Factor Authentication Enabled
        ===========================================

        Hello {username},

        Enhanced Security Activated
        Two-factor authentication has been successfully enabled on your GeNieGO account. Your account is now more secure!

        Your Backup Codes
        Please save these backup codes in a safe place. You can use them to access your account if you lose access to your authenticator device:

        Backup Codes:
{backup_codes_text}

        SECURITY INFORMATION:
        - Store these codes in a secure location
        - Each code can only be used once
        - Keep them separate from your authenticator device
        - Do not share these codes with anyone

        From now on, you'll need to provide a code from your authenticator app when logging in.

        ---
        GeNieGO SSO Server
        This is an automated message, please do not reply to this email.
        """

        return {"subject": subject, "html": html_body, "text": text_body}

    @staticmethod
    def get_email_verification_template(username: str, verification_url: str) -> dict:
        """
        Get email verification template.

        Args:
            username: The user's username
            verification_url: The verification URL

        Returns:
            Dictionary with 'subject', 'html', and 'text' keys
        """
        subject = "GeNieGO - Verify Your Email Address"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify Your Email Address</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); padding: 30px 20px; text-align: center; border-radius: 12px 12px 0 0; }}
                .content {{ background-color: #ffffff; padding: 40px 30px; border-left: 1px solid #e9ecef; border-right: 1px solid #e9ecef; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; font-size: 14px; color: #6c757d; border: 1px solid #e9ecef; border-top: none; }}
                .verify-button {{ display: inline-block; background: linear-gradient(135deg, #50C878 0%, #22c55e 100%); color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 25px 0; transition: all 0.3s ease; }}
                .verify-button:hover {{ transform: translateY(-2px); box-shadow: 0 8px 25px rgba(80, 200, 120, 0.3); }}
                .security-notice {{ background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 1px solid #50C878; border-radius: 12px; padding: 25px; margin: 25px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; color: white; font-size: 28px; font-weight: 600;">Verify Your Email Address</h1>
                    <p style="margin: 8px 0 0 0; color: rgba(255,255,255,0.9); font-size: 16px;">GeNieGO SSO Server</p>
                </div>
                <div class="content">
                    <p style="font-size: 16px; margin-bottom: 25px;">Hello {username},</p>

                    <p style="font-size: 16px; margin-bottom: 25px;">Welcome to GeNieGO! To complete your registration and secure your account, please verify your email address by clicking the button below:</p>

                    <div style="text-align: center; margin: 35px 0;">
                        <a href="{verification_url}" class="verify-button">Verify Email Address</a>
                    </div>

                    <div class="security-notice">
                        <h3 style="margin-top: 0; color: #50C878; font-size: 18px; font-weight: 600;">Security Information</h3>
                        <p style="margin-bottom: 0; font-size: 14px; color: #495057;">This verification link will expire in 24 hours. If you didn't create an account with GeNieGO, please ignore this email.</p>
                    </div>

                    <p style="font-size: 14px; color: #6c757d; margin-top: 30px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="font-size: 14px; color: #50C878; word-break: break-all; background-color: #f8f9fa; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">{verification_url}</p>
                </div>
                <div class="footer">
                    <p style="margin: 0; font-size: 14px;">GeNieGO SSO Server - Secure Single Sign-On for Genieland Ecosystem</p>
                    <p style="margin: 8px 0 0 0; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        GeNieGO - Verify Your Email Address

        Hello {username},

        Welcome to GeNieGO! To complete your registration and secure your account, please verify your email address.

        Click the link below to verify your email:
        {verification_url}

        SECURITY INFORMATION:
        - This verification link will expire in 24 hours
        - If you didn't create an account with GeNieGO, please ignore this email
        - Do not share this verification link with anyone

        ---
        GeNieGO SSO Server
        This is an automated message, please do not reply to this email.
        """

        return {"subject": subject, "html": html_body, "text": text_body}
