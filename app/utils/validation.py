"""
Data validation utilities.
"""

import re
from datetime import date, datetime
from typing import Any, Callable, Dict, List, Optional

from app.core.constants import EMAIL_PATTERN, MIN_PASSWORD_LENGTH
from app.core.exceptions import ValidationError

# Define role constants locally if not in constants file
MIN_PASSWORD_LENGTH_DEV = 4
SUPER_ADMIN_ROLE = "super_admin"
ADMIN_ROLE = "admin"
MANAGER_ROLE = "manager"
USER_ROLE = "user"
VIEWER_ROLE = "viewer"
GUEST_ROLE = "guest"


class ValidationResult:
    """Represents the result of a validation operation."""

    def __init__(self, is_valid: bool = True, errors: Optional[List[str]] = None):
        self.is_valid = is_valid
        self.errors = errors or []

    def add_error(self, error: str) -> None:
        """Add an error to the result."""
        self.errors.append(error)
        self.is_valid = False

    def merge(self, other: "ValidationResult") -> "ValidationResult":
        """Merge another validation result."""
        if not other.is_valid:
            self.is_valid = False
            self.errors.extend(other.errors)
        return self


class BaseValidator:
    """Base validator class with common validation methods."""

    @staticmethod
    def validate_required(value: Any, field_name: str = "field") -> ValidationResult:
        """Validate that a field is not empty."""
        result = ValidationResult()

        if value is None:
            result.add_error(f"{field_name} is required")
        elif isinstance(value, str) and not value.strip():
            result.add_error(f"{field_name} cannot be empty")
        elif isinstance(value, (list, dict)) and len(value) == 0:
            result.add_error(f"{field_name} cannot be empty")

        return result

    @staticmethod
    def validate_string_length(
        value: Any,
        min_length: int = 0,
        max_length: Optional[int] = None,
        field_name: str = "field",
    ) -> ValidationResult:
        """Validate string length constraints."""
        result = ValidationResult()

        if not isinstance(value, str):
            result.add_error(f"{field_name} must be a string")
            return result

        length = len(value.strip())

        if length < min_length:
            result.add_error(
                f"{field_name} must be at least {min_length} characters long"
            )

        if max_length and length > max_length:
            result.add_error(
                f"{field_name} must be no more than {max_length} characters long"
            )

        return result

    @staticmethod
    def validate_email(email: Any, field_name: str = "email") -> ValidationResult:
        """Validate email format."""
        result = ValidationResult()

        if not isinstance(email, str):
            result.add_error(f"{field_name} must be a string")
            return result

        email = email.strip().lower()

        if not re.match(EMAIL_PATTERN, email):
            result.add_error(f"{field_name} must be a valid email address")

        return result

    @staticmethod
    def validate_password(
        password: Any,
        min_length: int = 8,
        require_uppercase: bool = False,
        require_lowercase: bool = False,
        require_digit: bool = False,
        require_special: bool = False,
        field_name: str = "password",
    ) -> ValidationResult:
        """Validate password strength."""
        result = ValidationResult()

        if not isinstance(password, str):
            result.add_error(f"{field_name} must be a string")
            return result

        # Length check
        if len(password) < min_length:
            result.add_error(
                f"{field_name} must be at least {min_length} characters long"
            )

        # Character requirements
        if require_uppercase and not re.search(r"[A-Z]", password):
            result.add_error(f"{field_name} must contain at least one uppercase letter")

        if require_lowercase and not re.search(r"[a-z]", password):
            result.add_error(f"{field_name} must contain at least one lowercase letter")

        if require_digit and not re.search(r"\d", password):
            result.add_error(f"{field_name} must contain at least one digit")

        if require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result.add_error(
                f"{field_name} must contain at least one special character"
            )

        return result

    @staticmethod
    def validate_choice(
        value: Any, choices: List[Any], field_name: str = "field"
    ) -> ValidationResult:
        """Validate that value is one of the allowed choices."""
        result = ValidationResult()

        if value not in choices:
            choices_str = ", ".join(str(c) for c in choices)
            result.add_error(f"{field_name} must be one of: {choices_str}")

        return result

    @staticmethod
    def validate_date(value: Any, field_name: str = "date") -> ValidationResult:
        """Validate date format."""
        result = ValidationResult()

        if isinstance(value, (datetime, date)):
            return result

        if isinstance(value, str):
            try:
                datetime.fromisoformat(value.replace("Z", "+00:00"))
                return result
            except ValueError:
                result.add_error(f"{field_name} must be a valid ISO date format")
        else:
            result.add_error(f"{field_name} must be a date string or datetime object")

        return result

    @staticmethod
    def validate_url(value: Any, field_name: str = "url") -> ValidationResult:
        """Validate URL format."""
        result = ValidationResult()

        if not isinstance(value, str):
            result.add_error(f"{field_name} must be a string")
            return result

        url_pattern = r"^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$"

        if not re.match(url_pattern, value):
            result.add_error(f"{field_name} must be a valid URL")

        return result


class UserValidator(BaseValidator):
    """Validator for user-related data."""

    @staticmethod
    def validate_user_registration(data: Dict[str, Any]) -> ValidationResult:
        """Validate user registration data."""
        result = ValidationResult()

        # Email validation
        if "email" in data:
            email_result = UserValidator.validate_email(data["email"])
            result.merge(email_result)
        else:
            result.add_error("Email is required")

        # Password validation
        if "password" in data:
            # Use constants for password validation
            from app.config import get_settings

            settings = get_settings()
            min_length = (
                MIN_PASSWORD_LENGTH_DEV if settings.DEBUG else MIN_PASSWORD_LENGTH
            )

            password_result = UserValidator.validate_password(
                data["password"],
                min_length=min_length,
                require_uppercase=False,  # Relaxed for demo
                require_lowercase=False,
                require_digit=False,
                require_special=False,
            )
            result.merge(password_result)
        else:
            result.add_error("Password is required")

        # Name validation
        if "firstName" in data:
            first_name_result = UserValidator.validate_string_length(
                data["firstName"], min_length=1, max_length=50, field_name="firstName"
            )
            result.merge(first_name_result)

        if "lastName" in data:
            last_name_result = UserValidator.validate_string_length(
                data["lastName"], min_length=1, max_length=50, field_name="lastName"
            )
            result.merge(last_name_result)

        return result

    @staticmethod
    def validate_user_login(data: Dict[str, Any]) -> ValidationResult:
        """Validate user login data."""
        result = ValidationResult()

        # Email validation
        if "email" in data:
            email_result = UserValidator.validate_email(data["email"])
            result.merge(email_result)
        else:
            result.add_error("Email is required")

        # Password validation (just required for login)
        if "password" not in data or not data["password"]:
            result.add_error("Password is required")

        return result

    @staticmethod
    def validate_password_reset(data: Dict[str, Any]) -> ValidationResult:
        """Validate password reset data."""
        result = ValidationResult()

        # Email validation
        if "email" in data:
            email_result = UserValidator.validate_email(data["email"])
            result.merge(email_result)
        else:
            result.add_error("Email is required")

        return result


def validate_input(
    data: Dict[str, Any], validator_func: Callable[[Dict[str, Any]], Any]
) -> None:
    """
    Validate input data and raise ValidationError if invalid.

    Args:
        data: Data to validate
        validator_func: Validation function to use

    Raises:
        ValidationError: If validation fails
    """
    result = validator_func(data)

    if not result.is_valid:
        raise ValidationError(message="; ".join(result.errors))


def sanitize_string(value: Any, max_length: int = 1000) -> str:
    """
    Sanitize string input by removing dangerous characters.

    Args:
        value: String to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized string
    """
    if not isinstance(value, str):
        return str(value)

    # Remove potential XSS characters
    dangerous_chars = ["<", ">", '"', "'", "&", "`"]
    sanitized = value

    for char in dangerous_chars:
        sanitized = sanitized.replace(char, "")

    # Limit length
    sanitized = sanitized[:max_length]

    # Remove extra whitespace
    sanitized = sanitized.strip()

    return sanitized


def sanitize_dict(
    data: Dict[str, Any], max_string_length: int = 1000
) -> Dict[str, Any]:
    """
    Sanitize all string values in a dictionary.

    Args:
        data: Dictionary to sanitize
        max_string_length: Maximum length for string values

    Returns:
        Sanitized dictionary
    """
    sanitized: Dict[str, Any] = {}

    for key, value in data.items():
        if isinstance(value, str):
            sanitized[key] = sanitize_string(value, max_string_length)
        elif isinstance(value, dict):
            sanitized[key] = sanitize_dict(value, max_string_length)
        elif isinstance(value, list):
            sanitized[key] = [
                (
                    sanitize_string(item, max_string_length)
                    if isinstance(item, str)
                    else item
                )
                for item in value
            ]
        else:
            sanitized[key] = value

    return sanitized
