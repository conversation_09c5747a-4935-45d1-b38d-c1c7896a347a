"""
Redis utility functions for cache and background tasks.
"""

import logging
import os
from typing import Any, Dict, Union

import redis

logger = logging.getLogger(__name__)

# Shared Redis client
_redis_client = None
_use_redis = False


def init_redis() -> None:
    """Initialize Redis connection."""
    global _redis_client, _use_redis

    try:
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        _redis_client = redis.from_url(redis_url, decode_responses=True)
        # Test connection
        _redis_client.ping()
        _use_redis = True
        logger.info(f"Redis connection established successfully at {redis_url}")
    except Exception as e:
        logger.warning(
            f"Redis not available at {os.getenv('REDIS_URL', 'redis://localhost:6379')}, using in-memory storage: {e}"
        )
        _use_redis = False


def get_redis_client() -> Union[Any, None]:
    """Get Redis client instance."""
    return _redis_client if _use_redis else None


def is_redis_available() -> bool:
    """Check if Redis is available."""
    return _use_redis and _redis_client is not None


def get_redis_status() -> Dict[str, Any]:
    """Get Redis connection status and info."""
    if not _use_redis or not _redis_client:
        return {
            "available": False,
            "storage_mode": "in-memory",
            "error": "Redis not available",
        }

    try:
        info = _redis_client.info()
        return {
            "available": True,
            "storage_mode": "redis",
            "version": info.get("redis_version"),
            "connected_clients": info.get("connected_clients"),
            "used_memory": info.get("used_memory"),
            "used_memory_human": info.get("used_memory_human"),
            "uptime_in_seconds": info.get("uptime_in_seconds"),
        }
    except Exception as e:
        return {"available": False, "storage_mode": "in-memory", "error": str(e)}


# Initialize Redis on module import
init_redis()
