"""
Utility helper functions.
"""

import re
from datetime import datetime
from typing import Any

from fastapi import HTTPException


def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


def sanitize_input(data: str) -> str:
    """Basic input sanitization."""
    if not data:
        return ""
    # Basic sanitization - expand as needed
    return data.strip()


def create_error_response(message: str, status_code: int = 400) -> HTTPException:
    """Create standardized error response."""
    return HTTPException(
        status_code=status_code,
        detail={"message": message, "timestamp": datetime.now().isoformat()},
    )


def format_response(data: Any, message: str = "Success") -> dict:
    """Format standardized API response."""
    return {"message": message, "data": data, "timestamp": datetime.now().isoformat()}
