"""
OAuth2 schemas for GeNieGO SSO Server.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class OAuth2GrantType(str, Enum):
    """OAuth2 grant types."""

    AUTHORIZATION_CODE = "authorization_code"
    REFRESH_TOKEN = "refresh_token"
    CLIENT_CREDENTIALS = "client_credentials"


class OAuth2ResponseType(str, Enum):
    """OAuth2 response types."""

    CODE = "code"
    TOKEN = "token"


class OAuth2CodeChallengeMethod(str, Enum):
    """OAuth2 PKCE code challenge methods."""

    S256 = "S256"
    PLAIN = "plain"


class OAuth2AuthorizeRequest(BaseModel):
    """OAuth2 authorization request schema."""

    response_type: OAuth2ResponseType = Field(..., description="Response type")
    client_id: str = Field(..., description="Client ID")
    redirect_uri: str = Field(..., description="Redirect URI")
    scope: Optional[str] = Field(None, description="Requested scopes")
    state: Optional[str] = Field(None, description="State parameter")
    code_challenge: Optional[str] = Field(None, description="PKCE code challenge")
    code_challenge_method: Optional[OAuth2CodeChallengeMethod] = Field(
        None, description="PKCE method"
    )

    @field_validator("response_type")
    @classmethod
    def validate_response_type(cls, v):
        if v != OAuth2ResponseType.CODE:
            raise ValueError("Only authorization code flow is supported")
        return v


class OAuth2AuthorizeResponse(BaseModel):
    """OAuth2 authorization response schema."""

    code: Optional[str] = Field(None, description="Authorization code")
    state: Optional[str] = Field(None, description="State parameter")
    error: Optional[str] = Field(None, description="Error code")
    error_description: Optional[str] = Field(None, description="Error description")


class OAuth2TokenRequest(BaseModel):
    """OAuth2 token request schema."""

    grant_type: OAuth2GrantType = Field(..., description="Grant type")
    code: Optional[str] = Field(None, description="Authorization code")
    redirect_uri: Optional[str] = Field(None, description="Redirect URI")
    client_id: str = Field(..., description="Client ID")
    client_secret: Optional[str] = Field(None, description="Client secret")
    code_verifier: Optional[str] = Field(None, description="PKCE code verifier")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    scope: Optional[str] = Field(None, description="Requested scope")

    @field_validator("grant_type")
    @classmethod
    def validate_grant_type(cls, v):
        if v not in [OAuth2GrantType.AUTHORIZATION_CODE, OAuth2GrantType.REFRESH_TOKEN]:
            raise ValueError("Unsupported grant type")
        return v


class OAuth2TokenResponse(BaseModel):
    """OAuth2 token response schema."""

    access_token: str = Field(..., description="Access token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration in seconds")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    scope: Optional[str] = Field(None, description="Granted scope")


class OAuth2ErrorResponse(BaseModel):
    """OAuth2 error response schema."""

    error: str = Field(..., description="Error code")
    error_description: Optional[str] = Field(None, description="Error description")
    error_uri: Optional[str] = Field(None, description="Error documentation URI")
    state: Optional[str] = Field(None, description="State parameter")


class OAuth2ClientCreateRequest(BaseModel):
    """OAuth2 client creation request schema."""

    client_id: str = Field(..., min_length=3, max_length=100, description="Client ID")
    client_name: str = Field(
        ..., min_length=1, max_length=255, description="Client name"
    )
    client_secret: Optional[str] = Field(None, description="Client secret")
    redirect_uris: List[str] = Field(
        ..., min_length=1, description="Allowed redirect URIs"
    )
    scopes: List[str] = Field(
        default=["openid", "profile"], description="Allowed scopes"
    )
    grant_types: List[OAuth2GrantType] = Field(
        default=[OAuth2GrantType.AUTHORIZATION_CODE], description="Allowed grant types"
    )
    is_confidential: bool = Field(default=True, description="Client is confidential")


class OAuth2ClientUpdateRequest(BaseModel):
    """OAuth2 client update request schema."""

    client_name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Client name"
    )
    client_secret: Optional[str] = Field(None, description="Client secret")
    redirect_uris: Optional[List[str]] = Field(
        None, description="Allowed redirect URIs"
    )
    scopes: Optional[List[str]] = Field(None, description="Allowed scopes")
    grant_types: Optional[List[OAuth2GrantType]] = Field(
        None, description="Allowed grant types"
    )
    is_confidential: Optional[bool] = Field(None, description="Client is confidential")
    is_active: Optional[bool] = Field(None, description="Client is active")


class OAuth2ClientResponse(BaseModel):
    """OAuth2 client response schema."""

    id: str = Field(..., description="Client ID")
    client_id: str = Field(..., description="Client identifier")
    client_name: str = Field(..., description="Client name")
    redirect_uris: List[str] = Field(..., description="Allowed redirect URIs")
    scopes: List[str] = Field(..., description="Allowed scopes")
    grant_types: List[str] = Field(..., description="Allowed grant types")
    is_confidential: bool = Field(..., description="Client is confidential")
    is_active: bool = Field(..., description="Client is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class OAuth2ClientListResponse(BaseModel):
    """OAuth2 client list response schema."""

    clients: List[OAuth2ClientResponse] = Field(..., description="List of clients")
    total: int = Field(..., description="Total number of clients")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")


class OAuth2ScopeResponse(BaseModel):
    """OAuth2 scope response schema."""

    name: str = Field(..., description="Scope name")
    description: str = Field(..., description="Scope description")
    is_default: bool = Field(..., description="Scope is default")


class OAuth2UserInfoResponse(BaseModel):
    """OAuth2 user info response schema."""

    sub: str = Field(..., description="Subject (user ID)")
    email: str = Field(..., description="Email address")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    is_verified: bool = Field(..., description="Email verification status")
    updated_at: datetime = Field(..., description="Profile last updated")

    model_config = ConfigDict(from_attributes=True)


class OAuth2AuthorizationCodeResponse(BaseModel):
    """OAuth2 authorization code response schema."""

    id: str = Field(..., description="Code ID")
    code: str = Field(..., description="Authorization code")
    client_id: str = Field(..., description="Client ID")
    user_id: str = Field(..., description="User ID")
    redirect_uri: str = Field(..., description="Redirect URI")
    scopes: List[str] = Field(..., description="Granted scopes")
    expires_at: datetime = Field(..., description="Code expiration")
    is_used: bool = Field(..., description="Code has been used")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict(from_attributes=True)


class OAuth2TokenValidationRequest(BaseModel):
    """OAuth2 token validation request schema."""

    token: str = Field(..., description="Token to validate")
    token_type_hint: Optional[str] = Field(None, description="Token type hint")


class OAuth2TokenValidationResponse(BaseModel):
    """OAuth2 token validation response schema."""

    active: bool = Field(..., description="Token is active")
    client_id: Optional[str] = Field(None, description="Client ID")
    user_id: Optional[str] = Field(None, description="User ID")
    scope: Optional[str] = Field(None, description="Token scope")
    expires_at: Optional[datetime] = Field(None, description="Token expiration")


class OAuth2TokenRevocationRequest(BaseModel):
    """OAuth2 token revocation request schema."""

    token: str = Field(..., description="Token to revoke")
    token_type_hint: Optional[str] = Field(None, description="Token type hint")
    client_id: str = Field(..., description="Client ID")
    client_secret: Optional[str] = Field(None, description="Client secret")
