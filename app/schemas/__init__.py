"""
Pydantic schemas for GeNieGO SSO Server API.

This module contains all request and response schemas for the API endpoints.
Schemas are organized by functional area and follow the naming convention:
- {Model}Create for creation requests
- {Model}Update for update requests
- {Model}Response for API responses
"""

# Import all schemas from each module
from .admin import *
from .auth import *
from .common import *
from .oauth2 import *
from .user import *
