"""
Pydantic schemas for organization management in GeNieGO SSO Server.

This module defines the request/response schemas for organization-related
API endpoints, including validation and serialization.
"""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field, validator


class OrganizationBase(BaseModel):
    """Base organization schema with common fields."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Organization name")
    description: Optional[str] = Field(None, max_length=1000, description="Organization description")
    website: Optional[str] = Field(None, max_length=255, description="Organization website URL")
    is_public: bool = Field(False, description="Whether organization can be discovered publicly")
    max_members: int = Field(50, ge=1, le=1000, description="Maximum number of members")
    settings: Optional[Dict] = Field(None, description="Organization settings")

    @validator('website')
    def validate_website(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('Website must start with http:// or https://')
        return v


class OrganizationCreate(OrganizationBase):
    """Schema for creating a new organization."""
    
    slug: str = Field(
        ...,
        min_length=3,
        max_length=50,
        pattern=r'^[a-z0-9-]+$',
        description="URL-friendly organization identifier (lowercase, numbers, hyphens only)"
    )

    @validator('slug')
    def validate_slug(cls, v):
        if v.startswith('-') or v.endswith('-'):
            raise ValueError('Slug cannot start or end with a hyphen')
        if '--' in v:
            raise ValueError('Slug cannot contain consecutive hyphens')
        return v


class OrganizationUpdate(BaseModel):
    """Schema for updating organization details."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    website: Optional[str] = Field(None, max_length=255)
    is_public: Optional[bool] = None
    max_members: Optional[int] = Field(None, ge=1, le=1000)
    billing_email: Optional[str] = Field(None, max_length=255)
    settings: Optional[Dict] = None

    @validator('website')
    def validate_website(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('Website must start with http:// or https://')
        return v


class OrganizationResponse(BaseModel):
    """Schema for organization response data."""
    
    id: str
    name: str
    slug: str
    description: Optional[str]
    website: Optional[str]
    is_active: bool
    is_public: bool
    max_members: int
    subscription_tier: str
    billing_email: Optional[str]
    settings: Optional[Dict]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    member_count: Optional[int] = None
    user_role: Optional[str] = None  # Current user's role in this organization

    class Config:
        from_attributes = True


class OrganizationMembershipBase(BaseModel):
    """Base schema for organization membership."""
    
    role: str = Field(..., pattern=r'^(owner|admin|member)$')
    permissions: Optional[Dict] = None


class OrganizationMembershipCreate(OrganizationMembershipBase):
    """Schema for creating organization membership."""
    
    user_id: str = Field(..., description="User ID to add to organization")


class OrganizationMembershipUpdate(BaseModel):
    """Schema for updating organization membership."""
    
    role: Optional[str] = Field(None, pattern=r'^(owner|admin|member)$')
    permissions: Optional[Dict] = None


class OrganizationMembershipResponse(BaseModel):
    """Schema for organization membership response."""
    
    id: str
    organization_id: str
    user_id: str
    role: str
    is_active: bool
    invited_by: Optional[str]
    invited_at: Optional[datetime]
    joined_at: datetime
    permissions: Optional[Dict]
    created_at: datetime
    updated_at: datetime
    
    # User details (when included)
    user_email: Optional[str] = None
    user_name: Optional[str] = None

    class Config:
        from_attributes = True


class OrganizationInvitationBase(BaseModel):
    """Base schema for organization invitation."""
    
    email: str = Field(..., max_length=255, description="Email address to invite")
    role: str = Field("member", pattern=r'^(admin|member)$')
    message: Optional[str] = Field(None, max_length=1000, description="Custom invitation message")


class OrganizationInvitationCreate(OrganizationInvitationBase):
    """Schema for creating organization invitation."""
    
    expires_in_days: int = Field(7, ge=1, le=30, description="Invitation expiry in days")


class OrganizationInvitationResponse(BaseModel):
    """Schema for organization invitation response."""
    
    id: str
    organization_id: str
    invited_by: str
    user_id: Optional[str]
    email: str
    role: str
    token: str
    expires_at: datetime
    status: str
    accepted_at: Optional[datetime]
    rejected_at: Optional[datetime]
    rejection_reason: Optional[str]
    message: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Organization details (when included)
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None
    
    # Inviter details (when included)
    inviter_name: Optional[str] = None
    inviter_email: Optional[str] = None

    class Config:
        from_attributes = True


class InvitationAcceptRequest(BaseModel):
    """Schema for accepting organization invitation."""
    
    token: str = Field(..., description="Invitation token")


class InvitationRejectRequest(BaseModel):
    """Schema for rejecting organization invitation."""
    
    token: str = Field(..., description="Invitation token")
    reason: Optional[str] = Field(None, max_length=500, description="Reason for rejection")


class ApplicationOrganizationBase(BaseModel):
    """Base schema for application organization assignment."""
    
    role: str = Field("application", pattern=r'^(application|service|integration)$')
    description: Optional[str] = Field(None, max_length=1000)
    settings: Optional[Dict] = None


class ApplicationOrganizationCreate(ApplicationOrganizationBase):
    """Schema for assigning application to organization."""
    
    application_id: str = Field(..., description="Application ID to assign")


class ApplicationOrganizationUpdate(BaseModel):
    """Schema for updating application organization assignment."""
    
    role: Optional[str] = Field(None, pattern=r'^(application|service|integration)$')
    description: Optional[str] = Field(None, max_length=1000)
    settings: Optional[Dict] = None
    is_active: Optional[bool] = None


class ApplicationOrganizationResponse(BaseModel):
    """Schema for application organization response."""
    
    id: str
    application_id: str
    organization_id: str
    assigned_by: str
    is_active: bool
    role: str
    description: Optional[str]
    settings: Optional[Dict]
    created_at: datetime
    updated_at: datetime
    
    # Application details (when included)
    application_name: Optional[str] = None
    application_client_id: Optional[str] = None

    class Config:
        from_attributes = True


class OrganizationStatsResponse(BaseModel):
    """Schema for organization statistics."""
    
    total_members: int
    active_members: int
    pending_invitations: int
    total_applications: int
    active_applications: int
    member_roles: Dict[str, int]  # Role counts
    recent_activity: List[Dict]  # Recent activity items
