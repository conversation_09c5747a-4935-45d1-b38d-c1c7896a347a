"""
Pydantic schemas for Role Transitions.

This module contains request and response schemas for role transition operations
including developer application submission, review, and management.
"""

from typing import List, Optional

from pydantic import BaseModel, Field


class DeveloperApplicationRequest(BaseModel):
    """Request schema for developer application submission."""
    
    application_reason: str = Field(
        ...,
        min_length=50,
        max_length=1000,
        description="Detailed reason for requesting developer access"
    )
    technical_background: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Technical background and experience"
    )
    intended_use: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="How you plan to use developer features"
    )


class DeveloperApplicationResponse(BaseModel):
    """Response schema for developer application information."""

    id: str = Field(..., description="Application unique identifier")
    user_id: Optional[str] = Field(default=None, description="Applicant user ID")
    status: str = Field(..., description="Application status")
    application_reason: str = Field(..., description="Reason for application")
    technical_background: Optional[str] = Field(
        default=None,
        description="Technical background"
    )
    intended_use: Optional[str] = Field(
        default=None,
        description="Intended use of developer features"
    )
    admin_notes: Optional[str] = Field(
        default=None,
        description="Admin review notes"
    )
    reviewed_by: Optional[str] = Field(
        default=None,
        description="ID of admin who reviewed the application"
    )
    reviewed_at: Optional[str] = Field(
        default=None,
        description="Review timestamp (ISO format)"
    )
    created_at: str = Field(..., description="Application creation time (ISO format)")
    updated_at: Optional[str] = Field(
        default=None,
        description="Last update time (ISO format)"
    )
    message: Optional[str] = Field(
        default=None,
        description="Response message"
    )


class UserInfo(BaseModel):
    """User information schema for applications."""

    id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: str = Field(..., description="Email address")
    role: str = Field(..., description="Current role")
    created_at: str = Field(..., description="Account creation time")
    last_login: Optional[str] = Field(default=None, description="Last login time")


class DeveloperApplicationWithUser(BaseModel):
    """Developer application with user information for admin review."""

    id: str = Field(..., description="Application unique identifier")
    user_id: str = Field(..., description="Applicant user ID")
    reason: str = Field(..., description="Reason for application")
    technical_background: Optional[str] = Field(
        default=None,
        description="Technical background"
    )
    intended_use: Optional[str] = Field(
        default=None,
        description="Intended use of developer features"
    )
    status: str = Field(..., description="Application status")
    admin_notes: Optional[str] = Field(
        default=None,
        description="Admin review notes"
    )
    reviewed_by: Optional[str] = Field(
        default=None,
        description="ID of admin who reviewed the application"
    )
    created_at: str = Field(..., description="Application creation time")
    updated_at: Optional[str] = Field(
        default=None,
        description="Last update time"
    )
    user: UserInfo = Field(..., description="Applicant user information")


class DeveloperApplicationReviewRequest(BaseModel):
    """Request schema for admin application review."""
    
    decision: str = Field(
        ...,
        pattern="^(approved|rejected)$",
        description="Review decision: 'approved' or 'rejected'"
    )
    admin_notes: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Admin notes about the decision"
    )


class DeveloperApplicationListResponse(BaseModel):
    """Response schema for paginated application lists."""
    
    applications: List[DeveloperApplicationResponse] = Field(
        ...,
        description="List of applications"
    )
    total: int = Field(..., description="Total number of applications")
    limit: int = Field(..., description="Items per page limit")
    offset: int = Field(..., description="Pagination offset")


class RoleTransitionStatusResponse(BaseModel):
    """Response schema for role transition status."""
    
    current_role: str = Field(..., description="Current user role")
    can_apply_for_developer: bool = Field(
        ...,
        description="Whether user can apply for developer role"
    )
    has_pending_application: bool = Field(
        ...,
        description="Whether user has a pending application"
    )
    application_id: Optional[str] = Field(
        default=None,
        description="ID of pending application if any"
    )


class RoleTransitionStatsResponse(BaseModel):
    """Response schema for role transition statistics (admin only)."""
    
    total_applications: int = Field(..., description="Total applications submitted")
    pending_applications: int = Field(..., description="Pending applications")
    approved_applications: int = Field(..., description="Approved applications")
    rejected_applications: int = Field(..., description="Rejected applications")
    withdrawn_applications: int = Field(..., description="Withdrawn applications")
    total_developers: int = Field(..., description="Total developer users")
    recent_applications: List[DeveloperApplicationResponse] = Field(
        ...,
        description="Recent applications (last 10)"
    )


# Error schemas

class RoleTransitionErrorResponse(BaseModel):
    """Error response schema for role transition operations."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[dict] = Field(default=None, description="Additional error details")


# Configuration schemas

class RoleTransitionConfigResponse(BaseModel):
    """Response schema for role transition configuration."""
    
    developer_application_enabled: bool = Field(
        ...,
        description="Whether developer applications are enabled"
    )
    auto_approval_enabled: bool = Field(
        ...,
        description="Whether auto-approval is enabled"
    )
    min_application_reason_length: int = Field(
        ...,
        description="Minimum length for application reason"
    )
    max_application_reason_length: int = Field(
        ...,
        description="Maximum length for application reason"
    )
    admin_review_required: bool = Field(
        ...,
        description="Whether admin review is required"
    )


# Notification schemas

class ApplicationStatusNotification(BaseModel):
    """Schema for application status change notifications."""
    
    application_id: str = Field(..., description="Application ID")
    user_id: str = Field(..., description="Applicant user ID")
    old_status: str = Field(..., description="Previous status")
    new_status: str = Field(..., description="New status")
    reviewed_by: Optional[str] = Field(
        default=None,
        description="ID of reviewer (if applicable)"
    )
    admin_notes: Optional[str] = Field(
        default=None,
        description="Admin notes (if any)"
    )
    timestamp: str = Field(..., description="Status change timestamp (ISO format)")


# Bulk operations schemas

class BulkApplicationReviewRequest(BaseModel):
    """Request schema for bulk application review."""
    
    application_ids: List[str] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of application IDs to review"
    )
    decision: str = Field(
        ...,
        pattern="^(approved|rejected)$",
        description="Review decision for all applications"
    )
    admin_notes: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Admin notes for all applications"
    )


class BulkApplicationReviewResponse(BaseModel):
    """Response schema for bulk application review."""
    
    successful_reviews: List[str] = Field(
        ...,
        description="Application IDs that were successfully reviewed"
    )
    failed_reviews: List[dict] = Field(
        ...,
        description="Application IDs that failed with error details"
    )
    total_processed: int = Field(..., description="Total applications processed")
    success_count: int = Field(..., description="Number of successful reviews")
    failure_count: int = Field(..., description="Number of failed reviews")


# Search and filter schemas

class ApplicationSearchRequest(BaseModel):
    """Request schema for searching applications."""
    
    status: Optional[str] = Field(
        default=None,
        description="Filter by status"
    )
    user_email: Optional[str] = Field(
        default=None,
        description="Filter by applicant email"
    )
    date_from: Optional[str] = Field(
        default=None,
        description="Filter applications from date (ISO format)"
    )
    date_to: Optional[str] = Field(
        default=None,
        description="Filter applications to date (ISO format)"
    )
    search_term: Optional[str] = Field(
        default=None,
        max_length=100,
        description="Search in application reason and notes"
    )
    limit: int = Field(default=20, ge=1, le=100, description="Results limit")
    offset: int = Field(default=0, ge=0, description="Results offset")
