"""
Pydantic schemas for GeNieGO SSO Server.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


# User Management Schemas
class UserRegistrationRequest(BaseModel):
    """User registration request schema."""

    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    password: str = Field(..., min_length=8, description="Password")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")

    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        if not v.isalnum():
            raise ValueError("Username must contain only alphanumeric characters")
        return v


class UserLoginRequest(BaseModel):
    """User login request schema."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="Password")


class MFALoginRequest(BaseModel):
    """MFA login request schema for completing login with MFA."""

    session_token: str = Field(..., description="Temporary session token from initial login")
    method: str = Field(..., description="MFA method: 'totp' or 'backup_code'")
    code: str = Field(..., description="TOTP code or backup code")
    device_id: Optional[str] = Field(
        default=None,
        description="Specific device ID for TOTP verification"
    )


class UserProfileUpdateRequest(BaseModel):
    """User profile update request schema."""

    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")


# Session Management Schemas
class SessionResponse(BaseModel):
    """Session response schema."""

    session_token: str = Field(..., description="Session token")
    expires_at: datetime = Field(..., description="Session expiration")
    user: "UserResponse" = Field(..., description="User information")


class MFARequiredResponse(BaseModel):
    """Response when MFA is required during login."""

    mfa_required: bool = Field(default=True, description="MFA verification required")
    session_token: str = Field(..., description="Temporary session token for MFA flow")
    available_methods: List[str] = Field(..., description="Available MFA methods")
    expires_in: int = Field(..., description="Session token expiration in seconds")
    message: str = Field(..., description="Instructions for user")


class MFALoginResponse(BaseModel):
    """Response schema for MFA login completion."""

    success: bool = Field(..., description="Whether MFA verification succeeded")
    session_token: Optional[str] = Field(
        default=None,
        description="Final session token (if successful)"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        description="Session expiration (if successful)"
    )
    user: Optional["UserResponse"] = Field(
        default=None,
        description="User information (if successful)"
    )
    message: str = Field(..., description="Result message")

    model_config = ConfigDict(from_attributes=True)


# OAuth2 Schemas
class OAuth2AuthorizeRequest(BaseModel):
    """OAuth2 authorization request schema."""

    client_id: str = Field(..., description="Client ID of the application")
    redirect_uri: str = Field(..., description="Redirect URI")
    response_type: str = Field(
        default="code", description="Response type (always 'code')"
    )
    scope: str = Field(default="openid profile", description="Requested scopes")
    state: Optional[str] = Field(None, description="State parameter")
    code_challenge: Optional[str] = Field(None, description="PKCE code challenge")
    code_challenge_method: Optional[str] = Field(
        default="S256", description="PKCE method"
    )

    @field_validator("response_type")
    @classmethod
    def validate_response_type(cls, v):
        if v != "code":
            raise ValueError("Only authorization code flow is supported")
        return v

    @field_validator("code_challenge_method")
    @classmethod
    def validate_code_challenge_method(cls, v):
        if v and v not in ["S256", "plain"]:
            raise ValueError("Invalid code challenge method")
        return v


class OAuth2UserInfoRequest(BaseModel):
    """OAuth2 user info request schema."""

    authorization_code: str = Field(..., description="Authorization code")
    client_id: str = Field(..., description="Client ID")
    code_verifier: Optional[str] = Field(None, description="PKCE code verifier")


class OAuth2UserInfoResponse(BaseModel):
    """OAuth2 user info response schema."""

    sub: str = Field(..., description="Subject (user ID)")
    email: str = Field(..., description="Email address")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    is_verified: bool = Field(..., description="Email verification status")

    model_config = ConfigDict(from_attributes=True)


class OAuth2TokenResponse(BaseModel):
    """OAuth2 token response schema."""

    access_token: str = Field(..., description="Access token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    scope: Optional[str] = Field(None, description="Granted scopes")
    refresh_token: Optional[str] = Field(None, description="Refresh token")

    model_config = ConfigDict(from_attributes=True)


# Application Management Schemas
class RegisteredApplicationResponse(BaseModel):
    """Registered application response schema."""

    id: str = Field(..., description="Application ID")
    client_id: str = Field(..., description="Client ID")
    application_name: str = Field(..., description="Application name")
    allowed_redirect_uris: List[str] = Field(..., description="Allowed redirect URIs")
    allowed_scopes: List[str] = Field(..., description="Allowed scopes")
    is_active: bool = Field(..., description="Application is active")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict(from_attributes=True)


class ApplicationRegistrationRequest(BaseModel):
    """Application registration request schema."""

    client_id: str = Field(..., description="Client ID")
    application_name: str = Field(..., description="Application name")
    client_secret: str = Field(..., description="Client secret")
    allowed_redirect_uris: List[str] = Field(..., description="Allowed redirect URIs")
    allowed_scopes: List[str] = Field(
        default=["openid", "profile"], description="Allowed scopes"
    )


# Error Schemas
class SSOErrorResponse(BaseModel):
    """SSO error response schema."""

    error: str = Field(..., description="Error type")
    error_description: Optional[str] = Field(None, description="Error description")
    error_uri: Optional[str] = Field(None, description="Error documentation URI")
    state: Optional[str] = Field(None, description="State parameter from request")


# Success Schemas
class SSOSuccessResponse(BaseModel):
    """SSO success response schema."""

    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")


# Password Reset Schemas
class PasswordResetRequest(BaseModel):
    """Password reset request schema."""

    email: EmailStr = Field(..., description="User email address")


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema."""

    token: str = Field(..., description="Reset token")
    new_password: str = Field(..., min_length=8, description="New password")


# Health Check Schema
class SSOHealthResponse(BaseModel):
    """SSO health check response schema."""

    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Service version")
    database: str = Field(..., description="Database status")
    redis: str = Field(..., description="Redis status")
    registered_applications: int = Field(
        ..., description="Number of registered applications"
    )
    active_sessions: int = Field(..., description="Number of active sessions")


# Import UserResponse from user.py to resolve forward reference
from .user import UserResponse
