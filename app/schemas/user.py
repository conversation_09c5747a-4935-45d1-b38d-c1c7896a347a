"""
User management schemas for GeNieGO SSO Server.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


class UserBase(BaseModel):
    """Base user schema."""

    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")

    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        if not v.isalnum():
            raise ValueError("Username must contain only alphanumeric characters")
        return v


class UserCreate(UserBase):
    """User creation schema."""

    password: str = Field(..., min_length=8, description="Password")


class UserUpdate(BaseModel):
    """User update schema."""

    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")


class UserResponse(UserBase):
    """User response schema."""

    id: str = Field(..., description="User ID")
    is_active: bool = Field(..., description="User is active")
    is_verified: bool = Field(..., description="User is verified")
    role: str = Field(default="user", description="User role")
    created_at: datetime = Field(..., description="Creation timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

    model_config = ConfigDict(from_attributes=True)


class UserProfile(BaseModel):
    """User profile schema for OAuth2 userinfo endpoint."""

    sub: str = Field(..., description="Subject (user ID)")
    email: str = Field(..., description="Email address")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    is_verified: bool = Field(..., description="Email verification status")

    model_config = ConfigDict(from_attributes=True)


class UserListResponse(BaseModel):
    """User list response schema."""

    users: List[UserResponse] = Field(..., description="List of users")
    total: int = Field(..., description="Total number of users")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")


class UserProfileUpdateRequest(BaseModel):
    """User profile update request schema."""

    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")


class UserListResponse(BaseModel):
    """User list response schema."""

    users: List[UserResponse] = Field(..., description="List of users")
    total: int = Field(..., description="Total number of users")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")


class UserProfileUpdateRequest(BaseModel):
    """User profile update request schema."""

    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")


# Enhanced User Profile Schemas
class UserProfileResponse(BaseModel):
    """Enhanced user profile response schema."""

    id: str = Field(..., description="User ID")
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    phone: Optional[str] = Field(None, description="Phone number")
    bio: Optional[str] = Field(None, description="User biography")
    location: Optional[str] = Field(None, description="User location")
    job_title: Optional[str] = Field(None, description="Job title")
    company: Optional[str] = Field(None, description="Company name")
    avatar_url: Optional[str] = Field(None, description="Avatar image URL")
    timezone: Optional[str] = Field(None, description="User timezone")
    language: Optional[str] = Field(None, description="Preferred language")
    is_active: bool = Field(..., description="User is active")
    is_verified: bool = Field(..., description="User is verified")
    role: str = Field(..., description="User role")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

    model_config = ConfigDict(from_attributes=True)


class UserProfileUpdateRequest(BaseModel):
    """Enhanced user profile update request schema."""

    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    bio: Optional[str] = Field(None, max_length=1000, description="User biography")
    location: Optional[str] = Field(None, max_length=255, description="User location")
    job_title: Optional[str] = Field(None, max_length=100, description="Job title")
    company: Optional[str] = Field(None, max_length=100, description="Company name")
    timezone: Optional[str] = Field(None, max_length=50, description="User timezone")
    language: Optional[str] = Field(
        None, max_length=10, description="Preferred language"
    )


# User Security Schemas
class UserSecurityOverviewResponse(BaseModel):
    """User security overview response schema."""

    password_last_changed: Optional[datetime] = Field(
        None, description="Password last changed date"
    )
    two_factor_enabled: bool = Field(..., description="2FA enabled status")
    active_sessions_count: int = Field(..., description="Number of active sessions")
    trusted_devices_count: int = Field(..., description="Number of trusted devices")
    recent_security_events: int = Field(..., description="Recent security events count")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    account_created: datetime = Field(..., description="Account creation date")

    model_config = ConfigDict(from_attributes=True)


class PasswordChangeRequest(BaseModel):
    """Password change request schema."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")

    @field_validator("new_password")
    @classmethod
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        return v


class TrustedDeviceResponse(BaseModel):
    """Trusted device response schema."""

    id: str = Field(..., description="Device ID")
    device_name: str = Field(..., description="Device name")
    device_type: Optional[str] = Field(None, description="Device type")
    browser: Optional[str] = Field(None, description="Browser")
    os: Optional[str] = Field(None, description="Operating system")
    location: Optional[str] = Field(None, description="Location")
    is_trusted: bool = Field(..., description="Trust status")
    first_seen_at: datetime = Field(..., description="First seen timestamp")
    last_seen_at: datetime = Field(..., description="Last seen timestamp")
    access_count: int = Field(..., description="Access count")

    model_config = ConfigDict(from_attributes=True)


# User Settings Schemas
class UserSettingsResponse(BaseModel):
    """User settings response schema."""

    id: str = Field(..., description="Settings ID")
    user_id: str = Field(..., description="User ID")
    profile_visibility: str = Field(..., description="Profile visibility setting")
    email_visibility: str = Field(..., description="Email visibility setting")
    activity_visibility: str = Field(..., description="Activity visibility setting")
    data_sharing_enabled: bool = Field(..., description="Data sharing enabled")
    marketing_emails_enabled: bool = Field(..., description="Marketing emails enabled")
    analytics_enabled: bool = Field(..., description="Analytics enabled")
    language: Optional[str] = Field(None, description="Language preference")
    timezone: Optional[str] = Field(None, description="Timezone preference")
    theme: str = Field(..., description="UI theme preference")
    font_size: str = Field(..., description="Font size preference")
    high_contrast: bool = Field(..., description="High contrast mode enabled")
    screen_reader: bool = Field(..., description="Screen reader support enabled")
    data_retention_days: int = Field(..., description="Data retention period in days")
    auto_delete_inactive: bool = Field(..., description="Auto delete inactive data")
    email_notifications: Optional[dict] = Field(
        None, description="Email notification preferences"
    )
    in_app_notifications: Optional[dict] = Field(
        None, description="In-app notification preferences"
    )
    push_notifications: Optional[dict] = Field(
        None, description="Push notification preferences"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class UserSettingsUpdateRequest(BaseModel):
    """User settings update request schema."""

    profile_visibility: Optional[str] = Field(
        None, description="Profile visibility setting"
    )
    email_visibility: Optional[str] = Field(
        None, description="Email visibility setting"
    )
    activity_visibility: Optional[str] = Field(
        None, description="Activity visibility setting"
    )
    data_sharing_enabled: Optional[bool] = Field(
        None, description="Data sharing enabled"
    )
    marketing_emails_enabled: Optional[bool] = Field(
        None, description="Marketing emails enabled"
    )
    analytics_enabled: Optional[bool] = Field(None, description="Analytics enabled")
    language: Optional[str] = Field(None, description="Language preference")
    timezone: Optional[str] = Field(None, description="Timezone preference")
    theme: Optional[str] = Field(None, description="UI theme preference")
    font_size: Optional[str] = Field(None, description="Font size preference")
    high_contrast: Optional[bool] = Field(
        None, description="High contrast mode enabled"
    )
    screen_reader: Optional[bool] = Field(
        None, description="Screen reader support enabled"
    )
    data_retention_days: Optional[int] = Field(
        None, description="Data retention period"
    )
    auto_delete_inactive: Optional[bool] = Field(
        None, description="Auto delete inactive data"
    )
    email_notifications: Optional[dict] = Field(
        None, description="Email notification preferences"
    )
    in_app_notifications: Optional[dict] = Field(
        None, description="In-app notification preferences"
    )
    push_notifications: Optional[dict] = Field(
        None, description="Push notification preferences"
    )
    auto_delete_inactive: Optional[bool] = Field(
        None, description="Auto-delete inactive data"
    )
