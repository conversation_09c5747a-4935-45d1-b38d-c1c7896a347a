"""
Pydantic schemas for Multi-Factor Authentication.

This module contains request and response schemas for MFA operations
including TOTP setup, verification, and device management.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class MFASetupRequest(BaseModel):
    """Request schema for MFA device setup."""
    
    device_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User-friendly name for the MFA device"
    )
    issuer_name: Optional[str] = Field(
        default="GeNieGO SSO",
        max_length=50,
        description="Issuer name for TOTP app display"
    )


class MFASetupResponse(BaseModel):
    """Response schema for MFA device setup."""
    
    device_id: str = Field(..., description="Unique device identifier")
    qr_code: str = Field(..., description="Base64 encoded QR code image")
    backup_codes: List[str] = Field(..., description="List of backup codes")
    manual_entry_key: str = Field(..., description="Secret key for manual entry")


class MFAVerificationRequest(BaseModel):
    """Request schema for MFA code verification."""
    
    code: str = Field(
        ...,
        min_length=6,
        max_length=6,
        description="6-digit TOTP code"
    )
    device_id: Optional[str] = Field(
        default=None,
        description="Specific device ID to verify against"
    )


class BackupCodeVerificationRequest(BaseModel):
    """Request schema for backup code verification."""
    
    backup_code: str = Field(
        ...,
        min_length=8,
        max_length=8,
        description="8-character backup code"
    )


class MFAVerificationResponse(BaseModel):
    """Response schema for MFA verification."""
    
    valid: bool = Field(..., description="Whether the code is valid")
    message: str = Field(..., description="Verification result message")


class MFADeviceResponse(BaseModel):
    """Response schema for MFA device information."""
    
    id: str = Field(..., description="Device unique identifier")
    device_name: str = Field(..., description="User-friendly device name")
    device_type: str = Field(..., description="Device type (totp, sms, etc.)")
    is_active: bool = Field(..., description="Whether device is active")
    is_verified: bool = Field(..., description="Whether device setup is complete")
    last_used_at: Optional[str] = Field(
        default=None,
        description="Last time device was used (ISO format)"
    )
    created_at: str = Field(..., description="Device creation time (ISO format)")
    backup_codes_count: int = Field(..., description="Number of unused backup codes")


class MFAChallengeResponse(BaseModel):
    """Response schema for MFA challenge information."""
    
    challenge_required: bool = Field(..., description="Whether MFA challenge is required")
    available_methods: List[str] = Field(..., description="Available MFA methods")
    device_count: int = Field(..., description="Number of active MFA devices")
    backup_codes_available: bool = Field(..., description="Whether backup codes are available")


class MFAStatusResponse(BaseModel):
    """Response schema for user MFA status."""
    
    mfa_enabled: bool = Field(..., description="Whether user has MFA enabled")
    device_count: int = Field(..., description="Number of MFA devices")
    verified_device_count: int = Field(..., description="Number of verified devices")
    backup_codes_available: bool = Field(..., description="Whether backup codes are available")


# Login flow integration schemas

class MFARequiredResponse(BaseModel):
    """Response when MFA is required during login."""
    
    mfa_required: bool = Field(default=True, description="MFA verification required")
    session_token: str = Field(..., description="Temporary session token for MFA flow")
    available_methods: List[str] = Field(..., description="Available MFA methods")
    expires_in: int = Field(..., description="Session token expiration in seconds")


class MFALoginRequest(BaseModel):
    """Request schema for MFA during login flow."""
    
    session_token: str = Field(..., description="Temporary session token from login")
    method: str = Field(..., description="MFA method (totp or backup_code)")
    code: str = Field(..., description="TOTP code or backup code")
    device_id: Optional[str] = Field(
        default=None,
        description="Specific device ID for TOTP verification"
    )


class MFALoginResponse(BaseModel):
    """Response schema for MFA login completion."""
    
    success: bool = Field(..., description="Whether MFA verification succeeded")
    access_token: Optional[str] = Field(
        default=None,
        description="JWT access token (if successful)"
    )
    token_type: Optional[str] = Field(
        default="bearer",
        description="Token type"
    )
    expires_in: Optional[int] = Field(
        default=None,
        description="Token expiration in seconds"
    )
    message: str = Field(..., description="Result message")


# Device management schemas

class MFADeviceUpdateRequest(BaseModel):
    """Request schema for updating MFA device."""
    
    device_name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="New device name"
    )
    is_active: Optional[bool] = Field(
        default=None,
        description="Whether device should be active"
    )


class BackupCodesRegenerateResponse(BaseModel):
    """Response schema for backup codes regeneration."""
    
    backup_codes: List[str] = Field(..., description="New backup codes")
    device_id: str = Field(..., description="Device ID the codes belong to")
    message: str = Field(..., description="Success message")


# Error schemas

class MFAErrorResponse(BaseModel):
    """Error response schema for MFA operations."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[dict] = Field(default=None, description="Additional error details")


# Configuration schemas

class MFAConfigResponse(BaseModel):
    """Response schema for MFA configuration."""
    
    totp_enabled: bool = Field(..., description="Whether TOTP is enabled")
    backup_codes_enabled: bool = Field(..., description="Whether backup codes are enabled")
    max_devices_per_user: int = Field(..., description="Maximum devices per user")
    backup_codes_count: int = Field(..., description="Number of backup codes generated")
    totp_window: int = Field(..., description="TOTP verification window")
