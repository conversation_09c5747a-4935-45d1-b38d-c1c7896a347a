"""
Admin management schemas for GeNieGO SSO Server.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


# System Settings Schemas
class SystemSettingResponse(BaseModel):
    """System setting response schema."""

    id: str = Field(..., description="Setting ID")
    setting_key: str = Field(..., description="Setting key")
    setting_value: Any = Field(..., description="Setting value")
    setting_type: str = Field(..., description="Setting type")
    category: str = Field(..., description="Setting category")
    description: Optional[str] = Field(None, description="Setting description")
    is_public: bool = Field(..., description="Public visibility")
    is_readonly: bool = Field(..., description="Read-only status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class SystemSettingsResponse(BaseModel):
    """System settings list response schema."""

    settings: List[SystemSettingResponse] = Field(..., description="List of settings")
    total: int = Field(..., description="Total number of settings")
    categories: List[str] = Field(..., description="Available categories")


class SystemSettingUpdateRequest(BaseModel):
    """System setting update request schema."""

    setting_value: Any = Field(..., description="New setting value")
    description: Optional[str] = Field(None, description="Setting description")


class SystemSettingCreateRequest(BaseModel):
    """System setting creation request schema."""

    setting_key: str = Field(..., description="Setting key")
    setting_value: Any = Field(..., description="Setting value")
    setting_type: str = Field(..., description="Setting type")
    category: str = Field(..., description="Setting category")
    description: Optional[str] = Field(None, description="Setting description")
    is_public: bool = Field(default=False, description="Public visibility")
    is_readonly: bool = Field(default=False, description="Read-only status")


# Security Dashboard Schemas
class SecurityThreatResponse(BaseModel):
    """Security threat response schema."""

    id: str = Field(..., description="Threat ID")
    threat_type: str = Field(..., description="Type of threat")
    severity: str = Field(..., description="Threat severity")
    description: str = Field(..., description="Threat description")
    source_ip: Optional[str] = Field(None, description="Source IP address")
    target_user: Optional[str] = Field(None, description="Target user")
    detected_at: datetime = Field(..., description="Detection timestamp")
    status: str = Field(..., description="Threat status")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")

    model_config = ConfigDict(from_attributes=True)


class SecurityIncidentResponse(BaseModel):
    """Security incident response schema."""

    id: str = Field(..., description="Incident ID")
    incident_type: str = Field(..., description="Type of incident")
    severity: str = Field(..., description="Incident severity")
    title: str = Field(..., description="Incident title")
    description: str = Field(..., description="Incident description")
    affected_users: int = Field(..., description="Number of affected users")
    status: str = Field(..., description="Incident status")
    created_at: datetime = Field(..., description="Creation timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")

    model_config = ConfigDict(from_attributes=True)


class SecurityOverviewResponse(BaseModel):
    """Admin security overview response schema."""

    active_threats: int = Field(..., description="Number of active threats")
    resolved_threats_today: int = Field(..., description="Threats resolved today")
    open_incidents: int = Field(..., description="Number of open incidents")
    failed_login_attempts: int = Field(..., description="Failed login attempts today")
    suspicious_activities: int = Field(
        ..., description="Suspicious activities detected"
    )
    security_score: float = Field(..., description="Overall security score")
    last_security_scan: Optional[datetime] = Field(
        None, description="Last security scan"
    )

    model_config = ConfigDict(from_attributes=True)


# System Health Schemas
class SystemHealthMetric(BaseModel):
    """System health metric schema."""

    metric_name: str = Field(..., description="Metric name")
    current_value: float = Field(..., description="Current value")
    threshold_warning: Optional[float] = Field(None, description="Warning threshold")
    threshold_critical: Optional[float] = Field(None, description="Critical threshold")
    status: str = Field(..., description="Metric status")
    unit: Optional[str] = Field(None, description="Metric unit")
    last_updated: datetime = Field(..., description="Last update timestamp")


class SystemHealthResponse(BaseModel):
    """System health overview response schema."""

    overall_status: str = Field(..., description="Overall system status")
    uptime_seconds: int = Field(..., description="System uptime in seconds")
    active_users: int = Field(..., description="Number of active users")
    active_sessions: int = Field(..., description="Number of active sessions")
    database_status: str = Field(..., description="Database status")
    redis_status: str = Field(..., description="Redis status")
    api_response_time: float = Field(..., description="Average API response time")
    error_rate: float = Field(..., description="Error rate percentage")
    metrics: List[SystemHealthMetric] = Field(..., description="Detailed metrics")
    alerts: List[str] = Field(..., description="Active alerts")

    model_config = ConfigDict(from_attributes=True)


# Audit Log Schemas
class AuditLogEntry(BaseModel):
    """Audit log entry schema."""

    id: str = Field(..., description="Log entry ID")
    user_id: Optional[str] = Field(None, description="User ID")
    user_email: Optional[str] = Field(None, description="User email")
    action: str = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Resource type")
    resource_id: Optional[str] = Field(None, description="Resource ID")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    timestamp: datetime = Field(..., description="Action timestamp")
    success: bool = Field(..., description="Action success status")

    model_config = ConfigDict(from_attributes=True)


class AuditLogResponse(BaseModel):
    """Audit log response schema."""

    logs: List[AuditLogEntry] = Field(..., description="List of audit log entries")
    total: int = Field(..., description="Total number of entries")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    filters: Dict[str, Any] = Field(..., description="Applied filters")

    model_config = ConfigDict(from_attributes=True)


class AuditExportRequest(BaseModel):
    """Audit data export request schema."""

    start_date: Optional[datetime] = Field(None, description="Start date for export")
    end_date: Optional[datetime] = Field(None, description="End date for export")
    user_ids: Optional[List[str]] = Field(None, description="Specific user IDs")
    actions: Optional[List[str]] = Field(None, description="Specific actions")
    resource_types: Optional[List[str]] = Field(
        None, description="Specific resource types"
    )
    format: str = Field(default="json", description="Export format")
    include_details: bool = Field(
        default=True, description="Include detailed information"
    )
