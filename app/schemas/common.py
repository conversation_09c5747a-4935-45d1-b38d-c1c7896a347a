"""
Common schemas for GeNieGO API.
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from pydantic import BaseModel, ConfigDict, Field

T = TypeVar("T")


class MessageResponse(BaseModel):
    """Schema for general message responses."""

    message: str
    timestamp: Optional[datetime] = None


class ErrorResponse(BaseModel):
    """Schema for error responses."""

    error: str
    detail: Optional[str] = None
    timestamp: datetime
    status_code: int

    model_config = ConfigDict(from_attributes=True)


class ValidationErrorResponse(BaseModel):
    """Schema for validation error responses."""

    error: str = "Validation Error"
    detail: Union[str, List[str]]
    timestamp: datetime
    status_code: int = 422
    errors: Optional[Any] = None

    model_config = ConfigDict(from_attributes=True)


class SuccessResponse(BaseModel):
    """Schema for success responses."""

    result: bool = True
    data: Optional[Any] = None
    message: Optional[str] = None


class FileUploadResponse(BaseModel):
    """Schema for file upload responses."""

    filename: str
    file_size: int
    content_type: str
    url: Optional[str] = None
    message: str = "File uploaded successfully"


class PaginatedResponse(BaseModel, Generic[T]):
    """Schema for paginated responses."""

    data: List[T]
    total: int
    page: int = Field(ge=1)
    size: int = Field(ge=1, le=100)
    pages: int

    @classmethod
    def create(
        cls, data: List[T], total: int, page: int, size: int
    ) -> "PaginatedResponse[T]":
        """Create paginated response."""
        pages = (total + size - 1) // size  # Ceiling division
        return cls(data=data, total=total, page=page, size=size, pages=pages)


class SearchRequest(BaseModel):
    """Schema for search requests."""

    search: Optional[str] = None
    start: int = Field(default=0, ge=0)
    length: int = Field(default=10, ge=1, le=100)


class HealthResponse(BaseModel):
    """Schema for health check responses."""

    status: str
    timestamp: str
    uptime: str
    version: str
    environment: str
    python_version: str
    platform: str
    services: Dict[str, Any]


# Legacy response format for compatibility
class LegacyResponse(BaseModel):
    """Legacy response format for existing clients."""

    result: bool
    code: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Any] = None


class DataTableResponse(BaseModel):
    """DataTables compatible response format."""

    data: List[Any]
    recordsTotal: int
    recordsFiltered: int
    draw: Optional[int] = None
