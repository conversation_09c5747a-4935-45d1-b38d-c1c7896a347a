"""
Audit Logging Middleware for GeNieGO SSO Server.

This middleware automatically logs security-critical operations
for compliance and security monitoring.
"""

import json
import logging
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.database import get_db
from app.services.audit_service import AuditService

logger = logging.getLogger(__name__)


class AuditLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to automatically log critical endpoint access."""

    def __init__(self, app, excluded_paths: list = None):
        super().__init__(app)
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/metrics",
            "/static"
        ]
        
        # Define critical endpoints that require audit logging
        self.critical_endpoints = {
            # Authentication endpoints
            "/api/v1/auth/login": "login_attempt",
            "/api/v1/auth/logout": "logout",
            "/api/v1/auth/register": "registration_attempt",
            "/api/v1/auth/mfa-login": "mfa_login_attempt",
            
            # MFA endpoints
            "/api/v1/mfa/setup": "mfa_setup",
            "/api/v1/mfa/verify": "mfa_verification",
            "/api/v1/mfa/verify-backup": "mfa_backup_verification",
            
            # Role transition endpoints
            "/api/v1/role-transitions/developer-application": "developer_application_submitted",
            "/api/v1/role-transitions/admin/applications": "admin_application_review",
            
            # Admin endpoints
            "/api/v1/admin/users": "admin_user_management",
            "/api/v1/admin/applications": "admin_application_management",
            
            # OAuth2 endpoints
            "/oauth2/token": "oauth2_token_request",
            "/oauth2/authorize": "oauth2_authorization",
            "/oauth2/userinfo": "oauth2_userinfo_access",
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Log critical endpoint access and responses."""
        
        # Skip audit logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)

        # Check if this is a critical endpoint
        endpoint_action = self._get_endpoint_action(request.url.path, request.method)
        
        if not endpoint_action:
            return await call_next(request)

        # Record start time
        start_time = time.time()
        
        # Extract request information
        request_info = self._extract_request_info(request)
        
        # Get user ID if available (from session or JWT)
        user_id = await self._extract_user_id(request)
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Log the audit event
            await self._log_audit_event(
                request=request,
                response=response,
                user_id=user_id,
                action=endpoint_action,
                request_info=request_info,
                processing_time=processing_time
            )
            
            return response

        except Exception as e:
            # Log failed request
            processing_time = time.time() - start_time
            
            await self._log_audit_event(
                request=request,
                response=None,
                user_id=user_id,
                action=endpoint_action,
                request_info=request_info,
                processing_time=processing_time,
                error=str(e)
            )
            
            raise

    def _get_endpoint_action(self, path: str, method: str) -> str:
        """Determine the audit action for an endpoint."""
        # Check exact path matches first
        if path in self.critical_endpoints:
            return self.critical_endpoints[path]
        
        # Check pattern matches
        for endpoint_pattern, action in self.critical_endpoints.items():
            if path.startswith(endpoint_pattern):
                # Add method suffix for different HTTP methods
                if method in ["POST", "PUT", "DELETE"]:
                    return f"{action}_{method.lower()}"
                return action
        
        return None

    def _extract_request_info(self, request: Request) -> dict:
        """Extract relevant information from the request."""
        return {
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params) if request.query_params else None,
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "referer": request.headers.get("referer"),
            "content_type": request.headers.get("content-type"),
        }

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"

    async def _extract_user_id(self, request: Request) -> str:
        """Extract user ID from request if available."""
        # Try to get user ID from session cookie
        session_token = request.cookies.get("geniengo_session")
        if session_token:
            # In a real implementation, you would decode the session token
            # For now, we'll return None and let the endpoint handlers provide user_id
            pass
        
        # Try to get user ID from Authorization header
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # In a real implementation, you would decode the JWT token
            # For now, we'll return None and let the endpoint handlers provide user_id
            pass
        
        return None

    async def _log_audit_event(
        self,
        request: Request,
        response: Response,
        user_id: str,
        action: str,
        request_info: dict,
        processing_time: float,
        error: str = None
    ):
        """Log the audit event to the database."""
        try:
            # Get database session
            async for db in get_db():
                audit_service = AuditService(db)
                
                # Determine status
                status = "success"
                if error:
                    status = "error"
                elif response and response.status_code >= 400:
                    status = "failure"
                
                # Prepare audit details
                details = {
                    "method": request_info["method"],
                    "path": request_info["path"],
                    "processing_time_ms": round(processing_time * 1000, 2),
                    "status_code": response.status_code if response else None,
                    "query_params": request_info["query_params"],
                    "content_type": request_info["content_type"],
                    "referer": request_info["referer"],
                }
                
                if error:
                    details["error"] = error
                
                # Log the event
                await audit_service.log_security_event(
                    user_id=user_id,
                    action=action,
                    resource_type="endpoint",
                    resource_id=request_info["path"],
                    ip_address=request_info["ip_address"],
                    user_agent=request_info["user_agent"],
                    details=details,
                    status=status,
                    error_message=error
                )
                
                break  # Exit the async generator

        except Exception as e:
            # Don't let audit logging failures break the application
            logger.error(f"Failed to log audit event: {str(e)}")


def create_audit_logging_middleware(excluded_paths: list = None):
    """Factory function to create audit logging middleware."""
    def middleware_factory(app):
        return AuditLoggingMiddleware(app, excluded_paths)
    return middleware_factory
