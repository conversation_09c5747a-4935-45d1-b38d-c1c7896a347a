"""
Rate Limiting Middleware for GeNieGO SSO Server.

This middleware applies rate limiting to critical endpoints to prevent abuse
and ensure system stability.
"""

import logging
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.rate_limiting_service import get_rate_limiting_service

logger = logging.getLogger(__name__)


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Middleware to apply rate limiting to critical endpoints."""

    def __init__(self, app, excluded_paths: list = None):
        super().__init__(app)
        self.rate_limiting_service = get_rate_limiting_service()
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/metrics"
        ]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting based on endpoint and user context."""
        
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)

        # Get client IP address
        client_ip = self._get_client_ip(request)
        
        try:
            # Apply rate limiting based on endpoint
            await self._apply_endpoint_rate_limiting(request, client_ip)
            
            # Process the request
            response = await call_next(request)
            
            return response

        except Exception as e:
            # If rate limiting fails, log error but allow request
            logger.error(f"Rate limiting error: {e}")
            return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"

    async def _apply_endpoint_rate_limiting(self, request: Request, client_ip: str):
        """Apply rate limiting based on the endpoint being accessed."""
        path = request.url.path
        method = request.method

        # Login endpoints - strict rate limiting
        if path.startswith("/api/v1/auth/login") and method == "POST":
            await self.rate_limiting_service.check_login_rate_limit(client_ip)
        
        # Registration endpoints - moderate rate limiting
        elif path.startswith("/api/v1/auth/register") and method == "POST":
            # We'll need to extract email from request body for email-based limiting
            # For now, just apply IP-based limiting
            await self._check_ip_rate_limit(client_ip, "register", 10, 3600)
        
        # MFA endpoints - moderate rate limiting
        elif path.startswith("/api/v1/mfa/") and method == "POST":
            await self._check_ip_rate_limit(client_ip, "mfa", 30, 600)
        
        # OAuth2 endpoints - moderate rate limiting
        elif path.startswith("/api/v1/oauth2/") and method == "POST":
            await self._check_ip_rate_limit(client_ip, "oauth2", 50, 3600)
        
        # Admin endpoints - strict rate limiting
        elif path.startswith("/api/v1/admin/") and method in ["POST", "PUT", "DELETE"]:
            await self._check_ip_rate_limit(client_ip, "admin", 20, 3600)
        
        # General API endpoints - lenient rate limiting
        elif path.startswith("/api/v1/") and method in ["GET", "POST", "PUT", "DELETE"]:
            await self._check_ip_rate_limit(client_ip, "api", 200, 3600)

    async def _check_ip_rate_limit(
        self, 
        ip_address: str, 
        endpoint_type: str, 
        limit: int, 
        window_seconds: int
    ):
        """Check IP-based rate limit for an endpoint type."""
        allowed, info = await self.rate_limiting_service.check_rate_limit(
            key=f"{endpoint_type}_ip:{ip_address}",
            limit=limit,
            window_seconds=window_seconds
        )
        
        if not allowed:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded for {endpoint_type} endpoints",
                headers={"Retry-After": str(info["retry_after"])}
            )


def create_rate_limiting_middleware(excluded_paths: list = None):
    """Factory function to create rate limiting middleware."""
    def middleware_factory(app):
        return RateLimitingMiddleware(app, excluded_paths)
    return middleware_factory
