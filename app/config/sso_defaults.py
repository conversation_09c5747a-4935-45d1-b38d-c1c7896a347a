"""
Default data initialization for GeNieGO SSO Server.
Contains default application structures and database initialization logic.
For configurable settings like secrets and URLs, see settings.py
"""

import os
import uuid
from datetime import datetime
from typing import Dict, List

from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.models import Organization, OrganizationMembership, RegisteredApplication, User


async def initialize_default_data(db: AsyncSession) -> bool:
    """Initialize default data for GeNieGO SSO Server."""
    try:
        print("🎯 Starting initialize_default_data function...")
        settings = get_settings()

        # Initialize default admin user
        print("🔧 About to initialize default admin user...")
        await initialize_default_admin_user(db)
        print("✅ Default admin user initialization completed")

        # Initialize default developer user
        print("🔧 About to call initialize_default_developer_user...")
        developer_user = await initialize_default_developer_user(db)
        print("✅ Called initialize_default_developer_user successfully")

        # Initialize default genie user
        print("🔧 About to initialize default genie user...")
        await initialize_default_genie_user(db)
        print("✅ Default genie user initialization completed")

        # Initialize registered applications (after developer user is created)
        print("🔧 About to initialize registered applications...")
        await initialize_registered_applications(db, settings, developer_user.id)
        print("✅ Registered applications initialized")

        # Initialize default Genieland organization
        print("🔧 About to initialize default Genieland organization...")
        await initialize_default_organization(db)
        print("✅ Default Genieland organization initialized")

        print("🔧 About to commit database transaction...")
        await db.commit()
        print("✅ Database transaction committed successfully")
        return True

    except Exception as e:
        print(f"❌ Exception in initialize_default_data: {e}")
        print(f"❌ Exception type: {type(e)}")
        import traceback

        print(f"❌ Full traceback: {traceback.format_exc()}")
        await db.rollback()
        print(f"Error initializing SSO default data: {e}")
        return False


async def initialize_registered_applications(
    db: AsyncSession, settings, developer_id: str
) -> None:
    """Initialize registered applications from configuration."""

    # Check if applications already exist
    existing_applications = await db.execute(
        text("SELECT client_id FROM registered_applications")
    )
    existing_client_ids = {row[0] for row in existing_applications.fetchall()}

    # Get applications configuration
    applications_config = get_default_applications_data(settings)

    for client_id, config in applications_config.items():
        if client_id not in existing_client_ids:
            application = RegisteredApplication(
                id=str(uuid.uuid4()),
                client_id=client_id,
                application_name=config["application_name"],
                allowed_redirect_uris=config["allowed_redirect_uris"],
                allowed_scopes=config["allowed_scopes"],
                developer_id=developer_id,
                is_active=config["is_active"],
                created_at=datetime.utcnow(),
            )

            # Set client secret if provided
            if config.get("client_secret"):
                application.set_client_secret(config["client_secret"])

            db.add(application)
            print(
                f"✅ Registered application: {config['application_name']} ({client_id})"
            )


async def initialize_default_admin_user(db: AsyncSession) -> None:
    """Initialize default admin user for GeNieGO SSO Server."""

    # Check if admin user already exists
    existing_admin = await db.execute(
        text(
            "SELECT id FROM users WHERE username = 'GenieAdmin' OR email = '<EMAIL>'"
        )
    )

    if existing_admin.fetchone():
        print("ℹ️  Default admin user already exists")
        return

    # Create default admin user
    admin_user = User(
        id=str(uuid.uuid4()),
        email="<EMAIL>",
        username="GenieAdmin",
        first_name="Genie",
        last_name="Administrator",
        role="admin",
        is_active=True,
        is_verified=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )

    # Set default password
    admin_user.set_password("GenieAdmin123!")

    db.add(admin_user)
    print("✅ Created default admin user: GenieAdmin / GenieAdmin123!")


async def initialize_default_developer_user(db: AsyncSession) -> User:
    """Initialize default developer user for GeNieGO SSO Server."""
    print("🚀 Starting initialize_default_developer_user function...")
    try:
        print("🔍 Checking for existing developer user...")

        # Check if developer user already exists
        existing_developer = await db.execute(
            text(
                "SELECT id FROM users WHERE username = 'GenieDeveloper' OR email = '<EMAIL>'"
            )
        )

        existing_row = existing_developer.fetchone()
        if existing_row:
            print("ℹ️  Default developer user already exists")
            # Get the existing developer user
            result = await db.execute(
                select(User).where(User.username == "GenieDeveloper")
            )
            return result.scalar_one()

        print("📝 Creating default developer user...")

        # Create default developer user
        developer_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="GenieDeveloper",
            first_name="Genie",
            last_name="Developer",
            role="developer",
            is_active=True,
            is_verified=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Set default password
        developer_user.set_password("GenieDev123!")

        db.add(developer_user)
        print("✅ Created default developer user: GenieDeveloper / GenieDev123!")
        return developer_user

    except Exception as e:
        print(f"❌ Error creating default developer user: {e}")
        raise e


async def initialize_default_genie_user(db: AsyncSession) -> None:
    """Initialize default genie user for GeNieGO SSO Server."""

    # Check if genie user already exists
    existing_genie = await db.execute(
        text(
            "SELECT id FROM users WHERE username = 'GenieUser' OR email = '<EMAIL>'"
        )
    )

    if existing_genie.fetchone():
        print("ℹ️  Default genie user already exists")
        return

    # Create default genie user
    genie_user = User(
        id=str(uuid.uuid4()),
        email="<EMAIL>",
        username="GenieUser",
        first_name="Genie",
        last_name="User",
        role="user",
        is_active=True,
        is_verified=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )

    # Set default password
    genie_user.set_password("GenieUser123!")

    db.add(genie_user)
    print("✅ Created default genie user: GenieUser / GenieUser123!")


async def initialize_default_organization(db: AsyncSession) -> None:
    """Initialize default Genieland organization and add all GenieXXXXXX users to it."""

    # Check if Genieland organization already exists
    existing_org = await db.execute(
        text("SELECT id FROM organizations WHERE slug = 'genieland'")
    )

    if existing_org.fetchone():
        print("ℹ️  Default Genieland organization already exists")
        return

    print("📝 Creating default Genieland organization...")

    # Create Genieland organization
    organization = Organization(
        id=str(uuid.uuid4()),
        name="Genieland",
        slug="genieland",
        description="The official Genieland organization for all Genie applications and services",
        website="https://genieland.ai",
        is_active=True,
        is_public=True,
        max_members=100,
        subscription_tier="enterprise",
        billing_email="<EMAIL>",
        settings={
            "default_organization": True,
            "auto_approve_applications": True,
            "allow_public_discovery": True,
        },
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )

    db.add(organization)
    await db.flush()  # Get organization ID
    print("✅ Created Genieland organization")

    # Get all GenieXXXXXX users
    genie_users = await db.execute(
        text("SELECT id, username, role FROM users WHERE username LIKE 'Genie%'")
    )

    users_data = genie_users.fetchall()
    print(f"🔍 Found {len(users_data)} Genie users to add to organization")

    # Add each user to the organization with appropriate role
    for user_data in users_data:
        user_id, username, user_role = user_data

        # Determine organization role based on user role (simplified 3-role model)
        if user_role == "admin":
            org_role = "owner"  # GenieAdmin becomes owner
        elif user_role == "developer":
            org_role = "admin"  # GenieDeveloper becomes admin
        else:
            org_role = "member"  # GenieUser becomes member

        # Create membership
        membership = OrganizationMembership(
            id=str(uuid.uuid4()),
            organization_id=organization.id,
            user_id=user_id,
            role=org_role,
            is_active=True,
            joined_at=datetime.utcnow(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        db.add(membership)
        print(f"✅ Added {username} to Genieland organization as {org_role}")

    print("✅ All Genie users added to Genieland organization")


def get_default_applications_data(settings=None) -> Dict[str, Dict]:
    """Get default applications configuration for GeNieGO SSO Server."""
    if settings is None:
        settings = get_settings()

    return {
        "system": {
            "application_name": "GeNieGO SSO System",
            "client_secret": os.environ.get(
                "GENIEGO_SYSTEM_SECRET", "system-secret-change-in-production"
            ),
            "allowed_redirect_uris": [
                "http://localhost:5550/auth/callback",
                "https://geniego.genieland.ai/auth/callback",
            ],
            "allowed_scopes": ["openid", "profile", "email", "admin", "developer"],
            "is_active": True,
        },
        "genie-move-auth-v4": {
            "application_name": "GenieMove",
            "client_secret": os.environ.get(
                "GENIE_MOVE_AUTH_SECRET", "genie-move-secret-change-in-production"
            ),
            "allowed_redirect_uris": [
                "http://localhost:5001/auth/callback",
                "https://move.genieland.ai/auth/callback",
            ],
            "allowed_scopes": ["openid", "profile", "email"],
            "is_active": True,
        },
        "genie-flow": {
            "application_name": "GenieFlow",
            "client_secret": os.environ.get(
                "GENIE_FLOW_SECRET", "genie-flow-secret-change-in-production"
            ),
            "allowed_redirect_uris": [
                "http://localhost:8123/auth/callback",
                "https://genieflow.genieland.ai/auth/callback",
            ],
            "allowed_scopes": ["openid", "profile", "email"],
            "is_active": True,
        },
        "genie-admin": {
            "application_name": "GenieAdmin",
            "client_secret": os.environ.get(
                "GENIE_ADMIN_SECRET", "genie-admin-secret-change-in-production"
            ),
            "allowed_redirect_uris": [
                "http://localhost:3001/auth/callback",
                "https://admin.genieland.ai/auth/callback",
            ],
            "allowed_scopes": ["openid", "profile", "email", "admin"],
            "is_active": True,
        },
    }


def get_default_admin_user_data() -> Dict[str, str]:
    """Get default admin user data for manual setup."""
    return {
        "email": "<EMAIL>",
        "username": "GenieAdmin",
        "password": "GenieAdmin123!",
        "first_name": "Genie",
        "last_name": "Administrator",
    }


def get_default_developer_user_data() -> Dict[str, str]:
    """Get default developer user data for manual setup."""
    return {
        "email": "<EMAIL>",
        "username": "GenieDeveloper",
        "password": "GenieDev123!",
        "first_name": "Genie",
        "last_name": "Developer",
    }


def get_default_genie_user_data() -> Dict[str, str]:
    """Get default genie user data for manual setup."""
    return {
        "email": "<EMAIL>",
        "username": "GenieUser",
        "password": "GenieUser123!",
        "first_name": "Genie",
        "last_name": "User",
    }
