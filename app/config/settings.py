"""
Configuration management for GeNieGO SSO Server FastAPI backend.
Contains environment-configurable settings like secrets, URLs, ports, etc.
For default application data and initialization, see sso_defaults.py
"""

import logging.config
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings for GeNieGO SSO Server backend."""

    # Server Configuration
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=5550, description="Server port")
    DEBUG: bool = Field(default=True, description="Debug mode")

    # API Configuration - Clean and scalable design
    API_PREFIX: str = Field(default="/api", description="Base API prefix")
    API_VERSION: str = Field(default="v1", description="Current API version")
    PROJECT_NAME: str = Field(default="GeNieGO SSO Server", description="Project name")
    VERSION: str = Field(default="1.0.0", description="Application version")

    # CORS Configuration
    # CORS Configuration - GeNieGO SSO Server specific origins
    CORS_ORIGINS: str = Field(
        default="http://localhost:5173,http://localhost:8080,http://localhost:5550,https://geniemove.yourdomain.com",
        description="Comma-separated list of allowed CORS origins for applications",
    )

    # Database Configuration - GeNieGO SSO Server MySQL
    DATABASE_URL: str = Field(
        default="mysql+aiomysql://geniengo_user:geniengo_password@localhost:3306/geniengo_sso_db",
        description="Database connection URL",
    )
    DATABASE_ECHO: bool = Field(
        default=False, description="Enable SQLAlchemy query logging"
    )
    # Database connection pool settings
    DATABASE_POOL_SIZE: int = Field(
        default=10, ge=1, description="Database connection pool size"
    )
    DATABASE_MAX_OVERFLOW: int = Field(
        default=20, ge=0, description="Maximum overflow connections"
    )
    DATABASE_POOL_RECYCLE: int = Field(
        default=300, ge=60, description="Connection recycle time in seconds"
    )

    # Redis Configuration
    REDIS_URL: str = Field(
        default="redis://localhost:6379", description="Redis connection URL"
    )

    # OAuth2 & JWT Configuration - GeNieGO SSO Server
    SECRET_KEY: str = Field(
        default="geniengo-secret-key-change-this-in-production",
        description="Secret key for session and authorization code signing",
    )
    JWT_SECRET_KEY: str = Field(
        default="geniengo-jwt-secret-key-change-this-in-production",
        description="JWT secret key for session tokens",
    )
    ENCRYPTION_KEY: str = Field(
        default="geniengo-32-byte-encryption-key-change-this",
        description="32-byte encryption key for sensitive data",
    )
    ALGORITHM: str = Field(default="HS256", description="JWT signing algorithm")
    JWT_ALGORITHM: str = Field(
        default="HS256", description="JWT algorithm for backward compatibility"
    )
    # Session and Authorization Code Configuration
    SESSION_EXPIRE_HOURS: int = Field(
        default=24, gt=0, description="User session expiration time in hours"
    )
    SESSION_EXPIRE_MINUTES: int = Field(
        default=1440, gt=0, description="User session expiration time in minutes (24 hours)"
    )
    AUTHORIZATION_CODE_EXPIRE_MINUTES: int = Field(
        default=10, gt=0, description="Authorization code expiration time in minutes"
    )
    # Legacy token settings for backward compatibility
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, gt=0, description="Access token expiration time in minutes (legacy)"
    )
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, gt=0, description="JWT access token expiration from .env (legacy)"
    )
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=7, gt=0, description="Refresh token expiration time in days (legacy)"
    )
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=7, gt=0, description="JWT refresh token expiration from .env (legacy)"
    )
    JWT_EXPIRATION_HOURS: int = Field(
        default=24,
        gt=0,
        description="JWT expiration in hours for backward compatibility",
    )

    # Environment configuration
    ENVIRONMENT: str = Field(
        default="development", description="Application environment"
    )

    # Application OAuth2 Client Configuration - GeNieGO registered applications
    GENIE_MOVE_AUTH_SECRET: str = Field(
        default="genie-move-auth-secret-change-in-production",
        description="GENIE-MOVE-AUTH-V4 application OAuth2 client secret",
    )
    GENIE_FLOW_SECRET: str = Field(
        default="genie-flow-secret-change-in-production",
        description="GenieFlow application OAuth2 client secret",
    )
    GENIE_ADMIN_SECRET: str = Field(
        default="genie-admin-secret-change-in-production",
        description="GenieAdmin application OAuth2 client secret",
    )
    # Legacy client secrets for backward compatibility
    ADMIN_PORTAL_SECRET: str = Field(
        default="admin-portal-secret-change-in-production",
        description="Admin portal OAuth2 client secret (legacy)",
    )
    MOBILE_APP_SECRET: str = Field(
        default="mobile-app-secret-change-in-production",
        description="Mobile app OAuth2 client secret (legacy)",
    )
    USER_PORTAL_SECRET: str = Field(
        default="user-portal-secret-change-in-production",
        description="User portal OAuth2 client secret (legacy)",
    )

    # Email Configuration (for password reset)
    SMTP_HOST: str = Field(default="localhost", description="SMTP server hostname")
    SMTP_PORT: int = Field(default=587, ge=1, le=65535, description="SMTP server port")
    SMTP_USERNAME: str = Field(default="", description="SMTP username")
    SMTP_PASSWORD: str = Field(default="", description="SMTP password")
    SMTP_USER: str = Field(default="", description="SMTP user from .env")
    SMTP_USE_TLS: bool = Field(default=True, description="Use TLS for SMTP connection")
    SMTP_USE_SSL: bool = Field(default=False, description="Use SSL for SMTP connection")
    SMTP_TLS: bool = Field(default=True, description="TLS setting from .env")
    SMTP_FROM_EMAIL: str = Field(
        default="<EMAIL>", description="Default from email address"
    )
    EMAILS_FROM_EMAIL: str = Field(
        default="<EMAIL>", description="From email from .env"
    )
    EMAILS_FROM_NAME: str = Field(
        default="GeNieGO SSO", description="From name from .env"
    )

    # Security Configuration
    ENABLE_DOCS: bool = Field(
        default=True, description="Enable API documentation endpoints"
    )
    ENABLE_ADMIN_ENDPOINTS: bool = Field(
        default=True, description="Enable admin endpoints"
    )
    PASSWORD_MIN_LENGTH: int = Field(
        default=8, ge=8, description="Minimum password length"
    )

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(
        default=60, gt=0, description="Rate limit requests per minute"
    )
    HEALTH_CHECK_RATE_LIMIT: int = Field(
        default=5, gt=0, description="Health check rate limit per minute"
    )
    RATE_LIMIT_REQUESTS: int = Field(
        default=100, gt=0, description="Rate limit requests from .env"
    )
    RATE_LIMIT_WINDOW: int = Field(
        default=60, gt=0, description="Rate limit window in seconds from .env"
    )

    # Multi-tenant Configuration - From .env
    DEFAULT_ROLE_ID: int = Field(
        default=2, ge=1, description="Default role ID for new users"
    )
    ADMIN_ROLE_ID: int = Field(default=1, ge=1, description="Admin role ID")
    SUPER_ADMIN_ROLE_ID: int = Field(default=4, ge=1, description="Super admin role ID")

    # OAuth2 Client IDs - From .env
    OAUTH2_CLIENT_WEB: str = Field(
        default="move-web-client", description="Web client ID"
    )
    OAUTH2_CLIENT_MOBILE: str = Field(
        default="move-mobile-client", description="Mobile client ID"
    )
    OAUTH2_CLIENT_DESKTOP: str = Field(
        default="move-desktop-client", description="Desktop client ID"
    )
    OAUTH2_CLIENT_API: str = Field(
        default="move-api-client", description="API client ID"
    )
    OAUTH2_CLIENT_ADMIN: str = Field(
        default="move-admin-client", description="Admin client ID"
    )
    OAUTH2_CLIENT_REPORTS: str = Field(
        default="move-reports-client", description="Reports client ID"
    )
    OAUTH2_CLIENT_INTEGRATION: str = Field(
        default="move-integration-client", description="Integration client ID"
    )

    # Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log message format",
    )
    LOG_FILE: str = Field(default="logs/app.log", description="Log file path")
    LOG_MAX_SIZE: int = Field(
        default=10485760, gt=0, description="Maximum log file size in bytes (10MB)"
    )
    LOG_BACKUP_COUNT: int = Field(
        default=5, ge=1, description="Number of backup log files to keep"
    )

    # Domain Configuration - GeNieGO SSO Server
    SSO_DOMAIN: str = Field(
        default="geniengo.genieland.ai", description="SSO server domain name"
    )
    DOMAIN_NAME: str = Field(
        default="localhost:5550", description="Domain name for the application"
    )

    # Core Service Integration (for external integrations)
    CORE_SERVER: str = Field(
        default="", description="Core server URL for external integrations"
    )

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: str = Field(default="", description="Google OAuth2 client ID")
    GOOGLE_CLIENT_SECRET: str = Field(
        default="", description="Google OAuth2 client secret"
    )
    GOOGLE_REDIRECT_URI: str = Field(
        default="", description="Google OAuth2 redirect URI for production"
    )
    GOOGLE_REDIRECT_URI_LOCAL: str = Field(
        default="", description="Google OAuth2 redirect URI for local development"
    )

    # SSO Server Configuration
    SSO_DOMAIN: str = Field(default="localhost:5550", description="SSO server domain")
    SSO_BASE_URL: str = Field(
        default="http://localhost:5550", description="SSO server base URL"
    )

    # OAuth2 Application Client Configuration
    OAUTH2_CLIENT_GENIE_MOVE: str = Field(
        default="genie-move-auth-v4", description="GENIE-MOVE-AUTH-V4 client ID"
    )
    OAUTH2_CLIENT_SYSTEM: str = Field(default="system", description="System client ID")
    OAUTH2_CLIENT_ADMIN: str = Field(
        default="geniengo-admin-client", description="Admin client ID"
    )

    # OAuth2 Application Client Secrets
    OAUTH2_CLIENT_GENIE_MOVE_SECRET: str = Field(
        default="genie-move-secret-change-in-production",
        description="GENIE-MOVE-AUTH-V4 client secret",
    )
    OAUTH2_CLIENT_SYSTEM_SECRET: str = Field(
        default="system-secret-change-in-production", description="System client secret"
    )
    OAUTH2_CLIENT_ADMIN_SECRET: str = Field(
        default="admin-secret-change-in-production", description="Admin client secret"
    )

    # API Path Management - Dynamic and scalable
    @property
    def api_versioned_prefix(self) -> str:
        """Get the versioned API prefix (e.g., '/api/v1')."""
        return f"{self.API_PREFIX}/{self.API_VERSION}"

    @property
    def auth_endpoint_prefix(self) -> str:
        """Get the auth endpoint prefix (e.g., '/api/v1/auth')."""
        return f"{self.api_versioned_prefix}/auth"

    @property
    def oauth_endpoint_prefix(self) -> str:
        """Get the OAuth endpoint prefix (e.g., '/oauth2')."""
        return "/oauth2"

    @property
    def admin_endpoint_prefix(self) -> str:
        """Get the admin endpoint prefix (e.g., '/api/v1/admin')."""
        return f"{self.api_versioned_prefix}/admin"

    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.DEBUG

    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return not self.DEBUG

    @property
    def log_level(self) -> str:
        """Get log level."""
        return self.LOG_LEVEL.lower()

    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]

    @property
    def database_config(self) -> Dict[str, Any]:
        """Get database configuration for SQLAlchemy engine."""
        return {
            "echo": self.DATABASE_ECHO,
            "pool_pre_ping": True,
            "pool_recycle": self.DATABASE_POOL_RECYCLE,
            "pool_size": self.DATABASE_POOL_SIZE,
            "max_overflow": self.DATABASE_MAX_OVERFLOW,
        }

    @property
    def redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration."""
        return {
            "url": self.REDIS_URL,
        }

    @property
    def logging_config(self) -> Dict[str, Any]:
        """Get logging configuration dictionary."""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": self.LOG_FORMAT,
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": self.LOG_LEVEL,
                    "formatter": "default",
                    "stream": "ext://sys.stdout",
                },
                "file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": self.LOG_LEVEL,
                    "formatter": "detailed",
                    "filename": self.LOG_FILE,
                    "maxBytes": self.LOG_MAX_SIZE,
                    "backupCount": self.LOG_BACKUP_COUNT,
                },
            },
            "loggers": {
                "": {
                    "level": self.LOG_LEVEL,
                    "handlers": ["console", "file"],
                },
            },
        }

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True)


# Create settings instance
settings = Settings()


@lru_cache()
def get_settings() -> Settings:
    """Get application settings with caching."""
    return settings


def configure_logging() -> None:
    """Configure application logging using settings."""
    settings = get_settings()
    # Ensure log directory exists
    log_dir = Path(settings.LOG_FILE).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # Configure logging
    logging.config.dictConfig(settings.logging_config)

    # Log startup information
    logger = logging.getLogger(__name__)
    logger.info("Application startup: %s", settings.PROJECT_NAME)
    logger.info("Environment: %s", settings.ENVIRONMENT)
    logger.info("Debug mode: %s", settings.DEBUG)
    logger.info("Log level: %s", settings.log_level)
    logger.info("Listening on %s:%d", settings.HOST, settings.PORT)
