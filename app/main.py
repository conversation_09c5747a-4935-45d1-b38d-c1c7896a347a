"""
GeNieGO SSO Server

FastAPI-based OAuth2 authorization server and identity provider
for Genieland ecosystem multi-tenant architecture.
"""

import json
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncIterator, Dict, Union

from fastapi import APIRouter, FastAPI, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse, Response
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
# Removed slowapi - using custom rate limiting middleware instead

# Use proper config imports following FastAPI best practices
from app.config import configure_logging, get_settings
from app.core.database import (
    create_tables,
    get_session_factory,
    test_database_connection,
)
from app.core.exceptions import APIError
# Removed slowapi limiter - using custom rate limiting middleware instead

# Import schemas for response models only
from app.schemas import ErrorResponse, ValidationErrorResponse

# Get settings instance
settings = get_settings()

# Initialize logging using FastAPI best practices
configure_logging()

# Get logger
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    """Modern lifespan event handler for FastAPI."""
    # Startup
    logger.info("Application starting up...")

    # Initialize database with retry logic
    import asyncio

    max_retries = 3
    retry_delay = 2.0

    for attempt in range(1, max_retries + 1):
        try:
            logger.info(f"Database initialization attempt {attempt}/{max_retries}")

            if await test_database_connection():
                await create_tables()
                logger.info("Database initialized successfully")

                # Initialize GeNieGO SSO default data
                from app.config import initialize_default_data

                session_factory = get_session_factory()
                async with session_factory() as db:
                    if await initialize_default_data(db):
                        logger.info(
                            "✅ GeNieGO SSO default data initialization completed successfully"
                        )
                        logger.info("📧 Default admin: GenieAdmin / GenieAdmin123!")
                        logger.info(
                            "👨‍💻 Default developer: GenieDeveloper / GenieDev123!"
                        )
                        logger.info("👤 Default user: GenieUser / GenieUser123!")
                        logger.info(
                            "🔐 GeNieGO SSO Server ready for application integration"
                        )

                        # Initialize consent cleanup service
                        try:
                            from app.services.consent_cleanup_service import initialize_consent_cleanup
                            await initialize_consent_cleanup()
                            logger.info("🧹 Consent cleanup service initialized")
                        except Exception as cleanup_error:
                            logger.warning(f"Consent cleanup initialization failed: {cleanup_error}")

                        break
                    else:
                        logger.warning(
                            f"GeNieGO SSO default data initialization failed on attempt {attempt}"
                        )
                        if attempt < max_retries:
                            logger.info(f"Retrying in {retry_delay} seconds...")
                            await asyncio.sleep(retry_delay)
                        else:
                            logger.error(
                                "❌ GeNieGO SSO default data initialization failed after all retries"
                            )
            else:
                logger.warning(f"Database connection failed on attempt {attempt}")
                if attempt < max_retries:
                    logger.info(
                        f"Retrying database connection in {retry_delay} seconds..."
                    )
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error("❌ Database connection failed after all retries")

        except Exception as e:
            logger.error(f"Database initialization failed on attempt {attempt}: {e}")
            if attempt < max_retries:
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logger.error("❌ Database initialization failed after all retries")
                logger.warning(
                    "Continuing without database - some features may not work"
                )

    logger.info("GeNieGO SSO Server startup complete")
    logger.info(f"SSO API endpoints available at {settings.api_versioned_prefix}/auth")
    logger.info("OAuth2 authorization endpoints available at /oauth2")
    logger.info(f"Health check available at /health")

    yield

    # Shutdown
    logger.info("Application shutting down...")


# Custom JSON encoder for datetime objects and other non-serializable types
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Exception):
            return str(obj)
        elif hasattr(obj, "__dict__"):
            try:
                return obj.__dict__
            except (TypeError, AttributeError):
                return str(obj)
        else:
            # For any other non-serializable object, convert to string
            return str(obj)


# Create FastAPI instance with default docs enabled but we'll override CSP
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="GeNieGO SSO Server - Central OAuth2 authorization server and identity provider for Genieland ecosystem",
    docs_url="/docs" if settings.ENABLE_DOCS else None,
    redoc_url="/redoc" if settings.ENABLE_DOCS else None,
    lifespan=lifespan,
    openapi_url="/openapi.json" if settings.ENABLE_DOCS else None,
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static", html=True), name="static")

# Log application startup
logger.info(f"Starting {settings.PROJECT_NAME} v{settings.VERSION}")
logger.info(f"Environment: {'development' if settings.DEBUG else 'production'}")
logger.info(f"Log level: {settings.LOG_LEVEL}")
logger.info(f"Redis URL: {settings.REDIS_URL}")

# Using custom rate limiting middleware instead of slowapi


# Exception handlers using proper patterns
@app.exception_handler(APIError)
async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """Handle custom API errors using the exceptions module."""
    error_response = ErrorResponse(
        error=exc.__class__.__name__,
        detail=exc.message,
        timestamp=datetime.now(),
        status_code=exc.status_code,
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=json.loads(
            json.dumps(error_response.model_dump(), cls=DateTimeEncoder)
        ),
    )


# Rate limit exception handler
# Rate limiting handled by custom middleware - no need for slowapi exception handler


# Global exception handler for HTTP exceptions
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with consistent error format."""
    error_response = ErrorResponse(
        error=str(exc.detail),
        detail=f"HTTP {exc.status_code} error",
        timestamp=datetime.now(),
        status_code=exc.status_code,
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=json.loads(
            json.dumps(error_response.model_dump(), cls=DateTimeEncoder)
        ),
    )


# Global exception handler for validation errors
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """Handle validation errors with consistent error format."""
    # Create more detailed error message
    error_details = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"])
        error_details.append(f"{field}: {error['msg']}")

    detailed_message = "; ".join(error_details)

    error_response = ValidationErrorResponse(
        error="Validation Error",
        detail=error_details,  # Return list instead of string
        timestamp=datetime.now(),
        status_code=422,
        errors=exc.errors(),
    )
    return JSONResponse(
        status_code=422,
        content=json.loads(
            json.dumps(error_response.model_dump(), cls=DateTimeEncoder)
        ),
    )


# Global exception handler for unexpected errors
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for unexpected errors."""
    if settings.DEBUG:
        import traceback

        logger.error(f"Unhandled exception: {exc}")
        logger.error(traceback.format_exc())

    error_response = ErrorResponse(
        error="Internal server error",
        detail="An unexpected error occurred" if not settings.DEBUG else str(exc),
        timestamp=datetime.now(),
        status_code=500,
    )
    return JSONResponse(
        status_code=500,
        content=json.loads(
            json.dumps(error_response.model_dump(), cls=DateTimeEncoder)
        ),
    )


# Add security middleware
try:
    from app.core.middleware import (
        RequestIDMiddleware,
        RequestLoggingMiddleware,
        SecurityHeadersMiddleware,
    )

    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RequestLoggingMiddleware)
    logger.info("Security middleware loaded successfully")
except ImportError as e:
    logger.warning(f"Could not load security middleware: {e}")
    logger.warning(
        "Running without security headers - this is not recommended for production"
    )

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=(
        settings.CORS_ORIGINS.split(",")
        if "," in settings.CORS_ORIGINS
        else [settings.CORS_ORIGINS]
    ),
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

# Add rate limiting middleware
from app.middleware.rate_limiting import RateLimitingMiddleware
app.add_middleware(RateLimitingMiddleware)

# Add audit logging middleware
from app.middleware.audit_logging import AuditLoggingMiddleware
app.add_middleware(AuditLoggingMiddleware)

# Include API routes with professional separation
# Health endpoint at /api/health (not versioned)
from app.api.v1.endpoints import health

health_router = APIRouter()
health_router.include_router(health.router, tags=["Health"])
app.include_router(health_router, prefix="/api")

# OAuth2 endpoints at root level: /oauth2/token, /oauth2/userinfo, etc.
# Following OAuth2 best practices by separating from API versioning
from app.api.v1.endpoints.oauth2 import router as oauth2_router

app.include_router(oauth2_router, prefix="/oauth2")

# API v1 endpoints: /api/v1/users, /api/v1/organisations, /api/v1/licenses
from app.api.v1.api import api_router as api_v1_router

app.include_router(api_v1_router, prefix="/api/v1")

# Debug endpoints removed for clean GeNieGO SSO Server
logger.info("GeNieGO SSO Server - Clean implementation without debug endpoints")

# =============================================================================
# ENDPOINTS
# =============================================================================


# Add direct health endpoint for compatibility
@app.get("/health", response_model=None)
async def direct_health_check() -> Union[Dict[str, Any], JSONResponse]:
    """Direct health check endpoint for backward compatibility."""
    # Import and call the health check function from the proper location
    from app.api.v1.endpoints.health import health_check
    from app.config import get_settings

    settings = get_settings()
    result = await health_check(settings)

    # Handle different return types
    if isinstance(result, JSONResponse):
        return result
    else:
        # Convert HealthResponse to dict for backward compatibility
        return result.model_dump()


# Global OPTIONS handler for preflight requests
@app.options("/{path:path}")
async def options_handler(request: Request) -> Response:
    """Handle preflight OPTIONS requests."""
    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        },
    )


@app.get("/favicon.ico")
async def favicon() -> Response:
    """Serve a simple favicon to prevent 404 errors."""
    # Return a simple transparent 1x1 ICO file
    ico_data = (
        b"\x00\x00\x01\x00\x01\x00\x01\x01\x00\x00\x00\x00\x00\x00(\x00"
        b"\x00\x00\x16\x00\x00\x00(\x00\x00\x00\x01\x00\x00\x00\x02\x00"
        b"\x00\x00\x01\x00\x01\x00\x00\x00\x00\x00\x04\x00\x00\x00\x00"
        b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
        b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x80\x00"
        b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
        b"\x00\x00\x00\x00\x00\x00\x00\x00"
    )

    return Response(
        content=ico_data,
        media_type="image/x-icon",
        headers={
            "Cache-Control": "public, max-age=86400",  # Cache for 1 day
        },
    )


@app.get("/index", response_class=HTMLResponse)
async def backend_template(request: Request) -> HTMLResponse:
    """Backend template endpoint serves the main application landing page."""
    from fastapi.templating import Jinja2Templates

    templates = Jinja2Templates(directory="app/templates")

    return templates.TemplateResponse(
        request,
        "index.html",
        {
            "project_name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "docs_url": "/docs" if settings.ENABLE_DOCS else "#",
        },
    )


@app.get("/", response_class=HTMLResponse)
async def root(request: Request) -> HTMLResponse:
    """Root endpoint serves the frontend application."""
    # Serve the frontend index.html from static files
    import os

    from fastapi.responses import FileResponse

    frontend_path = "app/static/frontend/index.html"
    if os.path.exists(frontend_path):
        # Serve the frontend directly
        return FileResponse(frontend_path, media_type="text/html")
    else:
        # Fallback to redirect to backend template if frontend not built
        return RedirectResponse(url="/index", status_code=302)


# Catch-all route for frontend routing (SPA support)
@app.get("/{path:path}", response_class=HTMLResponse)
async def catch_all(request: Request, path: str) -> HTMLResponse:
    """Catch-all route to serve frontend for client-side routing."""
    import os

    from fastapi.responses import FileResponse

    # Skip API routes, static files, and backend routes
    if (
        path.startswith("api/")
        or path.startswith("static/")
        or path.startswith("docs")
        or path.startswith("redoc")
        or path.startswith("openapi.json")
        or path.startswith("oauth2/")  # Backend OAuth2 API routes
        or path == "index"
        or path == "health"
    ):
        # Let FastAPI handle these routes normally
        raise HTTPException(status_code=404, detail="Not found")

    # Only serve frontend for known frontend routes
    frontend_routes = [
        "dashboard",
        "user",
        "developer",
        "admin",
        "auth",
        "landing",
        "info",
        "oauth2",
        "profile",
        "settings",
        "applications",
        "analytics",
        "login",
        "register",
        # Error pages
        "404",
        "500",
        "maintenance",
    ]

    # Check if path starts with any frontend route
    is_frontend_route = any(path.startswith(route) for route in frontend_routes)

    if is_frontend_route:
        # For frontend routes, serve the frontend SPA
        frontend_path = "app/static/frontend/index.html"
        if os.path.exists(frontend_path):
            return FileResponse(frontend_path, media_type="text/html")
        else:
            # Fallback to redirect to backend template if frontend not built
            return RedirectResponse(url="/index", status_code=302)
    else:
        # For unknown routes, also serve the frontend SPA so React Router can handle 404
        frontend_path = "app/static/frontend/index.html"
        if os.path.exists(frontend_path):
            return FileResponse(frontend_path, media_type="text/html")
        else:
            # Only return backend 404 if frontend is not available
            raise HTTPException(status_code=404, detail="Not found")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
