"""
Organization Management Service for GeNieGO SSO Server.

This service provides comprehensive organization management functionality
including organization CRUD, membership management, and invitation system.
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models import (
    ApplicationOrganization,
    Organization,
    OrganizationInvitation,
    OrganizationMembership,
    RegisteredApplication,
    User,
)

logger = logging.getLogger(__name__)


class OrganizationManager:
    """Comprehensive organization management service."""

    def __init__(self, db: AsyncSession):
        self.db = db

    # Organization CRUD Operations

    async def create_organization(
        self,
        creator: User,
        name: str,
        slug: str,
        description: Optional[str] = None,
        website: Optional[str] = None,
        is_public: bool = False,
        max_members: int = 50,
        settings: Optional[Dict] = None,
    ) -> Organization:
        """Create a new organization with the creator as owner."""
        try:
            # Check if slug is already taken by an active organization
            existing = await self.get_organization_by_slug(slug)
            if existing:
                raise ValueError(f"Organization slug '{slug}' is already taken")

            # Create organization
            organization = Organization(
                name=name,
                slug=slug,
                description=description,
                website=website,
                is_public=is_public,
                max_members=max_members,
                settings=settings or {},
            )
            self.db.add(organization)
            await self.db.flush()  # Get organization ID

            # Create owner membership for creator
            membership = OrganizationMembership(
                organization_id=organization.id,
                user_id=creator.id,
                role="owner",
                joined_at=datetime.utcnow(),
            )
            self.db.add(membership)
            await self.db.commit()

            logger.info(f"Organization {name} ({slug}) created by user {creator.id}")
            return organization

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create organization {name}: {e}")

            # Check if it's a unique constraint violation on slug
            error_str = str(e).lower()
            if "duplicate entry" in error_str and "slug" in error_str:
                raise ValueError(f"Organization slug '{slug}' is already taken")

            raise

    async def get_organization_by_id(self, organization_id: str) -> Optional[Organization]:
        """Get organization by ID with relationships."""
        try:
            result = await self.db.execute(
                select(Organization)
                .options(
                    selectinload(Organization.memberships),
                    selectinload(Organization.invitations),
                    selectinload(Organization.applications),
                )
                .where(
                    and_(
                        Organization.id == organization_id,
                        Organization.is_active == True,
                    )
                )
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get organization {organization_id}: {e}")
            return None

    async def get_organization_by_slug(self, slug: str) -> Optional[Organization]:
        """Get organization by slug."""
        try:
            result = await self.db.execute(
                select(Organization).where(
                    and_(
                        Organization.slug == slug,
                        Organization.is_active == True,
                    )
                )
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get organization by slug {slug}: {e}")
            return None

    async def update_organization(
        self,
        organization_id: str,
        updater: User,
        **updates,
    ) -> Optional[Organization]:
        """Update organization details."""
        try:
            # Check permissions
            if not await self.user_can_manage_organization(updater.id, organization_id):
                raise PermissionError("User does not have permission to update this organization")

            organization = await self.get_organization_by_id(organization_id)
            if not organization:
                return None

            # Update allowed fields
            allowed_fields = [
                "name", "description", "website", "is_public", 
                "max_members", "billing_email", "settings"
            ]
            
            for field, value in updates.items():
                if field in allowed_fields and hasattr(organization, field):
                    setattr(organization, field, value)

            organization.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Organization {organization_id} updated by user {updater.id}")
            return organization

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update organization {organization_id}: {e}")
            raise

    async def delete_organization(
        self,
        organization_id: str,
        deleter: User,
    ) -> bool:
        """Delete organization (only owners can delete)."""
        try:
            # Check if user is owner
            membership = await self.get_user_membership(deleter.id, organization_id)
            if not membership or membership.role != "owner":
                raise PermissionError("Only organization owners can delete organizations")

            organization = await self.get_organization_by_id(organization_id)
            if not organization:
                return False

            # Soft delete
            organization.is_active = False
            organization.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Organization {organization_id} deleted by user {deleter.id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete organization {organization_id}: {e}")
            raise

    # User Organizations

    async def get_user_organizations(self, user_id: str) -> List[Organization]:
        """Get all organizations where user is a member."""
        try:
            result = await self.db.execute(
                select(Organization)
                .join(OrganizationMembership)
                .options(selectinload(Organization.memberships))
                .where(
                    and_(
                        OrganizationMembership.user_id == user_id,
                        OrganizationMembership.is_active == True,
                        Organization.is_active == True,
                    )
                )
                .order_by(Organization.name)
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get organizations for user {user_id}: {e}")
            return []

    async def get_public_organizations(self, limit: int = 50) -> List[Organization]:
        """Get public organizations that can be discovered."""
        try:
            result = await self.db.execute(
                select(Organization)
                .where(
                    and_(
                        Organization.is_public == True,
                        Organization.is_active == True,
                    )
                )
                .order_by(Organization.name)
                .limit(limit)
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get public organizations: {e}")
            return []

    # Membership Management

    async def get_user_membership(
        self, user_id: str, organization_id: str
    ) -> Optional[OrganizationMembership]:
        """Get user's membership in organization."""
        try:
            result = await self.db.execute(
                select(OrganizationMembership).where(
                    and_(
                        OrganizationMembership.user_id == user_id,
                        OrganizationMembership.organization_id == organization_id,
                        OrganizationMembership.is_active == True,
                    )
                )
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get membership for user {user_id} in org {organization_id}: {e}")
            return None

    async def get_organization_members(
        self, organization_id: str, include_inactive: bool = False
    ) -> List[OrganizationMembership]:
        """Get all members of an organization."""
        try:
            conditions = [OrganizationMembership.organization_id == organization_id]
            if not include_inactive:
                conditions.append(OrganizationMembership.is_active == True)

            result = await self.db.execute(
                select(OrganizationMembership)
                .options(selectinload(OrganizationMembership.user))
                .where(and_(*conditions))
                .order_by(OrganizationMembership.role, OrganizationMembership.joined_at)
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get members for organization {organization_id}: {e}")
            return []

    async def add_member(
        self,
        organization_id: str,
        user_id: str,
        role: str = "member",
        inviter_id: Optional[str] = None,
        permissions: Optional[Dict] = None,
    ) -> Optional[OrganizationMembership]:
        """Add a user to organization."""
        try:
            # Check if organization exists and can add members
            organization = await self.get_organization_by_id(organization_id)
            if not organization:
                raise ValueError("Organization not found")

            if not organization.can_add_member():
                raise ValueError("Organization has reached maximum member limit")

            # Check if user is already an active member
            existing = await self.get_user_membership(user_id, organization_id)
            if existing:
                raise ValueError("User is already a member of this organization")

            # Check if user has an inactive membership that we can reactivate
            result = await self.db.execute(
                select(OrganizationMembership).where(
                    and_(
                        OrganizationMembership.user_id == user_id,
                        OrganizationMembership.organization_id == organization_id,
                        OrganizationMembership.is_active == False,
                    )
                )
            )
            inactive_membership = result.scalar_one_or_none()

            if inactive_membership:
                # Reactivate existing membership
                inactive_membership.is_active = True
                inactive_membership.role = role
                inactive_membership.invited_by = inviter_id
                inactive_membership.invited_at = datetime.utcnow() if inviter_id else None
                inactive_membership.joined_at = datetime.utcnow()
                inactive_membership.permissions = permissions
                inactive_membership.updated_at = datetime.utcnow()
                await self.db.commit()

                logger.info(f"Reactivated membership for user {user_id} in organization {organization_id} as {role}")
                return inactive_membership

            # Create membership
            membership = OrganizationMembership(
                organization_id=organization_id,
                user_id=user_id,
                role=role,
                invited_by=inviter_id,
                invited_at=datetime.utcnow() if inviter_id else None,
                joined_at=datetime.utcnow(),
                permissions=permissions,
            )
            self.db.add(membership)
            await self.db.commit()

            logger.info(f"User {user_id} added to organization {organization_id} as {role}")
            return membership

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to add member {user_id} to organization {organization_id}: {e}")
            raise

    async def update_member_role(
        self,
        organization_id: str,
        user_id: str,
        new_role: str,
        updater_id: str,
    ) -> Optional[OrganizationMembership]:
        """Update member's role in organization."""
        try:
            # Check updater permissions
            updater_membership = await self.get_user_membership(updater_id, organization_id)
            if not updater_membership:
                raise PermissionError("Updater is not a member of this organization")

            # Get target membership
            membership = await self.get_user_membership(user_id, organization_id)
            if not membership:
                raise ValueError("User is not a member of this organization")

            # Check if updater can manage this member
            if not updater_membership.can_manage_member(membership):
                raise PermissionError("Insufficient permissions to update this member's role")

            # Update role
            membership.role = new_role
            membership.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"User {user_id} role updated to {new_role} in organization {organization_id}")
            return membership

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update member role: {e}")
            raise

    async def remove_member(
        self,
        organization_id: str,
        user_id: str,
        remover_id: str,
    ) -> bool:
        """Remove member from organization."""
        try:
            # Check remover permissions
            remover_membership = await self.get_user_membership(remover_id, organization_id)
            if not remover_membership:
                raise PermissionError("Remover is not a member of this organization")

            # Get target membership
            membership = await self.get_user_membership(user_id, organization_id)
            if not membership:
                return False  # User is not a member

            # Check if remover can manage this member (or user is removing themselves)
            if user_id != remover_id and not remover_membership.can_manage_member(membership):
                raise PermissionError("Insufficient permissions to remove this member")

            # Cannot remove the last owner
            if membership.role == "owner":
                owner_count = len([
                    m for m in await self.get_organization_members(organization_id)
                    if m.role == "owner" and m.is_active
                ])
                if owner_count <= 1:
                    raise ValueError("Cannot remove the last owner of the organization")

            # Soft delete membership
            membership.is_active = False
            membership.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"User {user_id} removed from organization {organization_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to remove member {user_id} from organization {organization_id}: {e}")
            raise

    # Permission Helpers

    async def user_can_manage_organization(self, user_id: str, organization_id: str) -> bool:
        """Check if user can manage organization settings."""
        membership = await self.get_user_membership(user_id, organization_id)
        return membership and membership.has_permission("manage_settings")

    async def user_can_manage_members(self, user_id: str, organization_id: str) -> bool:
        """Check if user can manage organization members."""
        membership = await self.get_user_membership(user_id, organization_id)
        return membership and membership.has_permission("manage_members")

    # Invitation Management

    async def create_invitation(
        self,
        organization_id: str,
        email: str,
        role: str,
        inviter_id: str,
        message: Optional[str] = None,
        expires_in_days: int = 7,
    ) -> OrganizationInvitation:
        """Create an invitation to join organization."""
        try:
            # Check inviter permissions
            if not await self.user_can_manage_members(inviter_id, organization_id):
                raise PermissionError("User does not have permission to invite members")

            # Check if organization can add members
            organization = await self.get_organization_by_id(organization_id)
            if not organization or not organization.can_add_member():
                raise ValueError("Organization cannot add more members")

            # Check if user is already a member or has pending invitation
            user = await self._get_user_by_email(email)
            if user:
                existing_membership = await self.get_user_membership(user.id, organization_id)
                if existing_membership:
                    raise ValueError("User is already a member of this organization")

            # Check for existing pending invitation
            existing_invitation = await self._get_pending_invitation(organization_id, email)
            if existing_invitation:
                raise ValueError("User already has a pending invitation to this organization")

            # Generate invitation token
            token = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)

            # Create invitation
            invitation = OrganizationInvitation(
                organization_id=organization_id,
                invited_by=inviter_id,
                user_id=user.id if user else None,
                email=email,
                role=role,
                token=token,
                expires_at=expires_at,
                message=message,
            )
            self.db.add(invitation)
            await self.db.commit()

            # Refresh to get the ID and load relationships
            await self.db.refresh(invitation)

            logger.info(f"Invitation created for {email} to join organization {organization_id}")
            return invitation

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create invitation: {e}")
            raise

    async def get_invitation_by_token(self, token: str) -> Optional[OrganizationInvitation]:
        """Get invitation by token."""
        try:
            result = await self.db.execute(
                select(OrganizationInvitation)
                .options(
                    selectinload(OrganizationInvitation.organization),
                    selectinload(OrganizationInvitation.inviter),
                )
                .where(OrganizationInvitation.token == token)
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get invitation by token: {e}")
            return None

    async def accept_invitation(
        self, token: str, user: User
    ) -> Tuple[bool, Optional[OrganizationMembership]]:
        """Accept organization invitation."""
        try:
            logger.info(f"Attempting to accept invitation with token: {token[:10]}... for user: {user.email}")

            invitation = await self.get_invitation_by_token(token)
            if not invitation:
                logger.warning(f"Invitation not found for token: {token[:10]}...")
                return False, None

            logger.info(f"Found invitation: {invitation.id}, status: {invitation.status}, email: {invitation.email}")

            # Validate invitation
            if not invitation.is_valid():
                logger.warning(f"Invitation is not valid. Status: {invitation.status}, Expired: {invitation.is_expired()}")
                if invitation.is_expired():
                    invitation.expire()
                    await self.db.commit()
                return False, None

            # Check if email matches
            if invitation.email.lower() != user.email.lower():
                logger.warning(f"Email mismatch. Invitation email: {invitation.email}, User email: {user.email}")
                return False, None

            # Check if organization can still add members
            # Get organization separately to avoid lazy loading issues
            organization = await self.get_organization_by_id(invitation.organization_id)
            if not organization or not organization.can_add_member():
                logger.warning(f"Organization {invitation.organization_id} cannot add more members")
                return False, None

            # Create membership
            membership = await self.add_member(
                organization_id=invitation.organization_id,
                user_id=user.id,
                role=invitation.role,
                inviter_id=invitation.invited_by,
            )

            # Mark invitation as accepted
            invitation.accept()
            invitation.user_id = user.id
            await self.db.commit()

            logger.info(f"User {user.id} accepted invitation to organization {invitation.organization_id}")
            return True, membership

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to accept invitation: {e}")
            return False, None

    async def reject_invitation(
        self, token: str, user: User, reason: Optional[str] = None
    ) -> bool:
        """Reject organization invitation."""
        try:
            invitation = await self.get_invitation_by_token(token)
            if not invitation:
                return False

            # Check if email matches
            if invitation.email.lower() != user.email.lower():
                return False

            # Mark invitation as rejected
            invitation.reject(reason)
            await self.db.commit()

            logger.info(f"User {user.id} rejected invitation to organization {invitation.organization_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to reject invitation: {e}")
            return False

    async def get_user_invitations(
        self,
        email: str,
        status_filter: str = "pending",
        limit: int = 50,
        offset: int = 0,
    ) -> List[OrganizationInvitation]:
        """Get latest invitations for a user by email (one per organization)."""
        try:
            # First, get all invitations for this email
            query = (
                select(OrganizationInvitation)
                .options(
                    selectinload(OrganizationInvitation.organization),
                    selectinload(OrganizationInvitation.inviter),
                )
                .where(OrganizationInvitation.email == email.lower())
                .order_by(OrganizationInvitation.created_at.desc())
            )

            result = await self.db.execute(query)
            all_invitations = result.scalars().all()

            # Filter to keep only the latest invitation per organization
            latest_invitations = {}
            for invitation in all_invitations:
                org_id = invitation.organization_id
                if org_id not in latest_invitations:
                    latest_invitations[org_id] = invitation
                elif invitation.created_at > latest_invitations[org_id].created_at:
                    latest_invitations[org_id] = invitation

            # Convert to list and apply status filter
            filtered_invitations = []
            for invitation in latest_invitations.values():
                # For users, exclude rejected/declined invitations
                if status_filter == "pending":
                    if invitation.status == "pending":
                        filtered_invitations.append(invitation)
                elif status_filter == "all":
                    # For users, "all" means all except rejected/declined
                    if invitation.status in ["pending", "accepted", "expired"]:
                        filtered_invitations.append(invitation)
                else:
                    if invitation.status == status_filter:
                        filtered_invitations.append(invitation)

            # Sort by creation date (most recent first)
            filtered_invitations.sort(key=lambda x: x.created_at, reverse=True)

            # Apply pagination
            start_idx = offset
            end_idx = offset + limit
            paginated_invitations = filtered_invitations[start_idx:end_idx]

            return list(paginated_invitations)

        except Exception as e:
            logger.error(f"Failed to get user invitations: {e}")
            return []

    async def cancel_invitation(
        self, invitation_id: str, canceller_id: str
    ) -> bool:
        """Cancel pending invitation."""
        try:
            result = await self.db.execute(
                select(OrganizationInvitation).where(OrganizationInvitation.id == invitation_id)
            )
            invitation = result.scalar_one_or_none()

            if not invitation:
                return False

            # Check permissions
            if not await self.user_can_manage_members(canceller_id, invitation.organization_id):
                raise PermissionError("User does not have permission to cancel invitations")

            # Mark as expired
            invitation.expire()
            await self.db.commit()

            logger.info(f"Invitation {invitation_id} cancelled by user {canceller_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to cancel invitation: {e}")
            raise

    # Helper Methods

    async def _get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        try:
            result = await self.db.execute(
                select(User).where(User.email == email.lower())
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _get_pending_invitation(
        self, organization_id: str, email: str
    ) -> Optional[OrganizationInvitation]:
        """Get pending invitation for email to organization."""
        try:
            result = await self.db.execute(
                select(OrganizationInvitation).where(
                    and_(
                        OrganizationInvitation.organization_id == organization_id,
                        OrganizationInvitation.email == email.lower(),
                        OrganizationInvitation.status == "pending",
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def get_organization_invitations(
        self, organization_id: str, status_filter: str = "pending"
    ) -> List[OrganizationInvitation]:
        """Get organization invitations with optional status filter (latest per email)."""
        try:
            # First, get all invitations for this organization
            result = await self.db.execute(
                select(OrganizationInvitation)
                .options(
                    selectinload(OrganizationInvitation.organization),
                    selectinload(OrganizationInvitation.inviter),
                    selectinload(OrganizationInvitation.user),
                )
                .where(OrganizationInvitation.organization_id == organization_id)
                .order_by(OrganizationInvitation.created_at.desc())
            )
            all_invitations = result.scalars().all()

            # Filter to keep only the latest invitation per email
            latest_invitations = {}
            for invitation in all_invitations:
                email = invitation.email
                if email not in latest_invitations:
                    latest_invitations[email] = invitation
                elif invitation.created_at > latest_invitations[email].created_at:
                    latest_invitations[email] = invitation

            # Apply status filter
            filtered_invitations = []
            for invitation in latest_invitations.values():
                if status_filter == "all" or invitation.status == status_filter:
                    filtered_invitations.append(invitation)

            # Sort by creation date (most recent first)
            filtered_invitations.sort(key=lambda x: x.created_at, reverse=True)

            return filtered_invitations

        except Exception as e:
            logger.error(f"Failed to get organization invitations: {e}")
            return []

    # Application Organization Management

    async def assign_application_to_organization(
        self,
        application_id: str,
        organization_id: str,
        assigner_id: str,
        role: str = "application",
        description: Optional[str] = None,
        settings: Optional[Dict] = None,
    ) -> Optional[ApplicationOrganization]:
        """Assign application to organization."""
        try:
            # Check if assigner has permission
            if not await self.user_can_manage_organization(assigner_id, organization_id):
                raise PermissionError("User does not have permission to manage applications")

            # Check if application is already assigned
            existing = await self._get_application_organization(application_id, organization_id)
            if existing:
                raise ValueError("Application is already assigned to this organization")

            # Create assignment
            assignment = ApplicationOrganization(
                application_id=application_id,
                organization_id=organization_id,
                assigned_by=assigner_id,
                role=role,
                description=description,
                settings=settings or {},
            )
            self.db.add(assignment)
            await self.db.commit()

            logger.info(f"Application {application_id} assigned to organization {organization_id}")
            return assignment

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to assign application to organization: {e}")
            raise

    async def get_organization_applications(
        self, organization_id: str, include_inactive: bool = False
    ) -> List[ApplicationOrganization]:
        """Get all applications assigned to organization."""
        try:
            conditions = [ApplicationOrganization.organization_id == organization_id]
            if not include_inactive:
                conditions.append(ApplicationOrganization.is_active == True)

            result = await self.db.execute(
                select(ApplicationOrganization)
                .options(selectinload(ApplicationOrganization.application))
                .where(and_(*conditions))
                .order_by(ApplicationOrganization.created_at.desc())
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get organization applications: {e}")
            return []

    async def get_application_organizations(
        self, application_id: str
    ) -> List[ApplicationOrganization]:
        """Get all organizations where application is assigned."""
        try:
            result = await self.db.execute(
                select(ApplicationOrganization)
                .options(selectinload(ApplicationOrganization.organization))
                .where(
                    and_(
                        ApplicationOrganization.application_id == application_id,
                        ApplicationOrganization.is_active == True,
                    )
                )
                .order_by(ApplicationOrganization.created_at.desc())
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get application organizations: {e}")
            return []

    async def update_application_organization(
        self,
        assignment_id: str,
        updater_id: str,
        **updates,
    ) -> Optional[ApplicationOrganization]:
        """Update application organization assignment."""
        try:
            # Get assignment
            result = await self.db.execute(
                select(ApplicationOrganization).where(ApplicationOrganization.id == assignment_id)
            )
            assignment = result.scalar_one_or_none()

            if not assignment:
                return None

            # Check permissions
            if not await self.user_can_manage_organization(updater_id, assignment.organization_id):
                raise PermissionError("User does not have permission to manage applications")

            # Update allowed fields
            allowed_fields = ["role", "description", "settings", "is_active"]

            for field, value in updates.items():
                if field in allowed_fields and hasattr(assignment, field):
                    setattr(assignment, field, value)

            assignment.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Application organization assignment {assignment_id} updated")
            return assignment

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update application organization: {e}")
            raise

    async def remove_application_from_organization(
        self,
        assignment_id: str,
        remover_id: str,
    ) -> bool:
        """Remove application from organization."""
        try:
            # Get assignment
            result = await self.db.execute(
                select(ApplicationOrganization).where(ApplicationOrganization.id == assignment_id)
            )
            assignment = result.scalar_one_or_none()

            if not assignment:
                return False

            # Check permissions
            if not await self.user_can_manage_organization(remover_id, assignment.organization_id):
                raise PermissionError("User does not have permission to manage applications")

            # Soft delete
            assignment.is_active = False
            assignment.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Application {assignment.application_id} removed from organization {assignment.organization_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to remove application from organization: {e}")
            raise

    async def _get_application_organization(
        self, application_id: str, organization_id: str
    ) -> Optional[ApplicationOrganization]:
        """Get application organization assignment."""
        try:
            result = await self.db.execute(
                select(ApplicationOrganization).where(
                    and_(
                        ApplicationOrganization.application_id == application_id,
                        ApplicationOrganization.organization_id == organization_id,
                        ApplicationOrganization.is_active == True,
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception:
            return None
