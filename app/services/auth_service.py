"""
GeNieGO SSO Server Authentication Service.

This service handles user authentication, session management, and OAuth2 authorization code flows.
"""

import base64
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import HTTPException
from sqlalchemy import delete, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.core.security import get_password_hash, verify_password
from app.models.user import AuthorizationCode, RegisteredApplication, User, UserSession
from app.schemas.user import UserProfileUpdateRequest, UserUpdate


class AuthService:
    """Authentication service for GeNieGO SSO Server."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.settings = get_settings()

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return verify_password(plain_password, hashed_password)

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        return get_password_hash(password)

    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        try:
            # Find user by email
            result = await self.db.execute(
                select(User).where(User.email == email, User.is_active == True)
            )
            user = result.scalar_one_or_none()

            if not user:
                return None

            # Verify password
            if not user.verify_password(password):
                return None

            # Update last login
            user.update_last_login()
            await self.db.commit()

            return user

        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Authentication error: {str(e)}"
            )

    async def register_user(
        self,
        email: str,
        username: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
    ) -> User:
        """Register a new user."""
        try:
            # Sanitize input data to prevent XSS
            from app.utils.validation import sanitize_string

            sanitized_first_name = sanitize_string(first_name) if first_name else None
            sanitized_last_name = sanitize_string(last_name) if last_name else None

            # Check if user already exists
            existing_user = await self.db.execute(
                select(User).where((User.email == email) | (User.username == username))
            )

            if existing_user.scalar_one_or_none():
                raise HTTPException(
                    status_code=400,
                    detail="User with this email or username already exists",
                )

            # Create new user
            user = User(
                email=email,
                username=username,
                first_name=sanitized_first_name,
                last_name=sanitized_last_name,
                is_active=True,
                is_verified=False,  # Require email verification
            )

            user.set_password(password)

            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Registration error: {str(e)}")

    async def create_user_session(
        self,
        user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> UserSession:
        """Create a new user session."""
        try:
            # Generate secure session token
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(
                hours=self.settings.SESSION_EXPIRE_HOURS
            )

            session = UserSession(
                user_id=user.id,
                session_token=session_token,
                expires_at=expires_at,
                ip_address=ip_address,
                user_agent=user_agent,
            )

            self.db.add(session)
            await self.db.commit()
            await self.db.refresh(session)

            return session

        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Session creation error: {str(e)}"
            )

    async def validate_session(self, session_token: str) -> Optional[UserSession]:
        """Validate and return session if valid."""
        try:
            from sqlalchemy.orm import selectinload

            result = await self.db.execute(
                select(UserSession)
                .options(selectinload(UserSession.user))
                .where(UserSession.session_token == session_token)
                .join(User)
                .where(User.is_active == True)
            )
            session = result.scalar_one_or_none()

            if not session or session.is_expired:
                return None

            return session

        except Exception:
            return None

    async def logout_user(self, session_token: str) -> bool:
        """Logout user by invalidating session."""
        try:
            await self.db.execute(
                delete(UserSession).where(UserSession.session_token == session_token)
            )
            await self.db.commit()
            return True

        except Exception:
            await self.db.rollback()
            return False

    async def create_authorization_code(
        self,
        user: User,
        client_id: str,
        redirect_uri: str,
        scopes: List[str],
        code_challenge: Optional[str] = None,
        code_challenge_method: Optional[str] = None,
    ) -> AuthorizationCode:
        """Create OAuth2 authorization code."""
        try:
            # Validate application
            application = await self.validate_application(client_id)
            if not application:
                raise HTTPException(status_code=400, detail="Invalid client_id")

            # Validate redirect URI
            if not application.is_redirect_uri_allowed(redirect_uri):
                raise HTTPException(status_code=400, detail="Invalid redirect_uri")

            # Validate scopes
            for scope in scopes:
                if not application.is_scope_allowed(scope):
                    raise HTTPException(
                        status_code=400, detail=f"Invalid scope: {scope}"
                    )

            # Generate authorization code
            code = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(
                minutes=self.settings.AUTHORIZATION_CODE_EXPIRE_MINUTES
            )

            auth_code = AuthorizationCode(
                code=code,
                client_id=client_id,
                user_id=user.id,
                redirect_uri=redirect_uri,
                scopes=scopes,
                code_challenge=code_challenge,
                code_challenge_method=code_challenge_method,
                expires_at=expires_at,
            )

            self.db.add(auth_code)
            await self.db.commit()
            await self.db.refresh(auth_code)

            return auth_code

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Authorization code creation error: {str(e)}"
            )

    async def validate_and_consume_authorization_code(
        self, code: str, client_id: str, code_verifier: Optional[str] = None
    ) -> Optional[AuthorizationCode]:
        """Validate and consume authorization code."""
        try:
            from sqlalchemy.orm import selectinload

            result = await self.db.execute(
                select(AuthorizationCode)
                .options(selectinload(AuthorizationCode.user))
                .where(AuthorizationCode.code == code)
                .join(User)
                .where(User.is_active == True)
            )
            auth_code = result.scalar_one_or_none()

            if not auth_code:
                return None

            # Check if expired
            if auth_code.is_expired:
                await self.db.delete(auth_code)
                await self.db.commit()
                return None

            # Check if already used
            if auth_code.is_used:
                return None

            # Validate client_id
            if auth_code.client_id != client_id:
                return None

            # Validate PKCE if present
            if auth_code.code_challenge and code_verifier:
                if not self.verify_pkce(code_verifier, auth_code.code_challenge):
                    return None

            # Mark as used
            auth_code.mark_as_used()
            await self.db.commit()

            return auth_code

        except Exception as e:
            await self.db.rollback()
            return None

    async def validate_application(
        self, client_id: str
    ) -> Optional[RegisteredApplication]:
        """Validate application by client_id."""
        try:
            result = await self.db.execute(
                select(RegisteredApplication).where(
                    RegisteredApplication.client_id == client_id,
                    RegisteredApplication.is_active == True,
                )
            )
            return result.scalar_one_or_none()

        except Exception:
            return None

    def verify_pkce(self, code_verifier: str, code_challenge: str) -> bool:
        """Verify PKCE code challenge."""
        try:
            verifier_hash = hashlib.sha256(code_verifier.encode()).digest()
            expected_challenge = (
                base64.urlsafe_b64encode(verifier_hash).decode().rstrip("=")
            )
            return expected_challenge == code_challenge
        except Exception:
            return False

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            result = await self.db.execute(
                select(User).where(User.id == user_id, User.is_active == True)
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def cleanup_expired_sessions(self) -> int:
        """Cleanup expired sessions (background task)."""
        try:
            result = await self.db.execute(
                delete(UserSession).where(UserSession.expires_at < datetime.utcnow())
            )
            await self.db.commit()
            return result.rowcount
        except Exception:
            await self.db.rollback()
            return 0

    async def cleanup_expired_authorization_codes(self) -> int:
        """Cleanup expired authorization codes (background task)."""
        try:
            result = await self.db.execute(
                delete(AuthorizationCode).where(
                    AuthorizationCode.expires_at < datetime.utcnow()
                )
            )
            await self.db.commit()
            return result.rowcount
        except Exception:
            await self.db.rollback()
            return 0

    async def list_users(
        self,
        page: int = 1,
        per_page: int = 10,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None,
        search: Optional[str] = None,
    ) -> List[User]:
        """List users with pagination and filters."""
        try:
            query = select(User)

            # Apply filters
            if is_active is not None:
                query = query.where(User.is_active == is_active)

            if is_verified is not None:
                query = query.where(User.is_verified == is_verified)

            if search:
                search_filter = or_(
                    User.username.ilike(f"%{search}%"),
                    User.email.ilike(f"%{search}%"),
                    User.first_name.ilike(f"%{search}%"),
                    User.last_name.ilike(f"%{search}%"),
                )
                query = query.where(search_filter)

            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)

            result = await self.db.execute(query)
            return list(result.scalars().all())

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Error listing users: {str(e)}"
            )

    async def get_users_count(
        self,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None,
        search: Optional[str] = None,
    ) -> int:
        """Get total count of users with filters."""
        try:
            from sqlalchemy import func

            query = select(func.count(User.id))

            # Apply same filters as list_users
            if is_active is not None:
                query = query.where(User.is_active == is_active)

            if is_verified is not None:
                query = query.where(User.is_verified == is_verified)

            if search:
                search_filter = or_(
                    User.username.ilike(f"%{search}%"),
                    User.email.ilike(f"%{search}%"),
                    User.first_name.ilike(f"%{search}%"),
                    User.last_name.ilike(f"%{search}%"),
                )
                query = query.where(search_filter)

            result = await self.db.execute(query)
            return result.scalar() or 0

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Error counting users: {str(e)}"
            )

    async def update_user(self, user_id: str, user_data: UserUpdate) -> User:
        """Update user information."""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Update fields
            if user_data.first_name is not None:
                user.first_name = user_data.first_name
            if user_data.last_name is not None:
                user.last_name = user_data.last_name

            user.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Error updating user: {str(e)}"
            )

    async def update_user_profile(
        self, user_id: str, profile_data: UserProfileUpdateRequest
    ) -> User:
        """Update user profile information."""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Update profile fields
            if profile_data.first_name is not None:
                user.first_name = profile_data.first_name
            if profile_data.last_name is not None:
                user.last_name = profile_data.last_name

            user.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Error updating user profile: {str(e)}"
            )

    async def activate_user(self, user_id: str) -> User:
        """Activate a user account."""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            user.is_active = True
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Error activating user: {str(e)}"
            )

    async def deactivate_user(self, user_id: str) -> User:
        """Deactivate a user account."""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            user.is_active = False
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Error deactivating user: {str(e)}"
            )

    async def get_or_create_google_user(
        self,
        google_id: str,
        email: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        picture: Optional[str] = None,
    ) -> User:
        """Get existing user or create new user from Google OAuth."""
        try:
            # First try to find user by email
            result = await self.db.execute(select(User).where(User.email == email))
            user = result.scalar_one_or_none()

            if user:
                # Update Google ID if not set
                if not user.google_id:
                    user.google_id = google_id
                    user.updated_at = datetime.utcnow()
                    await self.db.commit()
                    await self.db.refresh(user)
                return user

            # Create new user from Google OAuth
            # Google OAuth users don't need passwords

            # Generate username from email if not provided
            username = email.split("@")[0]

            # Ensure username is unique
            counter = 1
            original_username = username
            while True:
                result = await self.db.execute(
                    select(User).where(User.username == username)
                )
                existing_user = result.scalar_one_or_none()
                if not existing_user:
                    break
                username = f"{original_username}{counter}"
                counter += 1

            user = User(
                email=email,
                username=username,
                password_hash=None,  # Google OAuth users don't have passwords
                first_name=first_name or "",
                last_name=last_name or "",
                google_id=google_id,
                is_active=True,
                is_verified=True,  # Google users are pre-verified
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )

            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)

            return user

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(
                status_code=500, detail=f"Error creating Google user: {str(e)}"
            )
