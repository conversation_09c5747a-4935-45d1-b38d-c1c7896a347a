"""
User Analytics Service for GeNieGO SSO Server.

This service provides user-centric analytics and application management
capabilities for end users to manage their connected applications.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    RegisteredApplication,
    User,
    UserAccessLog,
    UserApplicationConnection,
)
from app.services.connections.activity_logger import ActivityLoggerService

logger = logging.getLogger(__name__)


class UserAnalyticsService:
    """Service for end user application management and analytics."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_user_applications(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get user's connected applications/applications."""
        try:
            query = (
                select(
                    UserApplicationConnection,
                    RegisteredApplication.application_name,
                    RegisteredApplication.description,
                    RegisteredApplication.client_id,
                    User.email.label("developer_email"),
                    User.first_name.label("developer_first_name"),
                    User.last_name.label("developer_last_name"),
                )
                .join(
                    RegisteredApplication,
                    UserApplicationConnection.application_id
                    == RegisteredApplication.id,
                )
                .join(User, RegisteredApplication.developer_id == User.id)
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.is_active == True)
                .order_by(desc(UserApplicationConnection.last_accessed_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            applications = []

            for (
                conn,
                application_name,
                description,
                client_id,
                dev_email,
                dev_first,
                dev_last,
            ) in result.fetchall():
                applications.append(
                    {
                        "id": conn.application_id,
                        "name": application_name,
                        "description": description,
                        "client_id": client_id,
                        "developer": {
                            "email": dev_email,
                            "name": f"{dev_first or ''} {dev_last or ''}".strip()
                            or dev_email,
                        },
                        "connection": {
                            "granted_scopes": conn.granted_scopes,
                            "first_connected_at": conn.first_connected_at,
                            "last_accessed_at": conn.last_accessed_at,
                            "access_count": conn.access_count,
                            "is_active": conn.is_active,
                        },
                    }
                )

            return applications

        except Exception as e:
            logger.error(f"Error getting user applications: {e}")
            raise

    async def get_application_details(
        self, user_id: str, application_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get specific application connection details."""
        try:
            query = (
                select(
                    UserApplicationConnection,
                    RegisteredApplication,
                    User.email.label("developer_email"),
                    User.first_name.label("developer_first_name"),
                    User.last_name.label("developer_last_name"),
                )
                .join(
                    RegisteredApplication,
                    UserApplicationConnection.application_id
                    == RegisteredApplication.id,
                )
                .join(User, RegisteredApplication.developer_id == User.id)
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.application_id == application_id)
                .where(UserApplicationConnection.is_active == True)
            )

            result = await self.db.execute(query)
            row = result.first()

            if not row:
                return None

            conn, application, dev_email, dev_first, dev_last = row

            # Get recent activity for this application
            recent_activity = await self.db.execute(
                select(UserAccessLog)
                .where(UserAccessLog.user_id == user_id)
                .where(UserAccessLog.application_id == application_id)
                .order_by(desc(UserAccessLog.timestamp))
                .limit(10)
            )

            activity_logs = []
            for log in recent_activity.scalars().all():
                activity_logs.append(
                    {
                        "action": log.action,
                        "timestamp": log.timestamp,
                        "success": log.success,
                        "ip_address": log.ip_address,
                        "error_message": log.error_message,
                    }
                )

            return {
                "id": application.id,
                "name": application.application_name,
                "description": application.description,
                "client_id": application.client_id,
                "developer": {
                    "email": dev_email,
                    "name": f"{dev_first or ''} {dev_last or ''}".strip() or dev_email,
                },
                "connection": {
                    "granted_scopes": conn.granted_scopes,
                    "first_connected_at": conn.first_connected_at,
                    "last_accessed_at": conn.last_accessed_at,
                    "access_count": conn.access_count,
                    "is_active": conn.is_active,
                },
                "recent_activity": activity_logs,
                "permissions": {
                    "can_revoke": True,
                    "scopes_granted": conn.granted_scopes,
                },
            }

        except Exception as e:
            logger.error(f"Error getting application details: {e}")
            raise

    async def revoke_application_access(
        self, user_id: str, application_id: str
    ) -> bool:
        """Revoke access to a specific application."""
        try:
            result = await self.db.execute(
                select(UserApplicationConnection)
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.application_id == application_id)
                .where(UserApplicationConnection.is_active == True)
            )
            connection = result.scalar_one_or_none()

            if not connection:
                return False

            # Deactivate the connection
            connection.is_active = False
            connection.updated_at = datetime.utcnow()

            # Log the revocation using the activity logger
            activity_logger = ActivityLoggerService(self.db)
            await activity_logger.log_connection_revocation(
                user_id=user_id,
                application_id=application_id,
                client_id=connection.client_id,
                success=True,
            )

            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error revoking application access: {e}")
            raise

    async def get_user_activity(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get user's own access logs and activity."""
        try:
            query = (
                select(
                    UserAccessLog,
                    RegisteredApplication.application_name,
                )
                .join(
                    RegisteredApplication,
                    UserAccessLog.application_id == RegisteredApplication.id,
                )
                .where(UserAccessLog.user_id == user_id)
                .order_by(desc(UserAccessLog.timestamp))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            activity = []

            for log, application_name in result.fetchall():
                activity.append(
                    {
                        "id": log.id,
                        "action": log.action,
                        "timestamp": log.timestamp,
                        "success": log.success,
                        "ip_address": log.ip_address,
                        "user_agent": log.user_agent,
                        "error_message": log.error_message,
                        "application": {
                            "id": log.application_id,
                            "name": application_name,
                            "client_id": log.client_id,
                        },
                    }
                )

            return activity

        except Exception as e:
            logger.error(f"Error getting user activity: {e}")
            raise

    async def get_user_permissions(self, user_id: str) -> Dict[str, Any]:
        """Get user's granted permissions across all applications."""
        try:
            query = (
                select(
                    UserApplicationConnection.granted_scopes,
                    RegisteredApplication.application_name,
                    RegisteredApplication.id,
                    RegisteredApplication.client_id,
                )
                .join(
                    RegisteredApplication,
                    UserApplicationConnection.application_id
                    == RegisteredApplication.id,
                )
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.is_active == True)
            )

            result = await self.db.execute(query)
            permissions = []
            all_scopes = set()

            for (
                granted_scopes,
                application_name,
                application_id,
                client_id,
            ) in result.fetchall():
                app_scopes = granted_scopes or []
                all_scopes.update(app_scopes)

                permissions.append(
                    {
                        "application": {
                            "id": application_id,
                            "name": application_name,
                            "client_id": client_id,
                        },
                        "granted_scopes": app_scopes,
                    }
                )

            return {
                "applications": permissions,
                "total_applications": len(permissions),
                "unique_scopes_granted": list(all_scopes),
                "summary": {
                    "total_scopes": len(all_scopes),
                    "most_common_scopes": self._get_most_common_scopes(permissions),
                },
            }

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            raise

    def _get_most_common_scopes(
        self, permissions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Helper method to get most common scopes across applications."""
        scope_counts = {}

        for perm in permissions:
            for scope in perm["granted_scopes"]:
                scope_counts[scope] = scope_counts.get(scope, 0) + 1

        # Sort by count and return top 10
        sorted_scopes = sorted(scope_counts.items(), key=lambda x: x[1], reverse=True)[
            :10
        ]

        return [
            {"scope": scope, "application_count": count}
            for scope, count in sorted_scopes
        ]
