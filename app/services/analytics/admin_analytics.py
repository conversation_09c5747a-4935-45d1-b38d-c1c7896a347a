"""
Admin Analytics Service for GeNieGO SSO Server.

This service provides comprehensive system analytics and user management
capabilities for administrators.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    AuthorizationCode,
    RegisteredApplication,
    User,
    UserAccessLog,
    UserApplicationConnection,
)

logger = logging.getLogger(__name__)


class AdminAnalyticsService:
    """Service for admin system analytics and user management."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_developer_users(
        self, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get all users with developer role."""
        try:
            query = (
                select(
                    User.id,
                    User.email,
                    User.username,
                    User.first_name,
                    User.last_name,
                    User.created_at,
                    User.last_login,
                    User.is_active,
                    func.count(RegisteredApplication.id).label("application_count"),
                )
                .outerjoin(
                    RegisteredApplication, User.id == RegisteredApplication.developer_id
                )
                .where(User.role == "developer")
                .group_by(User.id)
                .order_by(desc(User.created_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            developers = []

            for row in result.fetchall():
                developers.append(
                    {
                        "id": row.id,
                        "email": row.email,
                        "username": row.username,
                        "first_name": row.first_name,
                        "last_name": row.last_name,
                        "created_at": row.created_at,
                        "last_login": row.last_login,
                        "is_active": row.is_active,
                        "application_count": row.application_count,
                        "role": "developer",  # Add role field for frontend logic
                    }
                )

            return developers

        except Exception as e:
            logger.error(f"Error getting developer users: {e}")
            raise

    async def get_regular_users(
        self, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get all regular users (non-admin, non-developer)."""
        try:
            query = (
                select(
                    User.id,
                    User.email,
                    User.username,
                    User.first_name,
                    User.last_name,
                    User.created_at,
                    User.last_login,
                    User.is_active,
                    func.count(UserApplicationConnection.id).label("connection_count"),
                    func.max(UserApplicationConnection.last_accessed_at).label(
                        "last_access"
                    ),
                )
                .outerjoin(
                    UserApplicationConnection,
                    User.id == UserApplicationConnection.user_id,
                )
                .where(User.role == "user")
                .group_by(User.id)
                .order_by(desc(User.created_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            users = []

            for row in result.fetchall():
                users.append(
                    {
                        "id": row.id,
                        "email": row.email,
                        "username": row.username,
                        "first_name": row.first_name,
                        "last_name": row.last_name,
                        "created_at": row.created_at,
                        "last_login": row.last_login,
                        "is_active": row.is_active,
                        "connection_count": row.connection_count,
                        "last_access": row.last_access,
                        "role": "user",  # Add role field for frontend logic
                    }
                )

            return users

        except Exception as e:
            logger.error(f"Error getting regular users: {e}")
            raise

    async def get_user_applications(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's connected applications."""
        try:
            query = (
                select(
                    UserApplicationConnection,
                    RegisteredApplication.application_name,
                    RegisteredApplication.client_id,
                    User.email.label("developer_email"),
                )
                .join(
                    RegisteredApplication,
                    UserApplicationConnection.application_id
                    == RegisteredApplication.id,
                )
                .join(User, RegisteredApplication.developer_id == User.id)
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.is_active == True)
                .order_by(desc(UserApplicationConnection.last_accessed_at))
            )

            result = await self.db.execute(query)
            applications = []

            for conn, application_name, client_id, developer_email in result.fetchall():
                applications.append(
                    {
                        "application_id": conn.application_id,
                        "application_name": application_name,
                        "client_id": client_id,
                        "developer_email": developer_email,
                        "granted_scopes": conn.granted_scopes,
                        "first_connected_at": conn.first_connected_at,
                        "last_accessed_at": conn.last_accessed_at,
                        "access_count": conn.access_count,
                    }
                )

            return applications

        except Exception as e:
            logger.error(f"Error getting user applications: {e}")
            raise

    async def get_user_activity(
        self, user_id: str, limit: int = 50, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get user's access logs and activity."""
        try:
            query = (
                select(
                    UserAccessLog,
                    RegisteredApplication.application_name,
                )
                .join(
                    RegisteredApplication,
                    UserAccessLog.application_id == RegisteredApplication.id,
                )
                .where(UserAccessLog.user_id == user_id)
                .order_by(desc(UserAccessLog.timestamp))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            activity = []

            for log, application_name in result.fetchall():
                activity.append(
                    {
                        "id": log.id,
                        "action": log.action,
                        "timestamp": log.timestamp,
                        "success": log.success,
                        "ip_address": log.ip_address,
                        "user_agent": log.user_agent,
                        "error_message": log.error_message,
                        "application": {
                            "id": log.application_id,
                            "name": application_name,
                            "client_id": log.client_id,
                        },
                    }
                )

            return activity

        except Exception as e:
            logger.error(f"Error getting user activity: {e}")
            raise

    async def get_applications_by_developer(
        self, developer_id: str, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Filter applications by developer."""
        try:
            query = (
                select(
                    RegisteredApplication,
                    User.email.label("developer_email"),
                    func.count(UserApplicationConnection.id).label("user_count"),
                )
                .join(User, RegisteredApplication.developer_id == User.id)
                .outerjoin(
                    UserApplicationConnection,
                    RegisteredApplication.id
                    == UserApplicationConnection.application_id,
                )
                .where(RegisteredApplication.developer_id == developer_id)
                .group_by(RegisteredApplication.id)
                .order_by(desc(RegisteredApplication.created_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.db.execute(query)
            applications = []

            for application, developer_email, user_count in result.fetchall():
                applications.append(
                    {
                        "id": application.id,
                        "client_id": application.client_id,
                        "application_name": application.application_name,
                        "description": application.description,
                        "developer_email": developer_email,
                        "is_active": application.is_active,
                        "admin_approved": application.admin_approved,
                        "created_at": application.created_at,
                        "user_count": user_count,
                    }
                )

            return applications

        except Exception as e:
            logger.error(f"Error getting applications by developer: {e}")
            raise

    async def approve_application(self, application_id: str) -> bool:
        """Approve application registration."""
        try:
            result = await self.db.execute(
                select(RegisteredApplication).where(
                    RegisteredApplication.id == application_id
                )
            )
            application = result.scalar_one_or_none()

            if not application:
                return False

            application.admin_approved = True
            application.updated_at = datetime.utcnow()

            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error approving application: {e}")
            raise

    async def reject_application(self, application_id: str) -> bool:
        """Reject application registration."""
        try:
            result = await self.db.execute(
                select(RegisteredApplication).where(
                    RegisteredApplication.id == application_id
                )
            )
            application = result.scalar_one_or_none()

            if not application:
                return False

            application.admin_approved = False
            application.is_active = False
            application.updated_at = datetime.utcnow()

            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error rejecting application: {e}")
            raise

    async def get_system_analytics(self) -> Dict[str, Any]:
        """Get comprehensive system-wide analytics dashboard."""
        try:
            # Get total counts (excluding admin users)
            total_users = (
                await self.db.scalar(
                    select(func.count(User.id)).where(
                        and_(User.is_active == True, User.role != "admin")
                    )
                )
                or 0
            )

            total_developers = (
                await self.db.scalar(
                    select(func.count(User.id)).where(
                        and_(User.role == "developer", User.is_active == True)
                    )
                )
                or 0
            )

            total_applications = (
                await self.db.scalar(
                    select(func.count(RegisteredApplication.id)).where(
                        RegisteredApplication.is_active == True
                    )
                )
                or 0
            )

            approved_applications = (
                await self.db.scalar(
                    select(func.count(RegisteredApplication.id)).where(
                        and_(
                            RegisteredApplication.is_active == True,
                            RegisteredApplication.admin_approved == True,
                        )
                    )
                )
                or 0
            )

            pending_applications = (
                await self.db.scalar(
                    select(func.count(RegisteredApplication.id)).where(
                        and_(
                            RegisteredApplication.is_active == True,
                            RegisteredApplication.admin_approved == False,
                        )
                    )
                )
                or 0
            )

            # Get activity metrics (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)

            total_connections = (
                await self.db.scalar(
                    select(func.count(UserApplicationConnection.id)).where(
                        UserApplicationConnection.is_active == True
                    )
                )
                or 0
            )

            active_connections = (
                await self.db.scalar(
                    select(func.count(UserApplicationConnection.id)).where(
                        and_(
                            UserApplicationConnection.is_active == True,
                            UserApplicationConnection.last_accessed_at
                            >= thirty_days_ago,
                        )
                    )
                )
                or 0
            )

            total_requests = (
                await self.db.scalar(
                    select(func.count(UserAccessLog.id)).where(
                        UserAccessLog.timestamp >= thirty_days_ago
                    )
                )
                or 0
            )

            successful_requests = (
                await self.db.scalar(
                    select(func.count(UserAccessLog.id)).where(
                        and_(
                            UserAccessLog.timestamp >= thirty_days_ago,
                            UserAccessLog.success == True,
                        )
                    )
                )
                or 0
            )

            success_rate = (
                (successful_requests / total_requests * 100)
                if total_requests > 0
                else 100
            )

            # Get growth metrics (last 7 days vs previous 7 days)
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            fourteen_days_ago = datetime.utcnow() - timedelta(days=14)

            new_users_7d = (
                await self.db.scalar(
                    select(func.count(User.id)).where(
                        and_(User.created_at >= seven_days_ago, User.role != "admin")
                    )
                )
                or 0
            )

            new_users_prev_7d = (
                await self.db.scalar(
                    select(func.count(User.id)).where(
                        and_(
                            User.created_at >= fourteen_days_ago,
                            User.created_at < seven_days_ago,
                            User.role != "admin",
                        )
                    )
                )
                or 0
            )

            user_growth_rate = (
                ((new_users_7d - new_users_prev_7d) / new_users_prev_7d * 100)
                if new_users_prev_7d > 0
                else 0
            )

            # Get top applications by user count
            top_applications_result = await self.db.execute(
                select(
                    RegisteredApplication.application_name,
                    RegisteredApplication.client_id,
                    User.email.label("developer_email"),
                    func.count(UserApplicationConnection.id).label("user_count"),
                )
                .join(User, RegisteredApplication.developer_id == User.id)
                .outerjoin(
                    UserApplicationConnection,
                    RegisteredApplication.id
                    == UserApplicationConnection.application_id,
                )
                .where(RegisteredApplication.is_active == True)
                .group_by(RegisteredApplication.id)
                .order_by(desc("user_count"))
                .limit(10)
            )

            top_applications = []
            for row in top_applications_result.fetchall():
                top_applications.append(
                    {
                        "name": row.application_name,
                        "client_id": row.client_id,
                        "developer_email": row.developer_email,
                        "user_count": row.user_count,
                    }
                )

            return {
                "overview": {
                    "total_users": total_users,
                    "total_developers": total_developers,
                    "total_applications": total_applications,
                    "approved_applications": approved_applications,
                    "pending_applications": pending_applications,
                    "total_connections": total_connections,
                    "active_connections_30d": active_connections,
                },
                "activity_metrics": {
                    "total_requests_30d": total_requests,
                    "successful_requests_30d": successful_requests,
                    "success_rate_30d": round(success_rate, 2),
                    "average_daily_requests": round(total_requests / 30, 2),
                },
                "growth_metrics": {
                    "new_users_7d": new_users_7d,
                    "new_users_prev_7d": new_users_prev_7d,
                    "user_growth_rate_7d": round(user_growth_rate, 2),
                },
                "top_applications": top_applications,
                "period": "last_30_days",
            }

        except Exception as e:
            logger.error(f"Error getting system analytics: {e}")
            raise

    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export individual user data for compliance."""
        try:
            # Get user details
            user_result = await self.db.execute(select(User).where(User.id == user_id))
            user = user_result.scalar_one_or_none()

            if not user:
                return {}

            # Get user's application connections
            connections = await self.get_user_applications(user_id)

            # Get user's activity logs
            activity = await self.get_user_activity(user_id, limit=1000)

            # Get summary statistics
            total_connections = len(connections)
            total_accesses = sum(conn["access_count"] for conn in connections)

            return {
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "role": user.role,
                    "is_active": user.is_active,
                    "is_verified": user.is_verified,
                    "created_at": user.created_at,
                    "last_login": user.last_login,
                },
                "connections": connections,
                "activity_logs": activity,
                "summary": {
                    "total_connections": total_connections,
                    "total_accesses": total_accesses,
                    "export_timestamp": datetime.utcnow(),
                },
            }

        except Exception as e:
            logger.error(f"Error exporting user data: {e}")
            raise

    async def export_users_bulk(
        self,
        role_filter: Optional[str] = None,
        active_filter: Optional[bool] = None,
        limit: int = 1000,
    ) -> List[Dict[str, Any]]:
        """Bulk export users with filtering."""
        try:
            query = select(User)

            # Apply filters
            if role_filter:
                query = query.where(User.role == role_filter)
            if active_filter is not None:
                query = query.where(User.is_active == active_filter)

            query = query.order_by(User.created_at).limit(limit)

            result = await self.db.execute(query)
            users = result.scalars().all()

            exported_users = []
            for user in users:
                # Get connection count for each user
                connection_count = (
                    await self.db.scalar(
                        select(func.count(UserApplicationConnection.id))
                        .where(UserApplicationConnection.user_id == user.id)
                        .where(UserApplicationConnection.is_active == True)
                    )
                    or 0
                )

                exported_users.append(
                    {
                        "id": user.id,
                        "email": user.email,
                        "username": user.username,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "role": user.role,
                        "is_active": user.is_active,
                        "is_verified": user.is_verified,
                        "created_at": user.created_at,
                        "last_login": user.last_login,
                        "connection_count": connection_count,
                    }
                )

            return exported_users

        except Exception as e:
            logger.error(f"Error bulk exporting users: {e}")
            raise
