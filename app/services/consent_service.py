"""
GDPR-Compliant Consent Management Service for GeNieGO SSO Server.

This service provides comprehensive consent management with audit logging,
scope validation, and GDPR compliance features.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models import ConsentAuditLog, RegisteredApplication, User, UserConsent

logger = logging.getLogger(__name__)


class ConsentResult:
    """Result object for consent operations."""

    def __init__(
        self,
        success: bool,
        consent: Optional[UserConsent] = None,
        error: Optional[str] = None,
        audit_log: Optional[ConsentAuditLog] = None,
    ):
        self.success = success
        self.consent = consent
        self.error = error
        self.audit_log = audit_log


class ConsentManager:
    """GDPR-compliant consent management with comprehensive audit logging."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def grant_consent(
        self,
        user_id: str,
        application_id: str,
        scopes: List[str],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_days: int = 365,
    ) -> ConsentResult:
        """Grant user consent for application scopes with audit logging."""
        try:
            # Validate application exists and is active
            app_result = await self.db.execute(
                select(RegisteredApplication).where(
                    and_(
                        RegisteredApplication.id == application_id,
                        RegisteredApplication.is_active == True,
                    )
                )
            )
            application = app_result.scalar_one_or_none()
            if not application:
                return ConsentResult(
                    success=False, error="Application not found or inactive"
                )

            # Validate scopes are allowed for this application
            allowed_scopes = set(application.allowed_scopes or [])
            requested_scopes = set(scopes)
            if not requested_scopes.issubset(allowed_scopes):
                invalid_scopes = requested_scopes - allowed_scopes
                return ConsentResult(
                    success=False,
                    error=f"Invalid scopes requested: {', '.join(invalid_scopes)}",
                )

            # Check for existing consent
            existing_result = await self.db.execute(
                select(UserConsent).where(
                    and_(
                        UserConsent.user_id == user_id,
                        UserConsent.application_id == application_id,
                        UserConsent.is_active == True,
                    )
                )
            )
            existing_consent = existing_result.scalar_one_or_none()

            previous_scopes = None
            if existing_consent:
                # Store previous scopes for audit
                previous_scopes = existing_consent.scopes
                # Revoke existing consent
                existing_consent.revoke()

            # Create new consent
            consent = UserConsent(
                user_id=user_id,
                application_id=application_id,
                scopes=scopes,
                granted_at=datetime.utcnow(),
                expires_at=datetime.utcnow() + timedelta(days=expires_days),
                ip_address=ip_address,
                user_agent=user_agent,
            )
            self.db.add(consent)
            await self.db.flush()  # Get consent ID

            # Create audit log
            audit_log = ConsentAuditLog(
                user_id=user_id,
                application_id=application_id,
                consent_id=consent.id,
                action="granted" if not existing_consent else "updated",
                scopes=scopes,
                previous_scopes=previous_scopes,
                ip_address=ip_address,
                user_agent=user_agent,
                reason="User granted consent via OAuth2 flow",
            )
            self.db.add(audit_log)
            await self.db.commit()

            logger.info(
                f"Consent granted for user {user_id} to application {application_id} "
                f"with scopes: {scopes}"
            )

            return ConsentResult(
                success=True, consent=consent, audit_log=audit_log
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to grant consent: {e}")
            return ConsentResult(success=False, error=str(e))

    async def revoke_consent(
        self,
        user_id: str,
        application_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        reason: Optional[str] = None,
    ) -> ConsentResult:
        """Revoke user consent for an application."""
        try:
            # Find active consent
            result = await self.db.execute(
                select(UserConsent).where(
                    and_(
                        UserConsent.user_id == user_id,
                        UserConsent.application_id == application_id,
                        UserConsent.is_active == True,
                    )
                )
            )
            consent = result.scalar_one_or_none()

            if not consent:
                return ConsentResult(
                    success=False, error="No active consent found"
                )

            # Store scopes for audit
            revoked_scopes = consent.scopes

            # Revoke consent
            consent.revoke()

            # Create audit log
            audit_log = ConsentAuditLog(
                user_id=user_id,
                application_id=application_id,
                consent_id=consent.id,
                action="revoked",
                scopes=revoked_scopes,
                ip_address=ip_address,
                user_agent=user_agent,
                reason=reason or "User revoked consent",
            )
            self.db.add(audit_log)
            await self.db.commit()

            logger.info(
                f"Consent revoked for user {user_id} from application {application_id}"
            )

            return ConsentResult(
                success=True, consent=consent, audit_log=audit_log
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to revoke consent: {e}")
            return ConsentResult(success=False, error=str(e))

    async def check_consent(
        self, user_id: str, application_id: str, required_scopes: List[str]
    ) -> bool:
        """Check if user has granted consent for required scopes."""
        try:
            result = await self.db.execute(
                select(UserConsent).where(
                    and_(
                        UserConsent.user_id == user_id,
                        UserConsent.application_id == application_id,
                        UserConsent.is_active == True,
                    )
                )
            )
            consent = result.scalar_one_or_none()

            if not consent or not consent.is_valid:
                return False

            return consent.has_scopes(required_scopes)

        except Exception as e:
            logger.error(f"Failed to check consent: {e}")
            return False

    async def get_user_consents(
        self, user_id: str, active_only: bool = True
    ) -> List[UserConsent]:
        """Get all consents for a user."""
        try:
            query = select(UserConsent).options(
                selectinload(UserConsent.application)
            ).where(UserConsent.user_id == user_id)

            if active_only:
                query = query.where(UserConsent.is_active == True)

            result = await self.db.execute(query.order_by(UserConsent.granted_at.desc()))
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get user consents: {e}")
            return []

    async def get_consent_audit_log(
        self,
        user_id: Optional[str] = None,
        application_id: Optional[str] = None,
        limit: int = 100,
    ) -> List[ConsentAuditLog]:
        """Get consent audit log with optional filtering."""
        try:
            query = select(ConsentAuditLog).options(
                selectinload(ConsentAuditLog.user),
                selectinload(ConsentAuditLog.application),
            )

            if user_id:
                query = query.where(ConsentAuditLog.user_id == user_id)
            if application_id:
                query = query.where(ConsentAuditLog.application_id == application_id)

            query = query.order_by(ConsentAuditLog.timestamp.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get consent audit log: {e}")
            return []

    async def cleanup_expired_consents(self) -> int:
        """Clean up expired consents and log the action."""
        try:
            # Find expired consents
            result = await self.db.execute(
                select(UserConsent).where(
                    and_(
                        UserConsent.is_active == True,
                        UserConsent.expires_at < datetime.utcnow(),
                    )
                )
            )
            expired_consents = result.scalars().all()

            cleanup_count = 0
            for consent in expired_consents:
                # Mark as inactive
                consent.revoke()

                # Create audit log
                audit_log = ConsentAuditLog(
                    user_id=consent.user_id,
                    application_id=consent.application_id,
                    consent_id=consent.id,
                    action="expired",
                    scopes=consent.scopes,
                    reason="Consent expired automatically",
                )
                self.db.add(audit_log)
                cleanup_count += 1

            await self.db.commit()

            if cleanup_count > 0:
                logger.info(f"Cleaned up {cleanup_count} expired consents")

            return cleanup_count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to cleanup expired consents: {e}")
            return 0
