"""
Rate Limiting Service for GeNieGO SSO Server.

This service provides Redis-based rate limiting for critical endpoints
to prevent abuse and ensure system stability.
"""

import logging
import time
from typing import Dict, Optional, Tuple

import redis.asyncio as redis
from fastapi import HTTPException, Request

from app.config.settings import get_settings

logger = logging.getLogger(__name__)


class RateLimitingService:
    """Redis-based rate limiting service."""

    def __init__(self):
        self.settings = get_settings()
        self.redis_client: Optional[redis.Redis] = None
        self._connect_redis()

    def _connect_redis(self):
        """Connect to Redis server."""
        try:
            self.redis_client = redis.from_url(
                self.settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            logger.info("Connected to Redis for rate limiting")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None

    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int,
        request: Optional[Request] = None
    ) -> Tuple[bool, Dict[str, any]]:
        """
        Check if request is within rate limit.
        
        Args:
            key: Unique identifier for the rate limit (e.g., IP, user_id)
            limit: Maximum number of requests allowed
            window_seconds: Time window in seconds
            request: Optional request object for additional context
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        if not self.redis_client:
            # If Redis is not available, allow the request but log warning
            logger.warning("Redis not available, rate limiting disabled")
            return True, {
                "allowed": True,
                "limit": limit,
                "remaining": limit,
                "reset_time": int(time.time()) + window_seconds,
                "retry_after": None
            }

        try:
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # Use Redis sorted set to track requests in time window
            pipe = self.redis_client.pipeline()
            
            # Remove old entries outside the window
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration for the key
            pipe.expire(key, window_seconds)
            
            results = await pipe.execute()
            current_count = results[1]
            
            # Check if limit exceeded
            if current_count >= limit:
                # Remove the request we just added since it's not allowed
                await self.redis_client.zrem(key, str(current_time))
                
                # Calculate retry after time
                oldest_request = await self.redis_client.zrange(key, 0, 0, withscores=True)
                retry_after = window_seconds
                if oldest_request:
                    retry_after = int(oldest_request[0][1]) + window_seconds - current_time
                
                return False, {
                    "allowed": False,
                    "limit": limit,
                    "remaining": 0,
                    "reset_time": current_time + retry_after,
                    "retry_after": retry_after
                }
            
            return True, {
                "allowed": True,
                "limit": limit,
                "remaining": limit - current_count - 1,
                "reset_time": current_time + window_seconds,
                "retry_after": None
            }

        except Exception as e:
            logger.error(f"Rate limiting error for key {key}: {e}")
            # On error, allow the request but log the issue
            return True, {
                "allowed": True,
                "limit": limit,
                "remaining": limit,
                "reset_time": int(time.time()) + window_seconds,
                "retry_after": None,
                "error": str(e)
            }

    async def check_login_rate_limit(self, ip_address: str, user_id: Optional[str] = None) -> bool:
        """Check rate limit for login attempts."""
        # IP-based rate limiting (more restrictive)
        ip_allowed, ip_info = await self.check_rate_limit(
            key=f"login_ip:{ip_address}",
            limit=10,  # 10 attempts per IP
            window_seconds=900  # 15 minutes
        )
        
        if not ip_allowed:
            logger.warning(f"Login rate limit exceeded for IP {ip_address}")
            raise HTTPException(
                status_code=429,
                detail="Too many login attempts from this IP address",
                headers={"Retry-After": str(ip_info["retry_after"])}
            )
        
        # User-based rate limiting (if user_id provided)
        if user_id:
            user_allowed, user_info = await self.check_rate_limit(
                key=f"login_user:{user_id}",
                limit=5,  # 5 attempts per user
                window_seconds=300  # 5 minutes
            )
            
            if not user_allowed:
                logger.warning(f"Login rate limit exceeded for user {user_id}")
                raise HTTPException(
                    status_code=429,
                    detail="Too many login attempts for this account",
                    headers={"Retry-After": str(user_info["retry_after"])}
                )
        
        return True

    async def check_mfa_rate_limit(self, user_id: str, ip_address: str) -> bool:
        """Check rate limit for MFA verification attempts."""
        # User-based MFA rate limiting
        user_allowed, user_info = await self.check_rate_limit(
            key=f"mfa_user:{user_id}",
            limit=10,  # 10 MFA attempts per user
            window_seconds=600  # 10 minutes
        )
        
        if not user_allowed:
            logger.warning(f"MFA rate limit exceeded for user {user_id}")
            raise HTTPException(
                status_code=429,
                detail="Too many MFA verification attempts",
                headers={"Retry-After": str(user_info["retry_after"])}
            )
        
        # IP-based MFA rate limiting
        ip_allowed, ip_info = await self.check_rate_limit(
            key=f"mfa_ip:{ip_address}",
            limit=20,  # 20 MFA attempts per IP
            window_seconds=600  # 10 minutes
        )
        
        if not ip_allowed:
            logger.warning(f"MFA rate limit exceeded for IP {ip_address}")
            raise HTTPException(
                status_code=429,
                detail="Too many MFA verification attempts from this IP",
                headers={"Retry-After": str(ip_info["retry_after"])}
            )
        
        return True

    async def check_api_rate_limit(self, user_id: str, endpoint: str) -> bool:
        """Check rate limit for API endpoints."""
        # General API rate limiting per user
        user_allowed, user_info = await self.check_rate_limit(
            key=f"api_user:{user_id}",
            limit=1000,  # 1000 requests per user
            window_seconds=3600  # 1 hour
        )
        
        if not user_allowed:
            logger.warning(f"API rate limit exceeded for user {user_id}")
            raise HTTPException(
                status_code=429,
                detail="API rate limit exceeded",
                headers={"Retry-After": str(user_info["retry_after"])}
            )
        
        # Endpoint-specific rate limiting
        endpoint_allowed, endpoint_info = await self.check_rate_limit(
            key=f"api_endpoint:{user_id}:{endpoint}",
            limit=100,  # 100 requests per endpoint per user
            window_seconds=3600  # 1 hour
        )
        
        if not endpoint_allowed:
            logger.warning(f"Endpoint rate limit exceeded for user {user_id} on {endpoint}")
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded for {endpoint}",
                headers={"Retry-After": str(endpoint_info["retry_after"])}
            )
        
        return True

    async def check_registration_rate_limit(self, ip_address: str, email: str) -> bool:
        """Check rate limit for user registration."""
        # IP-based registration rate limiting
        ip_allowed, ip_info = await self.check_rate_limit(
            key=f"register_ip:{ip_address}",
            limit=5,  # 5 registrations per IP
            window_seconds=3600  # 1 hour
        )
        
        if not ip_allowed:
            logger.warning(f"Registration rate limit exceeded for IP {ip_address}")
            raise HTTPException(
                status_code=429,
                detail="Too many registration attempts from this IP address",
                headers={"Retry-After": str(ip_info["retry_after"])}
            )
        
        # Email-based registration rate limiting
        email_allowed, email_info = await self.check_rate_limit(
            key=f"register_email:{email}",
            limit=3,  # 3 attempts per email
            window_seconds=1800  # 30 minutes
        )
        
        if not email_allowed:
            logger.warning(f"Registration rate limit exceeded for email {email}")
            raise HTTPException(
                status_code=429,
                detail="Too many registration attempts for this email",
                headers={"Retry-After": str(email_info["retry_after"])}
            )
        
        return True

    async def get_rate_limit_status(self, key: str) -> Dict[str, any]:
        """Get current rate limit status for a key."""
        if not self.redis_client:
            return {"error": "Redis not available"}
        
        try:
            current_count = await self.redis_client.zcard(key)
            ttl = await self.redis_client.ttl(key)
            
            return {
                "key": key,
                "current_count": current_count,
                "ttl": ttl,
                "exists": ttl > 0
            }
        except Exception as e:
            logger.error(f"Error getting rate limit status for {key}: {e}")
            return {"error": str(e)}

    async def reset_rate_limit(self, key: str) -> bool:
        """Reset rate limit for a specific key (admin function)."""
        if not self.redis_client:
            return False
        
        try:
            await self.redis_client.delete(key)
            logger.info(f"Rate limit reset for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Error resetting rate limit for {key}: {e}")
            return False

    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()


# Global instance
rate_limiting_service = RateLimitingService()


def get_rate_limiting_service() -> RateLimitingService:
    """Dependency to get rate limiting service."""
    return rate_limiting_service
