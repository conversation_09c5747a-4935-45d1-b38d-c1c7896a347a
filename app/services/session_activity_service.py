"""
Session Activity Logging Service for GeNieGO SSO Server.

This service provides comprehensive session activity tracking and logging
for security monitoring and user session management.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from sqlalchemy import and_, desc, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import SSOSession, SessionBinding, User
from app.core.database import get_db_session

logger = logging.getLogger(__name__)


class SessionActivityService:
    """Service for tracking and logging session activities."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def log_session_activity(
        self,
        session_id: str,
        activity_type: str,
        details: Optional[Dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> bool:
        """Log session activity."""
        try:
            # Update session last activity
            result = await self.db.execute(
                select(SSOSession).where(SSOSession.id == session_id)
            )
            session = result.scalar_one_or_none()
            
            if session:
                session.last_activity = datetime.utcnow()
                
                # Update IP and user agent if provided
                if ip_address:
                    session.ip_address = ip_address
                if user_agent:
                    session.user_agent = user_agent
                
                await self.db.commit()
                
                logger.info(f"Session activity logged: {session_id} - {activity_type}")
                return True
            else:
                logger.warning(f"Session not found for activity logging: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to log session activity: {e}")
            await self.db.rollback()
            return False

    async def get_session_activity_timeline(
        self, 
        user_id: str, 
        limit: int = 50
    ) -> List[Dict]:
        """Get session activity timeline for a user."""
        try:
            # Get user sessions with activity
            result = await self.db.execute(
                select(SSOSession)
                .where(SSOSession.user_id == user_id)
                .order_by(desc(SSOSession.last_activity))
                .limit(limit)
            )
            sessions = result.scalars().all()

            timeline = []
            for session in sessions:
                # Get session bindings (connected applications)
                bindings_result = await self.db.execute(
                    select(SessionBinding)
                    .where(SessionBinding.session_id == session.id)
                    .order_by(desc(SessionBinding.created_at))
                )
                bindings = bindings_result.scalars().all()

                # Create timeline entry
                timeline_entry = {
                    "session_id": session.id,
                    "device_fingerprint": session.device_fingerprint,
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "created_at": session.created_at.isoformat(),
                    "last_activity": session.last_activity.isoformat(),
                    "expires_at": session.expires_at.isoformat(),
                    "is_active": session.is_active,
                    "device_info": self._parse_device_info(session.user_agent),
                    "location": await self._get_location_info(session.ip_address),
                    "connected_apps": [
                        {
                            "application_id": binding.application_id,
                            "connected_at": binding.created_at.isoformat(),
                            "last_used": binding.last_used.isoformat() if binding.last_used else None,
                        }
                        for binding in bindings
                    ],
                }
                timeline.append(timeline_entry)

            return timeline

        except Exception as e:
            logger.error(f"Failed to get session activity timeline: {e}")
            return []

    async def get_active_sessions_summary(self, user_id: str) -> Dict:
        """Get summary of active sessions for a user."""
        try:
            # Get active sessions
            result = await self.db.execute(
                select(SSOSession)
                .where(
                    and_(
                        SSOSession.user_id == user_id,
                        SSOSession.is_active == True,
                        SSOSession.expires_at > datetime.utcnow()
                    )
                )
                .order_by(desc(SSOSession.last_activity))
            )
            active_sessions = result.scalars().all()

            # Calculate statistics
            total_active = len(active_sessions)
            device_types = {}
            locations = {}
            recent_activity = 0

            recent_threshold = datetime.utcnow() - timedelta(hours=24)

            for session in active_sessions:
                # Count device types
                device_info = self._parse_device_info(session.user_agent)
                device_type = device_info.get("device_type", "unknown")
                device_types[device_type] = device_types.get(device_type, 0) + 1

                # Count locations
                location = await self._get_location_info(session.ip_address)
                location_key = location.get("city", "Unknown")
                locations[location_key] = locations.get(location_key, 0) + 1

                # Count recent activity
                if session.last_activity >= recent_threshold:
                    recent_activity += 1

            return {
                "total_active_sessions": total_active,
                "device_types": device_types,
                "locations": locations,
                "recent_activity_24h": recent_activity,
                "last_updated": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Failed to get active sessions summary: {e}")
            return {
                "total_active_sessions": 0,
                "device_types": {},
                "locations": {},
                "recent_activity_24h": 0,
                "last_updated": datetime.utcnow().isoformat(),
                "error": str(e),
            }

    async def cleanup_inactive_sessions(self, inactive_days: int = 30) -> int:
        """Clean up sessions that have been inactive for specified days."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=inactive_days)
            
            # Find inactive sessions
            result = await self.db.execute(
                select(SSOSession)
                .where(
                    and_(
                        SSOSession.last_activity < cutoff_date,
                        SSOSession.is_active == True
                    )
                )
            )
            inactive_sessions = result.scalars().all()

            # Deactivate inactive sessions
            deactivated_count = 0
            for session in inactive_sessions:
                session.is_active = False
                deactivated_count += 1

            await self.db.commit()

            if deactivated_count > 0:
                logger.info(f"Deactivated {deactivated_count} inactive sessions (inactive for {inactive_days} days)")

            return deactivated_count

        except Exception as e:
            logger.error(f"Failed to cleanup inactive sessions: {e}")
            await self.db.rollback()
            return 0

    def _parse_device_info(self, user_agent: str) -> Dict:
        """Parse device information from user agent string."""
        if not user_agent:
            return {"device_type": "unknown", "browser": "Unknown", "os": "Unknown"}

        user_agent_lower = user_agent.lower()
        
        # Detect device type
        if any(mobile in user_agent_lower for mobile in ["mobile", "android", "iphone"]):
            device_type = "mobile"
        elif "tablet" in user_agent_lower or "ipad" in user_agent_lower:
            device_type = "tablet"
        else:
            device_type = "desktop"

        # Detect browser
        browser = "Unknown"
        if "chrome" in user_agent_lower:
            browser = "Chrome"
        elif "firefox" in user_agent_lower:
            browser = "Firefox"
        elif "safari" in user_agent_lower and "chrome" not in user_agent_lower:
            browser = "Safari"
        elif "edge" in user_agent_lower:
            browser = "Edge"

        # Detect OS
        os = "Unknown"
        if "windows" in user_agent_lower:
            os = "Windows"
        elif "mac" in user_agent_lower:
            os = "macOS"
        elif "linux" in user_agent_lower:
            os = "Linux"
        elif "android" in user_agent_lower:
            os = "Android"
        elif "ios" in user_agent_lower or "iphone" in user_agent_lower or "ipad" in user_agent_lower:
            os = "iOS"

        return {
            "device_type": device_type,
            "browser": browser,
            "os": os,
        }

    async def _get_location_info(self, ip_address: str) -> Dict:
        """Get location information from IP address."""
        # For now, return a placeholder
        # In production, you would integrate with a GeoIP service
        return {
            "city": "Unknown",
            "country": "Unknown",
            "region": "Unknown",
        }

    async def get_security_events(
        self, 
        user_id: str, 
        event_types: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Dict]:
        """Get security-related events for a user."""
        try:
            # Get recent sessions with potential security events
            result = await self.db.execute(
                select(SSOSession)
                .where(SSOSession.user_id == user_id)
                .order_by(desc(SSOSession.created_at))
                .limit(limit * 2)  # Get more to filter
            )
            sessions = result.scalars().all()

            security_events = []
            
            for session in sessions:
                # Check for suspicious activities
                events = self._analyze_session_security(session)
                security_events.extend(events)

            # Sort by timestamp and limit
            security_events.sort(key=lambda x: x["timestamp"], reverse=True)
            return security_events[:limit]

        except Exception as e:
            logger.error(f"Failed to get security events: {e}")
            return []

    def _analyze_session_security(self, session: SSOSession) -> List[Dict]:
        """Analyze session for security events."""
        events = []
        
        # Check for new device login
        # This is a simplified check - in production you'd have more sophisticated detection
        if session.created_at >= datetime.utcnow() - timedelta(hours=1):
            events.append({
                "type": "new_device_login",
                "severity": "medium",
                "timestamp": session.created_at.isoformat(),
                "details": {
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "device_info": self._parse_device_info(session.user_agent),
                },
                "message": "New device login detected",
            })

        # Check for expired session still active
        if session.expires_at < datetime.utcnow() and session.is_active:
            events.append({
                "type": "expired_session_active",
                "severity": "high",
                "timestamp": session.expires_at.isoformat(),
                "details": {
                    "session_id": session.id,
                    "expired_at": session.expires_at.isoformat(),
                },
                "message": "Expired session still marked as active",
            })

        return events


# Convenience functions for use in API endpoints
async def log_session_activity(
    session_id: str,
    activity_type: str,
    details: Optional[Dict] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
) -> bool:
    """Log session activity."""
    async with get_db_session() as db:
        service = SessionActivityService(db)
        return await service.log_session_activity(
            session_id, activity_type, details, ip_address, user_agent
        )


async def get_user_session_timeline(user_id: str, limit: int = 50) -> List[Dict]:
    """Get session activity timeline for a user."""
    async with get_db_session() as db:
        service = SessionActivityService(db)
        return await service.get_session_activity_timeline(user_id, limit)


async def get_user_sessions_summary(user_id: str) -> Dict:
    """Get active sessions summary for a user."""
    async with get_db_session() as db:
        service = SessionActivityService(db)
        return await service.get_active_sessions_summary(user_id)
