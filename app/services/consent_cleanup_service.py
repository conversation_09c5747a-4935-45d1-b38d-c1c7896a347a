"""
Consent Expiration Cleanup Service for GeNieGO SSO Server.

This service provides automated cleanup of expired consents and
maintenance tasks for GDPR compliance.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.models import ConsentAuditLog, UserConsent
from app.services.consent_service import ConsentManager

logger = logging.getLogger(__name__)


class ConsentCleanupService:
    """Automated cleanup service for expired consents and maintenance tasks."""

    def __init__(self):
        self.is_running = False

    async def cleanup_expired_consents(self) -> Dict[str, int]:
        """Clean up expired consents and create audit logs."""
        cleanup_stats = {
            "expired_consents": 0,
            "audit_logs_created": 0,
            "errors": 0,
        }

        try:
            async with get_db_session() as db:
                consent_manager = ConsentManager(db)
                
                # Use the existing cleanup method
                expired_count = await consent_manager.cleanup_expired_consents()
                cleanup_stats["expired_consents"] = expired_count
                cleanup_stats["audit_logs_created"] = expired_count  # One audit log per expired consent

                logger.info(f"Consent cleanup completed: {expired_count} expired consents processed")

        except Exception as e:
            logger.error(f"Error during consent cleanup: {e}")
            cleanup_stats["errors"] = 1

        return cleanup_stats

    async def cleanup_old_audit_logs(self, retention_days: int = 365) -> int:
        """Clean up old audit logs beyond retention period."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            async with get_db_session() as db:
                # Find old audit logs
                result = await db.execute(
                    select(ConsentAuditLog).where(
                        ConsentAuditLog.timestamp < cutoff_date
                    )
                )
                old_logs = result.scalars().all()

                # Delete old logs
                deleted_count = 0
                for log in old_logs:
                    await db.delete(log)
                    deleted_count += 1

                await db.commit()

                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old audit logs (older than {retention_days} days)")

                return deleted_count

        except Exception as e:
            logger.error(f"Error during audit log cleanup: {e}")
            return 0

    async def generate_consent_report(self) -> Dict[str, any]:
        """Generate consent statistics report."""
        try:
            async with get_db_session() as db:
                # Total consents
                total_result = await db.execute(select(UserConsent))
                total_consents = len(total_result.scalars().all())

                # Active consents
                active_result = await db.execute(
                    select(UserConsent).where(
                        and_(
                            UserConsent.is_active == True,
                            UserConsent.expires_at > datetime.utcnow()
                        )
                    )
                )
                active_consents = len(active_result.scalars().all())

                # Expired consents
                expired_result = await db.execute(
                    select(UserConsent).where(
                        and_(
                            UserConsent.is_active == True,
                            UserConsent.expires_at <= datetime.utcnow()
                        )
                    )
                )
                expired_consents = len(expired_result.scalars().all())

                # Revoked consents
                revoked_result = await db.execute(
                    select(UserConsent).where(UserConsent.is_active == False)
                )
                revoked_consents = len(revoked_result.scalars().all())

                # Expiring soon (within 30 days)
                expiring_soon_date = datetime.utcnow() + timedelta(days=30)
                expiring_result = await db.execute(
                    select(UserConsent).where(
                        and_(
                            UserConsent.is_active == True,
                            UserConsent.expires_at > datetime.utcnow(),
                            UserConsent.expires_at <= expiring_soon_date
                        )
                    )
                )
                expiring_soon = len(expiring_result.scalars().all())

                # Recent audit activity (last 7 days)
                recent_date = datetime.utcnow() - timedelta(days=7)
                audit_result = await db.execute(
                    select(ConsentAuditLog).where(
                        ConsentAuditLog.timestamp >= recent_date
                    )
                )
                recent_activity = len(audit_result.scalars().all())

                report = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "total_consents": total_consents,
                    "active_consents": active_consents,
                    "expired_consents": expired_consents,
                    "revoked_consents": revoked_consents,
                    "expiring_soon": expiring_soon,
                    "recent_activity_7d": recent_activity,
                    "health_score": self._calculate_health_score(
                        total_consents, active_consents, expired_consents
                    ),
                }

                logger.info(f"Consent report generated: {report}")
                return report

        except Exception as e:
            logger.error(f"Error generating consent report: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
            }

    def _calculate_health_score(self, total: int, active: int, expired: int) -> float:
        """Calculate consent system health score (0-100)."""
        if total == 0:
            return 100.0  # No consents = healthy

        active_ratio = active / total
        expired_ratio = expired / total

        # Health score based on active ratio and low expired ratio
        health_score = (active_ratio * 80) + ((1 - expired_ratio) * 20)
        return min(100.0, max(0.0, health_score))

    async def run_maintenance_cycle(self) -> Dict[str, any]:
        """Run a complete maintenance cycle."""
        if self.is_running:
            logger.warning("Maintenance cycle already running, skipping")
            return {"status": "skipped", "reason": "already_running"}

        self.is_running = True
        start_time = datetime.utcnow()

        try:
            logger.info("Starting consent maintenance cycle")

            # 1. Clean up expired consents
            cleanup_stats = await self.cleanup_expired_consents()

            # 2. Clean up old audit logs (keep 1 year)
            audit_cleanup_count = await self.cleanup_old_audit_logs(retention_days=365)

            # 3. Generate health report
            health_report = await self.generate_consent_report()

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            maintenance_result = {
                "status": "completed",
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "cleanup_stats": cleanup_stats,
                "audit_cleanup_count": audit_cleanup_count,
                "health_report": health_report,
            }

            logger.info(f"Consent maintenance cycle completed in {duration:.2f}s")
            return maintenance_result

        except Exception as e:
            logger.error(f"Error during maintenance cycle: {e}")
            return {
                "status": "error",
                "start_time": start_time.isoformat(),
                "error": str(e),
            }
        finally:
            self.is_running = False

    async def start_scheduled_cleanup(self, interval_hours: int = 24):
        """Start scheduled cleanup task."""
        logger.info(f"Starting scheduled consent cleanup (every {interval_hours} hours)")

        while True:
            try:
                await self.run_maintenance_cycle()
                await asyncio.sleep(interval_hours * 3600)  # Convert hours to seconds
            except asyncio.CancelledError:
                logger.info("Scheduled cleanup cancelled")
                break
            except Exception as e:
                logger.error(f"Error in scheduled cleanup: {e}")
                # Wait 1 hour before retrying on error
                await asyncio.sleep(3600)


# Global cleanup service instance
consent_cleanup_service = ConsentCleanupService()


async def run_consent_cleanup():
    """Convenience function to run consent cleanup."""
    return await consent_cleanup_service.run_maintenance_cycle()


async def start_consent_cleanup_scheduler(interval_hours: int = 24):
    """Start the consent cleanup scheduler."""
    await consent_cleanup_service.start_scheduled_cleanup(interval_hours)


# Cleanup task for FastAPI startup
async def initialize_consent_cleanup():
    """Initialize consent cleanup on application startup."""
    try:
        # Run initial cleanup
        result = await consent_cleanup_service.run_maintenance_cycle()
        logger.info(f"Initial consent cleanup completed: {result['status']}")

        # Start background scheduler (don't await - let it run in background)
        asyncio.create_task(start_consent_cleanup_scheduler(interval_hours=24))
        
    except Exception as e:
        logger.error(f"Failed to initialize consent cleanup: {e}")
