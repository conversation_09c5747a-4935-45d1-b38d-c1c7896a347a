"""
MFA Session Management Service for GeNieGO SSO Server.

This service manages temporary sessions during the MFA authentication flow.
In production, this should use Redis for distributed session storage.
"""

import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, Optional

from app.models.user import User


class MFASessionManager:
    """Manages temporary sessions during MFA authentication flow."""
    
    def __init__(self):
        # In-memory storage for development
        # In production, this should use Redis
        self._sessions: Dict[str, Dict] = {}
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
    
    def create_mfa_session(
        self,
        user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_in_minutes: int = 10
    ) -> str:
        """
        Create a temporary session for MFA flow.
        
        Returns:
            Temporary session token
        """
        # Clean up expired sessions periodically
        self._cleanup_expired_sessions()
        
        # Generate secure session token
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(minutes=expires_in_minutes)
        
        # Store session data
        self._sessions[session_token] = {
            "user_id": user.id,
            "user": user,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "created_at": datetime.utcnow(),
            "expires_at": expires_at,
            "mfa_verified": False
        }
        
        return session_token
    
    def get_mfa_session(self, session_token: str) -> Optional[Dict]:
        """
        Get MFA session data by token.
        
        Returns:
            Session data if valid, None if expired or not found
        """
        session_data = self._sessions.get(session_token)
        
        if not session_data:
            return None
        
        # Check if session is expired
        if datetime.utcnow() > session_data["expires_at"]:
            # Remove expired session
            self._sessions.pop(session_token, None)
            return None
        
        return session_data
    
    def verify_mfa_session(self, session_token: str) -> bool:
        """
        Mark MFA session as verified.
        
        Returns:
            True if session was found and marked as verified
        """
        session_data = self._sessions.get(session_token)
        
        if not session_data:
            return False
        
        # Check if session is expired
        if datetime.utcnow() > session_data["expires_at"]:
            self._sessions.pop(session_token, None)
            return False
        
        # Mark as MFA verified
        session_data["mfa_verified"] = True
        return True
    
    def consume_mfa_session(self, session_token: str) -> Optional[User]:
        """
        Consume MFA session and return user if MFA was verified.
        
        Returns:
            User object if MFA was verified, None otherwise
        """
        session_data = self._sessions.get(session_token)
        
        if not session_data:
            return None
        
        # Check if session is expired
        if datetime.utcnow() > session_data["expires_at"]:
            self._sessions.pop(session_token, None)
            return None
        
        # Check if MFA was verified
        if not session_data.get("mfa_verified", False):
            return None
        
        # Remove session (consume it)
        user = session_data["user"]
        self._sessions.pop(session_token, None)
        
        return user
    
    def invalidate_mfa_session(self, session_token: str) -> bool:
        """
        Invalidate MFA session.
        
        Returns:
            True if session was found and removed
        """
        return self._sessions.pop(session_token, None) is not None
    
    def _cleanup_expired_sessions(self) -> None:
        """Clean up expired sessions periodically."""
        current_time = time.time()
        
        # Only cleanup every 5 minutes
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        now = datetime.utcnow()
        expired_tokens = []
        
        for token, session_data in self._sessions.items():
            if now > session_data["expires_at"]:
                expired_tokens.append(token)
        
        # Remove expired sessions
        for token in expired_tokens:
            self._sessions.pop(token, None)
        
        self._last_cleanup = current_time
    
    def get_session_count(self) -> int:
        """Get current number of active MFA sessions (for monitoring)."""
        self._cleanup_expired_sessions()
        return len(self._sessions)


# Global instance for the application
# In production, this should be properly injected as a dependency
mfa_session_manager = MFASessionManager()


def get_mfa_session_manager() -> MFASessionManager:
    """Dependency to get MFA session manager."""
    return mfa_session_manager
