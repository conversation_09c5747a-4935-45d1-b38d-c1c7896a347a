"""
Central Session Management Service for GeNieGO SSO Server.

This service provides centralized session management with cross-application tracking,
Single Logout (SLO) functionality, and OAuth2 token binding.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import redis
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models import RegisteredApplication, SessionBinding, SSOSession, User
from app.utils.redis_utils import get_redis_client

logger = logging.getLogger(__name__)


class DeviceInfo:
    """Device information for session tracking."""

    def __init__(
        self,
        fingerprint: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ):
        self.fingerprint = fingerprint
        self.ip_address = ip_address
        self.user_agent = user_agent


class LogoutResult:
    """Result of logout operation."""

    def __init__(
        self,
        success: bool,
        applications_notified: int = 0,
        failed_notifications: Optional[List[str]] = None,
        error: Optional[str] = None,
    ):
        self.success = success
        self.applications_notified = applications_notified
        self.failed_notifications = failed_notifications or []
        self.error = error


class CentralSessionManager:
    """Enterprise SSO session management with cross-application tracking."""

    def __init__(self, db: AsyncSession, redis_client: Optional[redis.Redis] = None):
        self.db = db
        self.redis = redis_client or get_redis_client()

    async def create_sso_session(
        self,
        user: User,
        applications: List[str],
        device_info: DeviceInfo,
        session_duration_hours: int = 24,
    ) -> SSOSession:
        """Create centralized SSO session with application bindings."""
        try:
            # Create SSO session
            session = SSOSession(
                user_id=user.id,
                device_fingerprint=device_info.fingerprint,
                ip_address=device_info.ip_address,
                user_agent=device_info.user_agent,
                expires_at=datetime.utcnow() + timedelta(hours=session_duration_hours),
            )
            self.db.add(session)
            await self.db.flush()  # Get session ID without committing

            # Create application bindings
            for app_id in applications:
                binding = SessionBinding(
                    sso_session_id=session.id,
                    application_id=app_id,
                    expires_with_sso=True,
                )
                self.db.add(binding)

            await self.db.commit()
            await self.db.refresh(session)

            # Cache session in Redis for fast lookup
            self._cache_session(session)

            logger.info(
                f"Created SSO session {session.id} for user {user.id} with {len(applications)} applications"
            )
            return session

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create SSO session for user {user.id}: {e}")
            raise

    async def get_session_by_id(self, session_id: str) -> Optional[SSOSession]:
        """Get SSO session by ID with Redis caching."""
        try:
            # Try Redis cache first
            cached_session = self._get_cached_session(session_id)
            if cached_session:
                return cached_session

            # Fallback to database
            result = await self.db.execute(
                select(SSOSession)
                .options(selectinload(SSOSession.session_bindings))
                .where(
                    and_(
                        SSOSession.id == session_id,
                        SSOSession.is_active == True,
                        SSOSession.expires_at > datetime.utcnow(),
                    )
                )
            )
            session = result.scalar_one_or_none()

            if session:
                # Cache the session
                self._cache_session(session)

            return session

        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None

    async def get_user_sessions(self, user_id: str) -> List[SSOSession]:
        """Get all active sessions for a user."""
        try:
            result = await self.db.execute(
                select(SSOSession)
                .options(selectinload(SSOSession.session_bindings))
                .where(
                    and_(
                        SSOSession.user_id == user_id,
                        SSOSession.is_active == True,
                        SSOSession.expires_at > datetime.utcnow(),
                    )
                )
                .order_by(SSOSession.last_activity.desc())
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get sessions for user {user_id}: {e}")
            return []

    async def update_session_activity(self, session_id: str) -> bool:
        """Update session last activity timestamp."""
        try:
            result = await self.db.execute(
                select(SSOSession).where(SSOSession.id == session_id)
            )
            session = result.scalar_one_or_none()

            if session and session.is_active and not session.is_expired:
                session.update_activity()
                await self.db.commit()

                # Update cache
                self._cache_session(session)
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update session activity {session_id}: {e}")
            return False

    async def extend_session(self, session_id: str, hours: int = 24) -> bool:
        """Extend session expiration time."""
        try:
            result = await self.db.execute(
                select(SSOSession).where(SSOSession.id == session_id)
            )
            session = result.scalar_one_or_none()

            if session and session.is_active:
                session.extend_session(hours)
                await self.db.commit()

                # Update cache
                self._cache_session(session)
                logger.info(f"Extended session {session_id} by {hours} hours")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to extend session {session_id}: {e}")
            return False

    async def invalidate_session(self, session_id: str) -> bool:
        """Invalidate a specific SSO session."""
        try:
            result = await self.db.execute(
                select(SSOSession).where(SSOSession.id == session_id)
            )
            session = result.scalar_one_or_none()

            if session:
                session.invalidate()
                await self.db.commit()

                # Remove from cache
                self._remove_cached_session(session_id)
                logger.info(f"Invalidated session {session_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to invalidate session {session_id}: {e}")
            return False

    async def propagate_logout(self, session_id: str) -> LogoutResult:
        """Single logout - invalidate all connected application sessions."""
        try:
            # Get session with bindings
            result = await self.db.execute(
                select(SSOSession)
                .options(selectinload(SSOSession.session_bindings))
                .where(SSOSession.id == session_id)
            )
            session = result.scalar_one_or_none()

            if not session:
                return LogoutResult(success=False, error="Session not found")

            # Get all application bindings
            bindings = session.session_bindings
            logout_results = []
            failed_notifications = []

            # Notify all connected applications
            for binding in bindings:
                try:
                    result = await self._notify_application_logout(
                        binding.application_id, binding.oauth_token_id
                    )
                    logout_results.append(result)
                    if not result:
                        failed_notifications.append(binding.application_id)
                except Exception as e:
                    logger.error(
                        f"Failed to notify application {binding.application_id}: {e}"
                    )
                    failed_notifications.append(binding.application_id)

                # Invalidate OAuth2 tokens
                self._invalidate_oauth_tokens(binding.oauth_token_id)

            # Invalidate SSO session
            session.invalidate()
            await self.db.commit()

            # Remove from Redis cache
            self._remove_cached_session(session_id)

            logger.info(
                f"Propagated logout for session {session_id} to {len(bindings)} applications"
            )

            return LogoutResult(
                success=True,
                applications_notified=len(logout_results),
                failed_notifications=failed_notifications,
            )

        except Exception as e:
            logger.error(f"Failed to propagate logout for session {session_id}: {e}")
            return LogoutResult(success=False, error=str(e))

    async def sync_session_state(self, session_id: str) -> bool:
        """Synchronize session state across all applications."""
        try:
            session = await self.get_session_by_id(session_id)
            if not session:
                return False

            # Update session activity
            await self.update_session_activity(session_id)

            # Sync with all bound applications
            for binding in session.session_bindings:
                self._sync_application_session(
                    binding.application_id, binding.oauth_token_id, session.is_active
                )

            return True

        except Exception as e:
            logger.error(f"Failed to sync session state {session_id}: {e}")
            return False

    def _cache_session(self, session: SSOSession) -> None:
        """Cache session in Redis."""
        try:
            if self.redis:
                cache_key = f"sso_session:{session.id}"
                session_data = session.to_dict()
                self.redis.setex(
                    cache_key, 3600, json.dumps(session_data, default=str)  # 1 hour cache
                )
        except Exception as e:
            logger.error(f"Failed to cache session {session.id}: {e}")

    def _get_cached_session(self, session_id: str) -> Optional[SSOSession]:
        """Get session from Redis cache."""
        try:
            if self.redis:
                cache_key = f"sso_session:{session_id}"
                cached_data = self.redis.get(cache_key)
                if cached_data:
                    session_dict = json.loads(cached_data)
                    # Note: This returns a dict, not a full SSOSession object
                    # For full functionality, we'd need to reconstruct the object
                    # For now, we'll return None to force database lookup
                    return None
        except Exception as e:
            logger.error(f"Failed to get cached session {session_id}: {e}")
        return None

    def _remove_cached_session(self, session_id: str) -> None:
        """Remove session from Redis cache."""
        try:
            if self.redis:
                cache_key = f"sso_session:{session_id}"
                self.redis.delete(cache_key)
        except Exception as e:
            logger.error(f"Failed to remove cached session {session_id}: {e}")

    async def _notify_application_logout(
        self, application_id: str, oauth_token_id: Optional[str]
    ) -> bool:
        """Notify application of logout (placeholder for future implementation)."""
        # TODO: Implement application notification system
        # This would typically involve calling application webhooks or APIs
        logger.info(f"Notifying application {application_id} of logout")
        return True

    def _invalidate_oauth_tokens(self, oauth_token_id: Optional[str]) -> None:
        """Invalidate OAuth2 tokens (placeholder for future implementation)."""
        # TODO: Implement OAuth2 token invalidation
        if oauth_token_id:
            logger.info(f"Invalidating OAuth2 token {oauth_token_id}")

    def _sync_application_session(
        self, application_id: str, oauth_token_id: Optional[str], is_active: bool
    ) -> None:
        """Sync session state with application (placeholder for future implementation)."""
        # TODO: Implement application session synchronization
        logger.info(
            f"Syncing session state with application {application_id}: active={is_active}"
        )
