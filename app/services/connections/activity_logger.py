"""
Activity Logger Service for GeNieGO SSO Server.

This service logs user access activities and integrates with the OAuth2 flow
to provide comprehensive activity tracking.
"""

import logging
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import UserAccessLog

logger = logging.getLogger(__name__)


class ActivityLoggerService:
    """Service for logging user access activities."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def log_oauth2_authorization(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        action: str = "oauth2_authorization",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> UserAccessLog:
        """Log OAuth2 authorization activity."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action=action,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_token_exchange(
        self,
        user_id: Optional[str],
        application_id: str,
        client_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[UserAccessLog]:
        """Log OAuth2 token exchange activity."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action="token_exchange",
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_user_info_access(
        self,
        user_id: Optional[str],
        application_id: str,
        client_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[UserAccessLog]:
        """Log user info endpoint access."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action="user_info_access",
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_connection_revocation(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> UserAccessLog:
        """Log connection revocation activity."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action="connection_revoked",
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_api_access(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        action: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> UserAccessLog:
        """Log general API access activity."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action=action,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_authentication_failure(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        error_message: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> UserAccessLog:
        """Log authentication failure."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action="authentication_failure",
            ip_address=ip_address,
            user_agent=user_agent,
            success=False,
            error_message=error_message,
        )

    # Alias methods for test compatibility - removed duplicate log_oauth2_authorization

    async def log_oauth2_token_exchange(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[UserAccessLog]:
        """Alias for log_token_exchange."""
        return await self.log_token_exchange(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_oauth2_userinfo_access(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[UserAccessLog]:
        """Alias for log_user_info_access."""
        return await self.log_user_info_access(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def log_activity(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        action: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> UserAccessLog:
        """Generic activity logging method."""
        return await self._create_log(
            user_id=user_id,
            application_id=application_id,
            client_id=client_id,
            action=action,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
        )

    async def _create_log(
        self,
        user_id: Optional[str],
        application_id: str,
        client_id: str,
        action: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[UserAccessLog]:
        """
        Create an access log entry.

        Enterprise pattern: Let the caller manage transactions.
        This allows for proper transaction composition and testing.
        """
        # Skip logging if user_id is None (e.g., for failed OAuth2 flows with unknown users)
        if user_id is None:
            logger.debug(
                f"Skipping access log for {action} to application {application_id}: "
                f"user_id is None ({'SUCCESS' if success else 'FAILURE'})"
            )
            return None

        try:
            log_entry = UserAccessLog(
                id=str(uuid.uuid4()),
                user_id=user_id,
                application_id=application_id,
                client_id=client_id,
                action=action,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message,
                timestamp=datetime.utcnow(),
            )

            self.db.add(log_entry)
            # Flush to ensure the object gets an ID and is visible in the same transaction
            await self.db.flush()
            await self.db.refresh(log_entry)

            logger.debug(
                f"Logged {action} for user {user_id} to application {application_id}: "
                f"{'SUCCESS' if success else 'FAILURE'}"
            )

            return log_entry

        except Exception as e:
            logger.error(f"Error creating access log: {e}")
            raise

    def extract_client_info(self, request) -> tuple[Optional[str], Optional[str]]:
        """Extract client IP and user agent from request."""
        try:
            # Get IP address (handle proxy headers)
            ip_address = None
            if hasattr(request, "headers"):
                # Check for common proxy headers
                forwarded_for = request.headers.get("X-Forwarded-For")
                if forwarded_for:
                    ip_address = forwarded_for.split(",")[0].strip()
                else:
                    real_ip = request.headers.get("X-Real-IP")
                    if real_ip:
                        ip_address = real_ip
                    elif hasattr(request, "client") and request.client:
                        ip_address = request.client.host

            # Get user agent
            user_agent = None
            if hasattr(request, "headers"):
                user_agent = request.headers.get("User-Agent")

            return ip_address, user_agent

        except Exception as e:
            logger.warning(f"Error extracting client info: {e}")
            return None, None
