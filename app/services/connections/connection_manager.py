"""
Connection Manager Service for GeNieGO SSO Server.

This service manages user-application connections and integrates with the OAuth2 flow
to track user connections and access patterns.
"""

import logging
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    RegisteredApplication,
    User,
    UserApplicationConnection,
)

logger = logging.getLogger(__name__)


class ConnectionManagerService:
    """Service for managing user-application connections."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_or_update_connection(
        self,
        user_id: str,
        application_id: str,
        client_id: str,
        granted_scopes: list,
        ip_address: Optional[str] = None,
        auto_commit: bool = True,
    ) -> UserApplicationConnection:
        """Create or update user-application connection during OAuth2 flow."""
        try:
            # Check if connection already exists
            stmt = select(UserApplicationConnection).where(
                UserApplicationConnection.user_id == user_id,
                UserApplicationConnection.application_id == application_id,
            )
            result = await self.db.execute(stmt)
            connection = result.scalar_one_or_none()

            logger.debug(
                f"Looking for connection: user_id={user_id}, application_id={application_id}"
            )
            logger.debug(f"Found existing connection: {connection is not None}")

            if connection:
                # Update existing connection
                connection.last_accessed_at = datetime.utcnow()
                connection.access_count += 1
                connection.is_active = True
                connection.updated_at = datetime.utcnow()

                # Update scopes if they've changed
                if set(granted_scopes) != set(connection.granted_scopes or []):
                    connection.granted_scopes = granted_scopes

                logger.info(
                    f"Updated connection for user {user_id} to application {application_id}"
                )
            else:
                # Create new connection
                connection = UserApplicationConnection(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    application_id=application_id,
                    client_id=client_id,
                    granted_scopes=granted_scopes,
                    first_connected_at=datetime.utcnow(),
                    last_accessed_at=datetime.utcnow(),
                    access_count=1,
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                )
                self.db.add(connection)
                logger.info(
                    f"Created new connection for user {user_id} to application {application_id}"
                )

            if auto_commit:
                await self.db.commit()
                await self.db.refresh(connection)
            else:
                await self.db.flush()  # Ensure the object gets an ID
            return connection

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating/updating connection: {e}")
            raise

    async def get_connection(
        self, user_id: str, application_id: str
    ) -> Optional[UserApplicationConnection]:
        """Get existing user-application connection."""
        try:
            result = await self.db.execute(
                select(UserApplicationConnection)
                .where(UserApplicationConnection.user_id == user_id)
                .where(UserApplicationConnection.application_id == application_id)
                .where(UserApplicationConnection.is_active == True)
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting connection: {e}")
            raise

    async def update_access(
        self, user_id: str, application_id: str, auto_commit: bool = True
    ) -> Optional[UserApplicationConnection]:
        """Update access time and count for existing connection."""
        try:
            connection = await self.get_connection(user_id, application_id)
            logger.debug(f"update_access: Found connection: {connection is not None}")
            if connection:
                old_count = connection.access_count
                connection.update_access()
                connection.updated_at = datetime.utcnow()
                logger.debug(
                    f"update_access: Updated access count from {old_count} to {connection.access_count}"
                )
                if auto_commit:
                    await self.db.commit()
                    await self.db.refresh(connection)
                else:
                    await self.db.flush()
                logger.debug(
                    f"Updated access for user {user_id} to application {application_id}"
                )
            else:
                logger.warning(
                    f"update_access: No connection found for user {user_id} to application {application_id}"
                )
            return connection

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating access: {e}")
            raise

    async def revoke_connection(self, user_id: str, application_id: str) -> bool:
        """Revoke user-application connection."""
        try:
            connection = await self.get_connection(user_id, application_id)
            if connection:
                connection.is_active = False
                connection.updated_at = datetime.utcnow()
                await self.db.commit()
                logger.info(
                    f"Revoked connection for user {user_id} to application {application_id}"
                )
                return True
            return False

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error revoking connection: {e}")
            raise

    async def get_application_by_client_id(
        self, client_id: str
    ) -> Optional[RegisteredApplication]:
        """Get application by client ID."""
        try:
            result = await self.db.execute(
                select(RegisteredApplication)
                .where(RegisteredApplication.client_id == client_id)
                .where(RegisteredApplication.is_active == True)
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting application by client_id: {e}")
            raise

    async def validate_connection_permissions(
        self, user_id: str, application_id: str, required_scopes: list
    ) -> bool:
        """Validate that user has required permissions for application."""
        try:
            connection = await self.get_connection(user_id, application_id)
            if not connection:
                return False

            granted_scopes = set(connection.granted_scopes or [])
            required_scopes_set = set(required_scopes)

            # Check if all required scopes are granted
            return required_scopes_set.issubset(granted_scopes)

        except Exception as e:
            logger.error(f"Error validating connection permissions: {e}")
            return False
