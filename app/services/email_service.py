"""
Email service for sending authentication-related emails.
"""

import logging
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from app.config import get_settings
from app.templates.email_templates import EmailTemplates

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails."""

    def __init__(self) -> None:
        self.settings = get_settings()

    async def send_password_reset_email(self, email: str, reset_hash: str) -> bool:
        """Send password reset email to user"""

        try:
            # Create reset link
            reset_url = f"{self.settings.DOMAIN_NAME}/oauth/reset_password/{reset_hash}"

            # Get email template
            template = EmailTemplates.get_password_reset_template(reset_url)
            subject = template["subject"]
            html_body = template["html"]
            text_body = template["text"]

            # Send email via SMTP
            logger.info(f"Sending password reset email to {email}: {reset_url}")
            logger.debug(f"Email subject: {subject}")
            logger.debug(f"Text body length: {len(text_body)} characters")
            logger.debug(f"HTML body length: {len(html_body)} characters")

            # Send actual email via SMTP
            if self.settings.SMTP_HOST:
                return await self._send_smtp_email(email, subject, html_body, text_body)
            else:
                logger.warning("SMTP not configured, email not sent")
                return False

        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {e}")
            return False

    async def _send_smtp_email(
        self, to_email: str, subject: str, html_body: str, text_body: str
    ) -> bool:
        """Send email via SMTP (placeholder implementation)"""

        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = self.settings.SMTP_FROM_EMAIL
            msg["To"] = to_email

            # Add text and HTML parts
            part1 = MIMEText(text_body, "plain")
            part2 = MIMEText(html_body, "html")

            msg.attach(part1)
            msg.attach(part2)

            # Send email
            if self.settings.SMTP_USE_SSL:
                # Use SMTP_SSL for port 465
                with smtplib.SMTP_SSL(
                    self.settings.SMTP_HOST, self.settings.SMTP_PORT
                ) as server:
                    if self.settings.SMTP_USERNAME and self.settings.SMTP_PASSWORD:
                        server.login(
                            self.settings.SMTP_USERNAME, self.settings.SMTP_PASSWORD
                        )
                    server.send_message(msg)
            else:
                # Use SMTP with TLS for port 587
                with smtplib.SMTP(
                    self.settings.SMTP_HOST, self.settings.SMTP_PORT
                ) as server:
                    if self.settings.SMTP_USE_TLS:
                        server.starttls()
                    if self.settings.SMTP_USERNAME and self.settings.SMTP_PASSWORD:
                        server.login(
                            self.settings.SMTP_USERNAME, self.settings.SMTP_PASSWORD
                        )
                    server.send_message(msg)

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send SMTP email to {to_email}: {e}")
            return False

    async def send_welcome_email(self, email: str, username: str) -> bool:
        """Send welcome email to new user"""

        try:
            # Get email template
            template = EmailTemplates.get_welcome_template(username)
            subject = template["subject"]
            html_body = template["html"]
            text_body = template["text"]

            logger.info(f"Sending welcome email to {email} ({username})")
            logger.debug(f"Email subject: {subject}")
            logger.debug(f"Text body length: {len(text_body)} characters")
            logger.debug(f"HTML body length: {len(html_body)} characters")

            # Send actual email via SMTP
            if self.settings.SMTP_HOST:
                return await self._send_smtp_email(email, subject, html_body, text_body)
            else:
                logger.warning("SMTP not configured, email not sent")
                return False

        except Exception as e:
            logger.error(f"Failed to send welcome email to {email}: {e}")
            return False

    async def send_two_factor_setup_email(
        self, email: str, username: str, backup_codes: list
    ) -> bool:
        """Send two-factor authentication setup email to user"""

        try:
            # Get email template
            template = EmailTemplates.get_two_factor_setup_template(
                username, backup_codes
            )
            subject = template["subject"]
            html_body = template["html"]
            text_body = template["text"]

            logger.info(f"Sending two-factor setup email to {email} ({username})")
            logger.debug(f"Email subject: {subject}")
            logger.debug(f"Text body length: {len(text_body)} characters")
            logger.debug(f"HTML body length: {len(html_body)} characters")
            logger.debug(f"Backup codes count: {len(backup_codes)}")

            # Send actual email via SMTP
            if self.settings.SMTP_HOST:
                return await self._send_smtp_email(email, subject, html_body, text_body)
            else:
                logger.warning("SMTP not configured, email not sent")
                return False

        except Exception as e:
            logger.error(f"Failed to send two-factor setup email to {email}: {e}")
            return False

    async def send_email_verification(self, email: str, username: str, verification_url: str) -> bool:
        """Send email verification email to new user"""

        try:
            # Get email template
            template = EmailTemplates.get_email_verification_template(username, verification_url)
            subject = template["subject"]
            html_body = template["html"]
            text_body = template["text"]

            logger.info(f"Sending email verification to {email} ({username})")
            logger.debug(f"Email subject: {subject}")
            logger.debug(f"Verification URL: {verification_url}")
            logger.debug(f"Text body length: {len(text_body)} characters")
            logger.debug(f"HTML body length: {len(html_body)} characters")

            # Send actual email via SMTP
            if self.settings.SMTP_HOST:
                return await self._send_smtp_email(email, subject, html_body, text_body)
            else:
                logger.warning("SMTP not configured, email not sent")
                return False

        except Exception as e:
            logger.error(f"Failed to send email verification to {email}: {e}")
            return False
