"""
Role Transition Service for GeNieGO SSO Server.

This service manages user role transitions, particularly from user to developer role,
including application submission, review, and approval workflows.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import HTTPException
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import AuditLog, DeveloperApplication, User

logger = logging.getLogger(__name__)


class RoleTransitionManager:
    """Manages user role transitions and developer application workflows."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def submit_developer_application(
        self,
        user: User,
        application_reason: str,
        technical_background: Optional[str] = None,
        intended_use: Optional[str] = None
    ) -> DeveloperApplication:
        """
        Submit a developer role application.
        
        Args:
            user: User submitting the application
            application_reason: Reason for requesting developer access
            technical_background: User's technical background
            intended_use: How they plan to use developer features
            
        Returns:
            Created DeveloperApplication instance
        """
        try:
            # Check if user already has a pending application
            existing_app = await self._get_pending_application(user.id)
            if existing_app:
                raise HTTPException(
                    status_code=400,
                    detail="You already have a pending developer application"
                )

            # Check if user is already a developer or admin
            if user.role in ["developer", "admin"]:
                raise HTTPException(
                    status_code=400,
                    detail="You already have developer or admin privileges"
                )

            # Create new application
            application = DeveloperApplication(
                user_id=user.id,
                status="pending",
                application_reason=application_reason,
                technical_background=technical_background,
                intended_use=intended_use
            )

            self.db.add(application)
            await self.db.commit()
            await self.db.refresh(application)

            # Log the application submission
            await self._log_audit_event(
                user_id=user.id,
                action="developer_application_submitted",
                resource_type="developer_application",
                resource_id=application.id,
                details={
                    "application_id": application.id,
                    "status": "pending"
                }
            )

            logger.info(f"Developer application {application.id} submitted by user {user.id}")
            return application

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error submitting developer application for user {user.id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to submit developer application"
            )

    async def get_user_applications(self, user_id: str) -> List[Dict]:
        """Get all applications submitted by a user."""
        try:
            result = await self.db.execute(
                select(DeveloperApplication)
                .where(DeveloperApplication.user_id == user_id)
                .order_by(DeveloperApplication.created_at.desc())
            )
            applications = result.scalars().all()
            
            return [app.to_dict() for app in applications]

        except Exception as e:
            logger.error(f"Error getting applications for user {user_id}: {str(e)}")
            return []

    async def get_pending_applications(
        self,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict]:
        """Get all pending developer applications for admin review."""
        try:
            result = await self.db.execute(
                select(DeveloperApplication)
                .where(DeveloperApplication.status == "pending")
                .order_by(DeveloperApplication.created_at.asc())
                .limit(limit)
                .offset(offset)
            )
            applications = result.scalars().all()
            
            return [app.to_dict() for app in applications]

        except Exception as e:
            logger.error(f"Error getting pending applications: {str(e)}")
            return []

    async def review_application(
        self,
        application_id: str,
        reviewer: User,
        decision: str,
        admin_notes: Optional[str] = None
    ) -> bool:
        """
        Review a developer application.
        
        Args:
            application_id: ID of the application to review
            reviewer: Admin user reviewing the application
            decision: "approved" or "rejected"
            admin_notes: Optional notes from the reviewer
            
        Returns:
            True if review was successful
        """
        try:
            # Verify reviewer is admin
            if reviewer.role != "admin":
                raise HTTPException(
                    status_code=403,
                    detail="Only administrators can review applications"
                )

            # Get the application
            application = await self._get_application_by_id(application_id)
            if not application:
                raise HTTPException(
                    status_code=404,
                    detail="Application not found"
                )

            if application.status != "pending":
                raise HTTPException(
                    status_code=400,
                    detail="Application has already been reviewed"
                )

            # Update application
            application.status = decision
            application.admin_notes = admin_notes
            application.reviewed_by = reviewer.id
            application.reviewed_at = datetime.utcnow()

            # If approved, update user role
            if decision == "approved":
                user = await self._get_user_by_id(application.user_id)
                if user:
                    user.role = "developer"
                    logger.info(f"User {user.id} role updated to developer")

            await self.db.commit()

            # Log the review
            await self._log_audit_event(
                user_id=reviewer.id,
                action=f"developer_application_{decision}",
                resource_type="developer_application",
                resource_id=application_id,
                details={
                    "application_id": application_id,
                    "applicant_user_id": application.user_id,
                    "decision": decision,
                    "has_notes": bool(admin_notes)
                }
            )

            logger.info(f"Application {application_id} {decision} by admin {reviewer.id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error reviewing application {application_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to review application"
            )

    async def get_application_by_id(self, application_id: str) -> Optional[Dict]:
        """Get application details by ID."""
        try:
            application = await self._get_application_by_id(application_id)
            return application.to_dict() if application else None

        except Exception as e:
            logger.error(f"Error getting application {application_id}: {str(e)}")
            return None

    async def withdraw_application(self, user: User, application_id: str) -> bool:
        """Allow user to withdraw their pending application."""
        try:
            application = await self._get_application_by_id(application_id)
            if not application:
                raise HTTPException(
                    status_code=404,
                    detail="Application not found"
                )

            # Verify ownership
            if application.user_id != user.id:
                raise HTTPException(
                    status_code=403,
                    detail="You can only withdraw your own applications"
                )

            if application.status != "pending":
                raise HTTPException(
                    status_code=400,
                    detail="Only pending applications can be withdrawn"
                )

            # Update status
            application.status = "withdrawn"
            await self.db.commit()

            # Log the withdrawal
            await self._log_audit_event(
                user_id=user.id,
                action="developer_application_withdrawn",
                resource_type="developer_application",
                resource_id=application_id,
                details={"application_id": application_id}
            )

            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error withdrawing application {application_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to withdraw application"
            )

    # Helper methods

    async def _get_pending_application(self, user_id: str) -> Optional[DeveloperApplication]:
        """Get user's pending application if any."""
        try:
            result = await self.db.execute(
                select(DeveloperApplication).where(
                    and_(
                        DeveloperApplication.user_id == user_id,
                        DeveloperApplication.status == "pending"
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _get_application_by_id(self, application_id: str) -> Optional[DeveloperApplication]:
        """Get application by ID."""
        try:
            result = await self.db.execute(
                select(DeveloperApplication).where(DeveloperApplication.id == application_id)
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            result = await self.db.execute(
                select(User).where(User.id == user_id)
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _log_audit_event(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        details: Optional[Dict] = None
    ) -> None:
        """Log audit event for role transition operations."""
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                status="success"
            )
            self.db.add(audit_log)
            # Note: Commit is handled by the calling method
        except Exception as e:
            logger.error(f"Error logging audit event: {str(e)}")
            # Don't raise exception for audit logging failures
