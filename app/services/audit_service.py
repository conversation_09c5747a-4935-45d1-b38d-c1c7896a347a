"""
Audit Logging Service for GeNieGO SSO Server.

This service provides comprehensive audit logging for security-critical operations
including authentication, authorization, data access, and administrative actions.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import Request
from sqlalchemy import and_, desc, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import AuditLog, User

logger = logging.getLogger(__name__)


class AuditService:
    """Comprehensive audit logging service for security compliance."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def log_authentication_event(
        self,
        user_id: Optional[str],
        action: str,
        status: str = "success",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None,
        error_message: Optional[str] = None
    ) -> None:
        """Log authentication-related events."""
        await self._create_audit_log(
            user_id=user_id,
            action=action,
            resource_type="authentication",
            resource_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            details=details,
            status=status,
            error_message=error_message
        )

    async def log_authorization_event(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        status: str = "success",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None,
        error_message: Optional[str] = None
    ) -> None:
        """Log authorization-related events."""
        await self._create_audit_log(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            details=details,
            status=status,
            error_message=error_message
        )

    async def log_data_access_event(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None
    ) -> None:
        """Log data access events."""
        await self._create_audit_log(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            details=details,
            status="success"
        )

    async def log_admin_action(
        self,
        admin_user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        target_user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> None:
        """Log administrative actions."""
        audit_details = details or {}
        if target_user_id:
            audit_details["target_user_id"] = target_user_id

        await self._create_audit_log(
            user_id=admin_user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            details=audit_details,
            status=status,
            error_message=error_message
        )

    async def log_security_event(
        self,
        user_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> None:
        """Log security-related events."""
        await self._create_audit_log(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            details=details,
            status=status,
            error_message=error_message
        )

    async def log_system_event(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict] = None,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> None:
        """Log system-level events."""
        await self._create_audit_log(
            user_id=None,  # System events don't have a user
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            status=status,
            error_message=error_message
        )

    async def get_user_audit_logs(
        self,
        user_id: str,
        limit: int = 100,
        offset: int = 0,
        action_filter: Optional[str] = None,
        resource_type_filter: Optional[str] = None
    ) -> List[Dict]:
        """Get audit logs for a specific user."""
        try:
            query = select(AuditLog).where(AuditLog.user_id == user_id)

            if action_filter:
                query = query.where(AuditLog.action == action_filter)

            if resource_type_filter:
                query = query.where(AuditLog.resource_type == resource_type_filter)

            query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)

            result = await self.db.execute(query)
            logs = result.scalars().all()

            return [log.to_dict() for log in logs]

        except Exception as e:
            logger.error(f"Error getting audit logs for user {user_id}: {str(e)}")
            return []

    async def get_security_audit_logs(
        self,
        limit: int = 100,
        offset: int = 0,
        ip_address_filter: Optional[str] = None,
        action_filter: Optional[str] = None,
        status_filter: Optional[str] = None
    ) -> List[Dict]:
        """Get security audit logs for admin review."""
        try:
            query = select(AuditLog)

            # Filter for security-related actions
            security_actions = [
                "login_failed", "login_success", "logout",
                "mfa_setup", "mfa_verification_failed", "mfa_verification_success",
                "password_change", "password_reset",
                "role_change", "account_locked", "account_unlocked",
                "suspicious_activity", "rate_limit_exceeded"
            ]

            if action_filter:
                query = query.where(AuditLog.action == action_filter)
            else:
                query = query.where(AuditLog.action.in_(security_actions))

            if ip_address_filter:
                query = query.where(AuditLog.ip_address == ip_address_filter)

            if status_filter:
                query = query.where(AuditLog.status == status_filter)

            query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)

            result = await self.db.execute(query)
            logs = result.scalars().all()

            return [log.to_dict() for log in logs]

        except Exception as e:
            logger.error(f"Error getting security audit logs: {str(e)}")
            return []

    async def get_admin_audit_logs(
        self,
        limit: int = 100,
        offset: int = 0,
        admin_user_id_filter: Optional[str] = None,
        action_filter: Optional[str] = None
    ) -> List[Dict]:
        """Get administrative action audit logs."""
        try:
            query = select(AuditLog)

            # Filter for admin actions
            admin_actions = [
                "user_created", "user_updated", "user_deleted",
                "role_changed", "account_locked", "account_unlocked",
                "application_approved", "application_rejected",
                "system_settings_changed", "admin_action"
            ]

            if action_filter:
                query = query.where(AuditLog.action == action_filter)
            else:
                query = query.where(AuditLog.action.in_(admin_actions))

            if admin_user_id_filter:
                query = query.where(AuditLog.user_id == admin_user_id_filter)

            query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)

            result = await self.db.execute(query)
            logs = result.scalars().all()

            return [log.to_dict() for log in logs]

        except Exception as e:
            logger.error(f"Error getting admin audit logs: {str(e)}")
            return []

    async def _create_audit_log(
        self,
        user_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict] = None,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> None:
        """Create an audit log entry."""
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                details=details,
                status=status,
                error_message=error_message
            )

            self.db.add(audit_log)
            await self.db.commit()

            logger.debug(f"Audit log created: {action} for user {user_id}")

        except Exception as e:
            logger.error(f"Failed to create audit log: {str(e)}")
            # Don't raise exception for audit logging failures
            # to avoid breaking the main application flow

    def extract_request_info(self, request: Request) -> Dict[str, Optional[str]]:
        """Extract audit-relevant information from request."""
        return {
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "session_id": request.cookies.get("geniengo_session")
        }

    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fallback to direct client IP
        if request.client:
            return request.client.host

        return None


def get_audit_service(db: AsyncSession) -> AuditService:
    """Dependency to get audit service."""
    return AuditService(db)