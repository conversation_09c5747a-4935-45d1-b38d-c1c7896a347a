"""
Multi-Factor Authentication Service for GeNieGO SSO Server.

This service provides comprehensive MFA management including TOTP setup,
verification, backup codes, and device management.
"""

import base64
import hashlib
import logging
import secrets
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import pyotp
import qrcode
from io import BytesIO
from fastapi import HTTPException
from passlib.context import CryptContext
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.models.user import AuditLog, MFABackupCode, MFADevice, User

logger = logging.getLogger(__name__)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class MFAManager:
    """Multi-Factor Authentication management service."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.settings = get_settings()

    async def setup_totp_device(
        self,
        user: User,
        device_name: str,
        issuer_name: str = "GeNieGO SSO"
    ) -> Dict[str, any]:
        """
        Set up a new TOTP device for the user.
        
        Returns:
            Dict containing secret, QR code data, and backup codes
        """
        try:
            # Check if device name already exists for this user
            existing_device = await self._get_device_by_name(user.id, device_name)
            if existing_device:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Device name '{device_name}' already exists"
                )

            # Generate TOTP secret
            secret = pyotp.random_base32()
            
            # Create TOTP URI for QR code
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user.email,
                issuer_name=issuer_name
            )

            # Generate QR code
            qr_code_data = self._generate_qr_code(totp_uri)

            # Generate backup codes
            backup_codes = self._generate_backup_codes()

            # Create MFA device (not verified yet)
            mfa_device = MFADevice(
                user_id=user.id,
                device_name=device_name,
                device_type="totp",
                secret_key=self._encrypt_secret(secret),
                backup_codes_count=len(backup_codes),
                is_active=True,
                is_verified=False
            )

            self.db.add(mfa_device)
            await self.db.flush()  # Get device ID

            # Store backup codes
            await self._store_backup_codes(user.id, mfa_device.id, backup_codes)

            await self.db.commit()

            # Log the setup attempt
            await self._log_audit_event(
                user_id=user.id,
                action="mfa_device_setup",
                resource_type="mfa_device",
                resource_id=mfa_device.id,
                details={"device_name": device_name, "device_type": "totp"}
            )

            return {
                "device_id": mfa_device.id,
                "secret": secret,  # Only return for initial setup
                "qr_code": qr_code_data,
                "backup_codes": backup_codes,
                "totp_uri": totp_uri
            }

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"TOTP setup error for user {user.id}: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail="Failed to set up TOTP device"
            )

    async def verify_totp_setup(
        self,
        user: User,
        device_id: str,
        totp_code: str
    ) -> bool:
        """
        Verify TOTP code during device setup to complete the setup process.
        """
        try:
            # Get the unverified device
            device = await self._get_device_by_id(device_id)
            if not device or device.user_id != user.id:
                raise HTTPException(status_code=404, detail="Device not found")

            if device.is_verified:
                raise HTTPException(status_code=400, detail="Device already verified")

            # Decrypt secret and verify code
            secret = self._decrypt_secret(device.secret_key)
            totp = pyotp.TOTP(secret)
            
            if not totp.verify(totp_code, valid_window=1):
                # Increment verification attempts
                device.verification_attempts += 1
                await self.db.commit()
                
                await self._log_audit_event(
                    user_id=user.id,
                    action="mfa_verification_failed",
                    resource_type="mfa_device",
                    resource_id=device_id,
                    details={"attempts": device.verification_attempts}
                )
                
                return False

            # Mark device as verified
            device.is_verified = True
            device.last_used_at = datetime.utcnow()
            await self.db.commit()

            await self._log_audit_event(
                user_id=user.id,
                action="mfa_device_verified",
                resource_type="mfa_device",
                resource_id=device_id,
                details={"device_name": device.device_name}
            )

            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"TOTP verification error: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail="Failed to verify TOTP code"
            )

    async def verify_totp_code(
        self,
        user: User,
        totp_code: str,
        device_id: Optional[str] = None
    ) -> bool:
        """
        Verify TOTP code for authentication.
        """
        try:
            # Get user's active MFA devices
            if device_id:
                devices = [await self._get_device_by_id(device_id)]
                if not devices[0] or devices[0].user_id != user.id:
                    return False
            else:
                devices = await self._get_user_active_devices(user.id)

            if not devices:
                return False

            # Try to verify with any active device
            for device in devices:
                if not device.is_verified or not device.is_active:
                    continue

                secret = self._decrypt_secret(device.secret_key)
                totp = pyotp.TOTP(secret)
                
                if totp.verify(totp_code, valid_window=1):
                    # Update last used time
                    device.last_used_at = datetime.utcnow()
                    await self.db.commit()

                    await self._log_audit_event(
                        user_id=user.id,
                        action="mfa_verification_success",
                        resource_type="mfa_device",
                        resource_id=device.id,
                        details={"device_name": device.device_name}
                    )
                    
                    return True

            # Log failed verification
            await self._log_audit_event(
                user_id=user.id,
                action="mfa_verification_failed",
                resource_type="user",
                resource_id=user.id,
                details={"code_provided": bool(totp_code)}
            )
            
            return False

        except Exception as e:
            logger.error(f"TOTP verification error: {str(e)}")
            return False

    async def verify_backup_code(
        self,
        user: User,
        backup_code: str
    ) -> bool:
        """
        Verify and consume a backup code.
        """
        try:
            # Get unused backup codes for the user
            result = await self.db.execute(
                select(MFABackupCode).where(
                    and_(
                        MFABackupCode.user_id == user.id,
                        MFABackupCode.is_used == False
                    )
                )
            )
            backup_codes = result.scalars().all()

            for code_record in backup_codes:
                if code_record.verify_code(backup_code):
                    # Mark as used
                    code_record.is_used = True
                    code_record.used_at = datetime.utcnow()
                    
                    # Update device backup codes count
                    device = await self._get_device_by_id(code_record.device_id)
                    if device:
                        device.backup_codes_count = max(0, device.backup_codes_count - 1)
                    
                    await self.db.commit()

                    await self._log_audit_event(
                        user_id=user.id,
                        action="mfa_backup_code_used",
                        resource_type="mfa_backup_code",
                        resource_id=code_record.id,
                        details={"device_id": code_record.device_id}
                    )
                    
                    return True

            # Log failed backup code attempt
            await self._log_audit_event(
                user_id=user.id,
                action="mfa_backup_code_failed",
                resource_type="user",
                resource_id=user.id,
                details={"code_provided": bool(backup_code)}
            )
            
            return False

        except Exception as e:
            logger.error(f"Backup code verification error: {str(e)}")
            return False

    async def get_user_devices(self, user_id: str) -> List[Dict[str, any]]:
        """Get all MFA devices for a user."""
        try:
            result = await self.db.execute(
                select(MFADevice).where(MFADevice.user_id == user_id)
            )
            devices = result.scalars().all()
            
            return [device.to_dict() for device in devices]

        except Exception as e:
            logger.error(f"Error getting user devices: {str(e)}")
            return []

    async def remove_device(self, user: User, device_id: str) -> bool:
        """Remove an MFA device and its backup codes."""
        try:
            device = await self._get_device_by_id(device_id)
            if not device or device.user_id != user.id:
                raise HTTPException(status_code=404, detail="Device not found")

            # Remove device (cascade will remove backup codes)
            await self.db.delete(device)
            await self.db.commit()

            await self._log_audit_event(
                user_id=user.id,
                action="mfa_device_removed",
                resource_type="mfa_device",
                resource_id=device_id,
                details={"device_name": device.device_name}
            )

            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error removing device: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to remove device"
            )

    # Helper methods

    async def _get_device_by_id(self, device_id: str) -> Optional[MFADevice]:
        """Get MFA device by ID."""
        try:
            result = await self.db.execute(
                select(MFADevice).where(MFADevice.id == device_id)
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _get_device_by_name(self, user_id: str, device_name: str) -> Optional[MFADevice]:
        """Get MFA device by user ID and device name."""
        try:
            result = await self.db.execute(
                select(MFADevice).where(
                    and_(
                        MFADevice.user_id == user_id,
                        MFADevice.device_name == device_name
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    async def _get_user_active_devices(self, user_id: str) -> List[MFADevice]:
        """Get all active MFA devices for a user."""
        try:
            result = await self.db.execute(
                select(MFADevice).where(
                    and_(
                        MFADevice.user_id == user_id,
                        MFADevice.is_active == True,
                        MFADevice.is_verified == True
                    )
                )
            )
            return result.scalars().all()
        except Exception:
            return []

    def _generate_qr_code(self, totp_uri: str) -> str:
        """Generate QR code for TOTP URI."""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(totp_uri)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64 for easy transmission
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_data = buffer.getvalue()

            return base64.b64encode(img_data).decode()

        except Exception as e:
            logger.error(f"QR code generation error: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to generate QR code"
            )

    def _generate_backup_codes(self, count: int = 10) -> List[str]:
        """Generate backup codes for MFA recovery."""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = secrets.token_hex(4).upper()
            codes.append(code)
        return codes

    async def _store_backup_codes(
        self,
        user_id: str,
        device_id: str,
        backup_codes: List[str]
    ) -> None:
        """Store hashed backup codes in database."""
        try:
            for code in backup_codes:
                code_hash = pwd_context.hash(code)
                backup_code = MFABackupCode(
                    user_id=user_id,
                    device_id=device_id,
                    code_hash=code_hash
                )
                self.db.add(backup_code)
        except Exception as e:
            logger.error(f"Error storing backup codes: {str(e)}")
            raise

    def _encrypt_secret(self, secret: str) -> str:
        """Encrypt TOTP secret for storage."""
        # TODO: Implement proper encryption using app encryption key
        # For now, we'll use base64 encoding as placeholder
        return base64.b64encode(secret.encode()).decode()

    def _decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt TOTP secret from storage."""
        # TODO: Implement proper decryption using app encryption key
        # For now, we'll use base64 decoding as placeholder
        return base64.b64decode(encrypted_secret.encode()).decode()

    async def _log_audit_event(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        details: Optional[Dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        status: str = "success"
    ) -> None:
        """Log audit event for MFA operations."""
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details,
                status=status
            )
            self.db.add(audit_log)
            # Note: Commit is handled by the calling method
        except Exception as e:
            logger.error(f"Error logging audit event: {str(e)}")
            # Don't raise exception for audit logging failures
