"""
Security Monitoring Service for GeNieGO SSO Server.

This service provides basic threat detection and security monitoring
capabilities including suspicious activity detection, anomaly detection,
and security alerting.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import AuditLog, User
from app.services.audit_service import AuditService

logger = logging.getLogger(__name__)


class SecurityMonitoringService:
    """Basic security monitoring and threat detection service."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.audit_service = AuditService(db)

    async def detect_suspicious_login_activity(
        self,
        time_window_hours: int = 24
    ) -> List[Dict]:
        """Detect suspicious login patterns."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            # Find IPs with multiple failed login attempts
            failed_login_query = select(
                AuditLog.ip_address,
                func.count(AuditLog.id).label('failed_attempts'),
                func.count(func.distinct(AuditLog.user_id)).label('unique_users')
            ).where(
                and_(
                    AuditLog.action == "login_failed",
                    AuditLog.created_at >= cutoff_time,
                    AuditLog.ip_address.isnot(None)
                )
            ).group_by(AuditLog.ip_address).having(
                func.count(AuditLog.id) >= 10  # 10+ failed attempts
            )
            
            result = await self.db.execute(failed_login_query)
            suspicious_ips = result.all()
            
            suspicious_activities = []
            for ip_data in suspicious_ips:
                suspicious_activities.append({
                    "type": "suspicious_login_attempts",
                    "ip_address": ip_data.ip_address,
                    "failed_attempts": ip_data.failed_attempts,
                    "unique_users_targeted": ip_data.unique_users,
                    "severity": "high" if ip_data.failed_attempts >= 50 else "medium",
                    "detected_at": datetime.utcnow().isoformat(),
                    "time_window_hours": time_window_hours
                })
            
            return suspicious_activities

        except Exception as e:
            logger.error(f"Error detecting suspicious login activity: {str(e)}")
            return []

    async def detect_account_takeover_attempts(
        self,
        time_window_hours: int = 6
    ) -> List[Dict]:
        """Detect potential account takeover attempts."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            # Find users with successful logins from multiple IPs in short time
            login_query = select(
                AuditLog.user_id,
                func.count(func.distinct(AuditLog.ip_address)).label('unique_ips'),
                func.count(AuditLog.id).label('login_count')
            ).where(
                and_(
                    AuditLog.action == "login_success",
                    AuditLog.created_at >= cutoff_time,
                    AuditLog.user_id.isnot(None),
                    AuditLog.ip_address.isnot(None)
                )
            ).group_by(AuditLog.user_id).having(
                func.count(func.distinct(AuditLog.ip_address)) >= 3  # 3+ different IPs
            )
            
            result = await self.db.execute(login_query)
            suspicious_users = result.all()
            
            takeover_attempts = []
            for user_data in suspicious_users:
                # Get the actual IP addresses for this user
                ip_query = select(
                    AuditLog.ip_address,
                    func.max(AuditLog.created_at).label('last_login')
                ).where(
                    and_(
                        AuditLog.user_id == user_data.user_id,
                        AuditLog.action == "login_success",
                        AuditLog.created_at >= cutoff_time
                    )
                ).group_by(AuditLog.ip_address)
                
                ip_result = await self.db.execute(ip_query)
                ip_addresses = [{"ip": row.ip_address, "last_login": row.last_login.isoformat()} 
                              for row in ip_result.all()]
                
                takeover_attempts.append({
                    "type": "potential_account_takeover",
                    "user_id": user_data.user_id,
                    "unique_ip_count": user_data.unique_ips,
                    "login_count": user_data.login_count,
                    "ip_addresses": ip_addresses,
                    "severity": "high" if user_data.unique_ips >= 5 else "medium",
                    "detected_at": datetime.utcnow().isoformat(),
                    "time_window_hours": time_window_hours
                })
            
            return takeover_attempts

        except Exception as e:
            logger.error(f"Error detecting account takeover attempts: {str(e)}")
            return []

    async def detect_brute_force_attacks(
        self,
        time_window_minutes: int = 30
    ) -> List[Dict]:
        """Detect brute force attacks."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=time_window_minutes)
            
            # Find rapid-fire login attempts from same IP
            brute_force_query = select(
                AuditLog.ip_address,
                func.count(AuditLog.id).label('attempt_count'),
                func.min(AuditLog.created_at).label('first_attempt'),
                func.max(AuditLog.created_at).label('last_attempt')
            ).where(
                and_(
                    AuditLog.action.in_(["login_failed", "login_success"]),
                    AuditLog.created_at >= cutoff_time,
                    AuditLog.ip_address.isnot(None)
                )
            ).group_by(AuditLog.ip_address).having(
                func.count(AuditLog.id) >= 20  # 20+ attempts in 30 minutes
            )
            
            result = await self.db.execute(brute_force_query)
            brute_force_ips = result.all()
            
            attacks = []
            for ip_data in brute_force_ips:
                # Calculate attack rate
                time_diff = (ip_data.last_attempt - ip_data.first_attempt).total_seconds()
                rate_per_minute = ip_data.attempt_count / max(time_diff / 60, 1)
                
                attacks.append({
                    "type": "brute_force_attack",
                    "ip_address": ip_data.ip_address,
                    "attempt_count": ip_data.attempt_count,
                    "first_attempt": ip_data.first_attempt.isoformat(),
                    "last_attempt": ip_data.last_attempt.isoformat(),
                    "rate_per_minute": round(rate_per_minute, 2),
                    "severity": "critical" if rate_per_minute >= 10 else "high",
                    "detected_at": datetime.utcnow().isoformat(),
                    "time_window_minutes": time_window_minutes
                })
            
            return attacks

        except Exception as e:
            logger.error(f"Error detecting brute force attacks: {str(e)}")
            return []

    async def detect_unusual_admin_activity(
        self,
        time_window_hours: int = 24
    ) -> List[Dict]:
        """Detect unusual administrative activity."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            # Find admin users with high activity
            admin_query = select(
                AuditLog.user_id,
                func.count(AuditLog.id).label('action_count'),
                func.count(func.distinct(AuditLog.action)).label('unique_actions')
            ).where(
                and_(
                    AuditLog.action.like("admin_%"),
                    AuditLog.created_at >= cutoff_time,
                    AuditLog.user_id.isnot(None)
                )
            ).group_by(AuditLog.user_id).having(
                func.count(AuditLog.id) >= 50  # 50+ admin actions
            )
            
            result = await self.db.execute(admin_query)
            active_admins = result.all()
            
            unusual_activities = []
            for admin_data in active_admins:
                # Get breakdown of actions
                action_query = select(
                    AuditLog.action,
                    func.count(AuditLog.id).label('count')
                ).where(
                    and_(
                        AuditLog.user_id == admin_data.user_id,
                        AuditLog.action.like("admin_%"),
                        AuditLog.created_at >= cutoff_time
                    )
                ).group_by(AuditLog.action)
                
                action_result = await self.db.execute(action_query)
                action_breakdown = {row.action: row.count for row in action_result.all()}
                
                unusual_activities.append({
                    "type": "unusual_admin_activity",
                    "admin_user_id": admin_data.user_id,
                    "total_actions": admin_data.action_count,
                    "unique_action_types": admin_data.unique_actions,
                    "action_breakdown": action_breakdown,
                    "severity": "medium" if admin_data.action_count >= 100 else "low",
                    "detected_at": datetime.utcnow().isoformat(),
                    "time_window_hours": time_window_hours
                })
            
            return unusual_activities

        except Exception as e:
            logger.error(f"Error detecting unusual admin activity: {str(e)}")
            return []

    async def detect_mfa_bypass_attempts(
        self,
        time_window_hours: int = 12
    ) -> List[Dict]:
        """Detect attempts to bypass MFA."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            # Find users with multiple MFA failures followed by successful login
            mfa_failure_query = select(
                AuditLog.user_id,
                AuditLog.ip_address,
                func.count(AuditLog.id).label('mfa_failures')
            ).where(
                and_(
                    AuditLog.action == "mfa_verification_failed",
                    AuditLog.created_at >= cutoff_time,
                    AuditLog.user_id.isnot(None)
                )
            ).group_by(AuditLog.user_id, AuditLog.ip_address).having(
                func.count(AuditLog.id) >= 5  # 5+ MFA failures
            )
            
            result = await self.db.execute(mfa_failure_query)
            mfa_failures = result.all()
            
            bypass_attempts = []
            for failure_data in mfa_failures:
                # Check if there was a successful login after failures
                success_query = select(AuditLog).where(
                    and_(
                        AuditLog.user_id == failure_data.user_id,
                        AuditLog.ip_address == failure_data.ip_address,
                        AuditLog.action == "login_success",
                        AuditLog.created_at >= cutoff_time
                    )
                ).order_by(desc(AuditLog.created_at)).limit(1)
                
                success_result = await self.db.execute(success_query)
                success_login = success_result.scalar_one_or_none()
                
                if success_login:
                    bypass_attempts.append({
                        "type": "mfa_bypass_attempt",
                        "user_id": failure_data.user_id,
                        "ip_address": failure_data.ip_address,
                        "mfa_failure_count": failure_data.mfa_failures,
                        "successful_login_after_failures": success_login.created_at.isoformat(),
                        "severity": "high",
                        "detected_at": datetime.utcnow().isoformat(),
                        "time_window_hours": time_window_hours
                    })
            
            return bypass_attempts

        except Exception as e:
            logger.error(f"Error detecting MFA bypass attempts: {str(e)}")
            return []

    async def get_security_summary(
        self,
        time_window_hours: int = 24
    ) -> Dict:
        """Get overall security summary."""
        try:
            # Run all detection methods
            suspicious_logins = await self.detect_suspicious_login_activity(time_window_hours)
            takeover_attempts = await self.detect_account_takeover_attempts(time_window_hours // 4)
            brute_force_attacks = await self.detect_brute_force_attacks(30)
            admin_activities = await self.detect_unusual_admin_activity(time_window_hours)
            mfa_bypasses = await self.detect_mfa_bypass_attempts(time_window_hours // 2)
            
            # Calculate threat levels
            total_threats = (
                len(suspicious_logins) + len(takeover_attempts) + 
                len(brute_force_attacks) + len(admin_activities) + len(mfa_bypasses)
            )
            
            critical_threats = len([t for threats in [brute_force_attacks] for t in threats if t.get("severity") == "critical"])
            high_threats = len([t for threats in [suspicious_logins, takeover_attempts, mfa_bypasses] for t in threats if t.get("severity") == "high"])
            
            # Determine overall threat level
            if critical_threats > 0:
                threat_level = "critical"
            elif high_threats >= 3:
                threat_level = "high"
            elif total_threats >= 5:
                threat_level = "medium"
            else:
                threat_level = "low"
            
            return {
                "summary": {
                    "threat_level": threat_level,
                    "total_threats": total_threats,
                    "critical_threats": critical_threats,
                    "high_threats": high_threats,
                    "generated_at": datetime.utcnow().isoformat(),
                    "time_window_hours": time_window_hours
                },
                "threats": {
                    "suspicious_login_activity": suspicious_logins,
                    "account_takeover_attempts": takeover_attempts,
                    "brute_force_attacks": brute_force_attacks,
                    "unusual_admin_activity": admin_activities,
                    "mfa_bypass_attempts": mfa_bypasses
                }
            }

        except Exception as e:
            logger.error(f"Error generating security summary: {str(e)}")
            return {
                "summary": {
                    "threat_level": "unknown",
                    "total_threats": 0,
                    "error": str(e),
                    "generated_at": datetime.utcnow().isoformat()
                },
                "threats": {}
            }

    async def get_ip_reputation(self, ip_address: str) -> Dict:
        """Get reputation information for an IP address."""
        try:
            # Get activity summary for this IP
            activity_query = select(
                AuditLog.action,
                func.count(AuditLog.id).label('count'),
                func.max(AuditLog.created_at).label('last_seen')
            ).where(
                AuditLog.ip_address == ip_address
            ).group_by(AuditLog.action)
            
            result = await self.db.execute(activity_query)
            activities = result.all()
            
            activity_summary = {}
            total_activities = 0
            failed_logins = 0
            successful_logins = 0
            last_activity = None
            
            for activity in activities:
                activity_summary[activity.action] = activity.count
                total_activities += activity.count
                
                if activity.action == "login_failed":
                    failed_logins = activity.count
                elif activity.action == "login_success":
                    successful_logins = activity.count
                
                if not last_activity or activity.last_seen > last_activity:
                    last_activity = activity.last_seen
            
            # Calculate reputation score (0-100, higher is better)
            reputation_score = 50  # Start neutral
            
            if failed_logins > successful_logins * 3:  # High failure rate
                reputation_score -= 30
            elif failed_logins > successful_logins:
                reputation_score -= 15
            
            if successful_logins > 0:
                reputation_score += min(10, successful_logins)
            
            # Determine reputation level
            if reputation_score >= 80:
                reputation_level = "trusted"
            elif reputation_score >= 60:
                reputation_level = "good"
            elif reputation_score >= 40:
                reputation_level = "neutral"
            elif reputation_score >= 20:
                reputation_level = "suspicious"
            else:
                reputation_level = "malicious"
            
            return {
                "ip_address": ip_address,
                "reputation_score": reputation_score,
                "reputation_level": reputation_level,
                "total_activities": total_activities,
                "failed_logins": failed_logins,
                "successful_logins": successful_logins,
                "activity_summary": activity_summary,
                "last_activity": last_activity.isoformat() if last_activity else None,
                "analyzed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting IP reputation for {ip_address}: {str(e)}")
            return {
                "ip_address": ip_address,
                "error": str(e),
                "analyzed_at": datetime.utcnow().isoformat()
            }


def get_security_monitoring_service(db: AsyncSession) -> SecurityMonitoringService:
    """Dependency to get security monitoring service."""
    return SecurityMonitoringService(db)
