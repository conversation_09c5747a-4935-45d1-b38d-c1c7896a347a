"""
Enhanced OAuth2 Scope Validation Service for GeNieGO SSO Server.

This service provides comprehensive scope validation with hierarchical permissions,
dynamic scope resolution, and security policy enforcement.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import RegisteredApplication, User, UserConsent

logger = logging.getLogger(__name__)


class ScopeValidationService:
    """Enhanced OAuth2 scope validation with hierarchical permissions and security policies."""

    # Standard OAuth2 scopes with hierarchical relationships
    STANDARD_SCOPES = {
        "openid": {
            "description": "Basic authentication and identity verification",
            "level": 1,
            "implies": [],
            "required_for": ["profile", "email"],
        },
        "profile": {
            "description": "Access to user profile information",
            "level": 2,
            "implies": ["openid"],
            "sensitive": False,
        },
        "email": {
            "description": "Access to user email address",
            "level": 2,
            "implies": ["openid"],
            "sensitive": True,
        },
        "read": {
            "description": "Read access to user data",
            "level": 3,
            "implies": ["openid", "profile"],
            "sensitive": False,
        },
        "write": {
            "description": "Write access to user data",
            "level": 4,
            "implies": ["openid", "profile", "read"],
            "sensitive": True,
        },
        "admin": {
            "description": "Administrative access",
            "level": 5,
            "implies": ["openid", "profile", "email", "read", "write"],
            "sensitive": True,
            "requires_approval": True,
        },
    }

    def __init__(self, db: AsyncSession):
        self.db = db

    async def validate_scopes(
        self,
        requested_scopes: List[str],
        application_id: str,
        user_id: Optional[str] = None,
        strict_mode: bool = True,
    ) -> Tuple[bool, List[str], List[str], Dict]:
        """
        Comprehensive scope validation.
        
        Returns:
            - is_valid: Whether all scopes are valid
            - valid_scopes: List of valid scopes
            - invalid_scopes: List of invalid scopes
            - validation_info: Additional validation information
        """
        try:
            # Get application
            app_result = await self.db.execute(
                select(RegisteredApplication).where(
                    RegisteredApplication.id == application_id
                )
            )
            application = app_result.scalar_one_or_none()
            
            if not application:
                return False, [], requested_scopes, {"error": "Application not found"}

            # Get user if provided
            user = None
            if user_id:
                user_result = await self.db.execute(
                    select(User).where(User.id == user_id)
                )
                user = user_result.scalar_one_or_none()

            valid_scopes = []
            invalid_scopes = []
            validation_info = {
                "application_name": application.name,
                "scope_details": {},
                "security_warnings": [],
                "implied_scopes": [],
                "requires_approval": [],
            }

            # Validate each requested scope
            for scope in requested_scopes:
                scope_validation = await self._validate_single_scope(
                    scope, application, user, strict_mode
                )
                
                if scope_validation["valid"]:
                    valid_scopes.append(scope)
                    validation_info["scope_details"][scope] = scope_validation
                    
                    # Add implied scopes
                    implied = self._get_implied_scopes(scope)
                    for implied_scope in implied:
                        if implied_scope not in valid_scopes and implied_scope not in requested_scopes:
                            validation_info["implied_scopes"].append(implied_scope)
                    
                    # Check for approval requirements
                    if self._scope_requires_approval(scope):
                        validation_info["requires_approval"].append(scope)
                    
                    # Add security warnings
                    if self._is_sensitive_scope(scope):
                        validation_info["security_warnings"].append(
                            f"Scope '{scope}' provides access to sensitive user data"
                        )
                else:
                    invalid_scopes.append(scope)
                    validation_info["scope_details"][scope] = scope_validation

            # Add implied scopes to valid scopes if not in strict mode
            if not strict_mode:
                for implied_scope in validation_info["implied_scopes"]:
                    if implied_scope not in valid_scopes:
                        implied_validation = await self._validate_single_scope(
                            implied_scope, application, user, strict_mode
                        )
                        if implied_validation["valid"]:
                            valid_scopes.append(implied_scope)

            # Final validation
            is_valid = len(invalid_scopes) == 0
            
            # Additional security checks
            if is_valid:
                security_check = await self._perform_security_checks(
                    valid_scopes, application, user
                )
                validation_info.update(security_check)
                if not security_check.get("security_passed", True):
                    is_valid = False

            return is_valid, valid_scopes, invalid_scopes, validation_info

        except Exception as e:
            logger.error(f"Scope validation error: {e}")
            return False, [], requested_scopes, {"error": str(e)}

    async def _validate_single_scope(
        self,
        scope: str,
        application: RegisteredApplication,
        user: Optional[User],
        strict_mode: bool,
    ) -> Dict:
        """Validate a single scope."""
        validation_result = {
            "valid": False,
            "reason": "",
            "level": 0,
            "sensitive": False,
            "requires_approval": False,
        }

        # Check if scope exists in standard scopes
        if scope in self.STANDARD_SCOPES:
            scope_info = self.STANDARD_SCOPES[scope]
            validation_result.update({
                "level": scope_info["level"],
                "sensitive": scope_info.get("sensitive", False),
                "requires_approval": scope_info.get("requires_approval", False),
                "description": scope_info["description"],
            })
        else:
            # Custom scope - check if application defines it
            validation_result.update({
                "level": 3,  # Default level for custom scopes
                "sensitive": True,  # Assume custom scopes are sensitive
                "requires_approval": True,
                "description": f"Custom scope: {scope}",
            })

        # Check if application allows this scope
        allowed_scopes = set(application.allowed_scopes or [])
        if scope not in allowed_scopes:
            validation_result["reason"] = f"Scope '{scope}' not allowed for application"
            return validation_result

        # Check user permissions if provided
        if user and validation_result["requires_approval"]:
            if not await self._user_can_approve_scope(user, scope):
                validation_result["reason"] = f"User lacks permission for scope '{scope}'"
                return validation_result

        # Check scope dependencies
        if scope in self.STANDARD_SCOPES:
            required_scopes = self.STANDARD_SCOPES[scope].get("required_for", [])
            if required_scopes:
                # This scope requires other scopes to be meaningful
                validation_result["dependencies"] = required_scopes

        validation_result["valid"] = True
        validation_result["reason"] = "Valid scope"
        return validation_result

    def _get_implied_scopes(self, scope: str) -> List[str]:
        """Get scopes implied by the given scope."""
        if scope in self.STANDARD_SCOPES:
            return self.STANDARD_SCOPES[scope].get("implies", [])
        return []

    def _scope_requires_approval(self, scope: str) -> bool:
        """Check if scope requires special approval."""
        if scope in self.STANDARD_SCOPES:
            return self.STANDARD_SCOPES[scope].get("requires_approval", False)
        return True  # Custom scopes require approval by default

    def _is_sensitive_scope(self, scope: str) -> bool:
        """Check if scope is sensitive."""
        if scope in self.STANDARD_SCOPES:
            return self.STANDARD_SCOPES[scope].get("sensitive", False)
        return True  # Custom scopes are sensitive by default

    async def _user_can_approve_scope(self, user: User, scope: str) -> bool:
        """Check if user can approve the given scope."""
        # For now, all users can approve standard scopes
        # In a more complex system, you might check user roles/permissions
        if scope in ["admin"]:
            return user.role in ["admin", "staff"]
        return True

    async def _perform_security_checks(
        self,
        scopes: List[str],
        application: RegisteredApplication,
        user: Optional[User],
    ) -> Dict:
        """Perform additional security checks."""
        security_info = {
            "security_passed": True,
            "security_warnings": [],
            "risk_level": "low",
        }

        # Check for high-risk scope combinations
        high_risk_scopes = {"admin", "write"}
        if len(set(scopes) & high_risk_scopes) > 0:
            security_info["risk_level"] = "high"
            security_info["security_warnings"].append(
                "High-risk scopes detected - additional verification may be required"
            )

        # Check application trust level
        if not application.is_trusted:
            sensitive_scopes = [s for s in scopes if self._is_sensitive_scope(s)]
            if sensitive_scopes:
                security_info["security_warnings"].append(
                    f"Untrusted application requesting sensitive scopes: {sensitive_scopes}"
                )

        # Check for scope escalation
        if user:
            existing_consent = await self._get_existing_consent(user.id, application.id)
            if existing_consent:
                new_scopes = set(scopes) - set(existing_consent.scopes)
                if new_scopes:
                    security_info["security_warnings"].append(
                        f"Scope escalation detected: {list(new_scopes)}"
                    )

        return security_info

    async def _get_existing_consent(
        self, user_id: str, application_id: str
    ) -> Optional[UserConsent]:
        """Get existing user consent for application."""
        try:
            result = await self.db.execute(
                select(UserConsent).where(
                    UserConsent.user_id == user_id,
                    UserConsent.application_id == application_id,
                    UserConsent.is_active == True,
                )
            )
            return result.scalar_one_or_none()
        except Exception:
            return None

    def get_scope_hierarchy(self) -> Dict[str, Dict]:
        """Get the complete scope hierarchy for documentation."""
        return self.STANDARD_SCOPES

    def get_scope_description(self, scope: str) -> str:
        """Get human-readable description of a scope."""
        if scope in self.STANDARD_SCOPES:
            return self.STANDARD_SCOPES[scope]["description"]
        return f"Custom application scope: {scope}"

    async def suggest_minimal_scopes(
        self, requested_scopes: List[str], application_id: str
    ) -> List[str]:
        """Suggest minimal scopes that satisfy the request."""
        # Get application
        app_result = await self.db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = app_result.scalar_one_or_none()
        
        if not application:
            return []

        # Filter to only allowed scopes
        allowed_scopes = set(application.allowed_scopes or [])
        valid_requested = [s for s in requested_scopes if s in allowed_scopes]

        # Remove redundant scopes (those implied by others)
        minimal_scopes = []
        for scope in valid_requested:
            implied_by_others = False
            for other_scope in valid_requested:
                if other_scope != scope and scope in self._get_implied_scopes(other_scope):
                    implied_by_others = True
                    break
            
            if not implied_by_others:
                minimal_scopes.append(scope)

        return minimal_scopes


# Convenience functions for use in API endpoints
async def validate_oauth2_scopes(
    db: AsyncSession,
    requested_scopes: List[str],
    application_id: str,
    user_id: Optional[str] = None,
    strict_mode: bool = True,
) -> Tuple[bool, List[str], List[str], Dict]:
    """Validate OAuth2 scopes with enhanced validation."""
    service = ScopeValidationService(db)
    return await service.validate_scopes(
        requested_scopes, application_id, user_id, strict_mode
    )


async def get_scope_suggestions(
    db: AsyncSession, requested_scopes: List[str], application_id: str
) -> List[str]:
    """Get minimal scope suggestions."""
    service = ScopeValidationService(db)
    return await service.suggest_minimal_scopes(requested_scopes, application_id)
