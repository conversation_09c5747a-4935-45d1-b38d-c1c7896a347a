"""
Model imports for GeNieGO SSO Server.

This module contains all database models for the GeNieGO SSO Server.
Clean SSO-focused models without legacy conflicts:
- User management: User, UserSession
- OAuth2 Authorization: AuthorizationCode, RegisteredApplication
- Password Reset: PasswordResetToken
"""

from .auth import PasswordResetToken

# GeNieGO SSO Server models
from .base import Base
from .organization import (
    ApplicationOrganization,
    Organization,
    OrganizationInvitation,
    OrganizationMembership,
)
from .user import (
    AuditLog,
    AuthorizationCode,
    ConsentAuditLog,
    DeveloperApplication,
    MFABackupCode,
    MFADevice,
    RegisteredApplication,
    SessionBinding,
    SSOSession,
    SystemSettings,
    TrustedDevice,
    User,
    UserAccessLog,
    UserApplicationConnection,
    UserConsent,
    UserSession,
    UserSettings,
)

__all__ = [
    "Base",
    # GeNieGO SSO Server models
    "User",
    "UserSession",
    "AuthorizationCode",
    "RegisteredApplication",
    "UserApplicationConnection",
    "UserAccessLog",
    "PasswordResetToken",
    "UserSettings",
    "TrustedDevice",
    "SystemSettings",
    "SSOSession",
    "SessionBinding",
    "UserConsent",
    "ConsentAuditLog",
    "MFADevice",
    "MFABackupCode",
    "AuditLog",
    "DeveloperApplication",
    "Organization",
    "OrganizationMembership",
    "OrganizationInvitation",
    "ApplicationOrganization",
]
