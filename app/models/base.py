"""
Base model class for GeNieGO.
"""

from datetime import datetime
from typing import Any, Dict

from sqlalchemy import Column, DateTime, String, Table
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import declarative_base


# Define a more specific type for the cls parameter
class BaseModel:
    """Base model with common fields and methods."""

    # Note: Using a type-ignored version of declared_attr to avoid typing issues
    @declared_attr  # type: ignore
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        # This avoids the typing issue by accessing the class name directly
        return cls.__name__.lower()  # type: ignore

    __table__: Table

    # Common audit fields
    created_date = Column(DateTime, default=datetime.utcnow, nullable=True)
    created_by = Column(String(60), nullable=True)
    update_date = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True
    )
    update_by = Column(String(60), nullable=True)

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        # Add type: ignore to avoid the __table__ attribute error
        return {
            column.name: getattr(self, column.name) for column in self.__table__.columns
        }


# Create declarative base
Base = declarative_base(cls=BaseModel)
