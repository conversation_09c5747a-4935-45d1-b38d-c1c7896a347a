"""
GeNieGO SSO Server organization management models.

Organization Management System
- Organization: Team collaboration and application grouping
- OrganizationMembership: Role-based organization membership
- OrganizationInvitation: User invitation system
- ApplicationOrganization: Application grouping under organizations
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.mysql import CHAR
from sqlalchemy.orm import relationship

from .base import Base


class Organization(Base):
    """Organization model for team collaboration and application grouping."""

    __tablename__ = "organizations"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Organization information
    name = Column(String(100), nullable=False, index=True)
    slug = Column(String(50), nullable=False, index=True)  # URL-friendly identifier
    description = Column(Text, nullable=True)
    website = Column(String(255), nullable=True)
    
    # Organization settings
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_public = Column(Boolean, default=False, nullable=False)  # Public organizations can be discovered
    max_members = Column(Integer, default=50, nullable=False)  # Member limit
    
    # Billing and subscription (for future use)
    subscription_tier = Column(String(20), default="free", nullable=False)  # free, pro, enterprise
    billing_email = Column(String(255), nullable=True)
    
    # Organization metadata
    settings = Column(JSON, nullable=True)  # Flexible settings storage
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    memberships = relationship(
        "OrganizationMembership", back_populates="organization", cascade="all, delete-orphan"
    )
    invitations = relationship(
        "OrganizationInvitation", back_populates="organization", cascade="all, delete-orphan"
    )
    applications = relationship(
        "ApplicationOrganization", back_populates="organization", cascade="all, delete-orphan"
    )
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_org_name_active", "name", "is_active"),
        Index("idx_org_slug_active", "slug", "is_active"),
        Index("idx_org_public", "is_public", "is_active"),
        Index("idx_org_created", "created_at"),
    )
    
    def to_dict(self) -> dict:
        """Convert organization to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "website": self.website,
            "is_active": self.is_active,
            "is_public": self.is_public,
            "max_members": self.max_members,
            "subscription_tier": self.subscription_tier,
            "billing_email": self.billing_email,
            "settings": self.settings,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def get_member_count(self) -> int:
        """Get current member count."""
        return len([m for m in self.memberships if m.is_active])
    
    def can_add_member(self) -> bool:
        """Check if organization can add more members."""
        return self.get_member_count() < self.max_members
    
    def __str__(self) -> str:
        return f"Organization {self.name} ({self.slug}): {self.get_member_count()}/{self.max_members} members"


class OrganizationMembership(Base):
    """Organization membership model with role-based access control."""

    __tablename__ = "organization_memberships"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Relationships
    organization_id = Column(CHAR(36), ForeignKey("organizations.id"), nullable=False, index=True)
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Membership details
    role = Column(String(20), nullable=False, default="member", index=True)  # owner, admin, member
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # Invitation details
    invited_by = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)
    invited_at = Column(DateTime, nullable=True)
    joined_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Permissions (JSON for flexible role-based permissions)
    permissions = Column(JSON, nullable=True)  # Custom permissions for this membership
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    organization = relationship("Organization", back_populates="memberships")
    user = relationship("User", foreign_keys=[user_id], back_populates="organization_memberships")
    inviter = relationship("User", foreign_keys=[invited_by])
    
    # Unique constraint: one membership per user per organization
    __table_args__ = (
        UniqueConstraint("organization_id", "user_id", name="uq_org_membership"),
        Index("idx_org_membership_role", "organization_id", "role", "is_active"),
        Index("idx_org_membership_user", "user_id", "is_active"),
        Index("idx_org_membership_inviter", "invited_by", "invited_at"),
    )
    
    def to_dict(self) -> dict:
        """Convert organization membership to dictionary."""
        return {
            "id": self.id,
            "organization_id": self.organization_id,
            "user_id": self.user_id,
            "role": self.role,
            "is_active": self.is_active,
            "invited_by": self.invited_by,
            "invited_at": self.invited_at.isoformat() if self.invited_at else None,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def has_permission(self, permission: str) -> bool:
        """Check if membership has specific permission."""
        # Role-based permissions (simplified 3-role model)
        role_permissions = {
            "owner": ["*"],  # All permissions
            "admin": [
                "manage_members", "manage_applications", "view_analytics",
                "manage_settings", "invite_users", "remove_members"
            ],
            "member": [
                "view_applications", "view_analytics"
            ]
        }
        
        # Check role-based permissions
        if self.role in role_permissions:
            if "*" in role_permissions[self.role] or permission in role_permissions[self.role]:
                return True
        
        # Check custom permissions
        if self.permissions and isinstance(self.permissions, dict):
            return self.permissions.get(permission, False)
        
        return False
    
    def can_manage_member(self, target_member: 'OrganizationMembership') -> bool:
        """Check if this membership can manage another member."""
        # Owners can manage everyone
        if self.role == "owner":
            return True
        
        # Admins can manage members, but not owners or other admins
        if self.role == "admin":
            return target_member.role == "member"
        
        # Members cannot manage other members
        return False
    
    def __str__(self) -> str:
        return f"OrganizationMembership {self.id}: {self.role} in {self.organization_id} for User {self.user_id}"


class OrganizationInvitation(Base):
    """Organization invitation model for user invitation system."""

    __tablename__ = "organization_invitations"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Relationships
    organization_id = Column(CHAR(36), ForeignKey("organizations.id"), nullable=False, index=True)
    invited_by = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)  # Null if inviting by email

    # Invitation details
    email = Column(String(255), nullable=False, index=True)  # Email being invited
    role = Column(String(20), nullable=False, default="member", index=True)  # Intended role (owner, admin, member)

    # Token management
    token = Column(String(255), unique=True, nullable=False, index=True)  # Invitation token
    expires_at = Column(DateTime, nullable=False, index=True)

    # Status tracking
    status = Column(String(20), nullable=False, default="pending", index=True)  # pending, accepted, rejected, expired

    # Response details
    accepted_at = Column(DateTime, nullable=True)
    rejected_at = Column(DateTime, nullable=True)
    rejection_reason = Column(Text, nullable=True)

    # Invitation message
    message = Column(Text, nullable=True)  # Custom invitation message

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    organization = relationship("Organization", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[invited_by])
    user = relationship("User", foreign_keys=[user_id], back_populates="organization_invitations")

    # Indexes for performance
    __table_args__ = (
        Index("idx_org_invitation_email_status", "email", "status"),
        Index("idx_org_invitation_token", "token"),
        Index("idx_org_invitation_expires", "expires_at", "status"),
        Index("idx_org_invitation_org_status", "organization_id", "status"),
    )

    def to_dict(self) -> dict:
        """Convert organization invitation to dictionary."""
        return {
            "id": self.id,
            "organization_id": self.organization_id,
            "invited_by": self.invited_by,
            "user_id": self.user_id,
            "email": self.email,
            "role": self.role,
            "token": self.token,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "status": self.status,
            "accepted_at": self.accepted_at.isoformat() if self.accepted_at else None,
            "rejected_at": self.rejected_at.isoformat() if self.rejected_at else None,
            "rejection_reason": self.rejection_reason,
            "message": self.message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def is_expired(self) -> bool:
        """Check if invitation is expired."""
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """Check if invitation is valid for acceptance."""
        return self.status == "pending" and not self.is_expired()

    def accept(self) -> None:
        """Mark invitation as accepted."""
        self.status = "accepted"
        self.accepted_at = datetime.utcnow()

    def reject(self, reason: Optional[str] = None) -> None:
        """Mark invitation as rejected."""
        self.status = "rejected"
        self.rejected_at = datetime.utcnow()
        self.rejection_reason = reason

    def expire(self) -> None:
        """Mark invitation as expired."""
        self.status = "expired"

    def __str__(self) -> str:
        return f"OrganizationInvitation {self.id}: {self.email} to {self.organization_id} ({self.status})"


class ApplicationOrganization(Base):
    """Application grouping under organizations model."""

    __tablename__ = "application_organizations"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Relationships
    application_id = Column(CHAR(36), ForeignKey("registered_applications.id"), nullable=False, index=True)
    organization_id = Column(CHAR(36), ForeignKey("organizations.id"), nullable=False, index=True)

    # Assignment details
    assigned_by = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Application role within organization
    role = Column(String(20), nullable=False, default="application", index=True)  # application, service, integration

    # Metadata
    description = Column(Text, nullable=True)  # Description of application's role in organization
    settings = Column(JSON, nullable=True)  # Organization-specific application settings

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    organization = relationship("Organization", back_populates="applications")
    application = relationship("RegisteredApplication")
    assigner = relationship("User", foreign_keys=[assigned_by])

    # Unique constraint: one assignment per application per organization
    __table_args__ = (
        UniqueConstraint("application_id", "organization_id", name="uq_app_org"),
        Index("idx_app_org_application", "application_id", "is_active"),
        Index("idx_app_org_organization", "organization_id", "is_active"),
        Index("idx_app_org_assigner", "assigned_by", "created_at"),
    )

    def to_dict(self) -> dict:
        """Convert application organization to dictionary."""
        return {
            "id": self.id,
            "application_id": self.application_id,
            "organization_id": self.organization_id,
            "assigned_by": self.assigned_by,
            "is_active": self.is_active,
            "role": self.role,
            "description": self.description,
            "settings": self.settings,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __str__(self) -> str:
        return f"ApplicationOrganization {self.id}: App {self.application_id} in Org {self.organization_id} ({self.role})"
