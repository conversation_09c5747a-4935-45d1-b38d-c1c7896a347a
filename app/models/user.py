"""
GeNieGO SSO Server user management models.
"""

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from passlib.context import CryptContext
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.mysql import CHAR
from sqlalchemy.orm import relationship

from .base import Base

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(Base):
    """Core user identity table for GeNieGO SSO Server."""

    __tablename__ = "users"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Core identity fields
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=True)  # Nullable for OAuth users

    # Profile information
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)

    # Extended profile information
    phone = Column(String(20), nullable=True)
    bio = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)
    job_title = Column(String(100), nullable=True)
    company = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    timezone = Column(String(50), nullable=True, default="UTC")
    language = Column(String(10), nullable=True, default="en")

    # Security settings
    password_changed_at = Column(DateTime, nullable=True)
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    two_factor_secret = Column(String(255), nullable=True)  # Encrypted TOTP secret
    recovery_codes = Column(JSON, nullable=True)  # Encrypted recovery codes

    # OAuth integration
    google_id = Column(String(255), nullable=True, unique=True, index=True)

    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Role - simple role field for SSO permissions
    role = Column(String(20), default="user", nullable=False, index=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    last_login = Column(DateTime, nullable=True, index=True)

    # Relationships
    sessions = relationship(
        "UserSession", back_populates="user", cascade="all, delete-orphan"
    )
    authorization_codes = relationship(
        "AuthorizationCode", back_populates="user", cascade="all, delete-orphan"
    )
    data_exports = relationship(
        "DataExport", back_populates="user", cascade="all, delete-orphan"
    )
    mfa_devices = relationship(
        "MFADevice", back_populates="user", cascade="all, delete-orphan"
    )
    mfa_backup_codes = relationship(
        "MFABackupCode", back_populates="user", cascade="all, delete-orphan"
    )
    audit_logs = relationship(
        "AuditLog", back_populates="user", cascade="all, delete-orphan"
    )
    developer_applications = relationship(
        "DeveloperApplication", foreign_keys="DeveloperApplication.user_id",
        back_populates="user", cascade="all, delete-orphan"
    )
    reviewed_applications = relationship(
        "DeveloperApplication", foreign_keys="DeveloperApplication.reviewed_by",
        back_populates="reviewer"
    )
    organization_memberships = relationship(
        "OrganizationMembership", foreign_keys="OrganizationMembership.user_id",
        back_populates="user", cascade="all, delete-orphan"
    )
    organization_invitations = relationship(
        "OrganizationInvitation", foreign_keys="OrganizationInvitation.user_id",
        back_populates="user", cascade="all, delete-orphan"
    )

    def set_password(self, password: str) -> None:
        """Hash and set password."""
        self.password_hash = pwd_context.hash(password)

    def verify_password(self, password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(password, self.password_hash)

    @property
    def full_name(self) -> str:
        """Get full name."""
        parts = []
        if self.first_name:
            parts.append(self.first_name)
        if self.last_name:
            parts.append(self.last_name)
        return " ".join(parts) if parts else self.username

    @property
    def display_name(self) -> str:
        """Get display name for UI."""
        return self.full_name or self.username

    def update_last_login(self) -> None:
        """Update last login timestamp."""
        self.last_login = datetime.utcnow()

    def __str__(self) -> str:
        return f"{self.username} ({self.email})"


class UserSession(Base):
    """User session management for GeNieGO SSO Server."""

    __tablename__ = "user_sessions"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Session data
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)

    # Session metadata
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="sessions")

    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if session is valid (not expired and user is active)."""
        return not self.is_expired and self.user.is_active

    def extend_session(self, hours: int = 24) -> None:
        """Extend session expiration."""
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)

    def __str__(self) -> str:
        return f"Session for {self.user.username} (expires: {self.expires_at})"


class AuthorizationCode(Base):
    """OAuth2 authorization codes issued to application servers."""

    __tablename__ = "authorization_codes"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Authorization code
    code = Column(String(255), unique=True, nullable=False, index=True)

    # OAuth2 parameters
    client_id = Column(
        String(100), nullable=False, index=True
    )  # Application identifier
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    redirect_uri = Column(Text, nullable=False)
    scopes = Column(JSON, nullable=True)  # List of requested scopes

    # PKCE support
    code_challenge = Column(String(255), nullable=True)
    code_challenge_method = Column(String(10), nullable=True)  # 'S256'

    # Lifecycle
    expires_at = Column(DateTime, nullable=False)
    used_at = Column(DateTime, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="authorization_codes")

    @property
    def is_expired(self) -> bool:
        """Check if authorization code is expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_used(self) -> bool:
        """Check if authorization code has been used."""
        return self.used_at is not None

    @property
    def is_valid(self) -> bool:
        """Check if authorization code is valid (not expired and not used)."""
        return not self.is_expired and not self.is_used

    def mark_as_used(self) -> None:
        """Mark authorization code as used."""
        self.used_at = datetime.utcnow()

    def __str__(self) -> str:
        return f"AuthCode for {self.client_id} (user: {self.user.username})"


class RegisteredApplication(Base):
    """Registered applications (OAuth2 clients) for GeNieGO SSO Server."""

    __tablename__ = "registered_applications"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Client identification
    client_id = Column(String(100), unique=True, nullable=False, index=True)
    client_secret_hash = Column(String(255), nullable=True)  # Hashed client secret

    # Application information
    application_name = Column(
        String(255), nullable=False
    )  # e.g., 'GenieMove', 'GenieFlow'
    description = Column(String(500), nullable=True)  # Application description

    # OAuth2 configuration
    allowed_redirect_uris = Column(
        JSON, nullable=False
    )  # List of allowed redirect URIs
    allowed_scopes = Column(JSON, nullable=False)  # List of allowed scopes

    # Developer attribution and approval
    developer_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    admin_approved = Column(Boolean, default=False, nullable=False)

    # Status
    is_active = Column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    developer = relationship("User", backref="registered_applications")

    def set_client_secret(self, secret: str) -> None:
        """Hash and set client secret."""
        self.client_secret_hash = pwd_context.hash(secret)

    def verify_client_secret(self, secret: str) -> bool:
        """Verify client secret against hash."""
        if not self.client_secret_hash:
            return False
        return pwd_context.verify(secret, self.client_secret_hash)

    def is_redirect_uri_allowed(self, redirect_uri: str) -> bool:
        """Check if redirect URI is allowed for this application."""
        return redirect_uri in (self.allowed_redirect_uris or [])

    def is_scope_allowed(self, scope: str) -> bool:
        """Check if scope is allowed for this application."""
        return scope in (self.allowed_scopes or [])

    def __str__(self) -> str:
        return f"{self.application_name} ({self.client_id})"


class UserApplicationConnection(Base):
    """User-application connection tracking for GeNieGO SSO Server."""

    __tablename__ = "user_application_connections"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    application_id = Column(
        CHAR(36), ForeignKey("registered_applications.id"), nullable=False, index=True
    )
    client_id = Column(String(100), nullable=False, index=True)

    # Connection details
    granted_scopes = Column(JSON, nullable=False)  # Scopes granted to this connection
    first_connected_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_accessed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    access_count = Column(Integer, default=1, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    user = relationship("User", backref="application_connections")
    application = relationship("RegisteredApplication", backref="user_connections")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint("user_id", "application_id", name="unique_user_application"),
    )

    def update_access(self) -> None:
        """Update last accessed time and increment access count."""
        self.last_accessed_at = datetime.utcnow()
        self.access_count += 1

    def __str__(self) -> str:
        return f"Connection: User {self.user_id} -> Application {self.application_id}"


class UserSettings(Base):
    """User preferences and settings for GeNieGO SSO Server."""

    __tablename__ = "user_settings"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(
        CHAR(36), ForeignKey("users.id"), nullable=False, index=True, unique=True
    )

    # Privacy settings
    profile_visibility = Column(
        String(20), default="private", nullable=False
    )  # public, private, friends
    email_visibility = Column(String(20), default="private", nullable=False)
    activity_visibility = Column(String(20), default="private", nullable=False)

    # Notification preferences
    email_notifications = Column(
        JSON,
        nullable=True,
        default=lambda: {
            "security_alerts": True,
            "product_updates": False,
            "newsletters": False,
            "usage_reports": True,
        },
    )
    in_app_notifications = Column(
        JSON,
        nullable=True,
        default=lambda: {
            "mentions": True,
            "comments": True,
            "task_assignments": True,
            "status_changes": True,
        },
    )

    # Accessibility settings
    theme = Column(String(20), default="system", nullable=False)  # system, light, dark
    font_size = Column(
        String(20), default="medium", nullable=False
    )  # small, medium, large
    high_contrast = Column(Boolean, default=False, nullable=False)
    screen_reader = Column(Boolean, default=False, nullable=False)

    # Data management preferences
    data_retention_days = Column(Integer, default=365, nullable=False)
    auto_delete_inactive = Column(Boolean, default=False, nullable=False)

    # Additional settings for API compatibility
    data_sharing_enabled = Column(Boolean, default=True, nullable=False)
    marketing_emails_enabled = Column(Boolean, default=False, nullable=False)
    analytics_enabled = Column(Boolean, default=True, nullable=False)
    language = Column(String(10), default="en", nullable=True)
    timezone = Column(String(50), default="UTC", nullable=True)
    push_notifications = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    user = relationship("User", backref="settings")

    def __str__(self) -> str:
        return f"Settings for user {self.user_id}"


class TrustedDevice(Base):
    """Trusted devices for user security management."""

    __tablename__ = "trusted_devices"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Device information
    device_name = Column(String(255), nullable=False)
    device_type = Column(String(50), nullable=True)  # desktop, mobile, tablet
    browser = Column(String(100), nullable=True)
    os = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    location = Column(String(255), nullable=True)

    # Device fingerprint for identification
    device_fingerprint = Column(String(255), nullable=False, index=True)

    # Trust status
    is_trusted = Column(Boolean, default=False, nullable=False)
    trust_expires_at = Column(DateTime, nullable=True)

    # Activity tracking
    first_seen_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_seen_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    access_count = Column(Integer, default=1, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    user = relationship("User", backref="trusted_devices")

    def update_access(self) -> None:
        """Update last seen time and increment access count."""
        self.last_seen_at = datetime.utcnow()
        self.access_count += 1

    @property
    def is_trust_expired(self) -> bool:
        """Check if device trust has expired."""
        if not self.trust_expires_at:
            return False
        return datetime.utcnow() > self.trust_expires_at

    def __str__(self) -> str:
        return f"Device {self.device_name} for user {self.user_id}"


class SystemSettings(Base):
    """System-wide configuration settings for GeNieGO SSO Server."""

    __tablename__ = "system_settings"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Setting identification
    setting_key = Column(String(100), unique=True, nullable=False, index=True)
    setting_value = Column(JSON, nullable=False)
    setting_type = Column(String(50), nullable=False)  # string, integer, boolean, json

    # Setting metadata
    category = Column(
        String(50), nullable=False, index=True
    )  # security, ui, email, oauth2
    description = Column(Text, nullable=True)
    is_public = Column(
        Boolean, default=False, nullable=False
    )  # Can be read by non-admins
    is_readonly = Column(
        Boolean, default=False, nullable=False
    )  # Cannot be modified via API

    # Validation
    validation_schema = Column(JSON, nullable=True)  # JSON schema for validation
    default_value = Column(JSON, nullable=True)

    # Audit trail
    created_by = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)
    updated_by = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    creator = relationship(
        "User", foreign_keys=[created_by], backref="created_settings"
    )
    updater = relationship(
        "User", foreign_keys=[updated_by], backref="updated_settings"
    )

    def __str__(self) -> str:
        return f"Setting {self.setting_key} = {self.setting_value}"


class DataExport(Base):
    """User data export requests for GDPR compliance."""

    __tablename__ = "data_exports"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Export details
    export_type = Column(
        String(50), nullable=False
    )  # 'full', 'profile', 'activity', etc.
    status = Column(
        String(20), default="pending", nullable=False
    )  # pending, processing, completed, failed
    file_path = Column(String(500), nullable=True)  # Path to generated export file
    file_size = Column(Integer, nullable=True)  # File size in bytes

    # Request details
    requested_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)  # When the download link expires

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Relationships
    user = relationship("User", back_populates="data_exports")

    def __str__(self) -> str:
        return f"DataExport {self.export_type} for User {self.user_id} - {self.status}"


class UserAccessLog(Base):
    """User access logging for GeNieGO SSO Server."""

    __tablename__ = "user_access_logs"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    application_id = Column(
        CHAR(36), ForeignKey("registered_applications.id"), nullable=True, index=True
    )
    client_id = Column(String(100), nullable=True, index=True)

    # Log details
    action = Column(
        String(50), nullable=False
    )  # 'login', 'token_exchange', 'api_access', etc.
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    success = Column(Boolean, default=True, nullable=False)
    error_message = Column(String(500), nullable=True)

    # Timestamp
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    user = relationship("User", backref="access_logs")
    application = relationship("RegisteredApplication", backref="access_logs")

    def __str__(self) -> str:
        status = "SUCCESS" if self.success else "FAILED"
        return f"AccessLog: {self.action} by User {self.user_id} -> Application {self.application_id} ({status})"


class SSOSession(Base):
    """Centralized SSO session management for GeNieGO SSO Server."""

    __tablename__ = "sso_sessions"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Device and session information
    device_fingerprint = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)

    # Session lifecycle
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Relationships
    user = relationship("User", backref="sso_sessions")
    session_bindings = relationship(
        "SessionBinding", back_populates="sso_session", cascade="all, delete-orphan"
    )

    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = datetime.utcnow()

    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at

    def extend_session(self, hours: int = 24) -> None:
        """Extend session expiration."""
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)
        self.update_activity()

    def invalidate(self) -> None:
        """Invalidate the session."""
        self.is_active = False

    def to_dict(self) -> dict:
        """Convert session to dictionary for caching."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "device_fingerprint": self.device_fingerprint,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_active": self.is_active,
        }

    def __str__(self) -> str:
        return f"SSOSession {self.id} for User {self.user_id} (active: {self.is_active})"


class SessionBinding(Base):
    """Session bindings for OAuth2 tokens to SSO sessions."""

    __tablename__ = "session_bindings"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    sso_session_id = Column(
        CHAR(36), ForeignKey("sso_sessions.id"), nullable=False, index=True
    )
    application_id = Column(
        CHAR(36), ForeignKey("registered_applications.id"), nullable=False, index=True
    )

    # OAuth2 token information
    oauth_token_id = Column(String(255), nullable=True, index=True)
    token_type = Column(String(50), default="bearer", nullable=False)

    # Binding lifecycle
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)
    expires_with_sso = Column(Boolean, default=True, nullable=False)

    # Relationships
    sso_session = relationship("SSOSession", back_populates="session_bindings")
    application = relationship("RegisteredApplication", backref="session_bindings")

    @property
    def is_expired(self) -> bool:
        """Check if binding is expired."""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        # If expires_with_sso is True, check SSO session expiration
        if self.expires_with_sso and self.sso_session:
            return self.sso_session.is_expired
        return False

    def invalidate(self) -> None:
        """Invalidate the session binding."""
        self.expires_at = datetime.utcnow()

    def __str__(self) -> str:
        return f"SessionBinding {self.id} (SSO: {self.sso_session_id}, App: {self.application_id})"


class UserConsent(Base):
    """GDPR-compliant user consent management for GeNieGO SSO Server."""

    __tablename__ = "user_consents"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    application_id = Column(
        CHAR(36), ForeignKey("registered_applications.id"), nullable=False, index=True
    )

    # Consent details
    scopes = Column(JSON, nullable=False)  # List of granted scopes
    granted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)  # Optional expiration
    is_active = Column(Boolean, default=True, nullable=False)

    # Audit information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    user = relationship("User", backref="consents")
    application = relationship("RegisteredApplication", backref="user_consents")

    # Unique constraint: one active consent per user-application pair
    __table_args__ = (
        Index("idx_user_consents_user_app", "user_id", "application_id"),
        Index("idx_user_consents_active", "is_active", "expires_at"),
    )

    @property
    def is_expired(self) -> bool:
        """Check if consent is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if consent is valid (active and not expired)."""
        return self.is_active and not self.is_expired

    def has_scope(self, scope: str) -> bool:
        """Check if consent includes a specific scope."""
        return scope in (self.scopes or [])

    def has_scopes(self, scopes: list) -> bool:
        """Check if consent includes all specified scopes."""
        granted_scopes = set(self.scopes or [])
        required_scopes = set(scopes)
        return required_scopes.issubset(granted_scopes)

    def revoke(self) -> None:
        """Revoke the consent."""
        self.is_active = False

    def extend_expiration(self, days: int = 365) -> None:
        """Extend consent expiration."""
        self.expires_at = datetime.utcnow() + timedelta(days=days)

    def to_dict(self) -> dict:
        """Convert consent to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "application_id": self.application_id,
            "scopes": self.scopes,
            "granted_at": self.granted_at.isoformat() if self.granted_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_active": self.is_active,
            "is_expired": self.is_expired,
            "is_valid": self.is_valid,
        }


class EmailVerificationToken(Base):
    """Email verification token for user registration."""

    __tablename__ = "email_verification_tokens"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # User reference
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Token details
    token = Column(String(255), unique=True, nullable=False, index=True)
    email = Column(String(255), nullable=False)  # Email being verified

    # Token status
    is_used = Column(Boolean, default=False, nullable=False)
    expires_at = Column(DateTime, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    used_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", backref="email_verification_tokens")

    # Indexes
    __table_args__ = (
        Index("idx_email_verification_token", "token"),
        Index("idx_email_verification_user", "user_id"),
        Index("idx_email_verification_expires", "expires_at"),
    )

    @property
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if token is valid (not used and not expired)."""
        return not self.is_used and not self.is_expired

    def mark_as_used(self) -> None:
        """Mark token as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()

    @classmethod
    def generate_token(cls) -> str:
        """Generate a secure verification token."""
        import secrets
        return secrets.token_urlsafe(32)

    def to_dict(self) -> dict:
        """Convert token to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "email": self.email,
            "is_used": self.is_used,
            "is_expired": self.is_expired,
            "is_valid": self.is_valid,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "used_at": self.used_at.isoformat() if self.used_at else None,
        }

    def __str__(self) -> str:
        return f"UserConsent {self.id} for User {self.user_id} -> App {self.application_id} (active: {self.is_active})"


class ConsentAuditLog(Base):
    """Audit log for consent changes to ensure GDPR compliance."""

    __tablename__ = "consent_audit_log"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    application_id = Column(
        CHAR(36), ForeignKey("registered_applications.id"), nullable=False, index=True
    )
    consent_id = Column(
        CHAR(36), ForeignKey("user_consents.id"), nullable=True, index=True
    )

    # Audit details
    action = Column(String(50), nullable=False)  # 'granted', 'revoked', 'expired', 'updated'
    scopes = Column(JSON, nullable=True)  # Scopes involved in the action
    previous_scopes = Column(JSON, nullable=True)  # Previous scopes (for updates)

    # Context information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    reason = Column(String(255), nullable=True)  # Optional reason for the action

    # Timestamp
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    user = relationship("User", backref="consent_audit_logs")
    application = relationship("RegisteredApplication", backref="consent_audit_logs")
    consent = relationship("UserConsent", backref="audit_logs")

    # Indexes for audit queries
    __table_args__ = (
        Index("idx_consent_audit_user_time", "user_id", "timestamp"),
        Index("idx_consent_audit_app_time", "application_id", "timestamp"),
        Index("idx_consent_audit_action", "action", "timestamp"),
    )

    def to_dict(self) -> dict:
        """Convert audit log to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "application_id": self.application_id,
            "consent_id": self.consent_id,
            "action": self.action,
            "scopes": self.scopes,
            "previous_scopes": self.previous_scopes,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "reason": self.reason,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }

    def __str__(self) -> str:
        return f"ConsentAuditLog {self.id}: {self.action} for User {self.user_id} -> App {self.application_id}"


# Multi-Factor Authentication Models

class MFADevice(Base):
    """Multi-Factor Authentication device management."""

    __tablename__ = "mfa_devices"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # User relationship
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Device information
    device_name = Column(String(100), nullable=False)  # User-friendly name
    device_type = Column(String(20), nullable=False, default="totp")  # totp, sms, etc.

    # TOTP configuration
    secret_key = Column(String(255), nullable=False)  # Encrypted TOTP secret
    backup_codes_count = Column(Integer, default=0, nullable=False)  # Number of unused backup codes

    # Device status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)  # Device setup completed

    # Usage tracking
    last_used_at = Column(DateTime, nullable=True, index=True)
    verification_attempts = Column(Integer, default=0, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="mfa_devices")
    backup_codes = relationship("MFABackupCode", back_populates="device", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index("idx_mfa_device_user_active", "user_id", "is_active"),
        Index("idx_mfa_device_type", "device_type"),
        UniqueConstraint("user_id", "device_name", name="uq_user_device_name"),
    )

    def to_dict(self) -> dict:
        """Convert MFA device to dictionary (excluding sensitive data)."""
        return {
            "id": self.id,
            "device_name": self.device_name,
            "device_type": self.device_type,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "backup_codes_count": self.backup_codes_count,
        }

    def __str__(self) -> str:
        return f"MFADevice {self.id}: {self.device_name} ({self.device_type}) for User {self.user_id}"


class MFABackupCode(Base):
    """Multi-Factor Authentication backup codes."""

    __tablename__ = "mfa_backup_codes"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Relationships
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)
    device_id = Column(CHAR(36), ForeignKey("mfa_devices.id"), nullable=False, index=True)

    # Code information
    code_hash = Column(String(255), nullable=False)  # Hashed backup code

    # Usage tracking
    is_used = Column(Boolean, default=False, nullable=False, index=True)
    used_at = Column(DateTime, nullable=True)
    used_ip = Column(String(45), nullable=True)  # IP address when used

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    user = relationship("User", back_populates="mfa_backup_codes")
    device = relationship("MFADevice", back_populates="backup_codes")

    # Indexes for performance
    __table_args__ = (
        Index("idx_mfa_backup_user_unused", "user_id", "is_used"),
        Index("idx_mfa_backup_device_unused", "device_id", "is_used"),
    )

    def verify_code(self, code: str) -> bool:
        """Verify backup code against hash."""
        return pwd_context.verify(code, self.code_hash)

    def to_dict(self) -> dict:
        """Convert backup code to dictionary (excluding sensitive data)."""
        return {
            "id": self.id,
            "is_used": self.is_used,
            "used_at": self.used_at.isoformat() if self.used_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def __str__(self) -> str:
        return f"MFABackupCode {self.id}: {'Used' if self.is_used else 'Available'} for User {self.user_id}"


# Security Audit Logging

class AuditLog(Base):
    """Security audit logging for compliance and monitoring."""

    __tablename__ = "audit_log"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # User relationship (nullable for system events)
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)

    # Event information
    action = Column(String(50), nullable=False, index=True)  # login, mfa_setup, role_change, etc.
    resource_type = Column(String(50), nullable=True, index=True)  # user, application, mfa_device
    resource_id = Column(String(255), nullable=True)  # ID of the affected resource

    # Request context
    ip_address = Column(String(45), nullable=True, index=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True, index=True)

    # Event details
    details = Column(JSON, nullable=True)  # Additional event-specific data
    status = Column(String(20), nullable=False, default="success")  # success, failure, error
    error_message = Column(Text, nullable=True)  # Error details if status != success

    # Timestamp
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    user = relationship("User", back_populates="audit_logs")

    # Indexes for audit queries
    __table_args__ = (
        Index("idx_audit_user_time", "user_id", "created_at"),
        Index("idx_audit_action_time", "action", "created_at"),
        Index("idx_audit_resource", "resource_type", "resource_id"),
        Index("idx_audit_ip_time", "ip_address", "created_at"),
        Index("idx_audit_status", "status", "created_at"),
    )

    def to_dict(self) -> dict:
        """Convert audit log to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "action": self.action,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "session_id": self.session_id,
            "details": self.details,
            "status": self.status,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def __str__(self) -> str:
        return f"AuditLog {self.id}: {self.action} ({self.status}) for User {self.user_id or 'System'}"


# Role Transition System

class DeveloperApplication(Base):
    """User applications for developer role upgrade."""

    __tablename__ = "developer_applications"

    # Primary key
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Applicant information
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Application details
    status = Column(String(20), nullable=False, default="pending", index=True)  # pending, approved, rejected
    application_reason = Column(Text, nullable=False)  # Why they want developer access
    technical_background = Column(Text, nullable=True)  # Technical experience
    intended_use = Column(Text, nullable=True)  # How they plan to use developer features

    # Review information
    admin_notes = Column(Text, nullable=True)  # Admin review notes
    reviewed_by = Column(CHAR(36), ForeignKey("users.id"), nullable=True, index=True)
    reviewed_at = Column(DateTime, nullable=True, index=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="developer_applications")
    reviewer = relationship("User", foreign_keys=[reviewed_by], back_populates="reviewed_applications")

    # Indexes for performance
    __table_args__ = (
        Index("idx_dev_app_user_status", "user_id", "status"),
        Index("idx_dev_app_status_time", "status", "created_at"),
        Index("idx_dev_app_reviewer", "reviewed_by", "reviewed_at"),
    )

    def to_dict(self) -> dict:
        """Convert developer application to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "status": self.status,
            "application_reason": self.application_reason,
            "technical_background": self.technical_background,
            "intended_use": self.intended_use,
            "admin_notes": self.admin_notes,
            "reviewed_by": self.reviewed_by,
            "reviewed_at": self.reviewed_at.isoformat() if self.reviewed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __str__(self) -> str:
        return f"DeveloperApplication {self.id}: {self.status} for User {self.user_id}"
