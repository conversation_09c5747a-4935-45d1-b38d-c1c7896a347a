"""
GeNieGO SSO Server authentication support models.
"""

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.mysql import CHAR
from sqlalchemy.orm import relationship

from .base import Base


class PasswordResetToken(Base):
    """Password reset tokens for GeNieGO SSO Server."""

    __tablename__ = "password_reset_tokens"

    # Primary key - UUID as CHAR(36)
    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key to user
    user_id = Column(CHAR(36), ForeignKey("users.id"), nullable=False, index=True)

    # Reset token
    token = Column(String(255), unique=True, nullable=False, index=True)

    # Token lifecycle
    expires_at = Column(DateTime, nullable=False)
    used_at = Column(DateTime, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", backref="password_reset_tokens")

    @property
    def is_expired(self) -> bool:
        """Check if reset token is expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_used(self) -> bool:
        """Check if reset token has been used."""
        return self.used_at is not None

    @property
    def is_valid(self) -> bool:
        """Check if reset token is valid (not expired and not used)."""
        return not self.is_expired and not self.is_used

    def mark_as_used(self) -> None:
        """Mark reset token as used."""
        self.used_at = datetime.utcnow()

    @classmethod
    def create_token(cls, user_id: str, hours_valid: int = 24) -> "PasswordResetToken":
        """Create a new password reset token."""
        import secrets

        token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=hours_valid)

        return cls(user_id=user_id, token=token, expires_at=expires_at)

    def __str__(self) -> str:
        return f"PasswordReset for user {self.user_id} (expires: {self.expires_at})"
