"""
User repository for GeNieGO SSO Server.
"""

from typing import List, Optional

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User, UserSession
from app.repositories.base import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for user operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(User, db)

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        result = await self.db.execute(
            select(User).where(User.email == email, User.is_active == True)
        )
        return result.scalar_one_or_none()

    async def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        result = await self.db.execute(
            select(User).where(User.username == username, User.is_active == True)
        )
        return result.scalar_one_or_none()

    async def email_exists(self, email: str) -> bool:
        """Check if email already exists."""
        result = await self.db.execute(select(User.id).where(User.email == email))
        return result.scalar_one_or_none() is not None

    async def username_exists(self, username: str) -> bool:
        """Check if username already exists."""
        result = await self.db.execute(select(User.id).where(User.username == username))
        return result.scalar_one_or_none() is not None

    async def get_active_users(self, limit: int = 100, offset: int = 0) -> List[User]:
        """Get active users with pagination."""
        result = await self.db.execute(
            select(User)
            .where(User.is_active == True)
            .limit(limit)
            .offset(offset)
            .order_by(User.created_at.desc())
        )
        return result.scalars().all()


class SessionRepository(BaseRepository[UserSession]):
    """Repository for user session operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(UserSession, db)

    async def get_by_token(self, session_token: str) -> Optional[UserSession]:
        """Get session by token."""
        result = await self.db.execute(
            select(UserSession)
            .where(UserSession.session_token == session_token)
            .join(User)
            .where(User.is_active == True)
        )
        return result.scalar_one_or_none()

    async def get_user_sessions(self, user_id: str) -> List[UserSession]:
        """Get all sessions for a user."""
        result = await self.db.execute(
            select(UserSession)
            .where(UserSession.user_id == user_id)
            .order_by(UserSession.created_at.desc())
        )
        return result.scalars().all()

    async def delete_user_sessions(self, user_id: str) -> int:
        """Delete all sessions for a user."""
        result = await self.db.execute(
            delete(UserSession).where(UserSession.user_id == user_id)
        )
        return result.rowcount

    async def cleanup_expired_sessions(self) -> int:
        """Delete expired sessions."""
        from datetime import datetime

        result = await self.db.execute(
            delete(UserSession).where(UserSession.expires_at < datetime.utcnow())
        )
        return result.rowcount
