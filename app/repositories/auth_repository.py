"""
Authentication repository for GeNieGO SSO Server.
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.auth import PasswordResetToken
from app.models.user import AuthorizationCode, RegisteredApplication
from app.repositories.base import BaseRepository


class AuthorizationCodeRepository(BaseRepository[AuthorizationCode]):
    """Repository for OAuth2 authorization code operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(AuthorizationCode, db)

    async def get_by_code(self, code: str) -> Optional[AuthorizationCode]:
        """Get authorization code by code value."""
        result = await self.db.execute(
            select(AuthorizationCode).where(AuthorizationCode.code == code)
        )
        return result.scalar_one_or_none()

    async def get_valid_code(
        self, code: str, client_id: str
    ) -> Optional[AuthorizationCode]:
        """Get valid (not expired, not used) authorization code."""
        result = await self.db.execute(
            select(AuthorizationCode).where(
                AuthorizationCode.code == code,
                AuthorizationCode.client_id == client_id,
                AuthorizationCode.expires_at > datetime.utcnow(),
                AuthorizationCode.used_at.is_(None),
            )
        )
        return result.scalar_one_or_none()

    async def cleanup_expired_codes(self) -> int:
        """Delete expired authorization codes."""
        result = await self.db.execute(
            delete(AuthorizationCode).where(
                AuthorizationCode.expires_at < datetime.utcnow()
            )
        )
        return result.rowcount


class ApplicationRepository(BaseRepository[RegisteredApplication]):
    """Repository for registered application operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(RegisteredApplication, db)

    async def get_by_client_id(self, client_id: str) -> Optional[RegisteredApplication]:
        """Get application by client ID."""
        result = await self.db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.client_id == client_id,
                RegisteredApplication.is_active == True,
            )
        )
        return result.scalar_one_or_none()

    async def get_active_applications(self) -> List[RegisteredApplication]:
        """Get all active applications."""
        result = await self.db.execute(
            select(RegisteredApplication)
            .where(RegisteredApplication.is_active == True)
            .order_by(RegisteredApplication.created_at.desc())
        )
        return result.scalars().all()

    async def client_id_exists(self, client_id: str) -> bool:
        """Check if client ID already exists."""
        result = await self.db.execute(
            select(RegisteredApplication.id).where(
                RegisteredApplication.client_id == client_id
            )
        )
        return result.scalar_one_or_none() is not None


class PasswordResetRepository(BaseRepository[PasswordResetToken]):
    """Repository for password reset token operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(PasswordResetToken, db)

    async def get_by_token(self, token: str) -> Optional[PasswordResetToken]:
        """Get password reset token by token value."""
        result = await self.db.execute(
            select(PasswordResetToken).where(PasswordResetToken.token == token)
        )
        return result.scalar_one_or_none()

    async def get_valid_token(self, token: str) -> Optional[PasswordResetToken]:
        """Get valid (not expired, not used) password reset token."""
        result = await self.db.execute(
            select(PasswordResetToken).where(
                PasswordResetToken.token == token,
                PasswordResetToken.expires_at > datetime.utcnow(),
                PasswordResetToken.used_at.is_(None),
            )
        )
        return result.scalar_one_or_none()

    async def cleanup_expired_tokens(self) -> int:
        """Delete expired password reset tokens."""
        result = await self.db.execute(
            delete(PasswordResetToken).where(
                PasswordResetToken.expires_at < datetime.utcnow()
            )
        )
        return result.rowcount

    async def delete_user_tokens(self, user_id: str) -> int:
        """Delete all password reset tokens for a user."""
        result = await self.db.execute(
            delete(PasswordResetToken).where(PasswordResetToken.user_id == user_id)
        )
        return result.rowcount
