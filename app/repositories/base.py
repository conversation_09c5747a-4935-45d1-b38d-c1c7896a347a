"""
Simple repository pattern for data access abstraction.
Provides basic CRUD operations for SQLAlchemy models.
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession

# Type variables for generic repository
T = TypeVar("T")  # Model type


class BaseRepository(Generic[T]):
    """
    Base repository class for SQLAlchemy models.
    Provides basic CRUD operations.
    """

    def __init__(self, model: type[T], db: AsyncSession):
        self.model = model
        self.db = db

    async def create(self, entity: T) -> T:
        """Create a new entity."""
        self.db.add(entity)
        await self.db.commit()
        await self.db.refresh(entity)
        return entity

    async def get_by_id(self, entity_id: str) -> Optional[T]:
        """Get entity by ID."""
        result = await self.db.execute(
            select(self.model).where(self.model.id == entity_id)
        )
        return result.scalar_one_or_none()

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get all entities with pagination."""
        result = await self.db.execute(select(self.model).offset(skip).limit(limit))
        return result.scalars().all()

    async def update(self, entity: T) -> T:
        """Update entity."""
        await self.db.commit()
        await self.db.refresh(entity)
        return entity

    async def delete(self, entity: T) -> bool:
        """Delete entity."""
        await self.db.delete(entity)
        await self.db.commit()
        return True

    async def count(self) -> int:
        """Count total entities."""
        result = await self.db.execute(select(self.model).count())
        return result.scalar()


class InMemoryRepository(Generic[T]):
    """
    In-memory repository for testing purposes.
    Provides basic CRUD operations without database persistence.
    """

    def __init__(self, model: type[T]):
        self.model = model
        self._data: Dict[str, T] = {}

    def create(self, entity: T) -> T:
        """Create a new entity."""
        # Handle both dict and object types
        if isinstance(entity, dict):
            entity_id = entity.get("id")
            if not entity_id:
                entity_id = str(len(self._data) + 1)
                entity["id"] = entity_id
        else:
            entity_id = getattr(entity, "id", None)
            if not entity_id:
                entity_id = str(len(self._data) + 1)
                if hasattr(entity, "id"):
                    entity.id = entity_id
        self._data[entity_id] = entity
        return entity

    def get_by_id(self, entity_id: str) -> Optional[T]:
        """Get entity by ID."""
        return self._data.get(entity_id)

    def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get all entities with pagination."""
        items = list(self._data.values())
        return items[skip : skip + limit]

    def update(self, entity: T) -> T:
        """Update entity."""
        # Handle both dict and object types
        if isinstance(entity, dict):
            entity_id = entity.get("id")
        else:
            entity_id = getattr(entity, "id")

        if entity_id and entity_id in self._data:
            self._data[entity_id] = entity
        return entity

    def delete(self, entity_or_id) -> bool:
        """Delete entity by entity object or ID."""
        # Handle both entity objects, dicts, and direct IDs
        if isinstance(entity_or_id, str):
            entity_id = entity_or_id
        elif isinstance(entity_or_id, dict):
            entity_id = entity_or_id.get("id")
        else:
            entity_id = getattr(entity_or_id, "id", None)

        if entity_id and entity_id in self._data:
            del self._data[entity_id]
            return True
        return False

    def count(self) -> int:
        """Count total entities."""
        return len(self._data)

    def clear(self):
        """Clear all data (for testing)."""
        self._data.clear()
