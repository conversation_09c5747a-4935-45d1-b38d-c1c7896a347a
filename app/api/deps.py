"""API-specific dependencies."""

from typing import Any

from fastapi.security import HTT<PERSON><PERSON>earer

from app.config import get_settings

security = HTTPBearer(auto_error=False)


def get_api_settings() -> Any:
    """Get API settings."""
    return get_settings()


# Add more API-specific dependencies here
# Example:
# def get_current_user(token: str = Depends(security)):
#     """Get current authenticated user."""
#     # Implement authentication logic
#     pass
