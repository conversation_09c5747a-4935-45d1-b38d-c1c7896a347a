"""
Organization application management API endpoints for GeNieGO SSO Server.

This module provides REST API endpoints for managing application assignments
to organizations, including assigning, updating, and removing applications.
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth_dependencies import get_current_user_from_session
from app.models import User
from app.schemas.organization import (
    ApplicationOrganizationCreate,
    ApplicationOrganizationResponse,
    ApplicationOrganizationUpdate,
)
from app.services.organization_service import OrganizationManager

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/{organization_id}/applications", response_model=List[ApplicationOrganizationResponse])
async def get_organization_applications(
    organization_id: str,
    include_inactive: bool = False,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all applications assigned to organization.
    
    User must be a member of the organization to view applications.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check if user is a member
        membership = await org_manager.get_user_membership(current_user.id, organization_id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this organization"
            )
        
        applications = await org_manager.get_organization_applications(
            organization_id, include_inactive=include_inactive
        )
        
        # Convert to response format with application details
        response_applications = []
        for app_org in applications:
            app_data = ApplicationOrganizationResponse.from_orm(app_org)
            if app_org.application:
                app_data.application_name = app_org.application.application_name
                app_data.application_client_id = app_org.application.client_id
            response_applications.append(app_data)
        
        return response_applications
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization applications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization applications"
        )


@router.post("/{organization_id}/applications", response_model=ApplicationOrganizationResponse, status_code=status.HTTP_201_CREATED)
async def assign_application_to_organization(
    organization_id: str,
    app_data: ApplicationOrganizationCreate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Assign application to organization.
    
    User must have manage_settings permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        assignment = await org_manager.assign_application_to_organization(
            application_id=app_data.application_id,
            organization_id=organization_id,
            assigner_id=current_user.id,
            role=app_data.role,
            description=app_data.description,
            settings=app_data.settings,
        )
        
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to assign application to organization"
            )
        
        return ApplicationOrganizationResponse.from_orm(assignment)
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to assign application to organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign application"
        )


@router.get("/applications/{application_id}/organizations", response_model=List[ApplicationOrganizationResponse])
async def get_application_organizations(
    application_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all organizations where application is assigned.
    
    User must be the application developer or have access to the application.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # TODO: Add application ownership/access check
        # For now, any authenticated user can view this
        
        organizations = await org_manager.get_application_organizations(application_id)
        
        # Convert to response format with organization details
        response_organizations = []
        for app_org in organizations:
            org_data = ApplicationOrganizationResponse.from_orm(app_org)
            if app_org.organization:
                # Only include basic organization info for security
                pass  # Organization details are already in the response
            response_organizations.append(org_data)
        
        return response_organizations
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get application organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve application organizations"
        )


@router.put("/{organization_id}/applications/{assignment_id}", response_model=ApplicationOrganizationResponse)
async def update_application_organization(
    organization_id: str,
    assignment_id: str,
    app_data: ApplicationOrganizationUpdate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Update application organization assignment.
    
    User must have manage_settings permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Convert Pydantic model to dict, excluding None values
        update_data = app_data.dict(exclude_unset=True)
        
        assignment = await org_manager.update_application_organization(
            assignment_id=assignment_id,
            updater_id=current_user.id,
            **update_data
        )
        
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application assignment not found"
            )
        
        return ApplicationOrganizationResponse.from_orm(assignment)
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update application organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update application assignment"
        )


@router.delete("/{organization_id}/applications/{assignment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_application_from_organization(
    organization_id: str,
    assignment_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Remove application from organization.
    
    User must have manage_settings permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        success = await org_manager.remove_application_from_organization(
            assignment_id=assignment_id,
            remover_id=current_user.id,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application assignment not found"
            )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove application from organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove application"
        )
