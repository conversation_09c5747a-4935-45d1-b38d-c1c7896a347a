"""
Role Transition API endpoints for GeNieGO SSO Server.

This module provides API endpoints for user role transitions,
particularly developer role applications and admin review workflows.
"""

import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.core.auth_dependencies import get_current_user_from_session
from app.core.database import get_db
from app.models.user import User, DeveloperApplication
from app.schemas.role_transitions import (
    DeveloperApplicationRequest,
    DeveloperApplicationResponse,
    DeveloperApplicationReviewRequest,
    DeveloperApplicationListResponse,
    DeveloperApplicationWithUser,
    UserInfo,
)
from app.services.role_transition_service import RoleTransitionManager

router = APIRouter(tags=["Role Transitions"])
logger = logging.getLogger(__name__)


def get_role_transition_manager(db: AsyncSession = Depends(get_db)) -> RoleTransitionManager:
    """Dependency to get role transition manager service."""
    return RoleTransitionManager(db)


@router.post(
    "/developer-application",
    response_model=DeveloperApplicationResponse,
    summary="Submit Developer Application",
    description="Submit an application to upgrade from user to developer role",
)
async def submit_developer_application(
    request: DeveloperApplicationRequest,
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
) -> DeveloperApplicationResponse:
    """Submit a developer role application."""
    try:
        application = await role_manager.submit_developer_application(
            user=current_user,
            application_reason=request.application_reason,
            technical_background=request.technical_background,
            intended_use=request.intended_use
        )

        return DeveloperApplicationResponse(
            id=application.id,
            status=application.status,
            application_reason=application.application_reason,
            technical_background=application.technical_background,
            intended_use=application.intended_use,
            created_at=application.created_at.isoformat(),
            message="Developer application submitted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting developer application for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit developer application"
        )


@router.get(
    "/my-applications",
    response_model=List[DeveloperApplicationResponse],
    summary="Get My Applications",
    description="Get all developer applications submitted by the current user",
)
async def get_my_applications(
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
) -> List[DeveloperApplicationResponse]:
    """Get all applications submitted by the current user."""
    try:
        applications = await role_manager.get_user_applications(current_user.id)
        
        return [
            DeveloperApplicationResponse(
                id=app["id"],
                status=app["status"],
                application_reason=app["application_reason"],
                technical_background=app["technical_background"],
                intended_use=app["intended_use"],
                admin_notes=app["admin_notes"],
                reviewed_by=app["reviewed_by"],
                reviewed_at=app["reviewed_at"],
                created_at=app["created_at"],
                updated_at=app["updated_at"]
            )
            for app in applications
        ]

    except Exception as e:
        logger.error(f"Error getting applications for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get applications"
        )


@router.delete(
    "/applications/{application_id}",
    summary="Withdraw Application",
    description="Withdraw a pending developer application",
)
async def withdraw_application(
    application_id: str,
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
) -> Dict[str, str]:
    """Withdraw a pending developer application."""
    try:
        success = await role_manager.withdraw_application(
            user=current_user,
            application_id=application_id
        )

        if success:
            return {"message": "Application withdrawn successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error withdrawing application {application_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to withdraw application"
        )


# Admin endpoints

@router.get(
    "/admin/pending-applications",
    response_model=DeveloperApplicationListResponse,
    summary="Get Pending Applications (Admin)",
    description="Get all pending developer applications for admin review",
)
async def get_pending_applications(
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
    limit: int = Query(default=50, ge=1, le=100),
    offset: int = Query(default=0, ge=0),
) -> DeveloperApplicationListResponse:
    """Get all pending developer applications for admin review."""
    # Verify admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    try:
        applications = await role_manager.get_pending_applications(
            limit=limit,
            offset=offset
        )
        
        application_responses = [
            DeveloperApplicationResponse(
                id=app["id"],
                user_id=app["user_id"],
                status=app["status"],
                application_reason=app["application_reason"],
                technical_background=app["technical_background"],
                intended_use=app["intended_use"],
                created_at=app["created_at"]
            )
            for app in applications
        ]

        return DeveloperApplicationListResponse(
            applications=application_responses,
            total=len(application_responses),
            limit=limit,
            offset=offset
        )

    except Exception as e:
        logger.error(f"Error getting pending applications: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get pending applications"
        )


@router.get(
    "/admin/applications",
    response_model=List[DeveloperApplicationWithUser],
    summary="Get All Applications (Admin)",
    description="Get all developer applications for admin review",
)
async def get_all_applications(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status: Optional[str] = Query(None, regex="^(pending|approved|rejected)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user_from_session),
):
    """Get all developer applications for admin review"""
    try:
        # Check if user is admin
        if current_user.role != "admin":
            raise HTTPException(
                status_code=403,
                detail="Only admins can view all applications"
            )

        # Build query
        query = select(DeveloperApplication)

        # Filter by status if provided
        if status:
            query = query.where(DeveloperApplication.status == status)

        # Apply pagination
        query = query.offset(offset).limit(limit)
        result = await db.execute(query)
        applications = result.scalars().all()

        # Convert to response format with user data
        result = []
        for app in applications:
            user_query = select(User).where(User.id == app.user_id)
            user_result = await db.execute(user_query)
            user_data = user_result.scalar_one_or_none()

            if user_data:
                result.append(DeveloperApplicationWithUser(
                    id=app.id,
                    user_id=app.user_id,
                    reason=app.application_reason,  # Fixed field name
                    technical_background=app.technical_background,
                    intended_use=app.intended_use,
                    status=app.status,
                    admin_notes=app.admin_notes,
                    reviewed_by=app.reviewed_by,
                    created_at=app.created_at.isoformat() if app.created_at else None,  # Convert to string
                    updated_at=app.updated_at.isoformat() if app.updated_at else None,  # Convert to string
                    user=UserInfo(
                        id=user_data.id,
                        username=user_data.username,
                        email=user_data.email,
                        role=user_data.role,
                        created_at=user_data.created_at.isoformat() if user_data.created_at else None,
                        last_login=user_data.last_login.isoformat() if user_data.last_login else None,
                    )
                ))

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get all applications: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get applications"
        )


@router.post(
    "/admin/applications/{application_id}/review",
    response_model=Dict[str, str],
    summary="Review Application (Admin)",
    description="Approve or reject a developer application",
)
async def review_application(
    application_id: str,
    review_request: DeveloperApplicationReviewRequest,
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
) -> Dict[str, str]:
    """Review a developer application (admin only)."""
    # Verify admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    try:
        success = await role_manager.review_application(
            application_id=application_id,
            reviewer=current_user,
            decision=review_request.decision,
            admin_notes=review_request.admin_notes
        )

        if success:
            return {
                "message": f"Application {review_request.decision} successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reviewing application {application_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to review application"
        )


@router.get(
    "/admin/applications/{application_id}",
    response_model=DeveloperApplicationResponse,
    summary="Get Application Details (Admin)",
    description="Get detailed information about a specific application",
)
async def get_application_details(
    application_id: str,
    current_user: User = Depends(get_current_user_from_session),
    role_manager: RoleTransitionManager = Depends(get_role_transition_manager),
) -> DeveloperApplicationResponse:
    """Get detailed information about a specific application (admin only)."""
    # Verify admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    try:
        application = await role_manager.get_application_by_id(application_id)
        
        if not application:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )

        return DeveloperApplicationResponse(
            id=application["id"],
            user_id=application["user_id"],
            status=application["status"],
            application_reason=application["application_reason"],
            technical_background=application["technical_background"],
            intended_use=application["intended_use"],
            admin_notes=application["admin_notes"],
            reviewed_by=application["reviewed_by"],
            reviewed_at=application["reviewed_at"],
            created_at=application["created_at"],
            updated_at=application["updated_at"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting application {application_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get application details"
        )
