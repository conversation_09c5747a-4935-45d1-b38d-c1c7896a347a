"""
User management endpoints for GeNieGO SSO Server.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.core.auth_dependencies import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.common import MessageResponse
from app.schemas.user import (
    UserListResponse,
    UserProfileUpdateRequest,
    UserResponse,
    UserUpdate,
)
from app.services.auth_service import AuthService

router = APIRouter(tags=["User Management"])
settings = get_settings()
logger = logging.getLogger(__name__)


def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Dependency to get authentication service."""
    return AuthService(db)


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get Current User",
    description="Get current authenticated user information",
)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> UserResponse:
    """Get current authenticated user information."""
    try:
        return UserResponse.model_validate(current_user)
    except Exception as e:
        logger.error(f"Error getting current user info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/",
    response_model=UserListResponse,
    summary="List Users",
    description="Get a paginated list of all users",
)
async def list_users(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_verified: Optional[bool] = Query(
        None, description="Filter by verification status"
    ),
    search: Optional[str] = Query(None, description="Search by username or email"),
    db: AsyncSession = Depends(get_db),
    auth_service: AuthService = Depends(get_auth_service),
) -> UserListResponse:
    """Get a paginated list of users."""
    try:
        users = await auth_service.list_users(
            page=page,
            per_page=per_page,
            is_active=is_active,
            is_verified=is_verified,
            search=search,
        )

        # Get total count for pagination
        total_count = await auth_service.get_users_count(
            is_active=is_active, is_verified=is_verified, search=search
        )

        return UserListResponse(
            users=[UserResponse.from_orm(user) for user in users],
            total=total_count,
            page=page,
            per_page=per_page,
        )
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/{user_id}",
    response_model=UserResponse,
    summary="Get User",
    description="Get a specific user by ID",
)
async def get_user(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Get a specific user by ID."""
    try:
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return UserResponse.from_orm(user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/{user_id}",
    response_model=UserResponse,
    summary="Update User",
    description="Update a specific user",
)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Update a specific user."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        updated_user = await auth_service.update_user(user_id, user_data)
        return UserResponse.from_orm(updated_user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/{user_id}",
    response_model=MessageResponse,
    summary="Delete User",
    description="Delete a specific user (soft delete)",
)
async def delete_user(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> MessageResponse:
    """Delete a specific user (soft delete)."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Soft delete by deactivating the user
        await auth_service.deactivate_user(user_id)

        return MessageResponse(message=f"User {user_id} has been deactivated")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/{user_id}/profile",
    response_model=UserResponse,
    summary="Get User Profile",
    description="Get user profile information",
)
async def get_user_profile(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Get user profile information."""
    try:
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return UserResponse.from_orm(user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/{user_id}/profile",
    response_model=UserResponse,
    summary="Update User Profile",
    description="Update user profile information",
)
async def update_user_profile(
    user_id: str,
    profile_data: UserProfileUpdateRequest,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Update user profile information."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        updated_user = await auth_service.update_user_profile(user_id, profile_data)
        return UserResponse.from_orm(updated_user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/{user_id}/activate",
    response_model=MessageResponse,
    summary="Activate User",
    description="Activate a user account",
)
async def activate_user(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> MessageResponse:
    """Activate a user account."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        await auth_service.activate_user(user_id)

        return MessageResponse(message=f"User {user_id} has been activated")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/{user_id}/deactivate",
    response_model=MessageResponse,
    summary="Deactivate User",
    description="Deactivate a user account",
)
async def deactivate_user(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> MessageResponse:
    """Deactivate a user account."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        await auth_service.deactivate_user(user_id)

        return MessageResponse(message=f"User {user_id} has been deactivated")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
