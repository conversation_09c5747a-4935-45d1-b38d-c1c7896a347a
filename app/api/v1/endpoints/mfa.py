"""
Multi-Factor Authentication API endpoints for GeNieGO SSO Server.

This module provides API endpoints for MFA device management, TOTP setup,
verification, and backup code management.
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth_dependencies import get_current_user_from_session
from app.core.database import get_db
from app.models.user import User
from app.schemas.mfa import (
    MFADeviceResponse,
    MFASetupRequest,
    MFASetupResponse,
    MFAVerificationRequest,
    MFAVerificationResponse,
    BackupCodeVerificationRequest,
)
from app.services.mfa_service import MFAManager

router = APIRouter(tags=["Multi-Factor Authentication"])
logger = logging.getLogger(__name__)


def get_mfa_manager(db: AsyncSession = Depends(get_db)) -> MFAManager:
    """Dependency to get MFA manager service."""
    return MFAManager(db)


@router.post(
    "/setup",
    response_model=MFASetupResponse,
    summary="Set up TOTP MFA device",
    description="Initialize TOTP MFA device setup for the authenticated user",
)
async def setup_mfa_device(
    request: MFASetupRequest,
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> MFASetupResponse:
    """Set up a new TOTP MFA device for the user."""
    try:
        setup_data = await mfa_manager.setup_totp_device(
            user=current_user,
            device_name=request.device_name,
            issuer_name=request.issuer_name or "GeNieGO SSO"
        )

        return MFASetupResponse(
            device_id=setup_data["device_id"],
            qr_code=setup_data["qr_code"],
            backup_codes=setup_data["backup_codes"],
            manual_entry_key=setup_data["secret"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MFA setup error for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set up MFA device"
        )


@router.post(
    "/verify-setup",
    response_model=MFAVerificationResponse,
    summary="Verify TOTP setup",
    description="Verify TOTP code to complete MFA device setup",
)
async def verify_mfa_setup(
    request: MFAVerificationRequest,
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> MFAVerificationResponse:
    """Verify TOTP code to complete MFA device setup."""
    try:
        is_valid = await mfa_manager.verify_totp_setup(
            user=current_user,
            device_id=request.device_id,
            totp_code=request.code
        )

        return MFAVerificationResponse(
            valid=is_valid,
            message="MFA device verified successfully" if is_valid else "Invalid TOTP code"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MFA setup verification error for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify MFA setup"
        )


@router.post(
    "/verify",
    response_model=MFAVerificationResponse,
    summary="Verify TOTP code",
    description="Verify TOTP code for authentication",
)
async def verify_mfa_code(
    request: MFAVerificationRequest,
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> MFAVerificationResponse:
    """Verify TOTP code for authentication."""
    try:
        is_valid = await mfa_manager.verify_totp_code(
            user=current_user,
            totp_code=request.code,
            device_id=request.device_id
        )

        return MFAVerificationResponse(
            valid=is_valid,
            message="TOTP code verified successfully" if is_valid else "Invalid TOTP code"
        )

    except Exception as e:
        logger.error(f"MFA verification error for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify TOTP code"
        )


@router.post(
    "/verify-backup",
    response_model=MFAVerificationResponse,
    summary="Verify backup code",
    description="Verify and consume a backup code for authentication",
)
async def verify_backup_code(
    request: BackupCodeVerificationRequest,
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> MFAVerificationResponse:
    """Verify and consume a backup code for authentication."""
    try:
        is_valid = await mfa_manager.verify_backup_code(
            user=current_user,
            backup_code=request.backup_code
        )

        return MFAVerificationResponse(
            valid=is_valid,
            message="Backup code verified successfully" if is_valid else "Invalid backup code"
        )

    except Exception as e:
        logger.error(f"Backup code verification error for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify backup code"
        )


@router.get(
    "/devices",
    response_model=List[MFADeviceResponse],
    summary="Get user MFA devices",
    description="Get all MFA devices for the authenticated user",
)
async def get_user_mfa_devices(
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> List[MFADeviceResponse]:
    """Get all MFA devices for the authenticated user."""
    try:
        devices = await mfa_manager.get_user_devices(current_user.id)
        
        return [
            MFADeviceResponse(
                id=device["id"],
                device_name=device["device_name"],
                device_type=device["device_type"],
                is_active=device["is_active"],
                is_verified=device["is_verified"],
                last_used_at=device["last_used_at"],
                created_at=device["created_at"],
                backup_codes_count=device["backup_codes_count"]
            )
            for device in devices
        ]

    except Exception as e:
        logger.error(f"Error getting MFA devices for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA devices"
        )


@router.delete(
    "/devices/{device_id}",
    summary="Remove MFA device",
    description="Remove an MFA device and its backup codes",
)
async def remove_mfa_device(
    device_id: str,
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> Dict[str, str]:
    """Remove an MFA device and its backup codes."""
    try:
        success = await mfa_manager.remove_device(
            user=current_user,
            device_id=device_id
        )

        if success:
            return {"message": "MFA device removed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MFA device not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing MFA device {device_id} for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove MFA device"
        )


@router.get(
    "/challenge",
    summary="Get MFA challenge",
    description="Get MFA challenge information for authentication flow",
)
async def get_mfa_challenge(
    current_user: User = Depends(get_current_user_from_session),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
) -> Dict[str, Any]:
    """Get MFA challenge information for authentication flow."""
    try:
        devices = await mfa_manager.get_user_devices(current_user.id)
        active_devices = [d for d in devices if d["is_active"] and d["is_verified"]]

        if not active_devices:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active MFA devices found"
            )

        return {
            "challenge_required": True,
            "available_methods": ["totp", "backup_code"],
            "device_count": len(active_devices),
            "backup_codes_available": any(d["backup_codes_count"] > 0 for d in active_devices)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MFA challenge for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA challenge"
        )
