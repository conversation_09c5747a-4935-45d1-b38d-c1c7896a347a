"""
GeNieGO SSO Server OAuth2 Authorization Endpoints.

This module implements OAuth2 authorization server endpoints for GeNieGO SSO Server.
It provides authorization codes to applications and user information endpoints.
"""

import logging
from typing import Any, Dict, Optional
from urllib.parse import urlencode

from fastapi import (
    APIRouter,
    Depends,
    Form,
    HTTPException,
    Query,
    Request,
)
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.core.database import get_db
from app.schemas.auth import (
    OAuth2AuthorizeRequest,
    OAuth2TokenResponse,
    OAuth2UserInfoRequest,
    OAuth2UserInfoResponse,
    SSOErrorResponse,
)
from app.services.consent_service import ConsentManager
from app.services.session_service import CentralSessionManager, DeviceInfo
from app.services.auth_service import AuthService
from app.services.connections.activity_logger import ActivityLoggerService
from app.services.connections.connection_manager import ConnectionManagerService

router = APIRouter(tags=["GeNieGO OAuth2 Authorization"])
settings = get_settings()
logger = logging.getLogger(__name__)


def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Dependency to get authentication service."""
    return AuthService(db)


def get_connection_manager(
    db: AsyncSession = Depends(get_db),
) -> ConnectionManagerService:
    """Dependency to get connection manager service."""
    return ConnectionManagerService(db)


def get_activity_logger(db: AsyncSession = Depends(get_db)) -> ActivityLoggerService:
    """Dependency to get activity logger service."""
    return ActivityLoggerService(db)


def get_oauth2_services(db: AsyncSession = Depends(get_db)):
    """Get all OAuth2 services with shared database session."""
    return {
        "auth_service": AuthService(db),
        "connection_manager": ConnectionManagerService(db),
        "activity_logger": ActivityLoggerService(db),
        "db": db,
    }


def get_current_user_session(
    request: Request, auth_service: AuthService = Depends(get_auth_service)
):
    """Get current user session from cookies."""
    session_token = request.cookies.get("geniengo_session")
    if not session_token:
        return None

    # This would be async in real implementation
    # For now, we'll handle it in the endpoint
    return session_token


def validate_oauth2_authorize_params(
    client_id: str = Query(..., description="Application client ID"),
    redirect_uri: str = Query(..., description="Redirect URI"),
    response_type: str = Query(..., description="Response type (must be 'code')"),
    scope: str = Query(default="openid profile", description="Requested scopes"),
    state: Optional[str] = Query(None, description="State parameter"),
    code_challenge: Optional[str] = Query(None, description="PKCE code challenge"),
    code_challenge_method: Optional[str] = Query(
        default="S256", description="PKCE method"
    ),
) -> OAuth2AuthorizeRequest:
    """Validate OAuth2 authorize parameters using Pydantic model."""
    try:
        return OAuth2AuthorizeRequest(
            client_id=client_id,
            redirect_uri=redirect_uri,
            response_type=response_type,
            scope=scope,
            state=state,
            code_challenge=code_challenge,
            code_challenge_method=code_challenge_method,
        )
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))


@router.get(
    "/authorize",
    summary="OAuth2 Authorization Endpoint",
    description="OAuth2 authorization endpoint for application authentication requests",
)
async def oauth2_authorize(
    request: Request,
    oauth2_params: OAuth2AuthorizeRequest = Depends(validate_oauth2_authorize_params),
    services: dict = Depends(get_oauth2_services),
) -> Any:
    """
    OAuth2 Authorization Endpoint for GeNieGO SSO Server.

    This is where application OAuth2 servers (like GENIE-MOVE-AUTH-V4) redirect users for authentication.
    If user is not logged in, redirects to login page.
    If logged in, generates authorization code and redirects back.
    """
    # Extract services from shared session
    auth_service = services["auth_service"]
    connection_manager = services["connection_manager"]
    activity_logger = services["activity_logger"]
    db = services["db"]

    try:
        # Extract parameters from validated request
        client_id = oauth2_params.client_id
        redirect_uri = oauth2_params.redirect_uri
        response_type = oauth2_params.response_type
        scope = oauth2_params.scope
        state = oauth2_params.state
        code_challenge = oauth2_params.code_challenge
        code_challenge_method = oauth2_params.code_challenge_method

        # Validate application (client)
        application = await auth_service.validate_application(client_id)
        if not application:
            error_params = {
                "error": "invalid_client",
                "error_description": "Invalid client_id",
                "state": state or "",
            }
            error_url = f"{redirect_uri}?{urlencode(error_params)}"
            return RedirectResponse(url=error_url, status_code=302)

        # Validate redirect URI
        if not application.is_redirect_uri_allowed(redirect_uri):
            raise HTTPException(status_code=400, detail="Invalid redirect_uri")

        # Check if user is logged in
        session_token = request.cookies.get("geniengo_session")
        current_session = None

        if session_token:
            current_session = await auth_service.validate_session(session_token)

        if not current_session:
            # User not logged in - redirect to login page with OAuth2 parameters
            login_params = {
                "client_id": client_id,
                "redirect_uri": redirect_uri,
                "response_type": response_type,
                "scope": scope,
                "state": state or "",
                "code_challenge": code_challenge or "",
                "code_challenge_method": code_challenge_method or "",
            }
            login_url = f"/auth/login?{urlencode(login_params)}"
            return RedirectResponse(url=login_url, status_code=302)

        # User is logged in - check consent before generating authorization code
        scopes = scope.split() if scope else ["openid", "profile"]

        # Get application for connection tracking
        application = await connection_manager.get_application_by_client_id(client_id)
        if not application:
            error_params = {
                "error": "invalid_client",
                "error_description": "Invalid client_id",
                "state": state or "",
            }
            error_url = f"{redirect_uri}?{urlencode(error_params)}"
            return RedirectResponse(url=error_url, status_code=302)

        # Check if user has granted consent for requested scopes
        consent_manager = ConsentManager(db)
        has_consent = await consent_manager.check_consent(
            user_id=current_session.user.id,
            application_id=application.id,
            required_scopes=scopes,
        )

        if not has_consent:
            # User hasn't granted consent - redirect to consent screen
            consent_params = {
                "client_id": client_id,
                "redirect_uri": redirect_uri,
                "response_type": response_type,
                "scope": scope,
                "state": state or "",
                "code_challenge": code_challenge or "",
                "code_challenge_method": code_challenge_method or "",
                "application_name": application.name,
                "requested_scopes": ",".join(scopes),
            }
            consent_url = f"/auth/consent?{urlencode(consent_params)}"
            return RedirectResponse(url=consent_url, status_code=302)

        # Extract client information for logging
        ip_address, user_agent = activity_logger.extract_client_info(request)

        try:
            # Create authorization code
            auth_code = await auth_service.create_authorization_code(
                user=current_session.user,
                client_id=client_id,
                redirect_uri=redirect_uri,
                scopes=scopes,
                code_challenge=code_challenge,
                code_challenge_method=code_challenge_method,
            )

            # Create or update user-application connection (no auto-commit)
            await connection_manager.create_or_update_connection(
                user_id=current_session.user.id,
                application_id=application.id,
                client_id=client_id,
                granted_scopes=scopes,
                ip_address=ip_address,
                auto_commit=False,
            )

            # Log the authorization activity (no auto-commit)
            await activity_logger.log_oauth2_authorization(
                user_id=current_session.user.id,
                application_id=application.id,
                client_id=client_id,
                ip_address=ip_address,
                user_agent=user_agent,
                success=True,
            )

            # Commit all changes together
            await db.commit()

        except Exception as e:
            logger.error(f"Error during OAuth2 authorization: {e}")

            # Rollback any partial changes
            await db.rollback()

            # Log the failure in a separate transaction
            try:
                await activity_logger.log_oauth2_authorization(
                    user_id=current_session.user.id,
                    application_id=application.id,
                    client_id=client_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message=str(e),
                )
                await db.commit()
            except Exception as log_error:
                logger.error(f"Failed to log authorization failure: {log_error}")
                await db.rollback()

            error_params = {
                "error": "server_error",
                "error_description": "Internal server error during authorization",
                "state": state or "",
            }
            error_url = f"{redirect_uri}?{urlencode(error_params)}"
            return RedirectResponse(url=error_url, status_code=302)

        # Redirect back to application with authorization code
        redirect_params = {
            "code": auth_code.code,
        }
        if state:
            redirect_params["state"] = state

        redirect_url = f"{redirect_uri}?{urlencode(redirect_params)}"
        return RedirectResponse(url=redirect_url, status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth2 authorization endpoint error: {e}")
        error_params = {
            "error": "server_error",
            "error_description": "Internal server error",
            "state": state or "",
        }
        error_url = f"{redirect_uri}?{urlencode(error_params)}"
        return RedirectResponse(url=error_url, status_code=302)


@router.post(
    "/userinfo",
    response_model=OAuth2UserInfoResponse,
    summary="OAuth2 User Information Endpoint",
    description="Used by application OAuth2 servers to get user information after exchanging authorization code",
)
async def oauth2_userinfo(
    request: Request,
    authorization_code: str = Form(..., description="Authorization code"),
    client_id: str = Form(..., description="Client ID"),
    code_verifier: Optional[str] = Form(None, description="PKCE code verifier"),
    services: dict = Depends(get_oauth2_services),
) -> OAuth2UserInfoResponse:
    """
    User Information Endpoint for GeNieGO SSO Server.

    Used by application OAuth2 servers (like GENIE-MOVE-AUTH-V4) to get user information
    after exchanging authorization code.
    """
    # Extract services from shared session
    auth_service = services["auth_service"]
    connection_manager = services["connection_manager"]
    activity_logger = services["activity_logger"]
    db = services["db"]

    # Get application for logging
    application = await connection_manager.get_application_by_client_id(client_id)
    if not application:
        raise HTTPException(status_code=400, detail="Invalid client_id")

    # Extract client information for logging
    ip_address, user_agent = activity_logger.extract_client_info(request)

    # Validate and consume authorization code
    auth_code = await auth_service.validate_and_consume_authorization_code(
        code=authorization_code, client_id=client_id, code_verifier=code_verifier
    )

    if not auth_code:
        # Log failed userinfo access
        await activity_logger.log_user_info_access(
            user_id=None,
            application_id=application.id,
            client_id=client_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=False,
            error_message="Invalid authorization code",
        )
        await db.commit()
        raise HTTPException(status_code=400, detail="Invalid authorization code")

    try:

        # Get user information
        user = await auth_service.get_user_by_id(auth_code.user_id)
        if not user:
            # Log failed userinfo access
            await activity_logger.log_user_info_access(
                user_id=auth_code.user_id,
                application_id=application.id,
                client_id=client_id,
                ip_address=ip_address,
                user_agent=user_agent,
                success=False,
                error_message="User not found",
            )
            raise HTTPException(status_code=400, detail="User not found")

        # Update connection access (no auto-commit)
        await connection_manager.update_access(
            user_id=user.id,
            application_id=application.id,
            auto_commit=False,
        )

        # Log successful userinfo access (no auto-commit)
        await activity_logger.log_user_info_access(
            user_id=user.id,
            application_id=application.id,
            client_id=client_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True,
        )

        # Commit all changes together
        await db.commit()

        return OAuth2UserInfoResponse(
            sub=user.id,
            email=user.email,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            is_verified=user.is_verified,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth2 userinfo endpoint error: {e}")
        # Rollback any partial changes
        await db.rollback()
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/token",
    response_model=OAuth2TokenResponse,
    summary="OAuth2 Token Endpoint",
    description="Exchange authorization code for access token (for applications that need tokens)",
)
async def oauth2_token(
    request: Request,
    grant_type: str = Form(..., description="Grant type (authorization_code)"),
    code: str = Form(..., description="Authorization code"),
    client_id: str = Form(..., description="Client ID"),
    client_secret: Optional[str] = Form(None, description="Client secret"),
    redirect_uri: str = Form(..., description="Redirect URI"),
    code_verifier: Optional[str] = Form(None, description="PKCE code verifier"),
    services: dict = Depends(get_oauth2_services),
) -> OAuth2TokenResponse:
    """
    OAuth2 Token Endpoint for GeNieGO SSO Server.

    This endpoint is provided for applications that need access tokens,
    though the primary flow is authorization code -> user info.
    """
    # Extract services from shared session
    auth_service = services["auth_service"]
    connection_manager = services["connection_manager"]
    activity_logger = services["activity_logger"]
    db = services["db"]

    try:
        # Extract client information for logging
        ip_address, user_agent = activity_logger.extract_client_info(request)

        # Validate grant type
        if grant_type != "authorization_code":
            raise HTTPException(
                status_code=400,
                detail="unsupported_grant_type",
                headers={"Cache-Control": "no-store", "Pragma": "no-cache"},
            )

        # Validate client
        application = await auth_service.validate_application(client_id)
        if not application:
            raise HTTPException(
                status_code=401,
                detail="invalid_client",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Validate client secret - required for confidential clients
        if application.client_secret_hash:
            # Client has a secret configured, so it's required
            if not client_secret:
                raise HTTPException(
                    status_code=401,
                    detail="invalid_client",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            if not application.verify_client_secret(client_secret):
                raise HTTPException(
                    status_code=401,
                    detail="invalid_client",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        # Validate and consume authorization code
        auth_code = await auth_service.validate_and_consume_authorization_code(
            code=code, client_id=client_id, code_verifier=code_verifier
        )

        if not auth_code:
            # Log failed token exchange
            await activity_logger.log_token_exchange(
                user_id=None,
                application_id=application.id,
                client_id=client_id,
                ip_address=ip_address,
                user_agent=user_agent,
                success=False,
                error_message="Invalid authorization code",
            )
            await db.commit()
            raise HTTPException(status_code=400, detail="invalid_grant")

        try:

            # Validate redirect URI matches
            if auth_code.redirect_uri != redirect_uri:
                # Log failed token exchange
                await activity_logger.log_token_exchange(
                    user_id=auth_code.user_id,
                    application_id=application.id,
                    client_id=client_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Redirect URI mismatch",
                )
                await db.commit()
                raise HTTPException(status_code=400, detail="invalid_grant")

            # Get user
            user = await auth_service.get_user_by_id(auth_code.user_id)
            if not user:
                # Log failed token exchange
                await activity_logger.log_token_exchange(
                    user_id=auth_code.user_id,
                    application_id=application.id,
                    client_id=client_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="User not found",
                )
                await db.commit()
                raise HTTPException(status_code=400, detail="invalid_grant")

            # Update connection access (no auto-commit)
            await connection_manager.update_access(
                user_id=user.id,
                application_id=application.id,
                auto_commit=False,
            )

            # Generate access token (JWT)
            from datetime import timedelta

            from app.core.security import create_access_token

            access_token = create_access_token(
                data={"sub": user.id, "client_id": client_id},
                expires_delta=timedelta(hours=1),
            )

            # Create session binding for OAuth2 token
            session_manager = CentralSessionManager(db)
            user_sessions = await session_manager.get_user_sessions(user.id)

            if user_sessions:
                # Use the most recent SSO session
                sso_session = user_sessions[0]

                # Create session binding
                from app.models import SessionBinding
                binding = SessionBinding(
                    sso_session_id=sso_session.id,
                    application_id=application.id,
                    oauth_token_id=access_token[:32],  # Use first 32 chars as token ID
                    token_type="bearer",
                    expires_with_sso=True,
                )
                db.add(binding)

                logger.info(
                    f"Created session binding for SSO session {sso_session.id} "
                    f"and application {application.id}"
                )
            else:
                logger.warning(f"No SSO session found for user {user.id} during OAuth2 token exchange")

            # Log successful token exchange (no auto-commit)
            await activity_logger.log_token_exchange(
                user_id=user.id,
                application_id=application.id,
                client_id=client_id,
                ip_address=ip_address,
                user_agent=user_agent,
                success=True,
            )

            # Commit all changes together
            await db.commit()

            return OAuth2TokenResponse(
                access_token=access_token,
                token_type="Bearer",
                expires_in=3600,  # 1 hour
                scope=(
                    " ".join(auth_code.scopes) if auth_code.scopes else "openid profile"
                ),
            )

        except Exception as e:
            logger.error(f"OAuth2 token endpoint error: {e}")
            # Rollback any partial changes
            await db.rollback()
            raise HTTPException(
                status_code=500,
                detail="server_error",
                headers={"Cache-Control": "no-store", "Pragma": "no-cache"},
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth2 token endpoint error: {e}")
        raise HTTPException(
            status_code=500,
            detail="server_error",
            headers={"Cache-Control": "no-store", "Pragma": "no-cache"},
        )


@router.post(
    "/logout",
    summary="Single Logout (SLO) Endpoint",
    description="Single logout endpoint that invalidates SSO session and notifies all connected applications",
)
async def oauth2_logout(
    request: Request,
    session_id: Optional[str] = Form(None, description="SSO session ID"),
    redirect_uri: Optional[str] = Form(None, description="Post-logout redirect URI"),
    services: dict = Depends(get_oauth2_services),
) -> Any:
    """
    Single Logout (SLO) Endpoint for GeNieGO SSO Server.

    This endpoint handles centralized logout by:
    1. Invalidating the SSO session
    2. Notifying all connected applications
    3. Cleaning up OAuth2 tokens
    4. Redirecting to appropriate logout page
    """
    try:
        # Extract services from shared session
        auth_service = services["auth_service"]
        db = services["db"]

        # Initialize session manager
        session_manager = CentralSessionManager(db)

        # Get current user session if session_id not provided
        if not session_id:
            # Try to get session from current request
            current_user = await auth_service.get_current_user_optional(request)
            if current_user:
                # Get user's active sessions
                user_sessions = await session_manager.get_user_sessions(current_user.id)
                if user_sessions:
                    session_id = user_sessions[0].id  # Use most recent session

        logout_result = None
        if session_id:
            # Perform single logout
            logout_result = await session_manager.propagate_logout(session_id)

            if logout_result.success:
                logger.info(
                    f"Single logout successful for session {session_id}, "
                    f"notified {logout_result.applications_notified} applications"
                )
            else:
                logger.error(f"Single logout failed for session {session_id}: {logout_result.error}")

        # Also invalidate current session cookie if exists
        try:
            await auth_service.logout_user(request)
        except Exception as e:
            logger.warning(f"Failed to invalidate session cookie: {e}")

        # Determine redirect URL
        if redirect_uri:
            # Validate redirect URI against registered applications
            # TODO: Add redirect URI validation
            return RedirectResponse(url=redirect_uri, status_code=302)
        else:
            # Redirect to logout success page
            logout_status = "success" if (logout_result and logout_result.success) else "partial"
            apps_notified = logout_result.applications_notified if logout_result else 0
            failed_apps = len(logout_result.failed_notifications) if logout_result else 0

            logout_url = (
                f"/auth/logout-success"
                f"?status={logout_status}"
                f"&apps_notified={apps_notified}"
                f"&failed_apps={failed_apps}"
            )
            return RedirectResponse(url=logout_url, status_code=302)

    except Exception as e:
        logger.error(f"OAuth2 logout endpoint error: {e}")
        # Redirect to logout error page
        return RedirectResponse(url="/auth/logout-error", status_code=302)
