"""
Organization management API endpoints for GeNieGO SSO Server.

This module provides REST API endpoints for organization CRUD operations,
including creation, retrieval, updating, and deletion of organizations.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth_dependencies import get_current_user_from_session
from app.models import User
from app.schemas.organization import (
    OrganizationCreate,
    OrganizationResponse,
    OrganizationUpdate,
)
from app.services.organization_service import OrganizationManager

logger = logging.getLogger(__name__)

router = APIRouter()

# Include organization sub-endpoints
from app.api.v1.endpoints import organization_members, organization_invitations, organization_applications
router.include_router(organization_members.router, tags=["organization-members"])
router.include_router(organization_invitations.router, tags=["organization-invitations"])
router.include_router(organization_applications.router, tags=["organization-applications"])


@router.post("/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization_data: OrganizationCreate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a new organization.
    
    The current user will become the owner of the organization.
    Organization slug must be unique across the system.
    """
    try:
        org_manager = OrganizationManager(db)
        
        organization = await org_manager.create_organization(
            creator=current_user,
            name=organization_data.name,
            slug=organization_data.slug,
            description=organization_data.description,
            website=organization_data.website,
            is_public=organization_data.is_public,
            max_members=organization_data.max_members,
            settings=organization_data.settings,
        )
        
        return OrganizationResponse.from_orm(organization)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.get("/", response_model=List[OrganizationResponse])
async def get_user_organizations(
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all organizations where the current user is a member.
    """
    try:
        org_manager = OrganizationManager(db)
        organizations = await org_manager.get_user_organizations(current_user.id)

        # Build response with user roles
        response_data = []
        for org in organizations:
            # Get user's role in this organization
            user_membership = await org_manager.get_user_membership(current_user.id, org.id)
            user_role = user_membership.role if user_membership else "member"

            # Create response with user role
            org_response = OrganizationResponse.from_orm(org)
            org_response.user_role = user_role
            org_response.member_count = org.get_member_count()
            response_data.append(org_response)

        return response_data
        
    except Exception as e:
        logger.error(f"Failed to get user organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organizations"
        )


@router.get("/public", response_model=List[OrganizationResponse])
async def get_public_organizations(
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
):
    """
    Get public organizations that can be discovered.
    
    This endpoint does not require authentication.
    """
    try:
        org_manager = OrganizationManager(db)
        organizations = await org_manager.get_public_organizations(limit=limit)
        
        return [OrganizationResponse.from_orm(org) for org in organizations]
        
    except Exception as e:
        logger.error(f"Failed to get public organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve public organizations"
        )


@router.get("/{organization_id}", response_model=OrganizationResponse)
async def get_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get organization by ID.
    
    User must be a member of the organization to view it.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check if user is a member
        membership = await org_manager.get_user_membership(current_user.id, organization_id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this organization"
            )
        
        organization = await org_manager.get_organization_by_id(organization_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )

        # Create response with user role and member count
        org_response = OrganizationResponse.from_orm(organization)
        org_response.user_role = membership.role
        org_response.member_count = organization.get_member_count()

        return org_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization"
        )


@router.get("/slug/{slug}", response_model=OrganizationResponse)
async def get_organization_by_slug(
    slug: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get organization by slug.
    
    User must be a member of the organization to view it.
    """
    try:
        org_manager = OrganizationManager(db)
        
        organization = await org_manager.get_organization_by_slug(slug)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Check if user is a member
        membership = await org_manager.get_user_membership(current_user.id, organization.id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this organization"
            )
        
        return OrganizationResponse.from_orm(organization)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization by slug {slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization"
        )


@router.put("/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    organization_id: str,
    organization_data: OrganizationUpdate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Update organization details.
    
    User must have manage_settings permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Convert Pydantic model to dict, excluding None values
        update_data = organization_data.dict(exclude_unset=True)
        
        organization = await org_manager.update_organization(
            organization_id=organization_id,
            updater=current_user,
            **update_data
        )
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        return OrganizationResponse.from_orm(organization)
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update organization"
        )


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Delete organization.
    
    Only organization owners can delete organizations.
    This is a soft delete - the organization is marked as inactive.
    """
    try:
        org_manager = OrganizationManager(db)
        
        success = await org_manager.delete_organization(
            organization_id=organization_id,
            deleter=current_user,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete organization"
        )


@router.delete("/{organization_id}/leave", status_code=status.HTTP_200_OK)
async def leave_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Leave an organization.

    Users can leave organizations they are members of, except owners.
    Owners must transfer ownership before leaving.
    """
    try:
        org_manager = OrganizationManager(db)

        # Check if user is a member
        membership = await org_manager.get_user_membership(current_user.id, organization_id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="You are not a member of this organization"
            )

        # Owners cannot leave without transferring ownership
        if membership.role == "owner":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization owners cannot leave. Transfer ownership first."
            )

        # Remove membership
        success = await org_manager.remove_member(
            organization_id=organization_id,
            user_id=current_user.id,
            remover=current_user,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to leave organization"
            )

        return {"message": "Left organization successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to leave organization {organization_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to leave organization"
        )
