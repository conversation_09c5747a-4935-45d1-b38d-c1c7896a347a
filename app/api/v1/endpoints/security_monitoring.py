"""
Security Monitoring API endpoints for GeNieGO SSO Server.

This module provides API endpoints for security monitoring and threat detection,
accessible only to administrators.
"""

import logging
from typing import Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth_dependencies import get_current_user_from_session
from app.core.database import get_db
from app.models.user import User
from app.services.security_monitoring_service import SecurityMonitoringService

router = APIRouter(tags=["Security Monitoring"])
logger = logging.getLogger(__name__)


def get_security_monitoring_service(db: AsyncSession = Depends(get_db)) -> SecurityMonitoringService:
    """Dependency to get security monitoring service."""
    return SecurityMonitoringService(db)


def require_admin_user(current_user: User = Depends(get_current_user_from_session)) -> User:
    """Dependency to ensure user has admin privileges."""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrator access required"
        )
    return current_user


@router.get(
    "/security-summary",
    summary="Get Security Summary",
    description="Get overall security threat summary (admin only)",
)
async def get_security_summary(
    time_window_hours: int = Query(default=24, ge=1, le=168),  # 1 hour to 1 week
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> Dict:
    """Get comprehensive security summary."""
    try:
        summary = await security_service.get_security_summary(time_window_hours)
        return summary

    except Exception as e:
        logger.error(f"Error getting security summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate security summary"
        )


@router.get(
    "/suspicious-logins",
    summary="Get Suspicious Login Activity",
    description="Detect suspicious login patterns (admin only)",
)
async def get_suspicious_login_activity(
    time_window_hours: int = Query(default=24, ge=1, le=168),
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> List[Dict]:
    """Get suspicious login activity."""
    try:
        suspicious_activity = await security_service.detect_suspicious_login_activity(
            time_window_hours
        )
        return suspicious_activity

    except Exception as e:
        logger.error(f"Error detecting suspicious login activity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to detect suspicious login activity"
        )


@router.get(
    "/account-takeover-attempts",
    summary="Get Account Takeover Attempts",
    description="Detect potential account takeover attempts (admin only)",
)
async def get_account_takeover_attempts(
    time_window_hours: int = Query(default=6, ge=1, le=48),
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> List[Dict]:
    """Get potential account takeover attempts."""
    try:
        takeover_attempts = await security_service.detect_account_takeover_attempts(
            time_window_hours
        )
        return takeover_attempts

    except Exception as e:
        logger.error(f"Error detecting account takeover attempts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to detect account takeover attempts"
        )


@router.get(
    "/brute-force-attacks",
    summary="Get Brute Force Attacks",
    description="Detect brute force attack patterns (admin only)",
)
async def get_brute_force_attacks(
    time_window_minutes: int = Query(default=30, ge=5, le=1440),  # 5 minutes to 24 hours
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> List[Dict]:
    """Get brute force attack detection."""
    try:
        attacks = await security_service.detect_brute_force_attacks(time_window_minutes)
        return attacks

    except Exception as e:
        logger.error(f"Error detecting brute force attacks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to detect brute force attacks"
        )


@router.get(
    "/unusual-admin-activity",
    summary="Get Unusual Admin Activity",
    description="Detect unusual administrative activity (admin only)",
)
async def get_unusual_admin_activity(
    time_window_hours: int = Query(default=24, ge=1, le=168),
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> List[Dict]:
    """Get unusual administrative activity."""
    try:
        unusual_activity = await security_service.detect_unusual_admin_activity(
            time_window_hours
        )
        return unusual_activity

    except Exception as e:
        logger.error(f"Error detecting unusual admin activity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to detect unusual admin activity"
        )


@router.get(
    "/mfa-bypass-attempts",
    summary="Get MFA Bypass Attempts",
    description="Detect attempts to bypass multi-factor authentication (admin only)",
)
async def get_mfa_bypass_attempts(
    time_window_hours: int = Query(default=12, ge=1, le=72),
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> List[Dict]:
    """Get MFA bypass attempts."""
    try:
        bypass_attempts = await security_service.detect_mfa_bypass_attempts(
            time_window_hours
        )
        return bypass_attempts

    except Exception as e:
        logger.error(f"Error detecting MFA bypass attempts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to detect MFA bypass attempts"
        )


@router.get(
    "/ip-reputation/{ip_address}",
    summary="Get IP Reputation",
    description="Get reputation information for an IP address (admin only)",
)
async def get_ip_reputation(
    ip_address: str,
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> Dict:
    """Get IP address reputation information."""
    try:
        reputation = await security_service.get_ip_reputation(ip_address)
        return reputation

    except Exception as e:
        logger.error(f"Error getting IP reputation for {ip_address}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get IP reputation"
        )


@router.get(
    "/threat-dashboard",
    summary="Get Threat Dashboard Data",
    description="Get comprehensive threat dashboard data (admin only)",
)
async def get_threat_dashboard(
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> Dict:
    """Get comprehensive threat dashboard data."""
    try:
        # Get data for different time windows
        summary_24h = await security_service.get_security_summary(24)
        brute_force_30m = await security_service.detect_brute_force_attacks(30)
        takeover_6h = await security_service.detect_account_takeover_attempts(6)
        
        # Compile dashboard data
        dashboard_data = {
            "overview": summary_24h["summary"],
            "recent_threats": {
                "brute_force_attacks": brute_force_30m[:5],  # Last 5
                "account_takeover_attempts": takeover_6h[:5],  # Last 5
                "suspicious_logins": summary_24h["threats"]["suspicious_login_activity"][:5]
            },
            "threat_counts": {
                "total_24h": summary_24h["summary"]["total_threats"],
                "brute_force_30m": len(brute_force_30m),
                "takeover_attempts_6h": len(takeover_6h),
                "suspicious_logins_24h": len(summary_24h["threats"]["suspicious_login_activity"])
            },
            "generated_at": summary_24h["summary"]["generated_at"]
        }
        
        return dashboard_data

    except Exception as e:
        logger.error(f"Error generating threat dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate threat dashboard"
        )


@router.get(
    "/security-metrics",
    summary="Get Security Metrics",
    description="Get security metrics and statistics (admin only)",
)
async def get_security_metrics(
    time_window_hours: int = Query(default=24, ge=1, le=168),
    admin_user: User = Depends(require_admin_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service),
) -> Dict:
    """Get security metrics and statistics."""
    try:
        # This would typically integrate with a metrics system
        # For now, return basic metrics from our detection systems
        
        summary = await security_service.get_security_summary(time_window_hours)
        
        metrics = {
            "time_window_hours": time_window_hours,
            "threat_metrics": {
                "total_threats_detected": summary["summary"]["total_threats"],
                "critical_threats": summary["summary"]["critical_threats"],
                "high_threats": summary["summary"]["high_threats"],
                "threat_level": summary["summary"]["threat_level"]
            },
            "detection_metrics": {
                "suspicious_login_ips": len(summary["threats"]["suspicious_login_activity"]),
                "brute_force_attacks": len(summary["threats"]["brute_force_attacks"]),
                "takeover_attempts": len(summary["threats"]["account_takeover_attempts"]),
                "mfa_bypass_attempts": len(summary["threats"]["mfa_bypass_attempts"]),
                "unusual_admin_activities": len(summary["threats"]["unusual_admin_activity"])
            },
            "generated_at": summary["summary"]["generated_at"]
        }
        
        return metrics

    except Exception as e:
        logger.error(f"Error getting security metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get security metrics"
        )
