"""
Organization invitation management API endpoints for GeNieGO SSO Server.

This module provides REST API endpoints for managing organization invitations,
including creating, accepting, rejecting, and canceling invitations.
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth_dependencies import get_current_user_from_session
from app.models import User
from app.schemas.organization import (
    InvitationAcceptRequest,
    InvitationRejectRequest,
    OrganizationInvitationCreate,
    OrganizationInvitationResponse,
)
from app.services.organization_service import OrganizationManager

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/{organization_id}/invitations", response_model=OrganizationInvitationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization_invitation(
    organization_id: str,
    invitation_data: OrganizationInvitationCreate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Create an invitation to join organization.
    
    User must have manage_members permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        invitation = await org_manager.create_invitation(
            organization_id=organization_id,
            email=invitation_data.email,
            role=invitation_data.role,
            inviter_id=current_user.id,
            message=invitation_data.message,
            expires_in_days=invitation_data.expires_in_days,
        )

        # Refresh the invitation to load relationships
        await db.refresh(invitation, ['organization', 'inviter'])

        # Convert to response format with organization details
        response_data = OrganizationInvitationResponse.from_orm(invitation)
        if invitation.organization:
            response_data.organization_name = invitation.organization.name
            response_data.organization_slug = invitation.organization.slug
        if invitation.inviter:
            response_data.inviter_name = f"{invitation.inviter.first_name} {invitation.inviter.last_name}".strip()
            response_data.inviter_email = invitation.inviter.email

        return response_data
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create organization invitation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create invitation"
        )


@router.get("/{organization_id}/invitations", response_model=List[OrganizationInvitationResponse])
async def get_organization_invitations(
    organization_id: str,
    status_filter: str = "pending",  # pending, accepted, rejected, expired, all
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get organization invitations.
    
    User must have manage_members permission to view invitations.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check permissions
        if not await org_manager.user_can_manage_members(current_user.id, organization_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to view invitations"
            )
        
        # Get invitations (we'll need to add this method to OrganizationManager)
        invitations = await org_manager.get_organization_invitations(
            organization_id, status_filter=status_filter
        )
        
        # Convert to response format
        response_invitations = []
        for invitation in invitations:
            response_data = OrganizationInvitationResponse.from_orm(invitation)
            if invitation.organization:
                response_data.organization_name = invitation.organization.name
                response_data.organization_slug = invitation.organization.slug
            if invitation.inviter:
                response_data.inviter_name = f"{invitation.inviter.first_name} {invitation.inviter.last_name}".strip()
                response_data.inviter_email = invitation.inviter.email
            response_invitations.append(response_data)
        
        return response_invitations
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization invitations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve invitations"
        )


@router.get("/invitations/{token}", response_model=OrganizationInvitationResponse)
async def get_invitation_by_token(
    token: str,
    db: AsyncSession = Depends(get_db),
):
    """
    Get invitation details by token.
    
    This endpoint does not require authentication as it's used for invitation acceptance.
    """
    try:
        org_manager = OrganizationManager(db)
        
        invitation = await org_manager.get_invitation_by_token(token)
        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invitation not found"
            )
        
        # Convert to response format
        response_data = OrganizationInvitationResponse.from_orm(invitation)
        if invitation.organization:
            response_data.organization_name = invitation.organization.name
            response_data.organization_slug = invitation.organization.slug
        if invitation.inviter:
            response_data.inviter_name = f"{invitation.inviter.first_name} {invitation.inviter.last_name}".strip()
            response_data.inviter_email = invitation.inviter.email
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get invitation by token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve invitation"
        )


@router.post("/invitations/accept", response_model=dict, status_code=status.HTTP_200_OK)
async def accept_organization_invitation(
    accept_data: InvitationAcceptRequest,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Accept organization invitation.
    
    User must be authenticated and the invitation email must match their email.
    """
    try:
        org_manager = OrganizationManager(db)
        
        success, membership = await org_manager.accept_invitation(
            token=accept_data.token,
            user=current_user,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired invitation"
            )
        
        return {
            "message": "Invitation accepted successfully",
            "membership_id": membership.id if membership else None,
            "organization_id": membership.organization_id if membership else None,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to accept invitation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to accept invitation"
        )


@router.post("/invitations/reject", response_model=dict, status_code=status.HTTP_200_OK)
async def reject_organization_invitation(
    reject_data: InvitationRejectRequest,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Reject organization invitation.
    
    User must be authenticated and the invitation email must match their email.
    """
    try:
        org_manager = OrganizationManager(db)
        
        success = await org_manager.reject_invitation(
            token=reject_data.token,
            user=current_user,
            reason=reject_data.reason,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid invitation or email mismatch"
            )
        
        return {
            "message": "Invitation rejected successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reject invitation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reject invitation"
        )


@router.delete("/{organization_id}/invitations/{invitation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def cancel_organization_invitation(
    organization_id: str,
    invitation_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Cancel pending organization invitation.
    
    User must have manage_members permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        success = await org_manager.cancel_invitation(
            invitation_id=invitation_id,
            canceller_id=current_user.id,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invitation not found"
            )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel invitation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel invitation"
        )
