"""
Consent Management API Endpoints for GeNieGO SSO Server.

This module provides API endpoints for GDPR-compliant consent management,
including consent granting, revoking, and audit trail access.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth_dependencies import get_current_regular_user_from_session
from app.core.database import get_db
from app.models import User
from app.services.consent_service import ConsentManager

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic models for request/response
class ConsentGrantRequest(BaseModel):
    application_id: str
    scopes: List[str]
    expires_days: Optional[int] = 365


class ConsentRevokeRequest(BaseModel):
    application_id: str
    reason: Optional[str] = None


class ConsentResponse(BaseModel):
    id: str
    user_id: str
    application_id: str
    application_name: Optional[str] = None
    scopes: List[str]
    granted_at: str
    expires_at: Optional[str] = None
    is_active: bool
    is_expired: bool
    is_valid: bool


class ConsentAuditResponse(BaseModel):
    id: str
    user_id: str
    application_id: str
    application_name: Optional[str] = None
    action: str
    scopes: Optional[List[str]] = None
    previous_scopes: Optional[List[str]] = None
    reason: Optional[str] = None
    timestamp: str


class ConsentCheckRequest(BaseModel):
    application_id: str
    scopes: List[str]


@router.post(
    "/grant",
    response_model=ConsentResponse,
    summary="Grant Consent",
    description="Grant user consent for application scopes with GDPR compliance",
)
async def grant_consent(
    request: Request,
    consent_request: ConsentGrantRequest,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> ConsentResponse:
    """Grant user consent for application scopes."""
    try:
        consent_manager = ConsentManager(db)
        
        # Extract client information
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        result = await consent_manager.grant_consent(
            user_id=current_user.id,
            application_id=consent_request.application_id,
            scopes=consent_request.scopes,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_days=consent_request.expires_days,
        )
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        consent = result.consent
        return ConsentResponse(
            id=consent.id,
            user_id=consent.user_id,
            application_id=consent.application_id,
            application_name=consent.application.name if consent.application else None,
            scopes=consent.scopes,
            granted_at=consent.granted_at.isoformat(),
            expires_at=consent.expires_at.isoformat() if consent.expires_at else None,
            is_active=consent.is_active,
            is_expired=consent.is_expired,
            is_valid=consent.is_valid,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Grant consent error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/revoke",
    summary="Revoke Consent",
    description="Revoke user consent for an application",
)
async def revoke_consent(
    request: Request,
    revoke_request: ConsentRevokeRequest,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Revoke user consent for an application."""
    try:
        consent_manager = ConsentManager(db)
        
        # Extract client information
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        result = await consent_manager.revoke_consent(
            user_id=current_user.id,
            application_id=revoke_request.application_id,
            ip_address=ip_address,
            user_agent=user_agent,
            reason=revoke_request.reason,
        )
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "message": "Consent revoked successfully",
            "application_id": revoke_request.application_id,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Revoke consent error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/check",
    summary="Check Consent",
    description="Check if user has granted consent for required scopes",
)
async def check_consent(
    check_request: ConsentCheckRequest,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Check if user has granted consent for required scopes."""
    try:
        consent_manager = ConsentManager(db)
        
        has_consent = await consent_manager.check_consent(
            user_id=current_user.id,
            application_id=check_request.application_id,
            required_scopes=check_request.scopes,
        )
        
        return {
            "has_consent": has_consent,
            "application_id": check_request.application_id,
            "required_scopes": check_request.scopes,
        }
        
    except Exception as e:
        logger.error(f"Check consent error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/my-consents",
    response_model=List[ConsentResponse],
    summary="Get My Consents",
    description="Get all consents granted by the current user",
)
async def get_my_consents(
    active_only: bool = True,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> List[ConsentResponse]:
    """Get all consents granted by the current user."""
    try:
        consent_manager = ConsentManager(db)
        
        consents = await consent_manager.get_user_consents(
            user_id=current_user.id,
            active_only=active_only,
        )
        
        return [
            ConsentResponse(
                id=consent.id,
                user_id=consent.user_id,
                application_id=consent.application_id,
                application_name=consent.application.name if consent.application else None,
                scopes=consent.scopes,
                granted_at=consent.granted_at.isoformat(),
                expires_at=consent.expires_at.isoformat() if consent.expires_at else None,
                is_active=consent.is_active,
                is_expired=consent.is_expired,
                is_valid=consent.is_valid,
            )
            for consent in consents
        ]
        
    except Exception as e:
        logger.error(f"Get my consents error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/audit-log",
    response_model=List[ConsentAuditResponse],
    summary="Get Consent Audit Log",
    description="Get consent audit log for the current user",
)
async def get_consent_audit_log(
    limit: int = 50,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> List[ConsentAuditResponse]:
    """Get consent audit log for the current user."""
    try:
        consent_manager = ConsentManager(db)
        
        audit_logs = await consent_manager.get_consent_audit_log(
            user_id=current_user.id,
            limit=min(limit, 100),  # Cap at 100 for performance
        )
        
        return [
            ConsentAuditResponse(
                id=log.id,
                user_id=log.user_id,
                application_id=log.application_id,
                application_name=log.application.name if log.application else None,
                action=log.action,
                scopes=log.scopes,
                previous_scopes=log.previous_scopes,
                reason=log.reason,
                timestamp=log.timestamp.isoformat(),
            )
            for log in audit_logs
        ]
        
    except Exception as e:
        logger.error(f"Get consent audit log error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
