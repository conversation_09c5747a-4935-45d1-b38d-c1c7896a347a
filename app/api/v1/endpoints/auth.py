"""
GeNieGO SSO Server Authentication Endpoints.

This module implements user authentication endpoints for GeNieGO SSO Server.
It provides user registration, login, logout, and profile management.
"""

import logging
from typing import Any, Dict, Optional
from urllib.parse import urlencode

import httpx
from fastapi import (
    APIRouter,
    Depends,
    Form,
    HTTPException,
    Query,
    Request,
    Response,
)
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.core.database import get_db
from app.schemas.auth import (
    MFALoginRequest,
    MFALoginResponse,
    MFARequiredResponse,
    SessionResponse,
    SSOSuccessResponse,
    UserLoginRequest,
    UserRegistrationRequest,
)
from app.schemas.user import UserResponse, UserUpdate
from app.models.user import User, EmailVerificationToken
from app.services.auth_service import AuthService
from app.services.email_service import EmailService
from app.services.mfa_service import MFAManager
from app.services.mfa_session_service import get_mfa_session_manager, MFASessionManager
from app.services.session_service import CentralSessionManager, DeviceInfo

router = APIRouter(tags=["GeNieGO Authentication"])
settings = get_settings()
logger = logging.getLogger(__name__)


def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Dependency to get authentication service."""
    return AuthService(db)


def get_email_service() -> EmailService:
    """Dependency to get email service."""
    return EmailService()


def get_mfa_manager(db: AsyncSession = Depends(get_db)) -> MFAManager:
    """Dependency to get MFA manager service."""
    return MFAManager(db)


@router.post(
    "/register",
    response_model=UserResponse,
    summary="User Registration",
    description="Register a new user account in GeNieGO SSO Server",
)
async def register_user(
    user_data: UserRegistrationRequest,
    auth_service: AuthService = Depends(get_auth_service),
    email_service: EmailService = Depends(get_email_service),
    db: AsyncSession = Depends(get_db),
) -> UserResponse:
    """Register a new user account with email verification."""
    try:
        # Register the user
        user = await auth_service.register_user(
            email=user_data.email,
            username=user_data.username,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
        )

        # Create email verification token
        from datetime import datetime, timedelta

        verification_token = EmailVerificationToken(
            user_id=user.id,
            email=user.email,
            token=EmailVerificationToken.generate_token(),
            expires_at=datetime.utcnow() + timedelta(hours=24)  # 24 hour expiry
        )

        db.add(verification_token)
        await db.commit()

        # Send verification email
        verification_url = f"{settings.DOMAIN_NAME}/auth/verify-email?token={verification_token.token}"
        await email_service.send_email_verification(
            email=user.email,
            username=user.username,
            verification_url=verification_url
        )

        logger.info(f"User registered and verification email sent: {user.email}")

        return UserResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")


@router.get(
    "/verify-email",
    response_model=dict,
    summary="Email Verification",
    description="Verify user email address using verification token",
)
async def verify_email(
    token: str = Query(..., description="Email verification token"),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Verify user email address."""
    try:
        from sqlalchemy import select

        # Find the verification token
        result = await db.execute(
            select(EmailVerificationToken).where(
                EmailVerificationToken.token == token
            )
        )
        verification_token = result.scalar_one_or_none()

        if not verification_token:
            raise HTTPException(status_code=400, detail="Invalid verification token")

        if not verification_token.is_valid:
            if verification_token.is_used:
                raise HTTPException(status_code=400, detail="Verification token already used")
            elif verification_token.is_expired:
                raise HTTPException(status_code=400, detail="Verification token expired")
            else:
                raise HTTPException(status_code=400, detail="Invalid verification token")

        # Get the user
        user_result = await db.execute(
            select(User).where(User.id == verification_token.user_id)
        )
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=400, detail="User not found")

        # Mark user as verified
        user.is_verified = True

        # Mark token as used
        verification_token.mark_as_used()

        await db.commit()

        logger.info(f"Email verified successfully for user: {user.email}")

        return {
            "message": "Email verified successfully",
            "user_id": user.id,
            "email": user.email,
            "verified": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification error: {e}")
        raise HTTPException(status_code=500, detail="Email verification failed")


@router.post(
    "/resend-verification",
    response_model=dict,
    summary="Resend Email Verification",
    description="Resend email verification for unverified users",
)
async def resend_verification_email(
    email: str = Form(..., description="User email address"),
    email_service: EmailService = Depends(get_email_service),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Resend email verification for unverified users."""
    try:
        from sqlalchemy import select

        # Find the user
        result = await db.execute(
            select(User).where(User.email == email)
        )
        user = result.scalar_one_or_none()

        if not user:
            # Don't reveal if email exists or not for security
            return {"message": "If the email exists and is unverified, a verification email has been sent"}

        if user.is_verified:
            return {"message": "Email is already verified"}

        # Check for existing unused token
        token_result = await db.execute(
            select(EmailVerificationToken).where(
                EmailVerificationToken.user_id == user.id,
                EmailVerificationToken.is_used == False
            ).order_by(EmailVerificationToken.created_at.desc())
        )
        existing_token = token_result.scalar_one_or_none()

        # If there's a valid existing token, don't create a new one
        if existing_token and existing_token.is_valid:
            verification_url = f"{settings.DOMAIN_NAME}/auth/verify-email?token={existing_token.token}"
        else:
            # Create new verification token
            from datetime import datetime, timedelta

            verification_token = EmailVerificationToken(
                user_id=user.id,
                email=user.email,
                token=EmailVerificationToken.generate_token(),
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )

            db.add(verification_token)
            await db.commit()

            verification_url = f"{settings.DOMAIN_NAME}/auth/verify-email?token={verification_token.token}"

        # Send verification email
        await email_service.send_email_verification(
            email=user.email,
            username=user.username,
            verification_url=verification_url
        )

        logger.info(f"Verification email resent to: {user.email}")

        return {"message": "Verification email sent successfully"}

    except Exception as e:
        logger.error(f"Resend verification error: {e}")
        raise HTTPException(status_code=500, detail="Failed to resend verification email")


@router.post(
    "/login",
    summary="User Login",
    description="Authenticate user and create session (with MFA support)",
)
async def login_user(
    request: Request,
    response: Response,
    user_data: UserLoginRequest,
    auth_service: AuthService = Depends(get_auth_service),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
    mfa_session_manager: MFASessionManager = Depends(get_mfa_session_manager),
    db: AsyncSession = Depends(get_db),
):
    """Authenticate user and create session with MFA support."""
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=user_data.email, password=user_data.password
        )

        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Check if user email is verified
        if not user.is_verified:
            raise HTTPException(
                status_code=403,
                detail="Email verification required. Please check your email and verify your account before logging in."
            )

        # Check if user has MFA devices
        user_devices = await mfa_manager.get_user_devices(user.id)
        active_devices = [d for d in user_devices if d["is_active"] and d["is_verified"]]

        if active_devices:
            # User has MFA enabled, require MFA verification
            mfa_session_token = mfa_session_manager.create_mfa_session(
                user=user,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
                expires_in_minutes=10  # 10 minutes to complete MFA
            )

            return MFARequiredResponse(
                mfa_required=True,
                session_token=mfa_session_token,
                available_methods=["totp", "backup_code"],
                expires_in=600,  # 10 minutes in seconds
                message="Multi-factor authentication required. Please provide your TOTP code or backup code."
            )

        # No MFA required, proceed with normal login
        return await _complete_login(
            user=user,
            request=request,
            response=response,
            auth_service=auth_service,
            db=db
        )



    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")


async def _complete_login(
    user: User,
    request: Request,
    response: Response,
    auth_service: AuthService,
    db: AsyncSession
) -> SessionResponse:
    """Complete login process by creating sessions and setting cookies."""
    # Create traditional session for backward compatibility
    session = await auth_service.create_user_session(
        user=user,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Create centralized SSO session
    session_manager = CentralSessionManager(db)
    device_info = DeviceInfo(
        fingerprint=None,  # TODO: Implement device fingerprinting
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Create SSO session (initially without application bindings)
    sso_session = await session_manager.create_sso_session(
        user=user,
        applications=[],  # Will be populated during OAuth2 flows
        device_info=device_info,
        session_duration_hours=settings.SESSION_EXPIRE_HOURS,
    )

    logger.info(f"Created SSO session {sso_session.id} for user {user.id} during login")

    # Set session cookie
    response.set_cookie(
        key="geniengo_session",
        value=session.session_token,
        max_age=settings.SESSION_EXPIRE_HOURS * 3600,
        httponly=True,
        secure=not settings.DEBUG,  # Use secure cookies in production
        samesite="lax",
    )

    return SessionResponse(
        session_token=session.session_token,
        expires_at=session.expires_at,
        user=UserResponse.model_validate(user),
    )


@router.post(
    "/mfa-login",
    response_model=MFALoginResponse,
    summary="Complete MFA Login",
    description="Complete login process with MFA verification",
)
async def complete_mfa_login(
    request: Request,
    response: Response,
    mfa_data: MFALoginRequest,
    auth_service: AuthService = Depends(get_auth_service),
    mfa_manager: MFAManager = Depends(get_mfa_manager),
    mfa_session_manager: MFASessionManager = Depends(get_mfa_session_manager),
    db: AsyncSession = Depends(get_db),
) -> MFALoginResponse:
    """Complete login process with MFA verification."""
    try:
        # Get MFA session
        session_data = mfa_session_manager.get_mfa_session(mfa_data.session_token)
        if not session_data:
            return MFALoginResponse(
                success=False,
                message="Invalid or expired MFA session"
            )

        user = session_data["user"]

        # Verify MFA code
        if mfa_data.method == "totp":
            is_valid = await mfa_manager.verify_totp_code(
                user=user,
                totp_code=mfa_data.code,
                device_id=mfa_data.device_id
            )
        elif mfa_data.method == "backup_code":
            is_valid = await mfa_manager.verify_backup_code(
                user=user,
                backup_code=mfa_data.code
            )
        else:
            return MFALoginResponse(
                success=False,
                message="Invalid MFA method"
            )

        if not is_valid:
            return MFALoginResponse(
                success=False,
                message="Invalid MFA code"
            )

        # Mark MFA session as verified
        mfa_session_manager.verify_mfa_session(mfa_data.session_token)

        # Consume MFA session and complete login
        verified_user = mfa_session_manager.consume_mfa_session(mfa_data.session_token)
        if not verified_user:
            return MFALoginResponse(
                success=False,
                message="MFA session verification failed"
            )

        # Complete login process
        session_response = await _complete_login(
            user=verified_user,
            request=request,
            response=response,
            auth_service=auth_service,
            db=db
        )

        return MFALoginResponse(
            success=True,
            session_token=session_response.session_token,
            expires_at=session_response.expires_at,
            user=session_response.user,
            message="Login completed successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MFA login error: {e}")
        return MFALoginResponse(
            success=False,
            message="MFA login failed"
        )


@router.post(
    "/logout",
    response_model=SSOSuccessResponse,
    summary="User Logout",
    description="Logout user and invalidate session with centralized SSO session management",
)
async def logout_user(
    request: Request,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service),
    db: AsyncSession = Depends(get_db),
) -> SSOSuccessResponse:
    """Logout user and invalidate session with centralized SSO session management."""
    try:
        session_token = request.cookies.get("geniengo_session")

        # Initialize centralized session manager
        session_manager = CentralSessionManager(db)

        # Get current user to find their SSO sessions
        current_user = None
        if session_token:
            try:
                session = await auth_service.validate_session(session_token)
                if session:
                    current_user = session.user
            except Exception as e:
                logger.warning(f"Failed to validate session during logout: {e}")

        # Perform centralized logout if user is found
        logout_result = None
        if current_user:
            # Get user's active SSO sessions
            user_sessions = await session_manager.get_user_sessions(current_user.id)

            # Perform single logout for all sessions
            total_apps_notified = 0
            for sso_session in user_sessions:
                result = await session_manager.propagate_logout(sso_session.id)
                if result.success:
                    total_apps_notified += result.applications_notified
                    logger.info(
                        f"SSO logout successful for session {sso_session.id}, "
                        f"notified {result.applications_notified} applications"
                    )
                else:
                    logger.error(f"SSO logout failed for session {sso_session.id}: {result.error}")

            if user_sessions:
                logger.info(f"Centralized logout completed for user {current_user.id}, "
                           f"terminated {len(user_sessions)} sessions, "
                           f"notified {total_apps_notified} applications")

        # Also invalidate traditional session cookie
        if session_token:
            await auth_service.logout_user(session_token)

        # Clear session cookie
        response.delete_cookie(
            key="geniengo_session",
            httponly=True,
            secure=not settings.DEBUG,
            samesite="lax",
        )

        return SSOSuccessResponse(message="Successfully logged out")

    except Exception as e:
        logger.error(f"User logout error: {e}")
        # Don't fail logout even if there's an error - clear cookie anyway
        response.delete_cookie(
            key="geniengo_session",
            httponly=True,
            secure=not settings.DEBUG,
            samesite="lax",
        )
        return SSOSuccessResponse(message="Logged out")


@router.get(
    "/profile",
    response_model=UserResponse,
    summary="Get User Profile",
    description="Get current user profile information",
)
async def get_user_profile(
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Get current user profile."""
    try:
        session_token = request.cookies.get("geniengo_session")
        if not session_token:
            raise HTTPException(status_code=401, detail="Not authenticated")

        session = await auth_service.validate_session(session_token)
        if not session:
            raise HTTPException(status_code=401, detail="Invalid session")

        return UserResponse.model_validate(session.user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user profile error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get profile")


@router.put(
    "/profile",
    response_model=UserResponse,
    summary="Update User Profile",
    description="Update current user profile information",
)
async def update_user_profile(
    request: Request,
    profile_data: UserUpdate,
    auth_service: AuthService = Depends(get_auth_service),
) -> UserResponse:
    """Update current user profile."""
    try:
        session_token = request.cookies.get("geniengo_session")
        if not session_token:
            raise HTTPException(status_code=401, detail="Not authenticated")

        session = await auth_service.validate_session(session_token)
        if not session:
            raise HTTPException(status_code=401, detail="Invalid session")

        user = session.user

        # Update profile fields
        if profile_data.first_name is not None:
            user.first_name = profile_data.first_name
        if profile_data.last_name is not None:
            user.last_name = profile_data.last_name

        await auth_service.db.commit()
        await auth_service.db.refresh(user)

        return UserResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update user profile error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update profile")


@router.get(
    "/login",
    response_class=HTMLResponse,
    summary="Login Page",
    description="Display login page for OAuth2 authorization flow",
)
async def login_page(
    request: Request,
    client_id: Optional[str] = None,
    redirect_uri: Optional[str] = None,
    response_type: Optional[str] = None,
    scope: Optional[str] = None,
    state: Optional[str] = None,
    code_challenge: Optional[str] = None,
    code_challenge_method: Optional[str] = None,
) -> HTMLResponse:
    """Display login page for OAuth2 authorization flow."""

    # Build OAuth2 parameters for form submission
    oauth_params = ""
    if client_id:
        oauth_params += f'<input type="hidden" name="client_id" value="{client_id}">\n'
    if redirect_uri:
        oauth_params += (
            f'<input type="hidden" name="redirect_uri" value="{redirect_uri}">\n'
        )
    if response_type:
        oauth_params += (
            f'<input type="hidden" name="response_type" value="{response_type}">\n'
        )
    if scope:
        oauth_params += f'<input type="hidden" name="scope" value="{scope}">\n'
    if state:
        oauth_params += f'<input type="hidden" name="state" value="{state}">\n'
    if code_challenge:
        oauth_params += (
            f'<input type="hidden" name="code_challenge" value="{code_challenge}">\n'
        )
    if code_challenge_method:
        oauth_params += f'<input type="hidden" name="code_challenge_method" value="{code_challenge_method}">\n'

    login_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>GeNieGO SSO - Login</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body {{ font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; background: #f5f5f5; }}
            .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            h2 {{ text-align: center; color: #333; margin-bottom: 30px; }}
            .form-group {{ margin-bottom: 20px; }}
            label {{ display: block; margin-bottom: 5px; color: #555; }}
            input[type="email"], input[type="password"] {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
            button {{ width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }}
            button:hover {{ background: #0056b3; }}
            .error {{ color: red; margin-top: 10px; }}
            .footer {{ text-align: center; margin-top: 20px; color: #666; font-size: 14px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>GeNieGO SSO</h2>
            <form method="post" action="/auth/login">
                {oauth_params}
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit">Sign In</button>
            </form>

            <div style="text-align: center; margin: 20px 0; color: #666;">
                <span>or</span>
            </div>

            <a href="/auth/google{f'?client_id={client_id}&redirect_uri={redirect_uri}&state={state}' if client_id else ''}"
               style="display: block; width: 100%; padding: 12px; background: #db4437; color: white; text-decoration: none; border-radius: 4px; text-align: center; box-sizing: border-box;">
                Sign in with Google
            </a>

            <div class="footer">
                <p>GeNieGO SSO Server v{settings.VERSION}</p>
            </div>
        </div>
    </body>
    </html>
    """

    return HTMLResponse(content=login_html)


@router.get(
    "/google",
    summary="Google OAuth Login",
    description="Initiate Google OAuth login flow",
)
async def google_oauth_login(
    request: Request,
    client_id: Optional[str] = Query(
        None, description="OAuth2 client ID for redirect after login"
    ),
    redirect_uri: Optional[str] = Query(None, description="OAuth2 redirect URI"),
    state: Optional[str] = Query(None, description="OAuth2 state parameter"),
) -> RedirectResponse:
    """Initiate Google OAuth login flow."""
    try:
        # Google OAuth2 configuration
        google_client_id = settings.GOOGLE_CLIENT_ID
        if not google_client_id:
            raise HTTPException(status_code=500, detail="Google OAuth not configured")

        # Build callback URL - use environment-specific redirect URI
        if settings.ENVIRONMENT == "production":
            callback_url = settings.GOOGLE_REDIRECT_URI
        else:
            callback_url = (
                settings.GOOGLE_REDIRECT_URI_LOCAL
                or f"{request.base_url}api/v1/auth/google/callback"
            )

        # Store OAuth2 parameters in state for callback
        oauth_state = {
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "state": state,
        }

        # Encode state parameters
        import base64
        import json

        encoded_state = (
            base64.urlsafe_b64encode(json.dumps(oauth_state).encode())
            .decode()
            .rstrip("=")
        )

        # Google OAuth2 authorization URL
        google_auth_params = {
            "client_id": google_client_id,
            "redirect_uri": callback_url,
            "scope": "openid email profile",
            "response_type": "code",
            "state": encoded_state,
            "access_type": "offline",
            "prompt": "consent",
        }

        google_auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(google_auth_params)}"

        return RedirectResponse(url=google_auth_url, status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google OAuth initiation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to initiate Google OAuth")


@router.get(
    "/google/callback",
    summary="Google OAuth Callback",
    description="Handle Google OAuth callback and create user session",
)
async def google_oauth_callback(
    request: Request,
    response: Response,
    code: str = Query(..., description="Authorization code from Google"),
    state: Optional[str] = Query(None, description="State parameter"),
    auth_service: AuthService = Depends(get_auth_service),
) -> RedirectResponse:
    """Handle Google OAuth callback."""
    try:
        # Decode state parameters
        oauth_state = {}
        if state:
            import base64
            import json

            try:
                # Add padding if needed
                padded_state = state + "=" * (4 - len(state) % 4)
                decoded_state = base64.urlsafe_b64decode(padded_state).decode()
                oauth_state = json.loads(decoded_state)
            except Exception:
                logger.warning("Failed to decode OAuth state")

        # Exchange code for tokens
        google_client_id = settings.GOOGLE_CLIENT_ID
        google_client_secret = settings.GOOGLE_CLIENT_SECRET

        if not google_client_id or not google_client_secret:
            raise HTTPException(status_code=500, detail="Google OAuth not configured")

        # Use environment-specific redirect URI
        if settings.ENVIRONMENT == "production":
            callback_url = settings.GOOGLE_REDIRECT_URI
        else:
            callback_url = (
                settings.GOOGLE_REDIRECT_URI_LOCAL
                or f"{request.base_url}api/v1/auth/google/callback"
            )

        token_data = {
            "client_id": google_client_id,
            "client_secret": google_client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": callback_url,
        }

        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://oauth2.googleapis.com/token", data=token_data
            )

            if token_response.status_code != 200:
                raise HTTPException(
                    status_code=400, detail="Failed to exchange code for token"
                )

            tokens = token_response.json()
            access_token = tokens.get("access_token")

            if not access_token:
                raise HTTPException(status_code=400, detail="No access token received")

            # Get user info from Google
            user_response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"},
            )

            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=400, detail="Failed to get user info from Google"
                )

            google_user = user_response.json()

        # Create or get user
        user = await auth_service.get_or_create_google_user(
            google_id=google_user["id"],
            email=google_user["email"],
            first_name=google_user.get("given_name"),
            last_name=google_user.get("family_name"),
            picture=google_user.get("picture"),
        )

        # Create session
        session = await auth_service.create_user_session(
            user=user,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )

        # Handle OAuth2 redirect
        if oauth_state.get("client_id") and oauth_state.get("redirect_uri"):
            # Continue OAuth2 flow
            from urllib.parse import urlencode

            oauth_params = {
                "client_id": oauth_state["client_id"],
                "redirect_uri": oauth_state["redirect_uri"],
                "response_type": "code",
                "scope": "openid profile",
            }
            if oauth_state.get("state"):
                oauth_params["state"] = oauth_state["state"]

            redirect_url = f"/oauth2/authorize?{urlencode(oauth_params)}"
        else:
            # Default redirect to dashboard after successful Google login
            redirect_url = "/dashboard?google_login=success"

        # Create HTML response that sets cookie via JavaScript and redirects
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Redirecting...</title>
        </head>
        <body>
            <script>
                // Set session cookie via JavaScript
                document.cookie = "geniengo_session={session.session_token}; path=/; max-age={int(settings.SESSION_EXPIRE_MINUTES * 60)}; SameSite=Lax";

                // Also set in localStorage as backup
                localStorage.setItem('session_token', '{session.session_token}');

                // Redirect after cookie is set
                setTimeout(function() {{
                    window.location.href = '{redirect_url}';
                }}, 100);
            </script>
            <p>Redirecting...</p>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content, status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google OAuth callback error: {e}")
        raise HTTPException(status_code=500, detail="Google OAuth callback failed")
