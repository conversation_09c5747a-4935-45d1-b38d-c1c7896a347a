"""
Session Management API Endpoints for GeNieGO SSO Server.

This module provides API endpoints for centralized SSO session management,
including session CRUD operations, activity tracking, and session validation.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth_dependencies import get_current_regular_user_from_session
from app.core.database import get_db
from app.models import User
from app.services.session_service import CentralSessionManager, DeviceInfo
from app.schemas.auth import SessionResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/active",
    response_model=List[dict],
    summary="Get Active Sessions",
    description="Get all active SSO sessions for the current user",
)
async def get_active_sessions(
    request: Request,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> List[dict]:
    """Get all active SSO sessions for the current user."""
    try:
        session_manager = CentralSessionManager(db)
        sessions = await session_manager.get_user_sessions(current_user.id)
        
        # Convert sessions to response format
        session_list = []
        for session in sessions:
            session_data = {
                "id": session.id,
                "device_fingerprint": session.device_fingerprint,
                "ip_address": session.ip_address,
                "user_agent": session.user_agent,
                "created_at": session.created_at.isoformat() if session.created_at else None,
                "last_activity": session.last_activity.isoformat() if session.last_activity else None,
                "expires_at": session.expires_at.isoformat() if session.expires_at else None,
                "is_active": session.is_active,
                "connected_applications": len(session.session_bindings),
                "applications": [
                    {
                        "id": binding.application_id,
                        "oauth_token_id": binding.oauth_token_id,
                        "created_at": binding.created_at.isoformat() if binding.created_at else None,
                    }
                    for binding in session.session_bindings
                ],
            }
            session_list.append(session_data)
        
        return session_list
        
    except Exception as e:
        logger.error(f"Failed to get active sessions for user {current_user.id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve sessions")


@router.get(
    "/{session_id}",
    response_model=dict,
    summary="Get Session Details",
    description="Get detailed information about a specific SSO session",
)
async def get_session_details(
    session_id: str,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Get detailed information about a specific SSO session."""
    try:
        session_manager = CentralSessionManager(db)
        session = await session_manager.get_session_by_id(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Verify session belongs to current user
        if session.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Return detailed session information
        return {
            "id": session.id,
            "user_id": session.user_id,
            "device_fingerprint": session.device_fingerprint,
            "ip_address": session.ip_address,
            "user_agent": session.user_agent,
            "created_at": session.created_at.isoformat() if session.created_at else None,
            "last_activity": session.last_activity.isoformat() if session.last_activity else None,
            "expires_at": session.expires_at.isoformat() if session.expires_at else None,
            "is_active": session.is_active,
            "is_expired": session.is_expired,
            "connected_applications": len(session.session_bindings),
            "applications": [
                {
                    "id": binding.application_id,
                    "oauth_token_id": binding.oauth_token_id,
                    "token_type": binding.token_type,
                    "created_at": binding.created_at.isoformat() if binding.created_at else None,
                    "expires_at": binding.expires_at.isoformat() if binding.expires_at else None,
                    "expires_with_sso": binding.expires_with_sso,
                    "is_expired": binding.is_expired,
                }
                for binding in session.session_bindings
            ],
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get session details for {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve session details")


@router.post(
    "/{session_id}/extend",
    summary="Extend Session",
    description="Extend the expiration time of a specific SSO session",
)
async def extend_session(
    session_id: str,
    hours: int = 24,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Extend the expiration time of a specific SSO session."""
    try:
        session_manager = CentralSessionManager(db)
        
        # Verify session belongs to current user
        session = await session_manager.get_session_by_id(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if session.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Extend session
        success = await session_manager.extend_session(session_id, hours)
        
        if success:
            return {
                "success": True,
                "message": f"Session extended by {hours} hours",
                "session_id": session_id,
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to extend session")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to extend session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to extend session")


@router.delete(
    "/{session_id}",
    summary="Terminate Session",
    description="Terminate a specific SSO session and notify connected applications",
)
async def terminate_session(
    session_id: str,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Terminate a specific SSO session and notify connected applications."""
    try:
        session_manager = CentralSessionManager(db)
        
        # Verify session belongs to current user
        session = await session_manager.get_session_by_id(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if session.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Perform single logout for this session
        logout_result = await session_manager.propagate_logout(session_id)
        
        if logout_result.success:
            return {
                "success": True,
                "message": "Session terminated successfully",
                "session_id": session_id,
                "applications_notified": logout_result.applications_notified,
                "failed_notifications": logout_result.failed_notifications,
            }
        else:
            return {
                "success": False,
                "message": logout_result.error or "Failed to terminate session",
                "session_id": session_id,
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to terminate session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to terminate session")


@router.post(
    "/logout-all",
    summary="Logout All Sessions",
    description="Terminate all active SSO sessions for the current user",
)
async def logout_all_sessions(
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Terminate all active SSO sessions for the current user."""
    try:
        session_manager = CentralSessionManager(db)
        
        # Get all user sessions
        sessions = await session_manager.get_user_sessions(current_user.id)
        
        if not sessions:
            return {
                "success": True,
                "message": "No active sessions to terminate",
                "sessions_terminated": 0,
            }
        
        # Terminate all sessions
        terminated_count = 0
        failed_count = 0
        total_apps_notified = 0
        
        for session in sessions:
            logout_result = await session_manager.propagate_logout(session.id)
            if logout_result.success:
                terminated_count += 1
                total_apps_notified += logout_result.applications_notified
            else:
                failed_count += 1
        
        return {
            "success": True,
            "message": f"Terminated {terminated_count} sessions",
            "sessions_terminated": terminated_count,
            "sessions_failed": failed_count,
            "total_applications_notified": total_apps_notified,
        }
        
    except Exception as e:
        logger.error(f"Failed to logout all sessions for user {current_user.id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to logout all sessions")


@router.post(
    "/{session_id}/activity",
    summary="Update Session Activity",
    description="Update the last activity timestamp for a session",
)
async def update_session_activity(
    session_id: str,
    current_user: User = Depends(get_current_regular_user_from_session),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Update the last activity timestamp for a session."""
    try:
        session_manager = CentralSessionManager(db)
        
        # Verify session belongs to current user
        session = await session_manager.get_session_by_id(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if session.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Update activity
        success = await session_manager.update_session_activity(session_id)
        
        if success:
            return {
                "success": True,
                "message": "Session activity updated",
                "session_id": session_id,
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to update session activity")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update session activity for {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update session activity")
