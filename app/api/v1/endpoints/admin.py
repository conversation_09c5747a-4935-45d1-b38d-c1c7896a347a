"""
Admin Portal API endpoints for GeNieGO SSO Server.

This module implements admin-specific endpoints for user management,
application oversight, and system analytics.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import get_settings
from app.core.auth_dependencies import get_current_admin
from app.core.database import get_db
from app.models.user import (
    AuditLog,
    AuthorizationCode,
    RegisteredApplication,
    SystemSettings,
    User,
    UserAccessLog,
    UserApplicationConnection,
)
from app.models.organization import Organization, OrganizationMembership, OrganizationInvitation
from app.schemas.admin import (
    SystemSettingCreateRequest,
    SystemSettingResponse,
    SystemSettingsResponse,
    SystemSettingUpdateRequest,
)
from app.schemas.common import MessageResponse
from app.schemas.user import UserListResponse, UserResponse
from app.services.analytics.admin_analytics import AdminAnalyticsService
from app.services.auth_service import AuthService

router = APIRouter(tags=["Admin Portal"])
settings = get_settings()
logger = logging.getLogger(__name__)


def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Dependency to get authentication service."""
    return AuthService(db)


# Pydantic schemas for admin endpoints
from pydantic import BaseModel, ConfigDict, Field


class AdminApplicationResponse(BaseModel):
    """Response schema for admin application view."""

    id: str
    client_id: str
    application_name: str
    owner_email: Optional[str] = None
    owner_name: Optional[str] = None
    allowed_redirect_uris: List[str]
    allowed_scopes: List[str]
    is_active: bool
    is_approved: bool
    created_at: datetime
    stats: Dict[str, Any]
    status: str

    model_config = ConfigDict(from_attributes=True)


class AdminApplicationUpdateRequest(BaseModel):
    """Request schema for admin application updates."""

    is_active: Optional[bool] = Field(None, description="Active status")
    is_approved: Optional[bool] = Field(None, description="Approval status")
    allowed_scopes: Optional[List[str]] = Field(
        None, description="Override allowed scopes"
    )


class SystemAnalyticsResponse(BaseModel):
    """Response schema for system analytics."""

    total_users: int
    active_users: int
    total_applications: int
    active_applications: int
    total_requests_30d: int
    total_requests_7d: int
    new_users_30d: int
    top_applications: List[Dict[str, Any]]
    user_growth: List[Dict[str, Any]]
    request_trends: List[Dict[str, Any]]


@router.get(
    "/applications",
    response_model=List[AdminApplicationResponse],
    summary="List All Applications (Admin)",
    description="Get all applications in the system for admin oversight",
)
async def list_all_applications(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_approved: Optional[bool] = Query(None, description="Filter by approval status"),
    search: Optional[str] = Query(None, description="Search by name or client ID"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[AdminApplicationResponse]:
    """List all applications for admin oversight."""
    try:
        # Build query
        query = select(RegisteredApplication)

        if is_active is not None:
            query = query.where(RegisteredApplication.is_active == is_active)

        # Note: is_approved field doesn't exist in current model, treating all as approved
        # This would need to be added to the RegisteredApplication model for full functionality

        if search:
            search_filter = or_(
                RegisteredApplication.application_name.ilike(f"%{search}%"),
                RegisteredApplication.client_id.ilike(f"%{search}%"),
            )
            query = query.where(search_filter)

        # Add pagination
        offset = (page - 1) * per_page
        query = (
            query.order_by(desc(RegisteredApplication.created_at))
            .offset(offset)
            .limit(per_page)
        )

        result = await db.execute(query)
        applications = result.scalars().all()

        # Get stats for each application
        response_data = []
        for application in applications:
            # Get user count for this application
            user_count = (
                await db.scalar(
                    select(func.count(func.distinct(AuthorizationCode.user_id))).where(
                        AuthorizationCode.client_id == application.client_id
                    )
                )
                or 0
            )

            # Get monthly request count
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            monthly_requests = (
                await db.scalar(
                    select(func.count(AuthorizationCode.id)).where(
                        and_(
                            AuthorizationCode.client_id == application.client_id,
                            AuthorizationCode.created_at >= thirty_days_ago,
                        )
                    )
                )
                or 0
            )

            # Get last request time
            last_request_result = await db.execute(
                select(AuthorizationCode.created_at)
                .where(AuthorizationCode.client_id == application.client_id)
                .order_by(desc(AuthorizationCode.created_at))
                .limit(1)
            )
            last_request = last_request_result.scalar_one_or_none()
            last_request_str = "Never"
            if last_request:
                time_diff = datetime.utcnow() - last_request
                if time_diff.days > 0:
                    last_request_str = f"{time_diff.days} days ago"
                elif time_diff.seconds > 3600:
                    hours = time_diff.seconds // 3600
                    last_request_str = f"{hours} hours ago"
                elif time_diff.seconds > 60:
                    minutes = time_diff.seconds // 60
                    last_request_str = f"{minutes} minutes ago"
                else:
                    last_request_str = "Just now"

            stats = {
                "total_users": user_count,
                "monthly_requests": monthly_requests,
                "last_request": last_request_str,
            }

            # Determine status
            status = "approved" if application.is_active else "inactive"

            response_data.append(
                AdminApplicationResponse(
                    id=application.id,
                    client_id=application.client_id,
                    application_name=application.application_name,
                    owner_email="<EMAIL>",  # Would come from user relationship
                    owner_name="Developer",  # Would come from user relationship
                    allowed_redirect_uris=application.allowed_redirect_uris,
                    allowed_scopes=application.allowed_scopes,
                    is_active=application.is_active,
                    is_approved=True,  # Default to approved for now
                    created_at=application.created_at,
                    stats=stats,
                    status=status,
                )
            )

        return response_data

    except Exception as e:
        logger.error(f"Error listing applications for admin: {e}")
        raise HTTPException(status_code=500, detail="Failed to list applications")


@router.get(
    "/applications/{application_id}",
    response_model=AdminApplicationResponse,
    summary="Get Application Details (Admin)",
    description="Get specific application details for admin oversight",
)
async def get_application_details(
    application_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> AdminApplicationResponse:
    """Get specific application details for admin oversight."""
    try:
        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = result.scalar_one_or_none()

        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        # Get usage statistics
        user_count_result = await db.execute(
            select(func.count(UserAccessLog.user_id.distinct())).where(
                UserAccessLog.application_id == application.id
            )
        )
        user_count = user_count_result.scalar() or 0

        # Get monthly requests
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        monthly_requests_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                and_(
                    UserAccessLog.application_id == application.id,
                    UserAccessLog.timestamp >= thirty_days_ago,
                )
            )
        )
        monthly_requests = monthly_requests_result.scalar() or 0

        # Get last request
        last_request_result = await db.execute(
            select(UserAccessLog.timestamp)
            .where(UserAccessLog.application_id == application.id)
            .order_by(desc(UserAccessLog.timestamp))
            .limit(1)
        )
        last_request = last_request_result.scalar()
        last_request_str = last_request.isoformat() if last_request else None

        stats = {
            "total_users": user_count,
            "monthly_requests": monthly_requests,
            "last_request": last_request_str,
        }

        # Determine status
        status = "approved" if application.is_active else "inactive"

        return AdminApplicationResponse(
            id=application.id,
            client_id=application.client_id,
            application_name=application.application_name,
            owner_email="<EMAIL>",  # Would come from user relationship
            owner_name="Developer",  # Would come from user relationship
            allowed_redirect_uris=application.allowed_redirect_uris,
            allowed_scopes=application.allowed_scopes,
            is_active=application.is_active,
            is_approved=True,  # Default to approved for now
            created_at=application.created_at,
            stats=stats,
            status=status,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting application details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get application details")


@router.get(
    "/applications/developers",
    response_model=List[AdminApplicationResponse],
    summary="List Developer Applications (Admin)",
    description="Get all applications created by developers",
)
async def list_developer_applications(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[AdminApplicationResponse]:
    """List all applications created by developers."""
    try:
        # Get all applications (for now, all applications are considered developer applications)
        query = select(RegisteredApplication).order_by(
            desc(RegisteredApplication.created_at)
        )
        result = await db.execute(query)
        applications = result.scalars().all()

        response_data = []
        for application in applications:
            # Get usage statistics
            user_count_result = await db.execute(
                select(func.count(UserAccessLog.user_id.distinct())).where(
                    UserAccessLog.application_id == application.id
                )
            )
            user_count = user_count_result.scalar() or 0

            # Get monthly requests
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            monthly_requests_result = await db.execute(
                select(func.count(UserAccessLog.id)).where(
                    and_(
                        UserAccessLog.application_id == application.id,
                        UserAccessLog.timestamp >= thirty_days_ago,
                    )
                )
            )
            monthly_requests = monthly_requests_result.scalar() or 0

            # Get last request
            last_request_result = await db.execute(
                select(UserAccessLog.timestamp)
                .where(UserAccessLog.application_id == application.id)
                .order_by(desc(UserAccessLog.timestamp))
                .limit(1)
            )
            last_request = last_request_result.scalar()
            last_request_str = last_request.isoformat() if last_request else None

            stats = {
                "total_users": user_count,
                "monthly_requests": monthly_requests,
                "last_request": last_request_str,
            }

            # Determine status
            status = "approved" if application.is_active else "inactive"

            response_data.append(
                AdminApplicationResponse(
                    id=application.id,
                    client_id=application.client_id,
                    application_name=application.application_name,
                    owner_email="<EMAIL>",  # Would come from user relationship
                    owner_name="Developer",  # Would come from user relationship
                    allowed_redirect_uris=application.allowed_redirect_uris,
                    allowed_scopes=application.allowed_scopes,
                    is_active=application.is_active,
                    is_approved=True,  # Default to approved for now
                    created_at=application.created_at,
                    stats=stats,
                    status=status,
                )
            )

        return response_data

    except Exception as e:
        logger.error(f"Error listing developer applications: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to list developer applications"
        )


@router.post(
    "/applications/{application_id}/approve",
    response_model=MessageResponse,
    summary="Approve Application (Admin)",
    description="Approve an application for production use",
)
async def approve_application(
    application_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Approve an application for production use."""
    try:
        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = result.scalar_one_or_none()

        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        if application.is_active:
            raise HTTPException(
                status_code=400, detail="Application is already approved"
            )

        # Approve application
        application.is_active = True
        await db.commit()

        logger.info(
            f"Application {application.application_name} approved by admin {current_admin.email}"
        )
        return MessageResponse(
            message=f"Application '{application.application_name}' approved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving application: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to approve application")


@router.post(
    "/applications/{application_id}/reject",
    response_model=MessageResponse,
    summary="Reject Application (Admin)",
    description="Reject an application",
)
async def reject_application(
    application_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Reject an application."""
    try:
        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = result.scalar_one_or_none()

        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        if not application.is_active:
            raise HTTPException(
                status_code=400, detail="Application is already rejected/inactive"
            )

        # Reject application
        application.is_active = False
        await db.commit()

        logger.info(
            f"Application {application.application_name} rejected by admin {current_admin.email}"
        )
        return MessageResponse(
            message=f"Application '{application.application_name}' rejected successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rejecting application: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to reject application")


@router.put(
    "/applications/{application_id}",
    response_model=AdminApplicationResponse,
    summary="Update Application (Admin)",
    description="Update application settings as admin",
)
async def admin_update_application(
    application_id: str,
    update_data: AdminApplicationUpdateRequest,
    auth_service: AuthService = Depends(get_auth_service),
) -> AdminApplicationResponse:
    """Update application as admin."""
    try:
        result = await auth_service.db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = result.scalar_one_or_none()

        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        # Update fields if provided
        if update_data.is_active is not None:
            application.is_active = update_data.is_active
        if update_data.allowed_scopes is not None:
            application.allowed_scopes = update_data.allowed_scopes

        await auth_service.db.commit()
        await auth_service.db.refresh(application)

        # Get updated stats
        user_count = (
            await auth_service.db.scalar(
                select(func.count(func.distinct(AuthorizationCode.user_id))).where(
                    AuthorizationCode.client_id == application.client_id
                )
            )
            or 0
        )

        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        monthly_requests = (
            await auth_service.db.scalar(
                select(func.count(AuthorizationCode.id)).where(
                    and_(
                        AuthorizationCode.client_id == application.client_id,
                        AuthorizationCode.created_at >= thirty_days_ago,
                    )
                )
            )
            or 0
        )

        stats = {
            "total_users": user_count,
            "monthly_requests": monthly_requests,
            "last_request": "Updated",
        }

        status = "approved" if application.is_active else "inactive"

        logger.info(f"Admin updated application: {application_id}")
        return AdminApplicationResponse(
            id=application.id,
            client_id=application.client_id,
            application_name=application.application_name,
            owner_email="<EMAIL>",
            owner_name="Developer",
            allowed_redirect_uris=application.allowed_redirect_uris,
            allowed_scopes=application.allowed_scopes,
            is_active=application.is_active,
            is_approved=True,
            created_at=application.created_at,
            stats=stats,
            status=status,
        )

    except HTTPException:
        raise
    except Exception as e:
        await auth_service.db.rollback()
        logger.error(f"Error updating application {application_id} as admin: {e}")
        raise HTTPException(status_code=500, detail="Failed to update application")


@router.delete(
    "/applications/{application_id}",
    response_model=MessageResponse,
    summary="Delete Application (Admin)",
    description="Permanently deactivate a application as admin",
)
async def admin_delete_application(
    application_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> MessageResponse:
    """Delete application as admin."""
    try:
        result = await auth_service.db.execute(
            select(RegisteredApplication).where(
                RegisteredApplication.id == application_id
            )
        )
        application = result.scalar_one_or_none()

        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        # Deactivate the application
        application.is_active = False
        await auth_service.db.commit()

        logger.info(f"Admin deactivated application: {application_id}")
        return MessageResponse(
            message=f"Application {application.application_name} has been deactivated by admin"
        )

    except HTTPException:
        raise
    except Exception as e:
        await auth_service.db.rollback()
        logger.error(f"Error deleting application {application_id} as admin: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete application")


@router.get(
    "/analytics/system",
    response_model=SystemAnalyticsResponse,
    summary="System Analytics (Admin)",
    description="Get comprehensive system analytics for admin dashboard",
)
async def get_system_analytics(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> SystemAnalyticsResponse:
    """Get comprehensive system analytics."""
    try:
        # Get total users (excluding admin users)
        total_users = (
            await db.scalar(select(func.count(User.id)).where(User.role != "admin"))
            or 0
        )

        # Get active users (logged in within last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        active_users = (
            await db.scalar(
                select(func.count(User.id)).where(User.last_login >= thirty_days_ago)
            )
            or 0
        )

        # Get total applications
        total_applications = (
            await db.scalar(select(func.count(RegisteredApplication.id))) or 0
        )

        # Get active applications
        active_applications = (
            await db.scalar(
                select(func.count(RegisteredApplication.id)).where(
                    RegisteredApplication.is_active == True
                )
            )
            or 0
        )

        # Get total requests in last 30 days
        total_requests_30d = (
            await db.scalar(
                select(func.count(AuthorizationCode.id)).where(
                    AuthorizationCode.created_at >= thirty_days_ago
                )
            )
            or 0
        )

        # Get total requests in last 7 days
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        total_requests_7d = (
            await db.scalar(
                select(func.count(AuthorizationCode.id)).where(
                    AuthorizationCode.created_at >= seven_days_ago
                )
            )
            or 0
        )

        # Get new users in last 30 days (excluding admin users)
        new_users_30d = (
            await db.scalar(
                select(func.count(User.id)).where(
                    and_(User.created_at >= thirty_days_ago, User.role != "admin")
                )
            )
            or 0
        )

        # Get top applications by usage
        top_applications_result = await db.execute(
            select(
                RegisteredApplication.application_name,
                RegisteredApplication.client_id,
                func.count(AuthorizationCode.id).label("request_count"),
            )
            .join(
                AuthorizationCode,
                RegisteredApplication.client_id == AuthorizationCode.client_id,
            )
            .where(AuthorizationCode.created_at >= thirty_days_ago)
            .group_by(
                RegisteredApplication.id,
                RegisteredApplication.application_name,
                RegisteredApplication.client_id,
            )
            .order_by(desc("request_count"))
            .limit(5)
        )

        top_applications = []
        for row in top_applications_result:
            top_applications.append(
                {
                    "name": row.application_name,
                    "client_id": row.client_id,
                    "requests": row.request_count,
                }
            )

        # Generate mock user growth data (last 7 days)
        user_growth = []
        for i in range(7):
            date = datetime.utcnow() - timedelta(days=6 - i)
            # In a real implementation, you'd query actual daily user counts
            daily_users = max(0, total_users - (6 - i) * 10)  # Mock declining growth
            user_growth.append(
                {"date": date.strftime("%Y-%m-%d"), "users": daily_users}
            )

        # Generate mock request trends (last 7 days)
        request_trends = []
        for i in range(7):
            date = datetime.utcnow() - timedelta(days=6 - i)
            # Mock request distribution
            daily_requests = max(0, int(total_requests_7d * (0.1 + 0.05 * i)))
            request_trends.append(
                {"date": date.strftime("%Y-%m-%d"), "requests": daily_requests}
            )

        return SystemAnalyticsResponse(
            total_users=total_users,
            active_users=active_users,
            total_applications=total_applications,
            active_applications=active_applications,
            total_requests_30d=total_requests_30d,
            total_requests_7d=total_requests_7d,
            new_users_30d=new_users_30d,
            top_applications=top_applications,
            user_growth=user_growth,
            request_trends=request_trends,
        )

    except Exception as e:
        logger.error(f"Error getting system analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system analytics")


@router.post(
    "/users/{user_id}/reset-password",
    response_model=MessageResponse,
    summary="Reset User Password (Admin)",
    description="Initiate password reset for a user as admin",
)
async def admin_reset_user_password(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> MessageResponse:
    """Reset user password as admin."""
    try:
        # Check if user exists
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # In a real implementation, this would:
        # 1. Generate a password reset token
        # 2. Send an email to the user
        # 3. Store the token in the database

        # For now, just log the action
        logger.info(f"Admin initiated password reset for user: {user_id}")

        return MessageResponse(
            message=f"Password reset email has been sent to {user.email}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting password for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset user password")


@router.get(
    "/users/{user_id}/export",
    summary="Export User Data (Admin)",
    description="Export user data for admin purposes",
)
async def admin_export_user_data(
    user_id: str,
    auth_service: AuthService = Depends(get_auth_service),
) -> Dict[str, Any]:
    """Export user data as admin."""
    try:
        # Get user details
        user = await auth_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get user's authorization history
        auth_history_result = await auth_service.db.execute(
            select(AuthorizationCode.client_id, AuthorizationCode.created_at)
            .where(AuthorizationCode.user_id == user_id)
            .order_by(desc(AuthorizationCode.created_at))
            .limit(50)  # Last 50 authorizations
        )

        auth_history = []
        for row in auth_history_result:
            auth_history.append(
                {
                    "client_id": row.client_id,
                    "authorized_at": row.created_at.isoformat(),
                }
            )

        return {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "authorization_history": auth_history,
            "exported_at": datetime.utcnow().isoformat(),
            "exported_by": "admin",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting user data for {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to export user data")


@router.get(
    "/users/export",
    summary="Export All Users (Admin)",
    description="Export all users data for admin purposes",
)
async def admin_export_all_users(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_verified: Optional[bool] = Query(None, description="Filter by verified status"),
    auth_service: AuthService = Depends(get_auth_service),
) -> Dict[str, Any]:
    """Export all users data as admin."""
    try:
        # Get users with filters
        users = await auth_service.list_users(
            page=1,
            per_page=1000,  # Large limit for export
            is_active=is_active,
            is_verified=is_verified,
        )

        users_data = []
        for user in users:
            users_data.append(
                {
                    "user_id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "role": user.role,
                    "is_active": user.is_active,
                    "is_verified": user.is_verified,
                    "created_at": user.created_at.isoformat(),
                    "last_login": (
                        user.last_login.isoformat() if user.last_login else None
                    ),
                }
            )

        return {
            "total_users": len(users_data),
            "filters": {"is_active": is_active, "is_verified": is_verified},
            "users": users_data,
            "exported_at": datetime.utcnow().isoformat(),
            "exported_by": "admin",
        }

    except Exception as e:
        logger.error(f"Error exporting all users: {e}")
        raise HTTPException(status_code=500, detail="Failed to export users data")


# ============================================================================
# New Admin Portal API Endpoints
# ============================================================================


def get_admin_analytics_service(
    db: AsyncSession = Depends(get_db),
) -> AdminAnalyticsService:
    """Dependency to get admin analytics service."""
    return AdminAnalyticsService(db)


@router.get(
    "/users",
    summary="List All Users",
    description="Get all users in the system for admin oversight",
)
async def list_all_users(
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    offset: int = Query(0, ge=0, description="Number of users to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """List all users for admin oversight."""
    try:
        # Get both developers and regular users
        developers = await admin_service.get_developer_users(limit=1000, offset=0)
        regular_users = await admin_service.get_regular_users(limit=1000, offset=0)

        # Combine all users
        all_users = developers + regular_users

        # Apply pagination to combined results
        total = len(all_users)
        paginated_users = all_users[offset : offset + limit]

        return {
            "users": paginated_users,
            "total": total,
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error listing all users: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve users")


@router.get(
    "/users/developers",
    summary="List Developer Users",
    description="Get all users with developer role",
)
async def list_developer_users(
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    offset: int = Query(0, ge=0, description="Number of users to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """List all developer role users."""
    try:
        developers = await admin_service.get_developer_users(limit=limit, offset=offset)

        return {
            "developers": developers,
            "total": len(developers),
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error listing developer users: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve developer users"
        )


@router.get(
    "/users/regular",
    summary="List Regular Users",
    description="Get all regular users (non-admin, non-developer)",
)
async def list_regular_users(
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    offset: int = Query(0, ge=0, description="Number of users to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """List all regular users."""
    try:
        users = await admin_service.get_regular_users(limit=limit, offset=offset)

        return {
            "users": users,
            "total": len(users),
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error listing regular users: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve regular users")


@router.get(
    "/users/end-users",
    summary="List End Users (Alias)",
    description="Get all end users (alias for regular users)",
)
async def list_end_users(
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    offset: int = Query(0, ge=0, description="Number of users to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """List all end users (alias for regular users)."""
    try:
        users = await admin_service.get_regular_users(limit=limit, offset=offset)

        return {
            "users": users,
            "total": len(users),
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error listing end users: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve end users")


@router.get(
    "/users/{user_id}",
    summary="Get User Details (Admin)",
    description="Get specific user details for admin oversight",
)
async def get_user_details(
    user_id: str,
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Get specific user details for admin oversight."""
    try:
        # Get user details from admin service
        developers = await admin_service.get_developer_users(limit=1000, offset=0)
        regular_users = await admin_service.get_regular_users(limit=1000, offset=0)

        # Find the user in either list
        user = None
        for u in developers + regular_users:
            if u.get("id") == user_id or u.get("user_id") == user_id:
                user = u
                break

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user details")


@router.get(
    "/users/{user_id}/applications",
    summary="Get User's Connected Applications",
    description="Get user's connected applications and connection details",
)
async def get_user_applications(
    user_id: str,
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Get user's connected applications."""
    try:
        applications = await admin_service.get_user_applications(user_id)

        return {
            "user_id": user_id,
            "applications": applications,
            "total_connections": len(applications),
        }

    except Exception as e:
        logger.error(f"Error getting user applications: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve user applications"
        )


@router.get(
    "/users/{user_id}/activity",
    summary="Get User's Access Logs",
    description="Get user's access logs and activity history",
)
async def get_user_activity(
    user_id: str,
    limit: int = Query(50, ge=1, le=500, description="Number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Get user's access logs and activity."""
    try:
        activity = await admin_service.get_user_activity(
            user_id=user_id, limit=limit, offset=offset
        )

        return {
            "user_id": user_id,
            "activity": activity,
            "total": len(activity),
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error getting user activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user activity")


@router.get(
    "/applications/by-developer/{developer_id}",
    summary="Filter Applications by Developer",
    description="Get all applications registered by a specific developer",
)
async def get_applications_by_developer(
    developer_id: str,
    limit: int = Query(
        100, ge=1, le=1000, description="Number of applications to return"
    ),
    offset: int = Query(0, ge=0, description="Number of applications to skip"),
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Filter applications by developer."""
    try:
        applications = await admin_service.get_applications_by_developer(
            developer_id=developer_id, limit=limit, offset=offset
        )

        return {
            "developer_id": developer_id,
            "applications": applications,
            "total": len(applications),
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error getting applications by developer: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve applications")


@router.post(
    "/applications/{application_id}/approve",
    summary="Approve Application Registration",
    description="Approve a application registration for production use",
)
async def approve_application(
    application_id: str,
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Approve application registration."""
    try:
        success = await admin_service.approve_application(application_id)

        if not success:
            raise HTTPException(status_code=404, detail="Application not found")

        return {
            "message": "Application approved successfully",
            "application_id": application_id,
            "approved_by": current_admin.email,
            "approved_at": datetime.utcnow(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving application: {e}")
        raise HTTPException(status_code=500, detail="Failed to approve application")


@router.post(
    "/applications/{application_id}/reject",
    summary="Reject Application Registration",
    description="Reject a application registration and deactivate it",
)
async def reject_application(
    application_id: str,
    current_admin: User = Depends(get_current_admin),
    admin_service: AdminAnalyticsService = Depends(get_admin_analytics_service),
) -> Dict[str, Any]:
    """Reject application registration."""
    try:
        success = await admin_service.reject_application(application_id)

        if not success:
            raise HTTPException(status_code=404, detail="Application not found")

        return {
            "message": "Application rejected and deactivated",
            "application_id": application_id,
            "rejected_by": current_admin.email,
            "rejected_at": datetime.utcnow(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rejecting application: {e}")
        raise HTTPException(status_code=500, detail="Failed to reject application")


# System Settings Management Endpoints
@router.get(
    "/settings",
    response_model=SystemSettingsResponse,
    summary="Get System Settings",
    description="Get all system configuration settings",
)
async def get_system_settings(
    category: Optional[str] = Query(None, description="Filter by category"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> SystemSettingsResponse:
    """Get system configuration settings."""
    try:
        # Build query
        query = select(SystemSettings)

        if category:
            query = query.where(SystemSettings.category == category)

        # Execute query
        result = await db.execute(
            query.order_by(SystemSettings.category, SystemSettings.setting_key)
        )
        settings_list = result.scalars().all()

        # Get unique categories
        categories_result = await db.execute(select(SystemSettings.category).distinct())
        categories = [cat for cat in categories_result.scalars().all()]

        return SystemSettingsResponse(
            settings=[
                SystemSettingResponse.model_validate(setting)
                for setting in settings_list
            ],
            total=len(settings_list),
            categories=categories,
        )

    except Exception as e:
        logger.error(f"Error getting system settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system settings")


@router.put(
    "/settings/{setting_key}",
    response_model=SystemSettingResponse,
    summary="Update System Setting",
    description="Update a specific system setting",
)
async def update_system_setting(
    setting_key: str,
    setting_data: SystemSettingUpdateRequest,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> SystemSettingResponse:
    """Update a system setting."""
    try:
        # Get existing setting
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.setting_key == setting_key)
        )
        setting = result.scalar_one_or_none()

        if not setting:
            raise HTTPException(status_code=404, detail="Setting not found")

        if setting.is_readonly:
            raise HTTPException(status_code=400, detail="Setting is read-only")

        # Update setting
        setting.setting_value = setting_data.setting_value
        if setting_data.description is not None:
            setting.description = setting_data.description
        setting.updated_by = current_admin.id
        setting.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(setting)

        logger.info(
            f"System setting {setting_key} updated by admin {current_admin.email}"
        )
        return SystemSettingResponse.model_validate(setting)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating system setting: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update system setting")


@router.post(
    "/settings",
    response_model=SystemSettingResponse,
    summary="Create System Setting",
    description="Create a new system setting",
)
async def create_system_setting(
    setting_data: SystemSettingCreateRequest,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> SystemSettingResponse:
    """Create a new system setting."""
    try:
        # Check if setting already exists
        result = await db.execute(
            select(SystemSettings).where(
                SystemSettings.setting_key == setting_data.setting_key
            )
        )
        existing_setting = result.scalar_one_or_none()

        if existing_setting:
            raise HTTPException(status_code=400, detail="Setting already exists")

        # Create new setting
        new_setting = SystemSettings(
            setting_key=setting_data.setting_key,
            setting_value=setting_data.setting_value,
            setting_type=setting_data.setting_type,
            category=setting_data.category,
            description=setting_data.description,
            is_public=setting_data.is_public,
            is_readonly=setting_data.is_readonly,
            created_by=current_admin.id,
            updated_by=current_admin.id,
        )

        db.add(new_setting)
        await db.commit()
        await db.refresh(new_setting)

        logger.info(
            f"System setting {setting_data.setting_key} created by admin {current_admin.email}"
        )
        return SystemSettingResponse.model_validate(new_setting)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating system setting: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create system setting")


# Security Monitoring Endpoints
@router.get(
    "/security/overview",
    response_model=Dict[str, Any],
    summary="Security Overview",
    description="Get system-wide security overview and metrics",
)
async def get_security_overview(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get security overview for admin dashboard."""
    try:
        # Get security metrics
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)

        # Failed login attempts in last 24h
        failed_logins_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                and_(
                    UserAccessLog.action == "login",
                    UserAccessLog.success == False,
                    UserAccessLog.timestamp >= last_24h,
                )
            )
        )
        failed_logins_24h = failed_logins_result.scalar() or 0

        # Successful logins in last 24h
        successful_logins_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                and_(
                    UserAccessLog.action == "login",
                    UserAccessLog.success == True,
                    UserAccessLog.timestamp >= last_24h,
                )
            )
        )
        successful_logins_24h = successful_logins_result.scalar() or 0

        # Users with 2FA enabled
        users_with_2fa_result = await db.execute(
            select(func.count(User.id)).where(User.two_factor_enabled == True)
        )
        users_with_2fa = users_with_2fa_result.scalar() or 0

        # Total active users (logged in last 7 days)
        active_users_result = await db.execute(
            select(func.count(func.distinct(UserAccessLog.user_id))).where(
                and_(
                    UserAccessLog.action == "login",
                    UserAccessLog.success == True,
                    UserAccessLog.timestamp >= last_7d,
                )
            )
        )
        active_users_7d = active_users_result.scalar() or 0

        # Suspicious activity (multiple failed logins from same IP)
        suspicious_ips_result = await db.execute(
            select(
                UserAccessLog.ip_address,
                func.count(UserAccessLog.id).label("failed_count"),
            )
            .where(
                and_(
                    UserAccessLog.action == "login",
                    UserAccessLog.success == False,
                    UserAccessLog.timestamp >= last_24h,
                )
            )
            .group_by(UserAccessLog.ip_address)
            .having(func.count(UserAccessLog.id) >= 5)
        )
        suspicious_ips = suspicious_ips_result.fetchall()

        return {
            "failed_logins_24h": failed_logins_24h,
            "successful_logins_24h": successful_logins_24h,
            "users_with_2fa": users_with_2fa,
            "active_users_7d": active_users_7d,
            "suspicious_ips_count": len(suspicious_ips),
            "suspicious_ips": [
                {"ip": ip, "failed_attempts": count} for ip, count in suspicious_ips
            ],
            "security_score": min(
                100, max(0, 100 - (failed_logins_24h * 2) - (len(suspicious_ips) * 10))
            ),
        }
    except Exception as e:
        logger.error(f"Error getting security overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security overview")


@router.get(
    "/security/alerts",
    response_model=List[Dict[str, Any]],
    summary="Security Alerts",
    description="Get recent security alerts and incidents",
)
async def get_security_alerts(
    limit: int = Query(50, ge=1, le=100),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get security alerts."""
    try:
        # Get recent failed login attempts
        failed_logins_result = await db.execute(
            select(UserAccessLog)
            .where(
                and_(UserAccessLog.action == "login", UserAccessLog.success == False)
            )
            .order_by(desc(UserAccessLog.timestamp))
            .limit(limit)
        )
        failed_logins = failed_logins_result.scalars().all()

        alerts = []
        for log in failed_logins:
            alerts.append(
                {
                    "id": log.id,
                    "type": "failed_login",
                    "severity": "medium",
                    "message": f"Failed login attempt for user {log.user_id}",
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                    "timestamp": log.timestamp.isoformat(),
                    "details": {
                        "user_id": log.user_id,
                        "error_message": log.error_message,
                    },
                }
            )

        return alerts
    except Exception as e:
        logger.error(f"Error getting security alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security alerts")


@router.get(
    "/security/audit-log",
    response_model=List[Dict[str, Any]],
    summary="Security Audit Log",
    description="Get detailed security audit log",
)
async def get_security_audit_log(
    action: Optional[str] = Query(None, description="Filter by action type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get security audit log with filtering."""
    try:
        # Build query with filters
        query = select(UserAccessLog)

        conditions = []
        if action:
            conditions.append(UserAccessLog.action == action)
        if user_id:
            conditions.append(UserAccessLog.user_id == user_id)
        if start_date:
            conditions.append(UserAccessLog.timestamp >= start_date)
        if end_date:
            conditions.append(UserAccessLog.timestamp <= end_date)

        if conditions:
            query = query.where(and_(*conditions))

        query = (
            query.order_by(desc(UserAccessLog.timestamp)).offset(offset).limit(limit)
        )

        result = await db.execute(query)
        logs = result.scalars().all()

        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "application_id": log.application_id,
                "action": log.action,
                "success": log.success,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "error_message": log.error_message,
                "timestamp": log.timestamp.isoformat(),
            }
            for log in logs
        ]
    except Exception as e:
        logger.error(f"Error getting security audit log: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security audit log")


# Advanced User Management Endpoints
@router.put(
    "/users/{user_id}/suspend",
    response_model=MessageResponse,
    summary="Suspend User",
    description="Suspend a user account",
)
async def suspend_user(
    user_id: str,
    reason: str = Query(..., description="Reason for suspension"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Suspend a user account."""
    try:
        # Get user
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        if user.role == "admin":
            raise HTTPException(status_code=400, detail="Cannot suspend admin users")

        if not user.is_active:
            raise HTTPException(status_code=400, detail="User is already suspended")

        # Suspend user
        user.is_active = False

        # Log the action
        access_log = UserAccessLog(
            user_id=user.id,
            application_id=None,  # Admin actions don't have specific application
            client_id=None,       # Admin actions don't have specific client
            action="suspend",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message=f"Suspended by admin. Reason: {reason}",
        )
        db.add(access_log)

        await db.commit()

        logger.info(
            f"User {user.email} suspended by admin {current_admin.email}. Reason: {reason}"
        )
        return MessageResponse(message=f"User {user.email} suspended successfully")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error suspending user: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to suspend user")


@router.put(
    "/users/{user_id}/reactivate",
    response_model=MessageResponse,
    summary="Reactivate User",
    description="Reactivate a suspended user account",
)
async def reactivate_user(
    user_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Reactivate a suspended user account."""
    try:
        # Get user
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        if user.is_active:
            raise HTTPException(status_code=400, detail="User is already active")

        # Reactivate user
        user.is_active = True

        # Log the action
        access_log = UserAccessLog(
            user_id=user.id,
            application_id=None,  # Admin actions don't have specific application
            client_id=None,       # Admin actions don't have specific client
            action="reactivate",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message="Reactivated by admin",
        )
        db.add(access_log)

        await db.commit()

        logger.info(f"User {user.email} reactivated by admin {current_admin.email}")
        return MessageResponse(message=f"User {user.email} reactivated successfully")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reactivating user: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to reactivate user")


@router.put(
    "/users/{user_id}/reset-password",
    response_model=MessageResponse,
    summary="Reset User Password",
    description="Reset a user's password (admin action)",
)
async def admin_reset_user_password(
    user_id: str,
    new_password: str = Query(..., min_length=8, description="New password"),
    current_admin: User = Depends(get_current_admin),
    auth_service: AuthService = Depends(get_auth_service),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Reset user password (admin action)."""
    try:
        # Get user
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Reset password
        user.set_password(new_password)
        user.password_changed_at = datetime.utcnow()

        # Log the action
        access_log = UserAccessLog(
            user_id=user.id,
            action="password_reset",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message="Password reset by admin",
        )
        db.add(access_log)

        await db.commit()

        logger.info(
            f"Password reset for user {user.email} by admin {current_admin.email}"
        )
        return MessageResponse(
            message=f"Password reset successfully for user {user.email}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting user password: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to reset user password")


# Advanced Application Management Endpoints
@router.put(
    "/applications/{app_id}/suspend",
    response_model=MessageResponse,
    summary="Suspend Application",
    description="Suspend an application (disable access)",
)
async def suspend_application(
    app_id: str,
    reason: str = Query(..., description="Reason for suspension"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Suspend an application."""
    try:
        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(RegisteredApplication.id == app_id)
        )
        app = result.scalar_one_or_none()

        if not app:
            raise HTTPException(status_code=404, detail="Application not found")

        if not app.is_active:
            raise HTTPException(
                status_code=400, detail="Application is already suspended"
            )

        # Suspend application
        app.is_active = False

        # Log the action
        access_log = UserAccessLog(
            user_id=current_admin.id,  # Admin who performed the action
            application_id=app.id,     # The application being suspended
            client_id=None,            # Admin actions don't have specific client
            action="app_suspend",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message=f"Application suspended by admin. Reason: {reason}",
        )
        db.add(access_log)

        await db.commit()

        logger.info(
            f"Application {app.application_name} suspended by admin {current_admin.email}. Reason: {reason}"
        )
        return MessageResponse(
            message=f"Application {app.application_name} suspended successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error suspending application: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to suspend application")


@router.put(
    "/applications/{app_id}/reactivate",
    response_model=MessageResponse,
    summary="Reactivate Application",
    description="Reactivate a suspended application",
)
async def reactivate_application(
    app_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Reactivate a suspended application."""
    try:
        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(RegisteredApplication.id == app_id)
        )
        app = result.scalar_one_or_none()

        if not app:
            raise HTTPException(status_code=404, detail="Application not found")

        if app.is_active:
            raise HTTPException(status_code=400, detail="Application is already active")

        # Reactivate application
        app.is_active = True

        # Log the action
        access_log = UserAccessLog(
            user_id=current_admin.id,  # Admin who performed the action
            application_id=app.id,     # The application being reactivated
            client_id=None,            # Admin actions don't have specific client
            action="app_reactivate",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message="Application reactivated by admin",
        )
        db.add(access_log)

        await db.commit()

        logger.info(
            f"Application {app.application_name} reactivated by admin {current_admin.email}"
        )
        return MessageResponse(
            message=f"Application {app.application_name} reactivated successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reactivating application: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to reactivate application")


@router.delete(
    "/applications/{app_id}/force-delete",
    response_model=MessageResponse,
    summary="Force Delete Application",
    description="Force delete an application and all its data (admin only)",
)
async def force_delete_application(
    app_id: str,
    confirm: bool = Query(False, description="Confirmation flag"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> MessageResponse:
    """Force delete an application (admin only)."""
    try:
        if not confirm:
            raise HTTPException(
                status_code=400, detail="Confirmation required for force delete"
            )

        # Get application
        result = await db.execute(
            select(RegisteredApplication).where(RegisteredApplication.id == app_id)
        )
        app = result.scalar_one_or_none()

        if not app:
            raise HTTPException(status_code=404, detail="Application not found")

        app_name = app.application_name

        # Delete all related data
        # Delete user connections
        await db.execute(
            select(UserApplicationConnection).where(
                UserApplicationConnection.application_id == app_id
            )
        )

        # Delete authorization codes
        await db.execute(
            select(AuthorizationCode).where(AuthorizationCode.application_id == app_id)
        )

        # Delete the application
        await db.delete(app)

        # Log the action
        access_log = UserAccessLog(
            application_id=app_id,
            action="app_force_delete",
            success=True,
            ip_address="admin_action",
            user_agent=f"Admin: {current_admin.email}",
            error_message="Application force deleted by admin",
        )
        db.add(access_log)

        await db.commit()

        logger.warning(
            f"Application {app_name} force deleted by admin {current_admin.email}"
        )
        return MessageResponse(
            message=f"Application {app_name} force deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error force deleting application: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=500, detail="Failed to force delete application"
        )


# System Health Monitoring Endpoints
@router.get(
    "/health/overview",
    response_model=Dict[str, Any],
    summary="System Health Overview",
    description="Get overall system health status and metrics",
)
async def get_system_health_overview(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get system health overview."""
    try:
        # Database health check
        try:
            await db.execute(select(1))
            db_status = "healthy"
            db_response_time = "< 10ms"  # Placeholder
        except Exception:
            db_status = "unhealthy"
            db_response_time = "timeout"

        # Get system metrics
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)

        # Total users (excluding admin users)
        total_users_result = await db.execute(
            select(func.count(User.id)).where(User.role != "admin")
        )
        total_users = total_users_result.scalar() or 0

        # Active users (logged in last 24h)
        active_users_result = await db.execute(
            select(func.count(func.distinct(UserAccessLog.user_id))).where(
                and_(
                    UserAccessLog.action == "login",
                    UserAccessLog.success == True,
                    UserAccessLog.timestamp >= last_24h,
                )
            )
        )
        active_users = active_users_result.scalar() or 0

        # Total applications
        total_apps_result = await db.execute(
            select(func.count(RegisteredApplication.id))
        )
        total_apps = total_apps_result.scalar() or 0

        # Active applications (used in last 24h)
        active_apps_result = await db.execute(
            select(func.count(func.distinct(UserAccessLog.application_id))).where(
                UserAccessLog.timestamp >= last_24h
            )
        )
        active_apps = active_apps_result.scalar() or 0

        # Error rate
        total_requests_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                UserAccessLog.timestamp >= last_24h
            )
        )
        total_requests = total_requests_result.scalar() or 0

        failed_requests_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                and_(
                    UserAccessLog.timestamp >= last_24h, UserAccessLog.success == False
                )
            )
        )
        failed_requests = failed_requests_result.scalar() or 0

        error_rate = (
            round((failed_requests / total_requests) * 100, 2)
            if total_requests > 0
            else 0
        )

        # Overall health score
        health_score = 100
        if db_status != "healthy":
            health_score -= 50
        if error_rate > 5:
            health_score -= 20
        if error_rate > 10:
            health_score -= 20

        health_status = (
            "healthy"
            if health_score >= 80
            else "degraded" if health_score >= 60 else "unhealthy"
        )

        return {
            "overall_status": health_status,
            "health_score": health_score,
            "database": {"status": db_status, "response_time": db_response_time},
            "metrics": {
                "total_users": total_users,
                "active_users_24h": active_users,
                "total_applications": total_apps,
                "active_applications_24h": active_apps,
                "total_requests_24h": total_requests,
                "error_rate_24h": error_rate,
            },
            "timestamp": now.isoformat(),
        }
    except Exception as e:
        logger.error(f"Error getting system health overview: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get system health overview"
        )


@router.get(
    "/health/metrics",
    response_model=Dict[str, Any],
    summary="Detailed System Metrics",
    description="Get detailed system performance metrics",
)
async def get_system_metrics(
    hours: int = Query(24, ge=1, le=168, description="Hours of metrics to retrieve"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get detailed system metrics."""
    try:
        start_time = datetime.utcnow() - timedelta(hours=hours)

        # Request metrics by hour
        hourly_metrics_result = await db.execute(
            select(
                func.date_format(UserAccessLog.timestamp, "%Y-%m-%d %H").label("hour"),
                func.count(UserAccessLog.id).label("total_requests"),
                func.sum(
                    func.case([(UserAccessLog.success == True, 1)], else_=0)
                ).label("successful_requests"),
                func.count(func.distinct(UserAccessLog.user_id)).label("unique_users"),
                func.count(func.distinct(UserAccessLog.application_id)).label(
                    "unique_apps"
                ),
            )
            .where(UserAccessLog.timestamp >= start_time)
            .group_by("hour")
            .order_by("hour")
        )
        hourly_metrics = hourly_metrics_result.fetchall()

        # Top applications by usage
        top_apps_result = await db.execute(
            select(
                RegisteredApplication.application_name,
                func.count(UserAccessLog.id).label("request_count"),
            )
            .join(
                UserAccessLog, RegisteredApplication.id == UserAccessLog.application_id
            )
            .where(UserAccessLog.timestamp >= start_time)
            .group_by(RegisteredApplication.id, RegisteredApplication.application_name)
            .order_by(desc("request_count"))
            .limit(10)
        )
        top_apps = top_apps_result.fetchall()

        # Error breakdown
        error_breakdown_result = await db.execute(
            select(
                UserAccessLog.error_message,
                func.count(UserAccessLog.id).label("error_count"),
            )
            .where(
                and_(
                    UserAccessLog.timestamp >= start_time,
                    UserAccessLog.success == False,
                    UserAccessLog.error_message.isnot(None),
                )
            )
            .group_by(UserAccessLog.error_message)
            .order_by(desc("error_count"))
            .limit(10)
        )
        error_breakdown = error_breakdown_result.fetchall()

        return {
            "period_hours": hours,
            "hourly_metrics": [
                {
                    "hour": hour,
                    "total_requests": total,
                    "successful_requests": successful,
                    "unique_users": users,
                    "unique_applications": apps,
                    "success_rate": (
                        round((successful / total) * 100, 2) if total > 0 else 0
                    ),
                }
                for hour, total, successful, users, apps in hourly_metrics
            ],
            "top_applications": [
                {"name": name, "request_count": count} for name, count in top_apps
            ],
            "error_breakdown": [
                {"error": error, "count": count} for error, count in error_breakdown
            ],
        }
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system metrics")


# Audit & Compliance Endpoints
@router.get(
    "/audit/logs",
    response_model=List[Dict[str, Any]],
    summary="Audit Logs",
    description="Get comprehensive audit logs for compliance",
)
async def get_audit_logs(
    action: Optional[str] = Query(None, description="Filter by action type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    application_id: Optional[str] = Query(None, description="Filter by application ID"),
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get audit logs with comprehensive filtering."""
    try:
        # Build query with filters
        query = (
            select(UserAccessLog, User.email, RegisteredApplication.application_name)
            .outerjoin(User, UserAccessLog.user_id == User.id)
            .outerjoin(
                RegisteredApplication,
                UserAccessLog.application_id == RegisteredApplication.id,
            )
        )

        conditions = []
        if action:
            conditions.append(UserAccessLog.action == action)
        if user_id:
            conditions.append(UserAccessLog.user_id == user_id)
        if application_id:
            conditions.append(UserAccessLog.application_id == application_id)
        if start_date:
            conditions.append(UserAccessLog.timestamp >= start_date)
        if end_date:
            conditions.append(UserAccessLog.timestamp <= end_date)

        if conditions:
            query = query.where(and_(*conditions))

        query = (
            query.order_by(desc(UserAccessLog.timestamp)).offset(offset).limit(limit)
        )

        result = await db.execute(query)
        logs = result.fetchall()

        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "user_email": email,
                "application_id": log.application_id,
                "application_name": app_name,
                "action": log.action,
                "success": log.success,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "error_message": log.error_message,
                "timestamp": log.timestamp.isoformat(),
            }
            for log, email, app_name in logs
        ]
    except Exception as e:
        logger.error(f"Error getting audit logs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get audit logs")


@router.get(
    "/audit/system-logs",
    response_model=List[Dict[str, Any]],
    summary="System Audit Logs",
    description="Get comprehensive system audit logs for security monitoring",
)
async def get_system_audit_logs(
    action: Optional[str] = Query(None, description="Filter by action type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    resource_id: Optional[str] = Query(None, description="Filter by resource ID"),
    status: Optional[str] = Query(None, description="Filter by status (success, failure, error)"),
    ip_address: Optional[str] = Query(None, description="Filter by IP address"),
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get system-wide audit logs with comprehensive filtering."""
    try:
        # Build query with filters using AuditLog model
        query = (
            select(AuditLog, User.email.label("user_email"))
            .outerjoin(User, AuditLog.user_id == User.id)
        )

        conditions = []

        # Apply filters
        if action:
            conditions.append(AuditLog.action == action)
        if user_id:
            conditions.append(AuditLog.user_id == user_id)
        if resource_type:
            conditions.append(AuditLog.resource_type == resource_type)
        if resource_id:
            conditions.append(AuditLog.resource_id == resource_id)
        if status:
            conditions.append(AuditLog.status == status)
        if ip_address:
            conditions.append(AuditLog.ip_address == ip_address)
        if start_date:
            conditions.append(AuditLog.created_at >= start_date)
        if end_date:
            conditions.append(AuditLog.created_at <= end_date)

        if conditions:
            query = query.where(and_(*conditions))

        # Order by timestamp descending and apply pagination
        query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)

        result = await db.execute(query)
        logs = result.fetchall()

        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "user_email": user_email,
                "action": log.action,
                "resource_type": log.resource_type,
                "resource_id": log.resource_id,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "session_id": log.session_id,
                "details": log.details,
                "status": log.status,
                "error_message": log.error_message,
                "created_at": log.created_at.isoformat(),
            }
            for log, user_email in logs
        ]
    except Exception as e:
        logger.error(f"Error getting system audit logs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system audit logs")


@router.get(
    "/audit/statistics",
    response_model=Dict[str, Any],
    summary="Audit Statistics",
    description="Get audit log statistics and summaries",
)
async def get_audit_statistics(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get audit log statistics and summaries."""
    try:
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # Total audit events
        total_events_result = await db.execute(
            select(func.count(AuditLog.id)).where(
                AuditLog.created_at.between(start_date, end_date)
            )
        )
        total_events = total_events_result.scalar() or 0

        # Events by action type
        action_stats_result = await db.execute(
            select(AuditLog.action, func.count(AuditLog.id))
            .where(AuditLog.created_at.between(start_date, end_date))
            .group_by(AuditLog.action)
            .order_by(desc(func.count(AuditLog.id)))
        )
        action_stats = action_stats_result.fetchall()

        # Events by status
        status_stats_result = await db.execute(
            select(AuditLog.status, func.count(AuditLog.id))
            .where(AuditLog.created_at.between(start_date, end_date))
            .group_by(AuditLog.status)
        )
        status_stats = status_stats_result.fetchall()

        # Events by resource type
        resource_stats_result = await db.execute(
            select(AuditLog.resource_type, func.count(AuditLog.id))
            .where(
                and_(
                    AuditLog.created_at.between(start_date, end_date),
                    AuditLog.resource_type.isnot(None)
                )
            )
            .group_by(AuditLog.resource_type)
            .order_by(desc(func.count(AuditLog.id)))
        )
        resource_stats = resource_stats_result.fetchall()

        # Top users by activity
        user_activity_result = await db.execute(
            select(User.email, func.count(AuditLog.id))
            .join(User, AuditLog.user_id == User.id)
            .where(AuditLog.created_at.between(start_date, end_date))
            .group_by(User.email)
            .order_by(desc(func.count(AuditLog.id)))
            .limit(10)
        )
        user_activity = user_activity_result.fetchall()

        # Failed events
        failed_events_result = await db.execute(
            select(func.count(AuditLog.id)).where(
                and_(
                    AuditLog.created_at.between(start_date, end_date),
                    AuditLog.status.in_(["failure", "error"])
                )
            )
        )
        failed_events = failed_events_result.scalar() or 0

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": (end_date - start_date).days
            },
            "summary": {
                "total_events": total_events,
                "failed_events": failed_events,
                "success_rate": round((total_events - failed_events) / total_events * 100, 2) if total_events > 0 else 100
            },
            "action_breakdown": [
                {"action": action, "count": count} for action, count in action_stats
            ],
            "status_breakdown": [
                {"status": status, "count": count} for status, count in status_stats
            ],
            "resource_breakdown": [
                {"resource_type": resource_type, "count": count} for resource_type, count in resource_stats
            ],
            "top_users": [
                {"email": email, "event_count": count} for email, count in user_activity
            ]
        }
    except Exception as e:
        logger.error(f"Error getting audit statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get audit statistics")


@router.get(
    "/audit/user/{user_id}",
    response_model=List[Dict[str, Any]],
    summary="User Audit Logs",
    description="Get audit logs for a specific user",
)
async def get_user_audit_logs(
    user_id: str = Path(..., description="User ID"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get audit logs for a specific user."""
    try:
        # Verify user exists
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Build query
        query = select(AuditLog).where(AuditLog.user_id == user_id)

        conditions = []
        if action:
            conditions.append(AuditLog.action == action)
        if resource_type:
            conditions.append(AuditLog.resource_type == resource_type)
        if start_date:
            conditions.append(AuditLog.created_at >= start_date)
        if end_date:
            conditions.append(AuditLog.created_at <= end_date)

        if conditions:
            query = query.where(and_(*conditions))

        # Order by timestamp descending and apply pagination
        query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)

        result = await db.execute(query)
        logs = result.scalars().all()

        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "user_email": user.email,
                "action": log.action,
                "resource_type": log.resource_type,
                "resource_id": log.resource_id,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "session_id": log.session_id,
                "details": log.details,
                "status": log.status,
                "error_message": log.error_message,
                "created_at": log.created_at.isoformat(),
            }
            for log in logs
        ]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user audit logs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user audit logs")


@router.get(
    "/audit/reports",
    response_model=Dict[str, Any],
    summary="Audit Reports",
    description="Generate compliance and audit reports",
)
async def get_audit_reports(
    report_type: str = Query(
        "summary", pattern="^(summary|detailed|compliance|security)$"
    ),
    start_date: Optional[datetime] = Query(None, description="Report start date"),
    end_date: Optional[datetime] = Query(None, description="Report end date"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Generate audit reports."""
    try:
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # Base metrics
        total_actions_result = await db.execute(
            select(func.count(UserAccessLog.id)).where(
                UserAccessLog.timestamp.between(start_date, end_date)
            )
        )
        total_actions = total_actions_result.scalar() or 0

        # Actions by type
        actions_by_type_result = await db.execute(
            select(UserAccessLog.action, func.count(UserAccessLog.id).label("count"))
            .where(UserAccessLog.timestamp.between(start_date, end_date))
            .group_by(UserAccessLog.action)
            .order_by(desc("count"))
        )
        actions_by_type = actions_by_type_result.fetchall()

        # Success/failure breakdown
        success_breakdown_result = await db.execute(
            select(UserAccessLog.success, func.count(UserAccessLog.id).label("count"))
            .where(UserAccessLog.timestamp.between(start_date, end_date))
            .group_by(UserAccessLog.success)
        )
        success_breakdown = success_breakdown_result.fetchall()

        # User activity
        unique_users_result = await db.execute(
            select(func.count(func.distinct(UserAccessLog.user_id))).where(
                UserAccessLog.timestamp.between(start_date, end_date)
            )
        )
        unique_users = unique_users_result.scalar() or 0

        # Application usage
        app_usage_result = await db.execute(
            select(
                RegisteredApplication.application_name,
                func.count(UserAccessLog.id).label("usage_count"),
            )
            .join(
                UserAccessLog, RegisteredApplication.id == UserAccessLog.application_id
            )
            .where(UserAccessLog.timestamp.between(start_date, end_date))
            .group_by(RegisteredApplication.id, RegisteredApplication.application_name)
            .order_by(desc("usage_count"))
            .limit(10)
        )
        app_usage = app_usage_result.fetchall()

        report = {
            "report_type": report_type,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": (end_date - start_date).days,
            },
            "summary": {
                "total_actions": total_actions,
                "unique_users": unique_users,
                "success_rate": (
                    round(
                        (
                            sum(
                                count for success, count in success_breakdown if success
                            )
                            / total_actions
                        )
                        * 100,
                        2,
                    )
                    if total_actions > 0
                    else 0
                ),
            },
            "actions_by_type": [
                {"action": action, "count": count} for action, count in actions_by_type
            ],
            "success_breakdown": [
                {"success": success, "count": count}
                for success, count in success_breakdown
            ],
            "top_applications": [
                {"name": name, "usage_count": count} for name, count in app_usage
            ],
        }

        if report_type == "security":
            # Add security-specific metrics
            failed_logins_result = await db.execute(
                select(func.count(UserAccessLog.id)).where(
                    and_(
                        UserAccessLog.timestamp.between(start_date, end_date),
                        UserAccessLog.action == "login",
                        UserAccessLog.success == False,
                    )
                )
            )
            failed_logins = failed_logins_result.scalar() or 0

            report["security_metrics"] = {
                "failed_login_attempts": failed_logins,
                "suspicious_activity_detected": 0,  # TODO: Implement
            }

        return report
    except Exception as e:
        logger.error(f"Error generating audit report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate audit report")


@router.post(
    "/audit/export",
    response_model=Dict[str, Any],
    summary="Export Audit Data",
    description="Export audit data for compliance purposes",
)
async def export_audit_data(
    export_format: str = Query("csv", pattern="^(csv|json|pdf)$"),
    start_date: Optional[datetime] = Query(None, description="Export start date"),
    end_date: Optional[datetime] = Query(None, description="Export end date"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Export audit data."""
    try:
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # TODO: Implement actual export functionality
        # For now, return export request confirmation
        export_id = secrets.token_hex(16)

        return {
            "export_id": export_id,
            "format": export_format,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "status": "pending",
            "message": "Export request submitted. You will be notified when ready.",
            "estimated_completion": (
                datetime.utcnow() + timedelta(minutes=5)
            ).isoformat(),
        }
    except Exception as e:
        logger.error(f"Error exporting audit data: {e}")
        raise HTTPException(status_code=500, detail="Failed to export audit data")


# ============================================================================
# Missing Admin Endpoints for Frontend Compatibility
# ============================================================================


@router.get(
    "/profile",
    summary="Get Admin Profile",
    description="Get current admin user profile information",
)
async def get_admin_profile(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get admin profile information."""
    try:
        return {
            "id": current_admin.id,
            "email": current_admin.email,
            "username": current_admin.username,
            "first_name": current_admin.first_name,
            "last_name": current_admin.last_name,
            "role": current_admin.role,
            "avatar_url": current_admin.avatar_url,
            "created_at": current_admin.created_at.isoformat(),
            "last_login": (
                current_admin.last_login.isoformat()
                if current_admin.last_login
                else None
            ),
            "is_active": current_admin.is_active,
            "is_verified": current_admin.is_verified,
        }
    except Exception as e:
        logger.error(f"Error getting admin profile: {e}")
        raise HTTPException(status_code=500, detail="Failed to get admin profile")


@router.get(
    "/users/profile",
    summary="Get Admin User Profile",
    description="Get admin user profile (alias for /profile)",
)
async def get_admin_user_profile(
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get admin user profile (alias endpoint)."""
    return await get_admin_profile(current_admin, db)


@router.get(
    "/notifications",
    summary="Get Admin Notifications",
    description="Get admin notifications and system alerts",
)
async def get_admin_notifications(
    limit: int = Query(
        50, ge=1, le=100, description="Number of notifications to return"
    ),
    offset: int = Query(0, ge=0, description="Number of notifications to skip"),
    unread_only: bool = Query(False, description="Return only unread notifications"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> List[Dict[str, Any]]:
    """Get admin notifications."""
    try:
        notifications = []

        # System status notification
        notifications.append(
            {
                "id": 1,
                "type": "system",
                "title": "System Status Update",
                "message": "All GeNieGO SSO services are operational. No issues detected.",
                "date": datetime.utcnow().isoformat(),
                "read": True,
                "actionUrl": "/admin/security/dashboard",
                "priority": "info",
            }
        )

        # Security notification
        notifications.append(
            {
                "id": 2,
                "type": "security",
                "title": "Security Monitoring Active",
                "message": "Security monitoring is active. No suspicious activities detected in the last 24 hours.",
                "date": (datetime.utcnow() - timedelta(hours=2)).isoformat(),
                "read": False,
                "actionUrl": "/admin/security/alerts",
                "priority": "info",
            }
        )

        # User activity notification
        user_count = await db.scalar(select(func.count(User.id))) or 0
        notifications.append(
            {
                "id": 3,
                "type": "activity",
                "title": "User Activity Summary",
                "message": f"Currently managing {user_count} users across all applications.",
                "date": (datetime.utcnow() - timedelta(hours=6)).isoformat(),
                "read": True,
                "actionUrl": "/admin/users",
                "priority": "info",
            }
        )

        # Filter unread only if requested
        if unread_only:
            notifications = [n for n in notifications if not n["read"]]

        # Apply pagination
        notifications = notifications[offset : offset + limit]

        return notifications

    except Exception as e:
        logger.error(f"Error getting admin notifications: {e}")
        raise HTTPException(status_code=500, detail="Failed to get admin notifications")


# ==============================|| ADMIN ORGANIZATION MANAGEMENT ||============================== #

@router.get(
    "/organizations",
    summary="List All Organizations",
    description="Get all organizations for admin oversight",
)
async def list_all_organizations(
    limit: int = Query(100, ge=1, le=1000, description="Number of organizations to return"),
    offset: int = Query(0, ge=0, description="Number of organizations to skip"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search by name or slug"),
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """List all organizations for admin oversight."""
    try:
        # Build query
        query = select(Organization)

        if is_active is not None:
            query = query.where(Organization.is_active == is_active)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Organization.name.ilike(search_term),
                    Organization.slug.ilike(search_term),
                    Organization.description.ilike(search_term)
                )
            )

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query) or 0

        # Apply pagination and ordering
        query = query.order_by(desc(Organization.created_at)).offset(offset).limit(limit)
        result = await db.execute(query)
        organizations = result.scalars().all()

        # Get member counts for each organization
        org_data = []
        for org in organizations:
            # Get member count
            member_count_query = select(func.count(OrganizationMembership.id)).where(
                and_(
                    OrganizationMembership.organization_id == org.id,
                    OrganizationMembership.is_active == True
                )
            )
            member_count = await db.scalar(member_count_query) or 0

            # Get owner info
            owner_query = select(User).join(
                OrganizationMembership,
                User.id == OrganizationMembership.user_id
            ).where(
                and_(
                    OrganizationMembership.organization_id == org.id,
                    OrganizationMembership.role == "owner",
                    OrganizationMembership.is_active == True
                )
            )
            owner_result = await db.execute(owner_query)
            owner = owner_result.scalar_one_or_none()

            org_data.append({
                "id": org.id,
                "name": org.name,
                "slug": org.slug,
                "description": org.description,
                "is_public": org.is_public,
                "is_active": org.is_active,
                "member_count": member_count,
                "max_members": org.max_members,
                "created_at": org.created_at.isoformat() if org.created_at else None,
                "updated_at": org.updated_at.isoformat() if org.updated_at else None,
                "owner": {
                    "id": owner.id if owner else None,
                    "username": owner.username if owner else None,
                    "email": owner.email if owner else None,
                } if owner else None,
                "settings": org.settings or {},
            })

        return {
            "organizations": org_data,
            "total": total,
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"Error listing organizations for admin: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve organizations")


@router.get(
    "/organizations/{organization_id}",
    summary="Get Organization Details",
    description="Get detailed organization information for admin oversight",
)
async def get_organization_details(
    organization_id: str,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get detailed organization information for admin oversight."""
    try:
        # Get organization
        org_query = select(Organization).where(Organization.id == organization_id)
        result = await db.execute(org_query)
        organization = result.scalar_one_or_none()

        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")

        # Get members with user details
        members_query = select(OrganizationMembership, User).join(
            User, OrganizationMembership.user_id == User.id
        ).where(
            OrganizationMembership.organization_id == organization_id
        ).order_by(OrganizationMembership.joined_at)

        members_result = await db.execute(members_query)
        members_data = []

        for membership, user in members_result:
            members_data.append({
                "id": membership.id,
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "role": membership.role,
                "is_active": membership.is_active,
                "joined_at": membership.joined_at.isoformat() if membership.joined_at else None,
                "invited_at": membership.invited_at.isoformat() if membership.invited_at else None,
                "permissions": membership.permissions or {},
            })

        # Get pending invitations
        invitations_query = select(OrganizationInvitation).where(
            and_(
                OrganizationInvitation.organization_id == organization_id,
                OrganizationInvitation.status == "pending"
            )
        ).order_by(desc(OrganizationInvitation.created_at))

        invitations_result = await db.execute(invitations_query)
        invitations = invitations_result.scalars().all()

        invitations_data = []
        for invitation in invitations:
            invitations_data.append({
                "id": invitation.id,
                "email": invitation.email,
                "role": invitation.role,
                "status": invitation.status,
                "created_at": invitation.created_at.isoformat() if invitation.created_at else None,
                "expires_at": invitation.expires_at.isoformat() if invitation.expires_at else None,
                "message": invitation.message,
            })

        return {
            "id": organization.id,
            "name": organization.name,
            "slug": organization.slug,
            "description": organization.description,
            "is_public": organization.is_public,
            "is_active": organization.is_active,
            "max_members": organization.max_members,
            "created_at": organization.created_at.isoformat() if organization.created_at else None,
            "updated_at": organization.updated_at.isoformat() if organization.updated_at else None,
            "settings": organization.settings or {},
            "members": members_data,
            "pending_invitations": invitations_data,
            "stats": {
                "total_members": len(members_data),
                "active_members": len([m for m in members_data if m["is_active"]]),
                "pending_invitations": len(invitations_data),
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting organization details for admin: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve organization details")


@router.patch(
    "/organizations/{organization_id}/status",
    summary="Update Organization Status",
    description="Enable or disable organization (admin only)",
)
async def update_organization_status(
    organization_id: str,
    is_active: bool,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Update organization active status (admin only)."""
    try:
        # Get organization
        org_query = select(Organization).where(Organization.id == organization_id)
        result = await db.execute(org_query)
        organization = result.scalar_one_or_none()

        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")

        # Update status
        organization.is_active = is_active
        organization.updated_at = datetime.utcnow()
        await db.commit()

        logger.info(f"Admin {current_admin.id} updated organization {organization_id} status to {is_active}")

        return {
            "message": f"Organization {'activated' if is_active else 'deactivated'} successfully",
            "organization_id": organization_id,
            "is_active": is_active,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating organization status: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update organization status")
