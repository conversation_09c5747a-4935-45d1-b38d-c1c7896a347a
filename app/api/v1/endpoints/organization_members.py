"""
Organization membership management API endpoints for GeNieGO SSO Server.

This module provides REST API endpoints for managing organization memberships,
including adding/removing members, updating roles, and viewing membership details.
"""

import logging
from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth_dependencies import get_current_user_from_session
from app.models import User
from app.schemas.organization import (
    OrganizationMembershipCreate,
    OrganizationMembershipResponse,
    OrganizationMembershipUpdate,
)
from app.services.organization_service import OrganizationManager

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/{organization_id}/members", response_model=List[OrganizationMembershipResponse])
async def get_organization_members(
    organization_id: str,
    include_inactive: bool = False,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all members of an organization.
    
    User must be a member of the organization to view members.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check if user is a member
        membership = await org_manager.get_user_membership(current_user.id, organization_id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this organization"
            )
        
        members = await org_manager.get_organization_members(
            organization_id, include_inactive=include_inactive
        )
        
        # Convert to response format with user details
        response_members = []
        for member in members:
            member_data = OrganizationMembershipResponse.from_orm(member)
            if member.user:
                member_data.user_email = member.user.email
                member_data.user_name = f"{member.user.first_name} {member.user.last_name}".strip()
            response_members.append(member_data)
        
        return response_members
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization members: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization members"
        )


@router.post("/{organization_id}/members", response_model=OrganizationMembershipResponse, status_code=status.HTTP_201_CREATED)
async def add_organization_member(
    organization_id: str,
    member_data: OrganizationMembershipCreate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Add a user to organization.
    
    User must have manage_members permission (owner or admin).
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check permissions
        if not await org_manager.user_can_manage_members(current_user.id, organization_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to manage members"
            )
        
        membership = await org_manager.add_member(
            organization_id=organization_id,
            user_id=member_data.user_id,
            role=member_data.role,
            inviter_id=current_user.id,
            permissions=member_data.permissions,
        )
        
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add member to organization"
            )
        
        return OrganizationMembershipResponse.from_orm(membership)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add organization member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add member to organization"
        )


@router.get("/{organization_id}/members/{user_id}", response_model=OrganizationMembershipResponse)
async def get_organization_member(
    organization_id: str,
    user_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Get specific member details in organization.
    
    User must be a member of the organization to view member details.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check if current user is a member
        current_membership = await org_manager.get_user_membership(current_user.id, organization_id)
        if not current_membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this organization"
            )
        
        # Get target member
        membership = await org_manager.get_user_membership(user_id, organization_id)
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Member not found in organization"
            )
        
        member_data = OrganizationMembershipResponse.from_orm(membership)
        if membership.user:
            member_data.user_email = membership.user.email
            member_data.user_name = f"{membership.user.first_name} {membership.user.last_name}".strip()
        
        return member_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get organization member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve member details"
        )


@router.put("/{organization_id}/members/{user_id}", response_model=OrganizationMembershipResponse)
async def update_organization_member(
    organization_id: str,
    user_id: str,
    member_data: OrganizationMembershipUpdate,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Update member's role or permissions in organization.
    
    User must have manage_members permission and appropriate hierarchy.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Check permissions
        if not await org_manager.user_can_manage_members(current_user.id, organization_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to manage members"
            )
        
        # Update role if provided
        if member_data.role:
            membership = await org_manager.update_member_role(
                organization_id=organization_id,
                user_id=user_id,
                new_role=member_data.role,
                updater_id=current_user.id,
            )
        else:
            # Get current membership for permissions update
            membership = await org_manager.get_user_membership(user_id, organization_id)
            if not membership:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Member not found in organization"
                )
        
        # Update permissions if provided
        if member_data.permissions is not None:
            membership.permissions = member_data.permissions
            membership.updated_at = datetime.utcnow()
            await db.commit()
        
        return OrganizationMembershipResponse.from_orm(membership)
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update organization member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update member"
        )


@router.delete("/{organization_id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_organization_member(
    organization_id: str,
    user_id: str,
    current_user: User = Depends(get_current_user_from_session),
    db: AsyncSession = Depends(get_db),
):
    """
    Remove member from organization.
    
    User must have manage_members permission or be removing themselves.
    """
    try:
        org_manager = OrganizationManager(db)
        
        # Allow users to remove themselves, or check manage_members permission
        if user_id != current_user.id:
            if not await org_manager.user_can_manage_members(current_user.id, organization_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to remove members"
                )
        
        success = await org_manager.remove_member(
            organization_id=organization_id,
            user_id=user_id,
            remover_id=current_user.id,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Member not found in organization"
            )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove organization member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove member"
        )
