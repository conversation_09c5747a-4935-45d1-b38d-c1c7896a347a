"""API v1 router configuration for GeNieGO SSO Server."""

from fastapi import APIRouter

from app.config import get_settings

settings = get_settings()

api_router = APIRouter()

# Include GeNieGO SSO Server endpoints
from app.api.v1.endpoints import admin, auth, consent, developer, mfa, oauth2, organizations, role_transitions, security_monitoring, sessions, user, users

# Include SSO authentication endpoints under /auth
api_router.include_router(auth.router, prefix="/auth")

# Include MFA endpoints under /mfa
api_router.include_router(mfa.router, prefix="/mfa")

# Include role transition endpoints under /role-transitions
api_router.include_router(role_transitions.router, prefix="/role-transitions")

# Include OAuth2 endpoints under /oauth2
api_router.include_router(oauth2.router, prefix="/oauth2")

# Include session management endpoints under /sessions
api_router.include_router(sessions.router, prefix="/sessions")

# Include consent management endpoints under /consent
api_router.include_router(consent.router, prefix="/consent")

# Include organization management endpoints under /organizations
api_router.include_router(organizations.router, prefix="/organizations", tags=["organizations"])

# Include user management endpoints under /users
api_router.include_router(users.router, prefix="/users")

# Include developer portal endpoints under /developer
api_router.include_router(developer.router, prefix="/developer")

# Include admin portal endpoints under /admin
api_router.include_router(admin.router, prefix="/admin")

# Include security monitoring endpoints under /security (admin only)
api_router.include_router(security_monitoring.router, prefix="/security")

# Include end user portal endpoints under /user
api_router.include_router(user.router, prefix="/user")
