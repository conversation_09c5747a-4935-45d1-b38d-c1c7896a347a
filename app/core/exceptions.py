"""
Custom exception classes and error handling utilities.
Provides consistent error responses and better error management.
"""

from typing import Any, Dict, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from pydantic import BaseModel


class APIError(Exception):
    """Base API exception class."""

    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(APIError):
    """Validation error exception."""

    def __init__(self, message: str, field: Optional[str] = None):
        details = {"field": field} if field else {}
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


class AuthenticationError(APIError):
    """Authentication error exception."""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message=message, status_code=status.HTTP_401_UNAUTHORIZED)


class AuthorizationError(APIError):
    """Authorization error exception."""

    def __init__(self, message: str = "Access denied"):
        super().__init__(message=message, status_code=status.HTTP_403_FORBIDDEN)


class NotFoundError(APIError):
    """Resource not found error exception."""

    def __init__(
        self, message: str = "Resource not found", resource: Optional[str] = None
    ):
        details = {"resource": resource} if resource else {}
        super().__init__(
            message=message, status_code=status.HTTP_404_NOT_FOUND, details=details
        )


class ConflictError(APIError):
    """Resource conflict error exception."""

    def __init__(
        self, message: str = "Resource conflict", resource: Optional[str] = None
    ):
        details = {"resource": resource} if resource else {}
        super().__init__(
            message=message, status_code=status.HTTP_409_CONFLICT, details=details
        )


class RateLimitError(APIError):
    """Rate limit exceeded error exception."""

    def __init__(
        self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None
    ):
        details = {"retry_after": retry_after} if retry_after else {}
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details,
        )


class BusinessLogicError(APIError):
    """Business logic error exception."""

    def __init__(self, message: str):
        super().__init__(message=message, status_code=status.HTTP_400_BAD_REQUEST)


# Error response models
class ErrorDetail(BaseModel):
    """Error detail model."""

    message: str
    field: Optional[str] = None
    code: Optional[str] = None


class ErrorResponse(BaseModel):
    """Standard error response model."""

    error: str
    message: str
    status_code: int
    details: Optional[Dict[str, Any]] = None


# Utility functions
def create_http_exception(error: APIError) -> HTTPException:
    """Convert APIError to HTTPException."""
    return HTTPException(
        status_code=error.status_code,
        detail={
            "error": error.__class__.__name__,
            "message": error.message,
            "details": error.details,
        },
    )


def create_validation_error(message: str, field: Optional[str] = None) -> HTTPException:
    """Create a validation error response."""
    return HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail={"error": "ValidationError", "message": message, "field": field},
    )


def create_authentication_error(message: str = "Invalid credentials") -> HTTPException:
    """Create an authentication error response."""
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={"error": "AuthenticationError", "message": message},
    )


def create_authorization_error(message: str = "Access denied") -> HTTPException:
    """Create an authorization error response."""
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail={"error": "AuthorizationError", "message": message},
    )


def create_not_found_error(
    message: str = "Resource not found", resource: Optional[str] = None
) -> HTTPException:
    """Create a not found error response."""
    details = {"resource": resource} if resource else None
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail={"error": "NotFoundError", "message": message, "details": details},
    )


def create_conflict_error(
    message: str = "Resource conflict", resource: Optional[str] = None
) -> HTTPException:
    """Create a conflict error response."""
    details = {"resource": resource} if resource else None
    return HTTPException(
        status_code=status.HTTP_409_CONFLICT,
        detail={"error": "ConflictError", "message": message, "details": details},
    )
