"""
Basic metrics collection for monitoring.
"""

import time
from collections import Counter as CollectionsCounter
from collections import defaultdict
from datetime import datetime
from typing import Any, Counter, Dict, List


class MetricsCollector:
    """Simple in-memory metrics collector."""

    def __init__(self) -> None:
        self.start_time: float = time.time()
        self.request_count: Counter[str] = CollectionsCounter()
        self.response_times: Dict[str, List[float]] = defaultdict(list)
        self.error_count: Counter[str] = CollectionsCounter()
        self.endpoint_stats: Dict[str, Dict[str, float]] = defaultdict(
            lambda: {"count": 0.0, "total_time": 0.0}
        )
        self.max_response_time_entries = 1000  # Limit memory usage

    def record_request(
        self, method: str, endpoint: str, status_code: int, response_time: float
    ) -> None:
        """Record a request with its metrics."""
        self.request_count[f"{method}:{status_code}"] += 1

        # Limit memory usage by keeping only recent response times
        endpoint_times = self.response_times[endpoint]
        endpoint_times.append(response_time)
        if len(endpoint_times) > self.max_response_time_entries:
            # Keep only the most recent entries
            self.response_times[endpoint] = endpoint_times[
                -self.max_response_time_entries :
            ]

        if status_code >= 400:
            self.error_count[f"{method}:{endpoint}"] += 1

        # Track endpoint statistics
        key = f"{method}:{endpoint}"
        self.endpoint_stats[key]["count"] += 1.0
        self.endpoint_stats[key]["total_time"] += response_time

    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics summary."""
        uptime = time.time() - self.start_time

        # Calculate total requests
        total_requests = sum(self.request_count.values())

        # Calculate average response times per endpoint
        avg_response_times = {}
        for endpoint, times in self.response_times.items():
            if times:
                avg_response_times[endpoint] = sum(times) / len(times)

        # Calculate requests per second
        rps = total_requests / uptime if uptime > 0 else 0

        # Top endpoints by request count
        top_endpoints = dict(
            sorted(
                {k: v["count"] for k, v in self.endpoint_stats.items()}.items(),
                key=lambda x: x[1],
                reverse=True,
            )[:10]
        )

        return {
            "uptime_seconds": round(uptime, 2),
            "total_requests": total_requests,
            "requests_per_second": round(rps, 2),
            "status_code_distribution": dict(self.request_count),
            "error_count": dict(self.error_count),
            "average_response_times": {
                k: round(v, 4) for k, v in avg_response_times.items()
            },
            "top_endpoints": top_endpoints,
            "collected_at": datetime.utcnow().isoformat(),
        }


# Global metrics collector instance
metrics_collector = MetricsCollector()
