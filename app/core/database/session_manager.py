"""
Enterprise-grade async database session manager for GeNieGO SSO.

This module provides production-ready async database session management with:
- Connection pooling optimized for cloud deployment
- Graceful connection failure handling
- Horizontal scaling support
- Proper session lifecycle management
- Transaction isolation and rollback handling
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.pool import QueuePool

from app.config.settings import Settings

logger = logging.getLogger(__name__)


class AsyncDatabaseSessionManager:
    """
    Enterprise-grade async database session manager.

    Designed for cloud-cluster deployment with:
    - Connection pooling with configurable limits
    - Automatic connection recovery
    - Session isolation and proper cleanup
    - Horizontal scaling support
    """

    def __init__(self, settings: Settings):
        self.settings = settings
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker] = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the database engine and session factory."""
        if self._initialized:
            return

        # Enterprise connection pool configuration
        # Optimized for cloud deployment and horizontal scaling
        pool_config = {
            "poolclass": QueuePool,
            "pool_size": 20,  # Base connections per instance
            "max_overflow": 30,  # Additional connections under load
            "pool_timeout": 30,  # Connection timeout
            "pool_recycle": 3600,  # Recycle connections every hour
            "pool_pre_ping": True,  # Validate connections before use
        }

        # Create async engine with enterprise configuration
        self._engine = create_async_engine(
            self.settings.DATABASE_URL,
            echo=self.settings.DEBUG,
            future=True,
            **pool_config,
        )

        # Create session factory with proper isolation
        self._session_factory = async_sessionmaker(
            bind=self._engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,  # Important for async operations
        )

        self._initialized = True
        logger.info("Database session manager initialized successfully")

    async def close(self) -> None:
        """Gracefully close all database connections."""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
            self._initialized = False
            logger.info("Database session manager closed")

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get an async database session with proper lifecycle management.

        This context manager ensures:
        - Proper session creation and cleanup
        - Automatic rollback on exceptions
        - Connection pool management
        - Transaction isolation
        """
        if not self._initialized:
            await self.initialize()

        if not self._session_factory:
            raise RuntimeError("Database session manager not initialized")

        session = self._session_factory()
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error, rolling back: {e}")
            raise
        finally:
            await session.close()

    @asynccontextmanager
    async def get_transactional_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get an async database session with automatic transaction management.

        This context manager provides:
        - Automatic transaction begin/commit/rollback
        - Proper exception handling
        - Session cleanup
        """
        async with self.get_session() as session:
            async with session.begin():
                try:
                    yield session
                except Exception as e:
                    # Transaction will be automatically rolled back
                    logger.error(f"Transaction failed, rolling back: {e}")
                    raise

    async def health_check(self) -> bool:
        """
        Perform a health check on the database connection.

        Returns:
            bool: True if database is healthy, False otherwise
        """
        try:
            async with self.get_session() as session:
                result = await session.execute("SELECT 1")
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    @property
    def engine(self) -> AsyncEngine:
        """Get the async database engine."""
        if not self._engine:
            raise RuntimeError("Database session manager not initialized")
        return self._engine


# Global session manager instance
_session_manager: Optional[AsyncDatabaseSessionManager] = None


async def get_session_manager() -> AsyncDatabaseSessionManager:
    """
    Get the global database session manager.

    This function provides a singleton pattern for the session manager,
    ensuring consistent database configuration across the application.
    """
    global _session_manager

    if _session_manager is None:
        settings = Settings()
        _session_manager = AsyncDatabaseSessionManager(settings)
        await _session_manager.initialize()

    return _session_manager


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function for FastAPI to get async database sessions.

    This function is designed to be used with FastAPI's dependency injection
    system and provides proper session lifecycle management.
    """
    session_manager = await get_session_manager()
    async with session_manager.get_session() as session:
        yield session


async def get_transactional_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function for FastAPI to get transactional async database sessions.

    Use this when you need automatic transaction management.
    """
    session_manager = await get_session_manager()
    async with session_manager.get_transactional_session() as session:
        yield session


async def close_session_manager() -> None:
    """Close the global session manager. Used for cleanup during shutdown."""
    global _session_manager
    if _session_manager:
        await _session_manager.close()
        _session_manager = None
