"""
Clean OAuth2 and JWT security implementation for GeNieGO.
"""

import hashlib
import secrets
import uuid
from datetime import datetime, timedelta
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.config import get_settings
from app.core.database import get_db
from app.models import User
from app.models.user import RegisteredApplication

settings = get_settings()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/oauth2/token",
    scopes={
        "admin": "Full system access",
        "staff": "Administrative access within scope",
        "organisation": "Organisation management access",
        "user": "Standard user access",
        "expired_user": "Limited access for expired accounts",
    },
)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # Add unique identifier to prevent duplicate tokens
    to_encode.update(
        {
            "exp": expire,
            "jti": secrets.token_urlsafe(16),  # JWT ID for uniqueness
            "iat": datetime.utcnow().timestamp(),  # Issued at with microsecond precision
        }
    )
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token() -> str:
    """Create a secure refresh token."""
    return secrets.token_urlsafe(32)


def generate_uuid() -> str:
    """Generate a unique UUID."""
    return str(uuid.uuid4())


def generate_hash(data: Optional[str] = None) -> str:
    """Generate a SHA512 hash."""
    if data is None:
        data = secrets.token_urlsafe(32)
    return hashlib.sha512(data.encode()).hexdigest()


async def authenticate_user(
    db: AsyncSession,
    username: str,
    password: str,
    organisation_id: Optional[int] = None,
) -> Optional[User]:
    """Authenticate user by username and password."""
    query = (
        select(User)
        .options(
            selectinload(User.role),
            selectinload(User.organisation),
            selectinload(User.account),
        )
        .where(User.username == username)
    )

    # Add organisation filtering if provided (multi-tenant)
    if organisation_id:
        query = query.where(User.organisation_id == organisation_id)

    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if user is None:
        return None

    # Check if user is enabled - safe attribute access
    enabled = getattr(user, "enabled", True)
    if not enabled:
        return None

    # Verify password - safe attribute access
    password_hash = getattr(user, "password_hash", "")
    if not verify_password(password, str(password_hash)):
        return None

    return user


async def authenticate_client(
    db: AsyncSession, client_id: str, client_secret: str
) -> Optional[RegisteredApplication]:
    """Authenticate OAuth2 client."""
    query = select(RegisteredApplication).where(
        RegisteredApplication.client_id == client_id,
        RegisteredApplication.enabled.is_(True),
    )
    result = await db.execute(query)
    client = result.scalar_one_or_none()

    if client is None:
        return None

    # Verify client secret - safe attribute access
    stored_secret = getattr(client, "client_secret", "")
    if stored_secret != client_secret:
        return None

    return client


async def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify JWT token and return payload."""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        return payload
    except JWTError:
        return None


async def get_current_user_or_client(
    token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
) -> Union[User, RegisteredApplication]:
    """Get current user or client from JWT token with database token validation."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Find token in database and verify it's not expired
        token_query = select(Token).where(
            Token.access_token == token, Token.expires > datetime.utcnow()
        )
        result = await db.execute(token_query)
        db_token = result.scalar_one_or_none()

        if not db_token:
            raise credentials_exception

        # Verify JWT signature and get subject
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        subject = payload.get("sub")
        if subject is None:
            raise credentials_exception

        # Get client associated with the token
        client_query = select(RegisteredApplication).where(
            RegisteredApplication.client_id == db_token.client_id,
            RegisteredApplication.enabled.is_(True),
        )
        client_result = await db.execute(client_query)
        client = client_result.scalar_one_or_none()

        if client:
            return client

        # If token has user_id, get user
        if getattr(db_token, "user_id", None):
            user_query = (
                select(User)
                .options(
                    selectinload(User.role),
                    selectinload(User.organisation),
                    selectinload(User.account),
                )
                .where(User.id == getattr(db_token, "user_id"))
            )

            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()

            if user and safe_getattr(user, "enabled", True):
                return user

        raise credentials_exception

    except JWTError:
        raise credentials_exception
    except HTTPException:
        raise
    except Exception:
        raise credentials_exception


def require_scopes_flexible(
    required_scopes: List[str],
) -> Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]]:
    """Dependency to require specific OAuth2 scopes for users or clients with database token validation."""

    async def check_scopes(
        token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
    ) -> Union[User, RegisteredApplication]:
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

        try:
            # Find token in database and verify it's not expired
            token_query = select(Token).where(
                Token.access_token == token, Token.expires > datetime.utcnow()
            )
            result = await db.execute(token_query)
            db_token = result.scalar_one_or_none()

            if not db_token:
                raise credentials_exception

            # Check if token has required scopes
            token_scopes = db_token.scopes or []
            if not any(scope in token_scopes for scope in required_scopes):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions",
                )

            # Get client associated with the token
            client_query = select(RegisteredApplication).where(
                RegisteredApplication.client_id == db_token.client_id,
                RegisteredApplication.enabled.is_(True),
            )
            client_result = await db.execute(client_query)
            client = client_result.scalar_one_or_none()

            if client:
                return client

            # If token has user_id, get user
            if getattr(db_token, "user_id", None):
                user_query = (
                    select(User)
                    .options(
                        selectinload(User.role),
                        selectinload(User.organisation),
                        selectinload(User.account),
                    )
                    .where(User.id == getattr(db_token, "user_id"))
                )

                user_result = await db.execute(user_query)
                user = user_result.scalar_one_or_none()

                if user and safe_getattr(user, "enabled", True):
                    return user

            raise credentials_exception

        except HTTPException:
            raise
        except Exception:
            raise credentials_exception

    return check_scopes


# Convenience functions for common scope requirements
def require_admin() -> (
    Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]]
):
    """Require admin scope."""
    return require_scopes_flexible(["admin"])


def require_staff() -> (
    Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]]
):
    """Require staff or admin scope."""
    return require_scopes_flexible(["admin", "staff"])


def require_organisation() -> (
    Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]]
):
    """Require organisation, staff, or admin scope."""
    return require_scopes_flexible(["admin", "staff", "organisation"])


def require_user() -> (
    Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]]
):
    """Require any user scope."""
    return require_scopes_flexible(["admin", "staff", "organisation", "user"])


def filter_by_organisation(current_user: User) -> Dict[str, Any]:
    """Get organisation filter for multi-tenant queries."""
    # Admin users can access all organisations (role_id 4 is super admin)
    role_id = getattr(current_user, "role_id", None)
    if role_id == 4:
        return {}

    # Other users are scoped to their organisation
    org_id = getattr(current_user, "organisation_id", None)
    return {"organisation_id": org_id} if org_id else {}


async def check_user_permission(
    current_user: User, target_user_id: int, db: AsyncSession
) -> bool:
    """Check if current user has permission to access target user."""
    # Super admin can access all users
    role_id = getattr(current_user, "role_id", None)
    if role_id == 4:
        return True

    # Admin can access users in their organisation
    if role_id == 1:
        # Get target user
        query = select(User).where(User.id == target_user_id)
        result = await db.execute(query)
        target_user = result.scalar_one_or_none()

        if target_user is None:
            return False

        current_org_id = getattr(current_user, "organisation_id", None)
        target_org_id = getattr(target_user, "organisation_id", None)
        return current_org_id == target_org_id

    # Users can only access themselves
    current_user_id = getattr(current_user, "id", None)
    return current_user_id == target_user_id


# Security warning utility for development
class SecurityWarning:
    """Security warning utility for development."""

    @staticmethod
    def log_unprotected_access(endpoint: str) -> None:
        """Log warning for unprotected access in development."""
        if settings.DEBUG:
            import logging

            logger = logging.getLogger(__name__)
            logger.warning(
                f"Unprotected access to {endpoint} - this should be secured in production"
            )


def require_admin_in_production() -> Union[
    Callable[[str, AsyncSession], Awaitable[Union[User, RegisteredApplication]]],
    Callable[[], None],
]:
    """Require admin access in production, allow in development with warning."""
    if settings.is_production:
        return require_admin()
    else:
        # In development, just log a warning
        SecurityWarning.log_unprotected_access("admin endpoint")
        return lambda: None


# Legacy functions for compatibility
async def get_current_user(
    token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
) -> User:
    """Get current user from JWT token (legacy - returns User only)."""
    entity = await get_current_user_or_client(token, db)

    # If it's a client, raise an error
    if isinstance(entity, RegisteredApplication):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Client credentials not valid for user-only endpoints",
        )

    return entity


def require_scopes(required_scopes: List[str]) -> Callable[[User], Awaitable[User]]:
    """Legacy scope requirement (user-only)."""

    async def check_scopes(current_user: User = Depends(get_current_user)) -> User:
        # For legacy compatibility, just return the user
        # Real scope checking would happen in the token validation
        return current_user

    return check_scopes


def safe_getattr(obj: Any, attr: str, default: Any = None) -> Any:
    """Safely get attribute from SQLAlchemy model object."""
    try:
        value = getattr(obj, attr, default)
        # If it's a SQLAlchemy Column, return the default
        # In practice, this shouldn't happen if the object is properly loaded
        if hasattr(value, "__class__") and "Column" in str(value.__class__):
            return default
        return value
    except:
        return default
