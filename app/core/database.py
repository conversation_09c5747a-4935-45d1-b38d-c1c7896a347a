"""
Database session management following FastAPI best practices.
Configuration is managed through app/config/settings.py
"""

import logging
from typing import AsyncGenerator

from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

from app.config import Settings, get_settings
from app.models.base import Base

logger = logging.getLogger(__name__)


def create_database_engine(settings: Settings) -> AsyncEngine:
    """Create async SQLAlchemy engine using settings."""
    return create_async_engine(settings.DATABASE_URL, **settings.database_config)


def create_session_factory(engine: AsyncEngine) -> async_sessionmaker[AsyncSession]:
    """Create async session factory."""
    return async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )


# Initialize database components using dependency injection pattern
def get_database_engine() -> AsyncEngine:
    """Get database engine using settings dependency injection."""
    settings = get_settings()
    return create_database_engine(settings)


def get_session_factory() -> async_sessionmaker[AsyncSession]:
    """Get session factory using settings dependency injection."""
    engine = get_database_engine()
    return create_session_factory(engine)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session following FastAPI dependency injection pattern."""
    session_factory = get_session_factory()
    async with session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def test_database_connection() -> bool:
    """Test database connection."""
    try:
        settings = get_settings()
        engine = create_database_engine(settings)
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        await engine.dispose()
        logger.info("Database connection test successful")
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


async def create_tables() -> None:
    """Create database tables."""
    settings = get_settings()
    engine = create_database_engine(settings)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    await engine.dispose()
    logger.info("Database tables created successfully")


async def drop_tables() -> None:
    """Drop database tables."""
    settings = get_settings()
    engine = create_database_engine(settings)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await engine.dispose()
    logger.info("Database tables dropped successfully")
