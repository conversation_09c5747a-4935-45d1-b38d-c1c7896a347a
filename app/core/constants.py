"""
Application constants and configuration values.
"""

# API Response Messages
SUCCESS_MESSAGE = "Operation completed successfully"
ERROR_INTERNAL_SERVER = "Internal server error"
ERROR_INVALID_INPUT = "Invalid input provided"
ERROR_NOT_FOUND = "Resource not found"
ERROR_RATE_LIMIT = "Rate limit exceeded"

# Validation Constants
EMAIL_PATTERN = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
MIN_PASSWORD_LENGTH = 8

# Rate Limiting
DEFAULT_RATE_LIMIT = "100/minute"
STRICT_RATE_LIMIT = "10/minute"
HEALTH_RATE_LIMIT = "60/minute"

# API Configuration - Use dynamic settings instead of hardcoded values
# API_PREFIX and API_VERSION are now managed in settings.py
# Use settings.api_versioned_prefix for the full versioned prefix
