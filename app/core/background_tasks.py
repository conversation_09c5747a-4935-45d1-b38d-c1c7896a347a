"""
Background task examples and utilities.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional

from fastapi import BackgroundTasks

from app.utils.redis_utils import get_redis_client, is_redis_available

logger = logging.getLogger(__name__)

# Fallback in-memory task tracking
_task_registry: Dict[str, Dict[str, Any]] = {}


def track_background_task(task_id: str, description: str) -> None:
    """Track a background task for monitoring."""
    task_data = {
        "id": task_id,
        "description": description,
        "status": "running",
        "started_at": datetime.utcnow().isoformat(),
        "completed_at": None,
        "error": None,
    }

    if is_redis_available():
        redis_client = get_redis_client()
        if redis_client:
            try:
                # Store in Redis with 24-hour expiration
                redis_client.setex(
                    f"task:{task_id}", 86400, json.dumps(task_data)  # 24 hours
                )
                return
            except Exception as e:
                logger.warning(f"Failed to store task in Redis: {e}")

    # Fallback to in-memory
    _task_registry[task_id] = task_data


def complete_background_task(task_id: str, error: Optional[str] = None) -> None:
    """Mark a background task as completed."""
    task_data = {
        "status": "error" if error else "completed",
        "completed_at": datetime.utcnow().isoformat(),
        "error": error,
    }

    if is_redis_available():
        redis_client = get_redis_client()
        if redis_client:
            try:
                # Get existing task data
                existing_data = redis_client.get(f"task:{task_id}")
                if existing_data:
                    task_info = json.loads(existing_data)
                    task_info.update(task_data)
                    # Update in Redis with 24-hour expiration
                    redis_client.setex(
                        f"task:{task_id}", 86400, json.dumps(task_info)  # 24 hours
                    )
                    return
            except Exception as e:
                logger.warning(f"Failed to update task in Redis: {e}")

    # Fallback to in-memory
    if task_id in _task_registry:
        _task_registry[task_id].update(task_data)


def get_background_tasks_status() -> List[Dict[str, Any]]:
    """Get status of all background tasks."""
    tasks = []

    if is_redis_available():
        redis_client = get_redis_client()
        if redis_client:
            try:
                # Get all task keys from Redis
                task_keys = redis_client.keys("task:*")
                for key in task_keys:
                    task_data = redis_client.get(key)
                    if task_data:
                        tasks.append(json.loads(task_data))
                return tasks
            except Exception as e:
                logger.warning(f"Failed to get tasks from Redis: {e}")

    # Fallback to in-memory
    return list(_task_registry.values())


def get_background_task_by_id(task_id: str) -> Optional[Dict[str, Any]]:
    """Get specific background task by ID."""
    if is_redis_available():
        redis_client = get_redis_client()
        if redis_client:
            try:
                task_data = redis_client.get(f"task:{task_id}")
                if task_data:
                    parsed_data = json.loads(task_data)
                    if isinstance(parsed_data, dict):
                        return parsed_data
            except Exception as e:
                logger.warning(f"Failed to get task from Redis: {e}")

    # Fallback to in-memory
    return _task_registry.get(task_id)


def cleanup_old_tasks(hours: int = 24) -> int:
    """Clean up task registry entries older than specified hours."""
    cutoff_timestamp = (datetime.utcnow() - timedelta(hours=hours)).timestamp()
    removed_count = 0

    if is_redis_available():
        redis_client = get_redis_client()
        if redis_client:
            try:
                # Get all task keys from Redis
                task_keys = redis_client.keys("task:*")

                for key in task_keys:
                    try:
                        task_data = redis_client.get(key)
                        if task_data:
                            task_info = json.loads(task_data)
                            completed_at = task_info.get("completed_at")

                            if completed_at:
                                # Parse ISO format datetime
                                completed_dt = datetime.fromisoformat(
                                    completed_at.replace("Z", "+00:00")
                                )

                                if completed_dt.timestamp() < cutoff_timestamp:
                                    redis_client.delete(key)
                                    removed_count += 1
                    except Exception as e:
                        logger.warning(f"Error processing task key {key}: {e}")
                        continue

                return removed_count
            except Exception as e:
                logger.warning(f"Failed to cleanup Redis tasks: {e}")

    # Fallback to in-memory cleanup
    tasks_to_remove = []
    for task_id, task_info in _task_registry.items():
        completed_at = task_info.get("completed_at")
        if completed_at:
            try:
                completed_dt = datetime.fromisoformat(
                    completed_at.replace("Z", "+00:00")
                )
                if completed_dt.timestamp() < cutoff_timestamp:
                    tasks_to_remove.append(task_id)
            except Exception:
                # If we can't parse the date, consider it old
                tasks_to_remove.append(task_id)

    for task_id in tasks_to_remove:
        del _task_registry[task_id]

    return len(tasks_to_remove)


# Example background task functions
async def send_email_task(email: str, subject: str, message: str) -> None:
    """Example: Send email in background."""
    task_id = f"email_{int(time.time())}"
    track_background_task(task_id, f"Send email to {email}")

    try:
        logger.info(f"Sending email to {email}: {subject}")

        # Simulate email sending
        await asyncio.sleep(2)

        # In real implementation, you would integrate with email service
        # like SendGrid, AWS SES, etc.
        logger.info(f"Email sent successfully to {email}")

        complete_background_task(task_id)

    except Exception as e:
        logger.error(f"Failed to send email to {email}: {e}")
        complete_background_task(task_id, str(e))


async def process_file_task(file_path: str, process_type: str) -> None:
    """Example: Process file in background."""
    task_id = f"file_{process_type}_{int(time.time())}"
    track_background_task(task_id, f"Process file: {file_path}")

    try:
        logger.info(f"Processing file: {file_path} ({process_type})")

        # Simulate file processing
        if process_type == "csv":
            await asyncio.sleep(5)  # CSV processing
        elif process_type == "image":
            await asyncio.sleep(3)  # Image processing
        else:
            await asyncio.sleep(1)  # Generic processing

        logger.info(f"File processed successfully: {file_path}")
        complete_background_task(task_id)

    except Exception as e:
        logger.error(f"Failed to process file {file_path}: {e}")
        complete_background_task(task_id, str(e))


async def generate_report_task(report_type: str, parameters: Dict[str, Any]) -> None:
    """Example: Generate report in background."""
    task_id = f"report_{report_type}_{int(time.time())}"
    track_background_task(task_id, f"Generate {report_type} report")

    try:
        logger.info(f"Generating {report_type} report with parameters: {parameters}")

        # Simulate report generation
        await asyncio.sleep(10)  # Long-running task

        # In real implementation, you would:
        # 1. Query database
        # 2. Process data
        # 3. Generate PDF/Excel/etc
        # 4. Store in file system or cloud storage
        # 5. Optionally send notification

        logger.info(f"Report generated successfully: {report_type}")
        complete_background_task(task_id)

    except Exception as e:
        logger.error(f"Failed to generate report {report_type}: {e}")
        complete_background_task(task_id, str(e))


async def cleanup_temp_files_task() -> None:
    """Example: Cleanup temporary files."""
    task_id = f"cleanup_{int(time.time())}"
    track_background_task(task_id, "Cleanup temporary files")

    try:
        logger.info("Starting cleanup of temporary files")

        # Simulate cleanup process
        await asyncio.sleep(3)

        # In real implementation:
        # 1. Find files older than X days
        # 2. Delete them safely
        # 3. Log results

        logger.info("Temporary files cleanup completed")
        complete_background_task(task_id)

    except Exception as e:
        logger.error(f"Failed to cleanup temporary files: {e}")
        complete_background_task(task_id, str(e))


def add_background_task(
    background_tasks: BackgroundTasks,
    task_func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> None:
    """Helper to add background task with tracking."""
    background_tasks.add_task(task_func, *args, **kwargs)


# Periodic task example (would need scheduler like Celery Beat in production)
async def periodic_health_check() -> None:
    """Example: Periodic health check task."""
    while True:
        try:
            logger.info("Running periodic health check")

            # Check system health
            # Check database connections
            # Check external services
            # Log results

            await asyncio.sleep(300)  # Run every 5 minutes

        except Exception as e:
            logger.error(f"Periodic health check failed: {e}")
            await asyncio.sleep(60)  # Retry in 1 minute


# Task scheduler class for more complex scenarios
class BackgroundTaskScheduler:
    """Simple background task scheduler."""

    def __init__(self) -> None:
        self.tasks: List[asyncio.Task] = []
        self.running = False

    async def start(self) -> None:
        """Start the scheduler."""
        self.running = True
        logger.info("Background task scheduler started")

    async def stop(self) -> None:
        """Stop the scheduler and cancel all tasks."""
        self.running = False

        for task in self.tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)

        logger.info("Background task scheduler stopped")

    def schedule_periodic_task(
        self, task_func: Callable[[], Any], interval_seconds: int
    ) -> asyncio.Task:
        """Schedule a task to run periodically."""

        async def periodic_wrapper() -> None:
            while self.running:
                try:
                    await task_func()
                except Exception as e:
                    logger.error(f"Periodic task error: {e}")

                await asyncio.sleep(interval_seconds)

        task = asyncio.create_task(periodic_wrapper())
        self.tasks.append(task)
        return task


# Global scheduler instance
scheduler = BackgroundTaskScheduler()
