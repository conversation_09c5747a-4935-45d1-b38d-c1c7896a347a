"""
Simple Redis caching utilities.
"""

import hashlib
import json
import logging
import time
from functools import wraps
from typing import Any, Callable, Optional

from fastapi import Request
from fastapi.responses import JSONResponse

from app.utils.redis_utils import get_redis_client, is_redis_available

logger = logging.getLogger(__name__)


def cache_key_from_request(request: Request, prefix: str = "") -> str:
    """Generate cache key from request."""
    key_parts = [request.method, str(request.url.path), str(request.url.query)]
    key_string = "|".join(key_parts)
    key_hash = hashlib.sha256(key_string.encode()).hexdigest()[:16]
    return f"{prefix}:{key_hash}" if prefix else key_hash


def get_from_cache(cache_key: str) -> Optional[Any]:
    """Get data from Redis cache."""
    if not is_redis_available():
        return None

    redis_client = get_redis_client()
    if not redis_client:
        return None

    try:
        cached_data = redis_client.get(f"cache:{cache_key}")
        if cached_data:
            data = json.loads(cached_data)
            # Check if expired
            if time.time() < data.get("expires_at", 0):
                return data.get("data")
            else:
                # Remove expired entry
                redis_client.delete(f"cache:{cache_key}")
    except Exception as e:
        logger.warning(f"Failed to get from cache: {e}")

    return None


def set_in_cache(cache_key: str, data: Any, ttl_seconds: int = 300) -> None:
    """Set data in Redis cache."""
    if not is_redis_available():
        return

    redis_client = get_redis_client()
    if not redis_client:
        return

    try:
        cache_data = {"data": data, "expires_at": time.time() + ttl_seconds}
        redis_client.setex(f"cache:{cache_key}", ttl_seconds, json.dumps(cache_data))
    except Exception as e:
        logger.warning(f"Failed to set in cache: {e}")


def clear_cache() -> bool:
    """Clear all cache entries."""
    if not is_redis_available():
        return False

    redis_client = get_redis_client()
    if not redis_client:
        return False

    try:
        cache_keys = redis_client.keys("cache:*")
        if cache_keys:
            redis_client.delete(*cache_keys)
        return True
    except Exception as e:
        logger.warning(f"Failed to clear cache: {e}")
        return False


def cached_response(ttl_seconds: int = 300, prefix: str = "api") -> Callable[..., Any]:
    """
    Simple decorator for caching API responses.

    Args:
        ttl_seconds: Time to live in seconds
        prefix: Cache key prefix
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def wrapper(request: Request, *args: Any, **kwargs: Any) -> Any:
            # Generate cache key
            cache_key = cache_key_from_request(request, prefix)

            # Check cache
            cached_data = get_from_cache(cache_key)
            if cached_data is not None:
                return JSONResponse(content=cached_data)

            # Call original function
            result = await func(request, *args, **kwargs)

            # Cache the result if it's JSON serializable
            if isinstance(result, (dict, list)):
                set_in_cache(cache_key, result, ttl_seconds)
                return JSONResponse(content=result)

            return result

        return wrapper

    return decorator
