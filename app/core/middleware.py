"""
Security middleware for the FastAPI application.
"""

import logging
import uuid
from typing import Any, Callable

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """Add a unique request ID to each request."""

    async def dispatch(self, request: Request, call_next: Callable) -> Any:
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # Add request ID to response headers
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add basic security headers to all responses."""

    async def dispatch(self, request: Request, call_next: Callable) -> Any:
        response = await call_next(request)

        # Check if this is a documentation endpoint
        is_docs_endpoint = request.url.path in ["/docs", "/redoc", "/openapi.json"]

        if is_docs_endpoint:
            # Force permissive CSP for documentation endpoints (override any existing CSP)
            docs_csp = (
                "default-src 'self'; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "worker-src 'self' blob:; "
                "img-src 'self' data: https://fastapi.tiangolo.com https://cdn.redoc.ly; "
                "font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com; "
                "connect-src 'self' http://localhost:* https://oauth.pstmn.io"
            )
            response.headers["Content-Security-Policy"] = docs_csp
        else:
            # For non-docs endpoints, set standard CSP only if not already set
            if "Content-Security-Policy" not in response.headers:
                csp_policy = (
                    "default-src 'self'; "
                    "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
                    "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                    "worker-src 'self' blob:; "
                    "img-src 'self' data: https://fastapi.tiangolo.com https://cdn.redoc.ly; "
                    "font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com; "
                    "connect-src 'self' http://localhost:* https://oauth.pstmn.io; "
                    "frame-src 'self' https://www.google.com https://maps.google.com"
                )
                response.headers["Content-Security-Policy"] = csp_policy

        # Set other security headers if not already set
        if "X-Content-Type-Options" not in response.headers:
            response.headers["X-Content-Type-Options"] = "nosniff"
        if "X-Frame-Options" not in response.headers:
            response.headers["X-Frame-Options"] = "DENY"
        if "X-XSS-Protection" not in response.headers:
            response.headers["X-XSS-Protection"] = "1; mode=block"
        if "Strict-Transport-Security" not in response.headers:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )
        if "Referrer-Policy" not in response.headers:
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log all HTTP requests and responses."""

    async def dispatch(self, request: Request, call_next: Callable) -> Any:
        import time

        start_time = time.time()

        # Log request
        logger.info(f"Request: {request.method} {request.url}")

        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log response
        logger.info(
            f"Response: {response.status_code} "
            f"({process_time:.3f}s) - {request.method} {request.url}"
        )

        return response


class CORSMiddleware(BaseHTTPMiddleware):
    """Simple CORS middleware for development."""

    async def dispatch(self, request: Request, call_next: Callable) -> Any:
        response = await call_next(request)

        # Add CORS headers
        response.headers.update(
            {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization",
            }
        )

        return response


# Middleware configuration
MIDDLEWARE_STACK = [
    RequestIDMiddleware,
    SecurityHeadersMiddleware,
    RequestLoggingMiddleware,
    # Add CORSMiddleware only in development
]
