/* GeNieGO - Main Stylesheet */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.container {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.version {
    text-align: center;
    margin-bottom: 40px;
    opacity: 0.8;
    font-size: 1.1em;
}

.links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.link-card {
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease, background 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.link-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.3);
}

.link-card a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1em;
}

.link-card p {
    margin: 10px 0 0 0;
    opacity: 0.8;
    font-size: 0.9em;
}

.footer {
    text-align: center;
    margin-top: 40px;
    opacity: 0.7;
    font-size: 0.9em;
}
