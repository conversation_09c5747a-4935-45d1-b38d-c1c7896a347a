/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e1e2e 0%, #181825 50%, #11111b 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120,113,108,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(200,200,255,0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.02) 0%, transparent 50%);
    background-size: 300px 300px, 200px 200px, 100px 100px;
    pointer-events: none;
    z-index: 0;
}

.container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    padding: 2rem;
    padding-top: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.liquid-glass {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 
        0 8px 32px 0 rgba(31, 38, 135, 0.37),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}
.liquid-glass::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}
.liquid-glass:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 
        0 15px 40px 0 rgba(31, 38, 135, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        inset 0 -1px 0 rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.25);
}
.liquid-glass:hover::before {
    left: 100%;
}
.liquid-glass:active {
    transform: translateY(-2px) scale(0.98);
}
.liquid-glass.tracked {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

h1 {
    font-size: 2rem;
    font-weight: normal;
    color: white;
    margin-bottom: 0.5rem;
    text-align: center;
}

.version {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    margin-bottom: 3rem;
}

.links {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
    justify-content: center;
}

.link-card {
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.link-card a {
    color: white;
    text-decoration: none;
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.link-card p {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    margin: 10px 0 0 0;
    opacity: 0.8;
}

.footer {
    margin-top: 3rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    h1 {
        font-size: 1.5rem;
    }
    .container {
        padding: 1rem;
    }
    .links {
        flex-direction: column;
        align-items: center;
    }
    .link-card {
        padding: 1rem;
        max-width: 100%;
    }
}
