// Enhanced mouse tracking for liquid glass effect
document.addEventListener('mousemove', (e) => {
    const cards = document.querySelectorAll('.liquid-glass');
    cards.forEach(card => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;
            card.style.transform = `
                perspective(1000px) 
                rotateX(${deltaY * 5}deg) 
                rotateY(${deltaX * 5}deg) 
                translateZ(20px)
            `;
            card.classList.add('tracked');
        } else {
            card.style.transform = '';
            card.classList.remove('tracked');
        }
    });
});

document.addEventListener('mouseleave', () => {
    const cards = document.querySelectorAll('.liquid-glass');
    cards.forEach(card => {
        card.style.transform = '';
        card.classList.remove('tracked');
    });
});
