{
	"info": {
		"_postman_id": "geniego-sso-2024-001",
		"name": "GeNieGO SSO Server API",
		"description": "# 🔐 GeNieGO SSO Server API Collection\n\n## 🚀 **Quick Start Guide**\n\n### **OAuth2 Authorization Server**\nGeNieGO SSO Server is the central OAuth2 authorization server for the Genieland ecosystem. It provides authentication services for all applications with enhanced connection tracking and activity logging.\n\n### **Available Flows:**\n\n#### 1. **Authorization Code Flow** (Primary for applications)\n**Step 1:** Get authorization code\n```\nGET /oauth2/authorize?response_type=code&client_id=application-client&redirect_uri=http://localhost:3000/callback&scope=openid profile&state=test123\n```\n**Step 2:** Exchange code for token\n```\nPOST /oauth2/token\nContent-Type: application/x-www-form-urlencoded\n\ngrant_type=authorization_code\ncode={authorization_code}\nredirect_uri=http://localhost:3000/callback\nclient_id=application-client\nclient_secret=application-secret\n```\n**Step 3:** Get user information\n```\nPOST /oauth2/userinfo\nContent-Type: application/x-www-form-urlencoded\n\nauthorization_code={authorization_code}\nclient_id=application-client\n```""\n\n### **🔧 Setup Instructions:**\n\n1. **Start the SSO Server:** `make docker-up` or `docker compose up -d`\n2. **Server runs on:** http://localhost:5550\n3. **Use Authorization Code flow** for application integration\n4. **Use the token** in Authorization header: `Bearer {access_token}`\n\n### **📋 API Endpoints:**\n\n**OAuth2 Endpoints:**\n- `GET /oauth2/authorize` - Authorization endpoint with connection tracking\n- `POST /oauth2/token` - Token exchange endpoint with activity logging\n- `POST /oauth2/userinfo` - User information endpoint with activity logging\n\n**User Management:**\n- `GET /api/v1/users` - List users with filtering\n- `GET /api/v1/users/{id}` - Get user by ID\n- `PUT /api/v1/users/{id}` - Update user\n- `DELETE /api/v1/users/{id}` - Delete user\n- `GET /api/v1/users/me` - Get current user profile\n- `PUT /api/v1/users/me` - Update current user profile\n- `POST /api/v1/users/{id}/deactivate` - Deactivate user\n**Authentication:**\n- `POST /api/v1/auth/login` - User login\n- `POST /api/v1/auth/logout` - User logout\n- `POST /api/v1/auth/register` - User registration\n- `POST /api/v1/auth/verify-email` - Email verification\n- `POST /api/v1/auth/forgot-password` - Password reset request\n- `POST /api/v1/auth/reset-password` - Password reset\n\n**Health & Status:**\n- `GET /health` - Basic health check\n- `GET /api/health` - API health check (consolidated)\n\n### **🔑 Environment Variables:**\n\n```\nbase_url = http://localhost:5550\nredirect_uri = http://localhost:3000/callback\naccess_token = {auto-captured from OAuth2 Token}\nuser_id = {auto-captured from user operations}\nauthorization_code = {auto-captured from authorization}\n```\n\n### **💡 Tips:**\n- All requests automatically capture relevant IDs and tokens\n- Use Authorization Code flow for application integration\n- Server runs on port 5550 (updated from 5550)\n- All endpoints follow OAuth2 and REST conventions\n- Check the Tests tab for automatic variable capture scripts",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"item": [
		{
			"name": "Health & Status",
			"item": [
				{
					"name": "Basic Health Check",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/health",
							"host": ["{{base_url}}"],
							"path": ["health"]
						}
					}
				},
				{
					"name": "API Health Check",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/health",
							"host": ["{{base_url}}"],
							"path": ["api", "health"]
						}
					}
				}
			]
		},
		{
			"name": "🔐 OAuth2 Authorization",
			"item": [
				{
					"name": "OAuth2 Authorization Endpoint",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"// Extract authorization code from redirect URL if present",
									"if (pm.response.code === 302) {",
									"    const location = pm.response.headers.get('Location');",
									"    if (location && location.includes('code=')) {",
									"        const urlParams = new URLSearchParams(location.split('?')[1]);",
									"        const code = urlParams.get('code');",
									"        if (code) {",
									"            pm.environment.set('authorization_code', code);",
									"            console.log('Authorization code captured:', code);",
									"        }",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/oauth2/authorize?response_type=code&client_id=application-client&redirect_uri={{redirect_uri}}&scope=openid profile&state=test123",
							"host": ["{{base_url}}"],
							"path": ["oauth2", "authorize"],
							"query": [
								{
									"key": "response_type",
									"value": "code"
								},
								{
									"key": "client_id",
									"value": "application-client"
								},
								{
									"key": "redirect_uri",
									"value": "{{redirect_uri}}"
								},
								{
									"key": "scope",
									"value": "openid profile"
								},
								{
									"key": "state",
									"value": "test123"
								}
							]
						}
					}
				},
				{
					"name": "OAuth2 Token Exchange",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    if (response.access_token) {",
									"        pm.environment.set('access_token', response.access_token);",
									"        console.log('Access token captured');",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/x-www-form-urlencoded"
							}
						],
						"body": {
							"mode": "urlencoded",
							"urlencoded": [
								{
									"key": "grant_type",
									"value": "authorization_code",
									"type": "text"
								},
								{
									"key": "code",
									"value": "{{authorization_code}}",
									"type": "text"
								},
								{
									"key": "redirect_uri",
									"value": "{{redirect_uri}}",
									"type": "text"
								},
								{
									"key": "client_id",
									"value": "application-client",
									"type": "text"
								},
								{
									"key": "client_secret",
									"value": "application-secret",
									"type": "text"
								}
							]
						},
						"url": {
							"raw": "{{base_url}}/oauth2/token",
							"host": ["{{base_url}}"],
							"path": ["oauth2", "token"]
						}
					}
				},
				{
					"name": "OAuth2 User Info",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/oauth2/userinfo",
							"host": ["{{base_url}}"],
							"path": ["oauth2", "userinfo"]
						}
					}
				}
			]
		},
		{
			"name": "👤 User Management",
			"item": [
				{
					"name": "List Users",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/users?page=1&per_page=10",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users"],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "per_page",
									"value": "10"
								},
								{
									"key": "is_active",
									"value": "true",
									"disabled": true
								},
								{
									"key": "search",
									"value": "",
									"disabled": true
								}
							]
						}
					}
				},
				{
					"name": "Get User by ID",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    if (response.id) {",
									"        pm.environment.set('user_id', response.id);",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/users/{{user_id}}",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "{{user_id}}"]
						}
					}
				},
				{
					"name": "Update User",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"email\": \"<EMAIL>\",\n    \"is_active\": true,\n    \"is_verified\": true\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/users/{{user_id}}",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "{{user_id}}"]
						}
					}
				},
				{
					"name": "Get Current User Profile",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/users/me",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "me"]
						}
					}
				},
				{
					"name": "Update Current User Profile",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"display_name\": \"Updated Name\",\n    \"bio\": \"Updated bio\",\n    \"avatar_url\": \"https://example.com/avatar.jpg\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/users/me",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "me"]
						}
					}
				},
				{
					"name": "Deactivate User",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/users/{{user_id}}/deactivate",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "{{user_id}}", "deactivate"]
						}
					}
				},
				{
					"name": "Delete User",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/users/{{user_id}}",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "users", "{{user_id}}"]
						}
					}
				}
			]
		},
		{
			"name": "🔑 Authentication",
			"item": [
				{
					"name": "User Login",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    if (response.access_token) {",
									"        pm.environment.set('access_token', response.access_token);",
									"    }",
									"    if (response.user && response.user.id) {",
									"        pm.environment.set('user_id', response.user.id);",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"username\": \"testuser\",\n    \"password\": \"testpassword123\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/login",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "login"]
						}
					}
				},
				{
					"name": "User Registration",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 201) {",
									"    const response = pm.response.json();",
									"    if (response.id) {",
									"        pm.environment.set('user_id', response.id);",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"newpassword123\",\n    \"display_name\": \"New User\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/register",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "register"]
						}
					}
				},
				{
					"name": "User Logout",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/auth/logout",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "logout"]
						}
					}
				},
				{
					"name": "Verify Email",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"token\": \"verification-token-here\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/verify-email",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "verify-email"]
						}
					}
				},
				{
					"name": "Forgot Password",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/forgot-password",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "forgot-password"]
						}
					}
				},
				{
					"name": "Reset Password",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"token\": \"reset-token-here\",\n    \"new_password\": \"newpassword123\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/reset-password",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "reset-password"]
						}
					}
				}
			]
		}
	],
	"event": [
		{
			"listen": "prerequest",
			"script": {
				"type": "text/javascript",
				"exec": [
					""
				]
			}
		},
		{
			"listen": "test",
			"script": {
				"type": "text/javascript",
				"exec": [
					""
				]
			}
		}
	],
	"variable": [
		{
			"key": "base_url",
			"value": "http://localhost:5550",
			"type": "string"
		},
		{
			"key": "redirect_uri",
			"value": "http://localhost:5550/auth/callback",
			"type": "string"
		}
	]
}
