# Core FastAPI dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
python-multipart>=0.0.6

# Database
sqlalchemy>=2.0.0
aiomysql>=0.2.0
alembic>=1.11.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Multi-Factor Authentication
pyotp>=2.8.0
qrcode[pil]>=7.4.0

# Rate Limiting & Caching
redis>=4.5.0
aioredis>=2.0.0

# Validation & Settings
pydantic>=2.0.0
pydantic-settings>=2.0.0
email-validator>=2.0.0

# Environment & Configuration
python-dotenv>=1.0.0

# HTTP client for external services
httpx>=0.24.0

# Logging and monitoring
structlog>=23.1.0

# Background tasks and queues
celery>=5.3.0
