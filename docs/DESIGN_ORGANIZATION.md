# Organization Management System Design

## Organization Management System

### Overview
The organization management system allows developers to create organizations, invite users, and manage team-based application development.

### System Role vs Organization Role Matrix

This matrix clarifies the relationship between **system roles** (user's role in the SSO system) and **organization roles** (user's role within a specific organization) and their access to organization-related features.

#### System Roles:
- **User (system)**: Regular users of the SSO system, can use applications
- **Developer (system)**: Can create applications and organizations, still external users
- **Admin (system)**: System administrators, can oversee everything

#### Organization Roles:
- **Member**: Basic member of an organization
- **Admin**: Can manage organization members and settings
- **Owner**: Full control over organization

#### Access Matrix:

| System Role | Organization Role | Can Create Orgs | Can Manage Orgs | Can View User Invitations | Can Accept Org Invitations | Notes |
|-------------|------------------|-----------------|-----------------|---------------------------|----------------------------|-------|
| **User** | None | ❌ | ❌ | ✅ | ✅ | Regular users can be invited to organizations |
| **User** | Member | ❌ | ❌ | ✅ | ✅ | Can be invited to other organizations |
| **User** | Admin | ❌ | ✅ (this org only) | ✅ | ✅ | Can manage this org, be invited to others |
| **User** | Owner | ❌ | ✅ (this org only) | ✅ | ✅ | Owns this org, can be invited to others |
| **Developer** | None | ✅ | ❌ | ✅ | ✅ | Can create orgs, can be invited to others |
| **Developer** | Member | ✅ | ❌ | ✅ | ✅ | Can create orgs, member of this org |
| **Developer** | Admin | ✅ | ✅ (this org only) | ✅ | ✅ | Can create orgs, admin of this org |
| **Developer** | Owner | ✅ | ✅ (this org only) | ✅ | ✅ | Can create orgs, owns this org |
| **Admin** | Any/None | ✅ | ✅ (all orgs) | ❌ | ❌ | System admin, doesn't need invitations |

#### Key Principles:

1. **System Role ≠ Organization Role**: A developer (system) can be a member (organization) in someone else's org
2. **Developers are External Users**: They use SSO features and can be invited to organizations
3. **Users Can Be Invited**: Regardless of their organization roles, users/developers can receive invitations
4. **Admins Oversee Everything**: System admins don't need invitations, they have access to all organizations
5. **Multiple Organization Membership**: Users/developers can be members of multiple organizations with different roles

#### Page Access Rules:

- **User Invitations Page** (`/user/invitations`): 
  - ✅ **User (system)**: Can view and manage their organization invitations
  - ✅ **Developer (system)**: Can view and manage their organization invitations  
  - ❌ **Admin (system)**: No need for invitations, has access to all organizations

- **Organization Management** (`/developer/organizations`):
  - ❌ **User (system)**: Cannot create organizations
  - ✅ **Developer (system)**: Can create and manage organizations
  - ✅ **Admin (system)**: Can oversee all organizations

- **Organization Dashboard** (`/developer/organizations/{id}`):
  - ❌ **User (system)**: Cannot access organization management
  - ✅ **Developer (system)**: Can access if they have admin/owner role in that organization
  - ✅ **Admin (system)**: Can access any organization

### Invitation Workflow

#### 1. Sending Invitations
- Organization admin/owner searches for users by email
- System shows all users (both User and Developer system roles)
- Admin sends invitation with specified organization role (member/admin)
- Invitation stored with pending status

#### 2. Receiving Invitations
- Users and Developers can view invitations in `/user/invitations`
- Invitations show organization details, role, and inviter information
- Users can accept or decline invitations

#### 3. Accepting Invitations
- Creates organization membership with specified role
- User gains access to organization based on their organization role
- Invitation status updated to "accepted"

#### 4. Managing Invitations
- Organization admins can cancel pending invitations
- System admins can oversee all invitations
- Users can view their invitation history

### Implementation Status

#### Completed Features:
- ✅ Organization creation and management
- ✅ User invitation system (backend)
- ✅ User invitations page (`/user/invitations`)
- ✅ Organization dashboard with member management
- ✅ Invitation accept/decline functionality
- ✅ Cancel invitation functionality
- ✅ Role-based access control (needs correction)

#### Pending Features:
- ⏳ Invitation notification system
- ⏳ User dashboard invitation widget
- ⏳ Invitation count badges
- ⏳ Email notifications for invitations

### Access Control Corrections Needed

The current implementation incorrectly blocks Developer (system) users from accessing user invitations. This needs to be corrected:

**WRONG (Current)**:
```javascript
// Blocks developers from seeing invitations - INCORRECT
if (user && (user.role === 'admin' || user.role === 'developer')) {
  // Block access - WRONG!
}
```

**CORRECT (Should Be)**:
```javascript
// Only block system admins from seeing invitations
if (user && user.role === 'admin') {
  // Block access for system admins only
  // Developers should still see invitations!
}
```

This correction ensures that:
- **User (system)**: ✅ Can see invitations
- **Developer (system)**: ✅ Can see invitations (FIXED)
- **Admin (system)**: ❌ Cannot see invitations (don't need them)
