## **CRITICAL DEPLOYMENT REMARKS**

- ONLY LOGIN VIA /login DON'T EVEN LOOP WITH /auth/login
- Never Deploy a docker for frontend
- Frontend are included in python server
- Ask for feedback(feedback-enhanced-mcp) every time you finish testing

**Critical Deployment Commands**:
```bash
# 1. Build frontend
cd frontend && npm run build

# 2. Deploy with <PERSON><PERSON> (ALWAYS use this command)
docker compose -f docker-compose.yml up -d --build

# 3. Test deployment
curl -f http://localhost:5550/
```

<!-- ## **Critical Testing Process**
- Use playwright-mcp to study frontend issue
- Never call click or fill functions in playwright-mcp, ask me with to do for you and ask me via feedback-enhanced-mcp -->