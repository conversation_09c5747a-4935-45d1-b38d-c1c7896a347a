# GeNieGO SSO Server - Production Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the GeNieGO SSO Server to production at `https://geniego.genieland.ai`.

## Prerequisites

- ✅ Google OAuth credentials configured (steps 1-5 from Google OAuth Setup Guide completed)
- ✅ Domain `geniego.genieland.ai` available
- ✅ SSL certificate for the domain
- ✅ Docker and Docker Compose installed on production server
- ✅ GeNieGO SSO Server code deployed to production server

## Step 6: Update Domain Configuration

### 6.1 DNS Configuration

1. **Point Domain to Server**
   ```bash
   # Ensure geniego.genieland.ai points to your production server IP
   # Example DNS record:
   # A    geniego.genieland.ai    YOUR_SERVER_IP
   ```

2. **Verify DNS Resolution**
   ```bash
   nslookup geniego.genieland.ai
   # Should return your server IP
   ```

### 6.2 SSL Certificate Setup

1. **Install SSL Certificate**
   - Use Let's Encrypt, Cloudflare, or your preferred SSL provider
   - Ensure certificate covers `geniego.genieland.ai`

2. **Configure Reverse Proxy (Nginx/Apache)**
   ```nginx
   # Example Nginx configuration
   server {
       listen 443 ssl;
       server_name geniego.genieland.ai;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       location / {
           proxy_pass http://localhost:5550;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

## Step 7: Production Environment Configuration

### 7.1 Update Environment Variables

Create production `.env` file:

```env
# GeNieGO SSO Server Production Configuration

# Application Settings
PROJECT_NAME=GeNieGO SSO Server
VERSION=1.0.0
API_PREFIX=/api
API_VERSION=v1
HOST=0.0.0.0
PORT=5550
DEBUG=false
ENVIRONMENT=production

# SSO Server Configuration
SSO_DOMAIN=geniego.genieland.ai
SSO_BASE_URL=https://geniego.genieland.ai

# Database Configuration (Production)
DATABASE_URL=mysql+aiomysql://geniengo_user:STRONG_PASSWORD_HERE@geniengo-sso-mysql:3306/geniengo_sso_db

# Redis Configuration
REDIS_URL=redis://geniengo-sso-redis:6379/0

# JWT Configuration (CHANGE THESE IN PRODUCTION)
JWT_SECRET_KEY=GENERATE_STRONG_JWT_SECRET_KEY_HERE
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Configuration (Your actual credentials)
GOOGLE_CLIENT_ID=753487643909-7s8tj7ea95pl54ino3m1mm2kjoqp6a8r.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://geniego.genieland.ai/api/v1/auth/google/callback
GOOGLE_REDIRECT_URI_LOCAL=http://localhost:5550/api/v1/auth/google/callback

# CORS Configuration
CORS_ORIGINS=https://geniego.genieland.ai,https://geniemove.genieland.ai

# Email Configuration
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=GeNieGO SSO Server

# OAuth2 Application Client IDs
OAUTH2_CLIENT_GENIE_MOVE=genie-move-auth-v4
OAUTH2_CLIENT_SYSTEM=system
OAUTH2_CLIENT_ADMIN=geniengo-admin-client

# Application Client Secrets (CHANGE IN PRODUCTION)
OAUTH2_CLIENT_GENIE_MOVE_SECRET=GENERATE_STRONG_SECRET_HERE
OAUTH2_CLIENT_SYSTEM_SECRET=GENERATE_STRONG_SECRET_HERE
OAUTH2_CLIENT_ADMIN_SECRET=GENERATE_STRONG_SECRET_HERE

# Security Settings
ENABLE_DOCS=false
ENABLE_ADMIN_ENDPOINTS=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
LOG_LEVEL=INFO
```

### 7.2 Update Docker Compose for Production

Create `docker-compose.prod.yml`:

```yaml
services:
  mysql:
    image: mysql:8.0
    container_name: geniengo-sso-mysql
    environment:
      MYSQL_ROOT_PASSWORD: STRONG_ROOT_PASSWORD_HERE
      MYSQL_DATABASE: geniengo_sso_db
      MYSQL_USER: geniengo_user
      MYSQL_PASSWORD: STRONG_PASSWORD_HERE
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - geniengo-sso-network

  redis:
    image: redis:7-alpine
    container_name: geniengo-sso-redis
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - geniengo-sso-network

  sso-server:
    build: .
    container_name: geniengo-sso-server
    ports:
      - "5550:5550"
    env_file:
      - .env
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5550/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - geniengo-sso-network

networks:
  geniengo-sso-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
```

## Step 8: Test Google OAuth Integration

### 8.1 Local Testing (Current Setup)

1. **Test OAuth2 Health Endpoint**
   ```bash
   curl -X GET http://localhost:5550/oauth2/health
   ```

2. **Test Authorization Endpoint**
   ```bash
   curl -v "http://localhost:5550/oauth2/authorize?client_id=system&redirect_uri=http://localhost:5550/auth/callback&response_type=code&state=test123"
   ```

3. **Test Google OAuth Flow (Local)**
   - Visit: `http://localhost:5550/api/v1/auth/google/login`
   - Should redirect to Google OAuth consent screen
   - After consent, should redirect back to your application

### 8.2 Production Testing

1. **Deploy to Production**
   ```bash
   # On production server
   docker compose -f docker-compose.prod.yml up -d
   ```

2. **Test Production Endpoints**
   ```bash
   curl -X GET https://geniego.genieland.ai/oauth2/health
   ```

3. **Test Google OAuth Flow (Production)**
   - Visit: `https://geniego.genieland.ai/api/v1/auth/google/login`
   - Should redirect to Google OAuth consent screen
   - After consent, should redirect back to your application

## Security Checklist

### ✅ Completed Security Measures

- ✅ Google OAuth credentials configured
- ✅ HTTPS/SSL certificate required for production
- ✅ Environment-specific redirect URIs
- ✅ CORS origins restricted to production domains
- ✅ Debug mode disabled in production
- ✅ Rate limiting enabled
- ✅ JWT secret keys configured
- ✅ Database passwords secured

### 🔧 Additional Security Recommendations

1. **Generate Strong Secrets**
   ```bash
   # Generate JWT secret
   openssl rand -hex 32
   
   # Generate client secrets
   openssl rand -hex 24
   ```

2. **Database Security**
   - Use strong passwords for MySQL
   - Restrict database access to application only
   - Enable MySQL SSL if needed

3. **Monitoring**
   - Set up application monitoring
   - Monitor OAuth2 usage patterns
   - Set up alerts for failed authentication attempts

## Integration with GENIE-MOVE-AUTH-V4

### Application Configuration

The GeNieGO SSO Server is now ready to integrate with GENIE-MOVE-AUTH-V4:

1. **Client Registration**
   - Client ID: `genie-move-auth-v4`
   - Client Secret: (configured in environment)
   - Redirect URI: `https://geniemove.genieland.ai/auth/callback`

2. **OAuth2 Flow**
   - Authorization URL: `https://geniego.genieland.ai/oauth2/authorize`
   - User Info URL: `https://geniego.genieland.ai/oauth2/userinfo`

## Troubleshooting

### Common Issues

1. **Google OAuth "redirect_uri_mismatch"**
   - Verify redirect URI in Google Cloud Console matches exactly
   - Check for HTTP vs HTTPS mismatch

2. **CORS Errors**
   - Ensure production domain is in CORS_ORIGINS
   - Check for trailing slashes in URLs

3. **SSL Certificate Issues**
   - Verify certificate is valid and covers the domain
   - Check certificate chain is complete

### Logs and Monitoring

```bash
# Check application logs
docker logs geniengo-sso-server

# Check database connectivity
docker exec geniengo-sso-server curl -f http://localhost:5550/health

# Monitor OAuth2 health
curl https://geniego.genieland.ai/oauth2/health
```

## Final Verification

✅ **Production Deployment Checklist:**

- [ ] DNS pointing to production server
- [ ] SSL certificate installed and working
- [ ] Production environment variables configured
- [ ] Docker containers running in production
- [ ] Google OAuth flow tested end-to-end
- [ ] OAuth2 endpoints responding correctly
- [ ] CORS configured for production domains
- [ ] Security settings applied
- [ ] Monitoring and logging configured

**🎯 Status: Ready for Production Integration with GENIE-MOVE-AUTH-V4**
