# Google OAuth Integration Setup Guide for GeNieGO SSO Server

## Overview

This guide provides detailed steps to set up Google OAuth integration for the GeNieGO SSO Server. Users will be able to register and login using their Google accounts.

## Prerequisites

- Google Cloud Platform (GCP) account
- GeNieGO SSO Server deployed and accessible
- Domain name for your SSO server (e.g., `sso.yourdomain.com`)

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create New Project**
   - Click "Select a project" dropdown at the top
   - Click "New Project"
   - Enter project name: `GeNieGO SSO Server`
   - Select your organization (if applicable)
   - Click "Create"

3. **Select Your Project**
   - Make sure your new project is selected in the project dropdown

## Step 2: Enable Google+ API

1. **Navigate to APIs & Services**
   - In the left sidebar, click "APIs & Services" > "Library"

2. **Enable Required APIs**
   - Search for "Google+ API" and click on it
   - Click "Enable"
   - Also enable "Google Identity" API if available

## Step 3: Configure OAuth Consent Screen

1. **Go to OAuth Consent Screen**
   - In the left sidebar, click "APIs & Services" > "OAuth consent screen"

2. **Choose User Type**
   - Select "External" (for public use)
   - Click "Create"

3. **Fill App Information**
   - **App name**: `GeNieGO SSO Server`
   - **User support email**: Your email address
   - **App logo**: Upload your company/app logo (optional)
   - **App domain**: Your domain (e.g., `yourdomain.com`)
   - **Authorized domains**: Add your domain (e.g., `yourdomain.com`)
   - **Developer contact information**: Your email address
   - Click "Save and Continue"

4. **Scopes Configuration**
   - Click "Add or Remove Scopes"
   - Add these scopes:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
     - `openid`
   - Click "Update" then "Save and Continue"

5. **Test Users (Optional)**
   - Add test user emails if needed during development
   - Click "Save and Continue"

6. **Summary**
   - Review your settings
   - Click "Back to Dashboard"

## Step 4: Create OAuth 2.0 Credentials

1. **Go to Credentials**
   - In the left sidebar, click "APIs & Services" > "Credentials"

2. **Create Credentials**
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"

3. **Configure OAuth Client**
   - **Application type**: Web application
   - **Name**: `GeNieGO SSO Server Web Client`
   
4. **Add Authorized Redirect URIs**
   Add these URIs (replace `yourdomain.com` with your actual domain):
   ```
   http://localhost:5550/auth/google/callback
   https://sso.yourdomain.com/auth/google/callback
   ```

5. **Create Client**
   - Click "Create"
   - **IMPORTANT**: Copy and save the Client ID and Client Secret

## Step 5: Configure GeNieGO SSO Server

1. **Update Environment Variables**
   Add these to your `.env` file:
   ```env
   # Google OAuth Configuration
   GOOGLE_CLIENT_ID=your_google_client_id_here
   GOOGLE_CLIENT_SECRET=your_google_client_secret_here
   GOOGLE_REDIRECT_URI=https://sso.yourdomain.com/auth/google/callback
   ```

2. **For Local Development**
   ```env
   GOOGLE_REDIRECT_URI=http://localhost:5550/auth/google/callback
   ```

## Step 6: Update Domain Configuration

1. **Production Deployment**
   - Ensure your SSO server is accessible at your configured domain
   - Update CORS settings in your application to allow your domain
   - Ensure HTTPS is properly configured

2. **DNS Configuration**
   - Point your subdomain (e.g., `sso.yourdomain.com`) to your server IP
   - Ensure SSL certificate is properly configured

## Step 7: Test Google OAuth Integration

1. **Access Login Page**
   - Go to: `https://sso.yourdomain.com/auth/login`
   - You should see a "Login with Google" button

2. **Test Login Flow**
   - Click "Login with Google"
   - You should be redirected to Google's OAuth consent screen
   - Grant permissions
   - You should be redirected back to your application

3. **Verify User Creation**
   - Check your database for the new user record
   - Verify the user has a `google_id` field populated

## Step 8: Production Considerations

### Security Settings

1. **Restrict API Keys** (if using any)
   - Go to "APIs & Services" > "Credentials"
   - Edit your API keys and add HTTP referrer restrictions

2. **OAuth Client Security**
   - Regularly rotate client secrets
   - Monitor OAuth usage in Google Cloud Console

### Domain Verification

1. **Verify Domain Ownership**
   - In Google Cloud Console, go to "APIs & Services" > "Domain verification"
   - Add and verify your domain

### Monitoring

1. **Enable Logging**
   - Monitor OAuth requests in Google Cloud Console
   - Set up alerts for unusual activity

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" Error**
   - Ensure the redirect URI in your code matches exactly what's configured in Google Cloud Console
   - Check for trailing slashes and HTTP vs HTTPS

2. **"invalid_client" Error**
   - Verify your Client ID and Client Secret are correct
   - Ensure the OAuth client is enabled

3. **"access_denied" Error**
   - User denied permission
   - Check OAuth consent screen configuration

4. **SSL/HTTPS Issues**
   - Google requires HTTPS for production OAuth flows
   - Ensure your SSL certificate is valid

### Debug Steps

1. **Check Application Logs**
   ```bash
   docker logs geniengo-sso-server
   ```

2. **Verify Environment Variables**
   ```bash
   docker exec geniengo-sso-server env | grep GOOGLE
   ```

3. **Test Redirect URI**
   - Manually visit your redirect URI to ensure it's accessible

## Support

For additional support:
- Check Google OAuth 2.0 documentation: https://developers.google.com/identity/protocols/oauth2
- Review GeNieGO SSO Server logs for detailed error messages
- Ensure all environment variables are properly set

## Security Notes

- Never commit Client ID and Client Secret to version control
- Use environment variables for all sensitive configuration
- Regularly review and rotate OAuth credentials
- Monitor OAuth usage for suspicious activity
- Implement proper session management and logout functionality
